// Unit Tests for Geometric Analysis
// Tests geometric scoring, convex hull, and sacred geometry calculations

use basilisk_bot::math::geometry::RealGeometricScorer;
use geo::{Point, algorithm::convex_hull::ConvexHull};
use basilisk_bot::shared_types::GeometricScorer;
use basilisk_bot::data::oracle::MockPriceOracle;
use basilisk_bot::shared_types::{Pool, ArbitragePool};
use ethers::types::Address;
use proptest::prelude::*;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::str::FromStr;

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_empty_pools_geometric_score() {
        let mock_oracle = MockPriceOracle::new();
        let scorer = RealGeometricScorer::new(mock_oracle);
        let path = vec![];
        let score = scorer.calculate_score(&path).await.unwrap();
        
        // Empty paths should return neutral scores
        assert_eq!(score.convexity_ratio, dec!(0.5));
        assert_eq!(score.liquidity_centroid_bias, dec!(0.5));
        assert_eq!(score.harmonic_path_score, dec!(0.5));
        assert_eq!(score.vesica_piscis_depth, dec!(0.5));
    }

    #[tokio::test]
    async fn test_single_pool_geometric_score() {
        let mock_oracle = MockPriceOracle::new();
        let scorer = RealGeometricScorer::new(mock_oracle);
        let path = vec![
            ArbitragePool {
                address: Address::from_str("******************************************").unwrap(),
                token0_symbol: "WETH".to_string(),
                token1_symbol: "USDC".to_string(),
                reserve0: dec!(1000.0),
                reserve1: dec!(1000.0),
                protocol: "test".to_string(),
            },
        ];
        
        let score = scorer.calculate_score(&path).await.unwrap();
        
        // Single pool should return neutral scores as it's too short for full analysis
        assert_eq!(score.convexity_ratio, dec!(0.5));
        assert_eq!(score.liquidity_centroid_bias, dec!(0.8)); // Single pool is well-centered
        assert_eq!(score.harmonic_path_score, dec!(0.5));
        assert_eq!(score.vesica_piscis_depth, dec!(0.5));
    }

    #[test]
    fn test_point_distance_properties() {
        let test_cases = vec![
            (Point::new(0.0, 0.0), Point::new(3.0, 4.0), 5.0),
            (Point::new(1.0, 1.0), Point::new(1.0, 1.0), 0.0),
            (Point::new(-1.0, -1.0), Point::new(1.0, 1.0), 2.828427124746190),
        ];

        for (p1, p2, expected_distance) in test_cases {
            let distance = p1.distance(&p2);
            assert!(
                (distance - expected_distance).abs() < 1e-10,
                "Distance between {:?} and {:?} should be {}, got {}",
                p1, p2, expected_distance, distance
            );
            
            // Test symmetry
            assert_eq!(distance, p2.distance(&p1));
        }
    }

    #[test]
    fn test_convex_hull_basic() {
        // Test with a simple square
        let points = vec![
            Point::new(0.0, 0.0),
            Point::new(1.0, 0.0),
            Point::new(1.0, 1.0),
            Point::new(0.0, 1.0),
            Point::new(0.5, 0.5), // Interior point
        ];
        
        let hull = convex_hull(&points);
        
        // Hull should have 4 points (the square corners)
        assert_eq!(hull.len(), 4);
        
        // Interior point should not be in hull
        assert!(!hull.iter().any(|p| (p.x - 0.5).abs() < 1e-10 && (p.y - 0.5).abs() < 1e-10));
    }

    #[test]
    fn test_convex_hull_collinear() {
        // Test with collinear points
        let points = vec![
            Point::new(0.0, 0.0),
            Point::new(1.0, 0.0),
            Point::new(2.0, 0.0),
            Point::new(3.0, 0.0),
        ];
        
        let hull = convex_hull(&points);
        
        // Hull should have 2 points (endpoints)
        assert_eq!(hull.len(), 2);
    }

    #[tokio::test]
    async fn test_geometric_score_bounds() {
        let mock_oracle = MockPriceOracle::new();
        let scorer = RealGeometricScorer::new(mock_oracle);

        // Create test arbitrage path with known properties
        let path = vec![
            ArbitragePool {
                address: Address::random(),
                token0_symbol: "WETH".to_string(),
                token1_symbol: "USDC".to_string(),
                reserve0: dec!(1000.0),
                reserve1: dec!(1000.0),
                protocol: "uniswap".to_string(),
            },
            ArbitragePool {
                address: Address::random(),
                token0_symbol: "USDC".to_string(),
                token1_symbol: "DAI".to_string(),
                reserve0: dec!(2000.0),
                reserve1: dec!(2000.0),
                protocol: "sushiswap".to_string(),
            },
            ArbitragePool {
                address: Address::random(),
                token0_symbol: "DAI".to_string(),
                token1_symbol: "WETH".to_string(),
                reserve0: dec!(1500.0),
                reserve1: dec!(1500.0),
                protocol: "curve".to_string(),
            },
        ];
        
        let score = scorer.calculate_score(&path).await.unwrap();
        
        // Scores should be bounded between 0 and 1
        assert!(score.convexity_ratio >= Decimal::ZERO);
        assert!(score.convexity_ratio <= Decimal::ONE);
        assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
        assert!(score.liquidity_centroid_bias <= Decimal::ONE);
        assert!(score.harmonic_path_score >= Decimal::ZERO);
        assert!(score.harmonic_path_score <= Decimal::ONE);
        assert!(score.vesica_piscis_depth >= Decimal::ZERO);
        assert!(score.vesica_piscis_depth <= Decimal::ONE);
    }
}

// Property-based tests
proptest! {
    fn test_geometric_score_properties(
        pool_count in 3..10usize, // Need at least 3 pools for meaningful geometric analysis
        liquidity_values in prop::collection::vec(1.0..1000000.0_f64, 3..10)
    ) {
        let rt = tokio::runtime::Runtime::new().unwrap();
        rt.block_on(async {
            let mock_oracle = MockPriceOracle::new();
            let scorer = RealGeometricScorer::new(mock_oracle);

            let path: Vec<ArbitragePool> = (0..pool_count).zip(liquidity_values.iter()).map(|(i, &liquidity)| {
                ArbitragePool {
                    address: Address::random(),
                    token0_symbol: "WETH".to_string(),
                    token1_symbol: "USDC".to_string(),
                    reserve0: Decimal::from_f64(liquidity).unwrap_or_default(),
                    reserve1: Decimal::from_f64(liquidity).unwrap_or_default(),
                    protocol: format!("protocol_{}", i),
                }
            }).collect();
            
            let score = scorer.calculate_score(&path).await.unwrap();
            
            // Geometric score components should be between 0 and 1
            prop_assert!(score.convexity_ratio >= Decimal::ZERO);
            prop_assert!(score.convexity_ratio <= Decimal::ONE);
            prop_assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
            prop_assert!(score.liquidity_centroid_bias <= Decimal::ONE);
            prop_assert!(score.harmonic_path_score >= Decimal::ZERO);
            prop_assert!(score.harmonic_path_score <= Decimal::ONE);
            prop_assert!(score.vesica_piscis_depth >= Decimal::ZERO);
            prop_assert!(score.vesica_piscis_depth <= Decimal::ONE);
        })
    }

    #[test]
    fn test_point_distance_properties(
        x1 in -1000.0..1000.0_f64,
        y1 in -1000.0..1000.0_f64,
        x2 in -1000.0..1000.0_f64,
        y2 in -1000.0..1000.0_f64
    ) {
        let p1 = Point::new(x1, y1);
        let p2 = Point::new(x2, y2);
        
        let distance = p1.distance(&p2);
        
        // Distance should be non-negative
        prop_assert!(distance >= 0.0);
        
        // Distance to self should be zero
        prop_assert_eq!(p1.distance(&p1), 0.0);
        
        // Distance should be symmetric
        prop_assert!((distance - p2.distance(&p1)).abs() < 1e-10);
    }
}