use crate::config::Config;
use crate::shared_types::{ChainBlock, DecodedLog, NatsTopics, SwapEvent};
use async_nats::Client as NatsClient;
use ethers::{
    abi::{<PERSON><PERSON>, Token},
    contract::Contract,
    providers::{Middleware, Provider, ProviderError, Ws},
    types::{Address, Block, BlockNumber, Filter, Log, H256, U256},
};
use futures_util::StreamExt;
use serde_json::Value;
use std::collections::HashMap;
use std::error::Error;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time;
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

pub struct ChainMonitor {
    chain_id: u64,
    current_ws_url: String,
    config: Arc<Config>,
    nats_client: NatsClient,
    last_block_hash: Option<H256>,
    last_block_number: Option<u64>,
}

impl ChainMonitor {
    pub fn new(
        chain_id: u64,
        ws_url: String,
        config: Arc<Config>,
        nats_client: NatsClient,
    ) -> Self {
        Self {
            chain_id,
            current_ws_url: ws_url,
            config,
            nats_client,
            last_block_hash: None,
            last_block_number: None,
        }
    }

    pub async fn start(&mut self) -> Result<(), Box<dyn Error>> {
        info!("Starting chain monitor for chain ID {}", self.chain_id);

        // Reconnection loop
        let mut retry_count = 0;
        let max_retries = 10;
        let mut backoff_duration = Duration::from_secs(1);

        loop {
            info!(
                "Connecting to RPC WebSocket for chain ID {} (attempt {})",
                self.chain_id,
                retry_count + 1
            );

            match Provider::<Ws>::connect(&self.current_ws_url).await {
                Ok(ws_provider) => {
                    let provider = Arc::new(ws_provider);
                    info!("Connected to RPC WebSocket for chain ID {}", self.chain_id);

                    // Reset retry count and backoff on successful connection
                    retry_count = 0;
                    backoff_duration = Duration::from_secs(1);

                    // Start both subscriptions concurrently
                    let blocks_provider = provider.clone();
                    let events_provider = provider.clone();

                    // Clone necessary data for the events task
                    let chain_id = self.chain_id;
                    let nats_client = self.nats_client.clone();

                    // Spawn events subscription as a separate task
                    let events_handle = tokio::spawn(async move {
                        ChainMonitor::subscribe_to_events_static(
                            chain_id,
                            events_provider,
                            nats_client,
                        )
                        .await
                    });

                    // Run blocks subscription in current task
                    let blocks_result = self.subscribe_to_blocks(blocks_provider).await;

                    // Wait for events task to complete or handle blocks error
                    if let Err(e) = blocks_result {
                        error!(
                            "Block subscription error for chain ID {}: {}",
                            self.chain_id, e
                        );
                    }

                    // Cancel events task if blocks failed
                    events_handle.abort();

                    // If we reach here, one of the subscriptions has ended
                    warn!(
                        "Reconnecting to RPC WebSocket for chain ID {}...",
                        self.chain_id
                    );
                }
                Err(e) => {
                    error!(
                        "Failed to connect to RPC WebSocket for chain ID {}: {}",
                        self.chain_id, e
                    );

                    // TODO: Implement RPC failover logic with the new Config struct
                    warn!("OROBOROS: RPC failover not implemented yet, using exponential backoff");

                    // Increment retry count
                    retry_count += 1;

                    // Check if we've exceeded max retries
                    if retry_count >= max_retries {
                        error!(
                            "Max retries ({}) exceeded for chain ID {}",
                            max_retries, self.chain_id
                        );
                        return Err(format!(
                            "Failed to connect to RPC after {} attempts",
                            max_retries
                        )
                        .into());
                    }
                }
            }

            // Exponential backoff with jitter
            let jitter = rand::random::<u64>() % 1000;
            let backoff_ms = backoff_duration.as_millis() as u64 + jitter;
            info!(
                "Waiting {}ms before reconnecting to RPC for chain ID {}",
                backoff_ms, self.chain_id
            );
            time::sleep(Duration::from_millis(backoff_ms)).await;

            // Increase backoff for next attempt (capped at 30 seconds)
            backoff_duration = std::cmp::min(backoff_duration * 2, Duration::from_secs(30));
        }
    }

    async fn subscribe_to_blocks(
        &mut self,
        provider: Arc<Provider<Ws>>,
    ) -> Result<(), Box<dyn Error>> {
        // Subscribe to new blocks
        let mut block_stream = provider.subscribe_blocks().await?;
        info!("Subscribed to new blocks for chain ID {}", self.chain_id);

        // Process incoming blocks
        while let Some(block) = block_stream.next().await {
            if let Some(block_number) = block.number {
                info!(
                    "Received block #{} for chain ID {}",
                    block_number, self.chain_id
                );

                // Check for chain reorganization
                if let Err(e) = self.check_for_reorg(&block).await {
                    error!("Chain reorganization check failed: {}", e);
                }

                // Convert to our internal ChainBlock type
                if let Some(chain_block) = self.convert_to_chain_block(block.clone()) {
                    // Publish to NATS
                    if let Err(e) = self.publish_block(chain_block).await {
                        error!("Failed to publish block: {}", e);
                    }
                }

                // Get full block with transactions
                match provider.get_block_with_txs(block_number).await {
                    Ok(Some(full_block)) => {
                        debug!(
                            "Retrieved full block #{} with {} transactions",
                            block_number,
                            full_block.transactions.len()
                        );

                        // Process transactions if needed
                        // This would be expanded in later phases for MEV strategies
                    }
                    Ok(None) => {
                        warn!(
                            "Block #{} not found when fetching transactions",
                            block_number
                        );
                    }
                    Err(e) => {
                        error!("Error fetching block with transactions: {}", e);
                    }
                }
            }
        }

        warn!("Block subscription ended for chain ID {}", self.chain_id);
        Ok(())
    }

    /// Check for chain reorganization by comparing parent hash
    async fn check_for_reorg(&mut self, block: &Block<H256>) -> Result<(), Box<dyn Error>> {
        let block_number = block.number.ok_or("Block has no number")?;
        let block_hash = block.hash.ok_or("Block has no hash")?;

        // If this is the first block we've seen, just store it
        if self.last_block_number.is_none() {
            self.last_block_number = Some(block_number.as_u64());
            self.last_block_hash = Some(block_hash);
            return Ok(());
        }

        let last_number = self.last_block_number
            .ok_or_else(|| anyhow::anyhow!("No previous block number available for reorg detection"))?;
        let last_hash = self.last_block_hash
            .ok_or_else(|| anyhow::anyhow!("No previous block hash available for reorg detection"))?;

        // Check if this is the next sequential block
        if block_number.as_u64() == last_number + 1 {
            // Verify parent hash matches our last block hash
            if block.parent_hash != last_hash {
                warn!(
                    "CHAIN REORG DETECTED! Block #{} parent hash {} does not match our last block hash {}",
                    block_number, block.parent_hash, last_hash
                );

                // Publish reorg notification
                self.publish_reorg_notification(last_number, block_number.as_u64())
                    .await?;

                // TODO: In a full implementation, we would:
                // 1. Invalidate any pending transactions from the orphaned blocks
                // 2. Re-process blocks from the fork point
                // 3. Update any cached state that might be affected
                // 4. Notify strategy modules to recalculate opportunities
            }
        } else if block_number.as_u64() <= last_number {
            // This could be a reorg where we're seeing an older block again
            warn!(
                "Received block #{} which is not newer than last block #{} - possible reorg",
                block_number, last_number
            );
        }

        // Update our tracking
        self.last_block_number = Some(block_number.as_u64());
        self.last_block_hash = Some(block_hash);

        Ok(())
    }

    async fn publish_reorg_notification(
        &self,
        old_block: u64,
        new_block: u64,
    ) -> Result<(), Box<dyn Error>> {
        let reorg_event = serde_json::json!({
            "chain_id": self.chain_id,
            "event_type": "chain_reorganization",
            "old_block_number": old_block,
            "new_block_number": new_block,
            "timestamp": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs()
        });

        let payload = serde_json::to_string(&reorg_event)?;
        let topic = format!("data.chain.{}.reorg", self.chain_id);
        self.nats_client.publish(topic, payload.into()).await?;

        warn!(
            "Published chain reorganization notification for chain {}",
            self.chain_id
        );
        Ok(())
    }

    fn convert_to_chain_block(&self, block: Block<H256>) -> Option<ChainBlock> {
        let block_number = block.number?;

        Some(ChainBlock {
            chain_id: self.chain_id,
            block_number: block_number.as_u64(),
            block_hash: block.hash?,
            timestamp: block.timestamp.as_u64(),
            gas_used: block.gas_used,
            gas_limit: block.gas_limit,
            base_fee_per_gas: block.base_fee_per_gas,
        })
    }

    // These methods will be implemented in Phase 1

    async fn subscribe_to_events_static(
        chain_id: u64,
        provider: Arc<Provider<Ws>>,
        nats_client: NatsClient,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        // Define Uniswap V3 Swap event signature
        // event Swap(
        //     address indexed sender,
        //     address indexed recipient,
        //     int256 amount0,
        //     int256 amount1,
        //     uint160 sqrtPriceX96,
        //     uint128 liquidity,
        //     int24 tick
        // )
        let swap_event_signature = "Swap(address,address,int256,int256,uint160,uint128,int24)";
        let swap_topic = ethers::utils::keccak256(swap_event_signature.as_bytes());

        // Define Uniswap V2 Swap event signature
        // event Swap(
        //     address indexed sender,
        //     uint amount0In,
        //     uint amount1In,
        //     uint amount0Out,
        //     uint amount1Out,
        //     address indexed to
        // )
        let swap_v2_event_signature = "Swap(address,uint256,uint256,uint256,uint256,address)";
        let swap_v2_topic = ethers::utils::keccak256(swap_v2_event_signature.as_bytes());

        // Create a filter for both Uniswap V2 and V3 Swap events
        let filter = Filter::new().topic0(vec![H256::from(swap_topic), H256::from(swap_v2_topic)]);

        // Subscribe to the filter
        let mut stream = provider.subscribe_logs(&filter).await?;
        info!("Subscribed to Swap events for chain ID {}", chain_id);

        // Process incoming logs
        while let Some(log) = stream.next().await {
            debug!("Received log event on chain ID {}", chain_id);

            // Determine which DEX protocol this is from
            if log.topics.len() > 0 {
                let event_signature = log.topics[0];

                if event_signature == H256::from(swap_topic) {
                    // This is a Uniswap V3 Swap event
                    // TODO: Implement static version of process_uniswap_v3_swap
                    debug!("Received Uniswap V3 Swap event on chain {}", chain_id);
                } else if event_signature == H256::from(swap_v2_topic) {
                    // This is a Uniswap V2 Swap event
                    // TODO: Implement static version of process_uniswap_v2_swap
                    debug!("Received Uniswap V2 Swap event on chain {}", chain_id);
                }
            }

            // Create a generic DecodedLog for all events
            let decoded_log = DecodedLog {
                chain_id,
                block_number: log.block_number.unwrap_or_default().as_u64(),
                transaction_hash: log.transaction_hash.unwrap_or_default(),
                address: log.address,
                event_name: "Unknown".to_string(), // This would be properly decoded in a full implementation
                data: HashMap::new(), // This would be properly decoded in a full implementation
            };

            // Publish the generic log
            if let Err(e) = Self::publish_log_static(&nats_client, decoded_log).await {
                error!("Failed to publish log: {}", e);
            }
        }

        warn!("Event subscription ended for chain ID {}", chain_id);
        Ok(())
    }

    async fn process_uniswap_v3_swap(&self, log: Log) -> Result<(), Box<dyn Error>> {
        // In a full implementation, we would decode the log data properly
        // For now, we'll create a placeholder SwapEvent

        let swap_event = SwapEvent {
            chain_id: self.chain_id,
            block_number: log.block_number.unwrap_or_default().as_u64(),
            transaction_hash: log.transaction_hash.unwrap_or_default(),
            pool_address: log.address,
            token0: Address::zero(),   // Would be decoded from pool contract
            token1: Address::zero(),   // Would be decoded from pool contract
            amount0_in: U256::zero(),  // Would be decoded from log data
            amount1_in: U256::zero(),  // Would be decoded from log data
            amount0_out: U256::zero(), // Would be decoded from log data
            amount1_out: U256::zero(), // Would be decoded from log data
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };

        // Publish the swap event
        self.publish_swap(swap_event).await?;

        Ok(())
    }

    async fn process_uniswap_v2_swap(&self, log: Log) -> Result<(), Box<dyn Error>> {
        // Similar to V3 processing, but with V2 event format
        // In a full implementation, we would decode the log data properly

        let swap_event = SwapEvent {
            chain_id: self.chain_id,
            block_number: log.block_number.unwrap_or_default().as_u64(),
            transaction_hash: log.transaction_hash.unwrap_or_default(),
            pool_address: log.address,
            token0: Address::zero(),   // Would be decoded from pool contract
            token1: Address::zero(),   // Would be decoded from pool contract
            amount0_in: U256::zero(),  // Would be decoded from log data
            amount1_in: U256::zero(),  // Would be decoded from log data
            amount0_out: U256::zero(), // Would be decoded from log data
            amount1_out: U256::zero(), // Would be decoded from log data
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };

        // Publish the swap event
        self.publish_swap(swap_event).await?;

        Ok(())
    }

    async fn publish_block(&self, block: ChainBlock) -> Result<(), Box<dyn Error>> {
        // Serialize the block
        let payload = serde_json::to_string(&block)?;

        // Publish to NATS
        let topic = NatsTopics::chain_blocks_for(self.chain_id);
        self.nats_client.publish(topic, payload.into()).await?;
        debug!("Published block #{} to NATS", block.block_number);

        Ok(())
    }

    async fn publish_log(&self, log: DecodedLog) -> Result<(), Box<dyn Error>> {
        Self::publish_log_static(&self.nats_client, log)
            .await
            .map_err(|e| e as Box<dyn Error>)
    }

    async fn publish_log_static(
        nats_client: &NatsClient,
        log: DecodedLog,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        // Serialize the log
        let payload = serde_json::to_string(&log)?;

        // Publish to NATS
        let topic = NatsTopics::chain_events_for(log.chain_id);
        nats_client.publish(topic, payload.into()).await?;
        debug!("Published log event {} to NATS", log.event_name);

        Ok(())
    }

    async fn publish_swap(&self, swap: SwapEvent) -> Result<(), Box<dyn Error>> {
        // Serialize the swap event
        let payload = serde_json::to_string(&swap)?;

        // Publish to NATS
        let topic = NatsTopics::chain_swaps_for(self.chain_id);
        self.nats_client.publish(topic, payload.into()).await?;
        debug!(
            "Published swap event from pool {} to NATS",
            swap.pool_address
        );

        Ok(())
    }
}
