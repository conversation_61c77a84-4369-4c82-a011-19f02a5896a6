// MISSION: Error Propagation Integration for Aetheric Resonance Engine
// WHY: Integrate enhanced error handling with ARE components for requirement 5.4
// HOW: Provide error propagation chains, structured logging, and alerting for critical failures

use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, warn, info, debug};

use crate::error::{
    BasiliskError, NetworkError, DataProviderError, ExecutionError, StrategyError, CriticalError,
    enhanced::{ErrorContext, EnhancedError, ErrorHandler, AlertChannelConfig, AlertChannelType},
    circuit_breaker::{CircuitBreakerRegistry, CircuitBreakerConfig},
};
use crate::logging::{ErrorCode, AlertSeverity, TradingContext};

/// Enhanced error propagation manager for ARE components
pub struct ErrorPropagationManager {
    error_handler: Arc<ErrorHandler>,
    circuit_breaker_registry: Arc<CircuitBreakerRegistry>,
    component_contexts: Arc<RwLock<std::collections::HashMap<String, ErrorContext>>>,
}

impl ErrorPropagationManager {
    pub fn new(alert_channels: Vec<AlertChannelConfig>) -> Self {
        let error_handler = Arc::new(ErrorHandler::new(alert_channels));
        let circuit_breaker_registry = Arc::new(CircuitBreakerRegistry::new());
        
        Self {
            error_handler,
            circuit_breaker_registry,
            component_contexts: Arc::new(RwLock::new(std::collections::HashMap::new())),
        }
    }

    /// Initialize circuit breakers for ARE components
    pub async fn initialize_circuit_breakers(&self) {
        // RPC circuit breakers for each chain
        let rpc_config = CircuitBreakerConfig::for_rpc_calls();
        self.circuit_breaker_registry.register("rpc_ethereum".to_string(), rpc_config.clone()).await;
        self.circuit_breaker_registry.register("rpc_base".to_string(), rpc_config.clone()).await;
        self.circuit_breaker_registry.register("rpc_arbitrum".to_string(), rpc_config).await;

        // Data provider circuit breakers
        let data_config = CircuitBreakerConfig::for_data_providers();
        self.circuit_breaker_registry.register("price_oracle".to_string(), data_config.clone()).await;
        self.circuit_breaker_registry.register("dex_data_provider".to_string(), data_config.clone()).await;
        self.circuit_breaker_registry.register("network_seismology".to_string(), data_config).await;

        // Execution circuit breakers
        let exec_config = CircuitBreakerConfig::for_execution_operations();
        self.circuit_breaker_registry.register("transaction_execution".to_string(), exec_config.clone()).await;
        self.circuit_breaker_registry.register("gas_estimation".to_string(), exec_config).await;

        info!("Initialized circuit breakers for ARE components");
    }

    /// Handle error with full propagation chain and alerting
    pub async fn handle_error_with_propagation(
        &self,
        error: BasiliskError,
        component: &str,
        function: &str,
        opportunity_id: Option<&str>,
        chain_id: Option<u64>,
    ) -> EnhancedError {
        let mut context = ErrorContext::new(component, function);
        
        if let Some(opp_id) = opportunity_id {
            context = context.with_opportunity(opp_id);
        }
        
        if let Some(chain) = chain_id {
            context = context.with_chain(chain);
        }

        // Add to component context tracking
        {
            let mut contexts = self.component_contexts.write().await;
            contexts.insert(component.to_string(), context.clone());
        }

        // Handle the error with full context
        let enhanced_error = self.error_handler.handle_error(error, context).await;

        // Log structured error information
        self.log_error_with_context(&enhanced_error).await;

        enhanced_error
    }

    /// Execute operation with circuit breaker protection and error handling
    pub async fn execute_with_protection<F, T, E>(
        &self,
        circuit_name: &str,
        component: &str,
        function: &str,
        mut operation: F,
    ) -> Result<T, EnhancedError>
    where
        F: FnMut() -> Result<T, E> + Send,
        E: Into<BasiliskError>,
    {
        let context = ErrorContext::new(component, function);
        
        // Try to get circuit breaker
        if let Some(breaker) = self.circuit_breaker_registry.get(circuit_name).await {
            match breaker.execute(operation).await {
                Ok(result) => Ok(result),
                Err(circuit_error) => {
                    let basilisk_error = match circuit_error {
                        crate::error::circuit_breaker::CircuitBreakerError::CircuitOpen { circuit_name, .. } => {
                            BasiliskError::CircuitBreakerOpen {
                                service: circuit_name,
                                reason: "Circuit breaker is open due to repeated failures".to_string(),
                            }
                        }
                        crate::error::circuit_breaker::CircuitBreakerError::OperationFailed { error, .. } => error,
                    };
                    
                    Err(self.error_handler.handle_error(basilisk_error, context).await)
                }
            }
        } else {
            // No circuit breaker, execute with retry logic only
            self.error_handler.execute_with_retry_and_alerting(|| operation(), context).await
        }
    }

    /// Log error with structured context for monitoring
    async fn log_error_with_context(&self, enhanced_error: &EnhancedError) {
        let context = &enhanced_error.context;
        
        match enhanced_error.severity {
            AlertSeverity::Critical => {
                error!(
                    error_id = %context.error_id,
                    component = %context.component,
                    function = %context.function,
                    trace_id = %context.trace_id,
                    opportunity_id = ?context.opportunity_id,
                    chain_id = ?context.chain_id,
                    error_code = %enhanced_error.error_code.as_str(),
                    retry_count = context.retry_count,
                    propagation_chain_length = context.propagation_chain.len(),
                    recovery_suggestions = ?enhanced_error.recovery_suggestions,
                    message = %enhanced_error.inner,
                    "CRITICAL ERROR with propagation chain"
                );
            }
            AlertSeverity::Error => {
                error!(
                    error_id = %context.error_id,
                    component = %context.component,
                    function = %context.function,
                    trace_id = %context.trace_id,
                    opportunity_id = ?context.opportunity_id,
                    chain_id = ?context.chain_id,
                    error_code = %enhanced_error.error_code.as_str(),
                    retry_count = context.retry_count,
                    propagation_chain_length = context.propagation_chain.len(),
                    message = %enhanced_error.inner,
                    "ERROR with propagation chain"
                );
            }
            AlertSeverity::Warning => {
                warn!(
                    error_id = %context.error_id,
                    component = %context.component,
                    function = %context.function,
                    trace_id = %context.trace_id,
                    opportunity_id = ?context.opportunity_id,
                    chain_id = ?context.chain_id,
                    error_code = %enhanced_error.error_code.as_str(),
                    retry_count = context.retry_count,
                    message = %enhanced_error.inner,
                    "WARNING with context"
                );
            }
            AlertSeverity::Info => {
                info!(
                    error_id = %context.error_id,
                    component = %context.component,
                    function = %context.function,
                    trace_id = %context.trace_id,
                    message = %enhanced_error.inner,
                    "INFO with context"
                );
            }
        }
    }

    /// Get circuit breaker statistics for monitoring
    pub async fn get_circuit_breaker_stats(&self) -> std::collections::HashMap<String, crate::error::circuit_breaker::CircuitBreakerStats> {
        self.circuit_breaker_registry.get_all_stats().await
    }

    /// Reset all circuit breakers (for maintenance/testing)
    pub async fn reset_all_circuit_breakers(&self) {
        self.circuit_breaker_registry.reset_all().await;
    }

    /// Get component error contexts for debugging
    pub async fn get_component_contexts(&self) -> std::collections::HashMap<String, ErrorContext> {
        self.component_contexts.read().await.clone()
    }
}

/// Convenience functions for common ARE error scenarios
impl ErrorPropagationManager {
    /// Handle RPC errors with appropriate circuit breaker
    pub async fn handle_rpc_error(
        &self,
        error: NetworkError,
        chain_id: u64,
        endpoint: &str,
        operation: &str,
    ) -> EnhancedError {
        let circuit_name = match chain_id {
            1 => "rpc_ethereum",
            8453 => "rpc_base", 
            42161 => "rpc_arbitrum",
            _ => "rpc_unknown",
        };

        let mut context = ErrorContext::new("RpcProvider", operation)
            .with_chain(chain_id)
            .with_data("endpoint", endpoint);

        context.propagate("RpcProvider", operation, &BasiliskError::Network(error.clone()));

        self.error_handler.handle_error(BasiliskError::Network(error), context).await
    }

    /// Handle data provider errors with fallback suggestions
    pub async fn handle_data_provider_error(
        &self,
        error: DataProviderError,
        data_source: &str,
        operation: &str,
    ) -> EnhancedError {
        let mut context = ErrorContext::new("DataProvider", operation)
            .with_data("data_source", data_source);

        context.propagate("DataProvider", operation, &BasiliskError::DataProvider(error.clone()));

        self.error_handler.handle_error(BasiliskError::DataProvider(error), context).await
    }

    /// Handle execution errors with transaction context
    pub async fn handle_execution_error(
        &self,
        error: ExecutionError,
        opportunity_id: &str,
        chain_id: u64,
        operation: &str,
    ) -> EnhancedError {
        let mut context = ErrorContext::new("ExecutionManager", operation)
            .with_opportunity(opportunity_id)
            .with_chain(chain_id);

        context.propagate("ExecutionManager", operation, &BasiliskError::Execution(error.clone()));

        self.error_handler.handle_error(BasiliskError::Execution(error), context).await
    }

    /// Handle strategy errors with opportunity context
    pub async fn handle_strategy_error(
        &self,
        error: StrategyError,
        opportunity_id: &str,
        strategy_type: &str,
        operation: &str,
    ) -> EnhancedError {
        let mut context = ErrorContext::new("StrategyManager", operation)
            .with_opportunity(opportunity_id)
            .with_data("strategy_type", strategy_type);

        context.propagate("StrategyManager", operation, &BasiliskError::Strategy(error.clone()));

        self.error_handler.handle_error(BasiliskError::Strategy(error), context).await
    }

    /// Handle critical system errors with immediate alerting
    pub async fn handle_critical_error(
        &self,
        error: CriticalError,
        component: &str,
        operation: &str,
    ) -> EnhancedError {
        let mut context = ErrorContext::new(component, operation)
            .with_data("severity", "critical")
            .with_data("requires_immediate_attention", true);

        context.propagate(component, operation, &BasiliskError::Critical(error.clone()));

        // Critical errors always trigger alerts regardless of configuration
        let enhanced_error = self.error_handler.handle_error(BasiliskError::Critical(error), context).await;
        
        // Additional critical error logging
        error!(
            error_id = %enhanced_error.context.error_id,
            component = component,
            operation = operation,
            error_code = %enhanced_error.error_code.as_str(),
            message = %enhanced_error.inner,
            "CRITICAL SYSTEM ERROR - IMMEDIATE ATTENTION REQUIRED"
        );

        enhanced_error
    }
}

/// Default alert channel configurations for ARE
pub fn default_alert_channels() -> Vec<AlertChannelConfig> {
    vec![
        // Always log all alerts
        AlertChannelConfig {
            channel_type: AlertChannelType::Log,
            enabled: true,
            min_severity: AlertSeverity::Info,
            rate_limit_seconds: 0,
            config: std::collections::HashMap::new(),
        },
        // Slack for errors and critical issues
        AlertChannelConfig {
            channel_type: AlertChannelType::Slack,
            enabled: false, // Enable when webhook URL is configured
            min_severity: AlertSeverity::Error,
            rate_limit_seconds: 300, // 5 minutes
            config: {
                let mut config = std::collections::HashMap::new();
                config.insert("webhook_url".to_string(), "".to_string()); // Configure in production
                config
            },
        },
        // Discord for warnings and above
        AlertChannelConfig {
            channel_type: AlertChannelType::Discord,
            enabled: false, // Enable when webhook URL is configured
            min_severity: AlertSeverity::Warning,
            rate_limit_seconds: 300, // 5 minutes
            config: {
                let mut config = std::collections::HashMap::new();
                config.insert("webhook_url".to_string(), "".to_string()); // Configure in production
                config
            },
        },
        // Webhook for external monitoring systems
        AlertChannelConfig {
            channel_type: AlertChannelType::Webhook,
            enabled: false, // Enable when URL is configured
            min_severity: AlertSeverity::Error,
            rate_limit_seconds: 60, // 1 minute
            config: {
                let mut config = std::collections::HashMap::new();
                config.insert("url".to_string(), "".to_string()); // Configure in production
                config
            },
        },
    ]
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::error::NetworkError;

    #[tokio::test]
    async fn test_error_propagation_manager() {
        let manager = ErrorPropagationManager::new(default_alert_channels());
        manager.initialize_circuit_breakers().await;

        let error = NetworkError::RpcTimeout {
            endpoint: "test".to_string(),
            timeout_ms: 5000,
        };

        let enhanced_error = manager.handle_rpc_error(error, 1, "test_endpoint", "get_block").await;
        
        assert_eq!(enhanced_error.error_code, ErrorCode::ERpcTimeout);
        assert!(enhanced_error.is_retryable);
        assert!(!enhanced_error.recovery_suggestions.is_empty());
    }

    #[tokio::test]
    async fn test_circuit_breaker_integration() {
        let manager = ErrorPropagationManager::new(default_alert_channels());
        manager.initialize_circuit_breakers().await;

        let stats = manager.get_circuit_breaker_stats().await;
        assert!(stats.contains_key("rpc_ethereum"));
        assert!(stats.contains_key("price_oracle"));
        assert!(stats.contains_key("transaction_execution"));
    }
}