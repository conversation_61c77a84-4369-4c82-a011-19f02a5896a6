// MISSION: Service Control Handler - Generic TUI Command Handler
// WHY: Provide a reusable handler for services that don't have specific control logic
// HOW: Generic NATS subscription and acknowledgment system

use async_nats::Client as NatsClient;
use tokio_stream::StreamExt;
use tracing::{debug, error, info, warn};
use crate::shared_types::control_messages::*;

/// Generic service control handler for services that need basic TUI command support
pub struct ServiceControlHandler {
    nats_client: NatsClient,
    service_name: String,
}

impl ServiceControlHandler {
    pub fn new(nats_client: NatsClient, service_name: String) -> Self {
        Self {
            nats_client,
            service_name,
        }
    }

    /// Start listening for control commands
    pub async fn run(&self) -> Result<(), Box<dyn std::error::Error>> {
        info!("{} ServiceControlHandler starting...", self.service_name);

        // Subscribe to relevant control commands
        let mut emergency_stop_subscriber = self.nats_client.subscribe("control.emergency_stop").await?;
        let mut config_reload_subscriber = self.nats_client.subscribe("control.config.reload").await?;
        let mut service_control_subscriber = self.nats_client.subscribe(format!("control.service.{}", self.service_name.to_lowercase())).await?;

        info!("{} subscribed to control commands", self.service_name);

        loop {
            tokio::select! {
                Some(msg) = emergency_stop_subscriber.next() => {
                    if let Err(e) = self.handle_emergency_stop_command(msg).await {
                        error!("Failed to handle emergency stop in {}: {}", self.service_name, e);
                    }
                }

                Some(msg) = config_reload_subscriber.next() => {
                    if let Err(e) = self.handle_config_reload_command(msg).await {
                        error!("Failed to handle config reload in {}: {}", self.service_name, e);
                    }
                }

                Some(msg) = service_control_subscriber.next() => {
                    if let Err(e) = self.handle_service_control_command(msg).await {
                        error!("Failed to handle service control in {}: {}", self.service_name, e);
                    }
                }

                else => {
                    warn!("{} control handler NATS subscription ended", self.service_name);
                    break;
                }
            }
        }

        Ok(())
    }

    /// Handle emergency stop command
    async fn handle_emergency_stop_command(&self, msg: async_nats::Message) -> Result<(), Box<dyn std::error::Error>> {
        match serde_json::from_slice::<EmergencyStopCommand>(&msg.payload) {
            Ok(command) => {
                warn!("EMERGENCY STOP received in {}: {} (initiated by: {})", 
                    self.service_name, command.reason, command.initiated_by);

                // Send acknowledgment
                let ack = CommandAcknowledgment {
                    command_id: command.command_id,
                    success: true,
                    message: Some(format!("{} emergency stop acknowledged", self.service_name)),
                    timestamp: chrono::Utc::now(),
                    service_name: self.service_name.clone(),
                    execution_time_ms: Some(1),
                };

                self.send_command_acknowledgment(ack).await?;
                info!("Emergency stop acknowledged by {}", self.service_name);
            }
            Err(e) => {
                error!("Failed to deserialize emergency stop command in {}: {}", self.service_name, e);
            }
        }
        Ok(())
    }

    /// Handle configuration reload command
    async fn handle_config_reload_command(&self, msg: async_nats::Message) -> Result<(), Box<dyn std::error::Error>> {
        match serde_json::from_slice::<serde_json::Value>(&msg.payload) {
            Ok(_config) => {
                info!("Configuration reload command received in {}", self.service_name);

                // Send acknowledgment
                let ack = CommandAcknowledgment {
                    command_id: uuid::Uuid::new_v4(),
                    success: true,
                    message: Some(format!("{} configuration reload acknowledged", self.service_name)),
                    timestamp: chrono::Utc::now(),
                    service_name: self.service_name.clone(),
                    execution_time_ms: Some(10),
                };

                self.send_command_acknowledgment(ack).await?;
                info!("Configuration reload acknowledged by {}", self.service_name);
            }
            Err(e) => {
                error!("Failed to deserialize config reload command in {}: {}", self.service_name, e);
            }
        }
        Ok(())
    }

    /// Handle service-specific control commands
    async fn handle_service_control_command(&self, msg: async_nats::Message) -> Result<(), Box<dyn std::error::Error>> {
        match serde_json::from_slice::<ServiceControlCommand>(&msg.payload) {
            Ok(command) => {
                let action_str = match command.action {
                    ServiceAction::Start => "started",
                    ServiceAction::Stop => "stopped",
                    ServiceAction::Restart => "restarted",
                    ServiceAction::HealthCheck => "health checked",
                };

                info!("Service control command received in {}: {:?}", self.service_name, command.action);

                // Send acknowledgment
                let ack = CommandAcknowledgment {
                    command_id: command.command_id,
                    success: true,
                    message: Some(format!("{} {} successfully", self.service_name, action_str)),
                    timestamp: chrono::Utc::now(),
                    service_name: self.service_name.clone(),
                    execution_time_ms: Some(5),
                };

                self.send_command_acknowledgment(ack).await?;
                info!("Service control command processed in {}: {}", self.service_name, action_str);
            }
            Err(e) => {
                error!("Failed to deserialize service control command in {}: {}", self.service_name, e);
            }
        }
        Ok(())
    }

    /// Send command acknowledgment back to TUI
    async fn send_command_acknowledgment(&self, ack: CommandAcknowledgment) -> Result<(), Box<dyn std::error::Error>> {
        let payload = serde_json::to_vec(&ack)?;
        self.nats_client.publish("control.command.ack", payload.into()).await?;
        debug!("Sent command acknowledgment from {} for command {}", self.service_name, ack.command_id);
        Ok(())
    }
}

/// Helper function to spawn a service control handler in the background
pub fn spawn_service_control_handler(nats_client: NatsClient, service_name: String) {
    let handler = ServiceControlHandler::new(nats_client, service_name.clone());
    
    tokio::spawn(async move {
        if let Err(e) = handler.run().await {
            error!("Service control handler for {} failed: {}", service_name, e);
        }
    });
}