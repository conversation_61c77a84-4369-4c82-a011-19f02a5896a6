// src/validation/deployment_mode_validator_cli.rs

//! CLI interface for deployment mode validation

use crate::validation::{
    DeploymentModeValidator, ValidationFramework, ValidationConfig
};
use crate::shared_types::RunMode;
use crate::config::Config;
use crate::error::BasiliskError;
use clap::{Args, Subcommand};
use std::sync::Arc;
use tracing::{info, error};

/// CLI arguments for deployment mode validation
#[derive(Debug, Args)]
pub struct DeploymentModeValidationArgs {
    #[command(subcommand)]
    pub command: DeploymentModeValidationCommand,
}

/// Deployment mode validation commands
#[derive(Debug, Subcommand)]
pub enum DeploymentModeValidationCommand {
    /// Validate all deployment modes
    ValidateAll {
        /// Enable verbose output
        #[arg(long)]
        verbose: bool,
    },
    /// Validate a specific deployment mode
    ValidateMode {
        /// The deployment mode to validate
        #[arg(value_enum)]
        mode: RunMode,
        /// Enable verbose output
        #[arg(long)]
        verbose: bool,
    },
    /// Run deployment mode validation demo
    Demo {
        /// Enable verbose output
        #[arg(long)]
        verbose: bool,
    },
    /// Show usage examples
    Examples,
}

/// Handle deployment mode validation CLI commands
pub async fn handle_deployment_mode_validation_command(
    args: DeploymentModeValidationArgs,
    config: Arc<Config>,
) -> Result<(), BasiliskError> {
    match args.command {
        DeploymentModeValidationCommand::ValidateAll { verbose } => {
            run_complete_deployment_mode_validation(config, verbose).await
        }
        DeploymentModeValidationCommand::ValidateMode { mode, verbose } => {
            run_single_mode_validation(config, mode, verbose).await
        }
        DeploymentModeValidationCommand::Demo { verbose } => {
            run_deployment_mode_validation_demo(config, verbose).await
        }
        DeploymentModeValidationCommand::Examples => {
            show_deployment_mode_usage_examples();
            Ok(())
        }
    }
}

/// Run complete deployment mode validation suite
pub async fn run_complete_deployment_mode_validation(
    config: Arc<Config>,
    verbose: bool,
) -> Result<(), BasiliskError> {
    info!("Starting complete deployment mode validation suite");

    // Create validation framework
    let validation_config = ValidationConfig::default();
    let framework = ValidationFramework::new(validation_config)
        .map_err(|e| BasiliskError::execution_error(format!("Failed to create validation framework: {}", e)))?;

    // Create deployment mode validator
    let mut validator = DeploymentModeValidator::new(config.clone());

    // Run validation through framework
    let result = framework.execute_validation(
        "deployment_mode_validation_suite",
        || async {
            validator.validate_all_modes().await
                .map(|result| result.metrics.unwrap_or_default())
        }
    ).await
    .map_err(|e| BasiliskError::execution_error(format!("Validation execution failed: {}", e)))?;

    // Display results
    if verbose {
        println!("\n{}", "=".repeat(80));
        println!("DEPLOYMENT MODE VALIDATION RESULTS");
        println!("{}", "=".repeat(80));
        
        if let Some(metrics) = &result.metrics {
            display_detailed_metrics(metrics);
        }
        
        if !result.errors.is_empty() {
            println!("\nERRORS:");
            for error in &result.errors {
                println!("  - {}: {}", error.code, error.message);
            }
        }
        
        if !result.warnings.is_empty() {
            println!("\nWARNINGS:");
            for warning in &result.warnings {
                println!("  - {}: {}", warning.code, warning.message);
            }
        }
    }

    match result.status {
        crate::validation::ValidationStatus::Passed => {
            info!("✅ All deployment modes validated successfully");
            println!("✅ All deployment modes validated successfully");
        }
        crate::validation::ValidationStatus::Warning => {
            info!("⚠️  Deployment mode validation completed with warnings");
            println!("⚠️  Deployment mode validation completed with warnings");
        }
        crate::validation::ValidationStatus::Failed => {
            error!("❌ Deployment mode validation failed");
            println!("❌ Deployment mode validation failed");
            return Err(BasiliskError::execution_error("Deployment mode validation failed"));
        }
        _ => {
            error!("❓ Deployment mode validation status unknown");
            println!("❓ Deployment mode validation status unknown");
        }
    }

    Ok(())
}

/// Run validation for a single deployment mode
pub async fn run_single_mode_validation(
    config: Arc<Config>,
    mode: RunMode,
    verbose: bool,
) -> Result<(), BasiliskError> {
    info!("Starting validation for deployment mode: {}", mode);

    // Create deployment mode validator
    let mut validator = DeploymentModeValidator::new(config.clone());

    // Run validation for specific mode
    let result = validator.validate_deployment_mode(mode).await
        .map_err(|e| BasiliskError::execution_error(format!("Mode validation failed: {}", e)))?;

    if verbose {
        println!("\n{}", "=".repeat(60));
        println!("DEPLOYMENT MODE VALIDATION: {}", mode.to_string().to_uppercase());
        println!("{}", "=".repeat(60));
        
        display_mode_specific_metrics(&validator, mode);
    }

    if result {
        info!("✅ Deployment mode {} validated successfully", mode);
        println!("✅ Deployment mode {} validated successfully", mode);
    } else {
        error!("❌ Deployment mode {} validation failed", mode);
        println!("❌ Deployment mode {} validation failed", mode);
        return Err(BasiliskError::execution_error(format!("Deployment mode {} validation failed", mode)));
    }

    Ok(())
}

/// Run deployment mode validation demo
pub async fn run_deployment_mode_validation_demo(
    config: Arc<Config>,
    verbose: bool,
) -> Result<(), BasiliskError> {
    println!("\n{}", "=".repeat(80));
    println!("DEPLOYMENT MODE VALIDATION DEMO");
    println!("{}", "=".repeat(80));
    println!("This demo showcases the 5-tier deployment ladder validation:");
    println!("1. Simulate Mode - Transaction interception testing");
    println!("2. Shadow Mode - Anvil fork state consistency");
    println!("3. Sentinel Mode - Minimal risk transaction testing");
    println!("4. Low-Capital Mode - Hardcoded limit enforcement");
    println!("5. Live Mode - Full production capability testing");
    println!("{}", "=".repeat(80));

    let modes = vec![
        RunMode::Simulate,
        RunMode::Shadow,
        RunMode::Sentinel,
        RunMode::LowCapital,
        RunMode::Live,
    ];

    let mut validator = DeploymentModeValidator::new(config.clone());

    for (i, mode) in modes.iter().enumerate() {
        println!("\n[{}/5] Validating {} Mode...", i + 1, mode.to_string().to_uppercase());
        
        let start_time = std::time::Instant::now();
        let result = validator.validate_deployment_mode(*mode).await
            .map_err(|e| BasiliskError::execution_error(format!("Demo validation failed: {}", e)))?;
        let duration = start_time.elapsed();

        if result {
            println!("✅ {} Mode: PASSED ({:.2}s)", mode.to_string().to_uppercase(), duration.as_secs_f64());
        } else {
            println!("❌ {} Mode: FAILED ({:.2}s)", mode.to_string().to_uppercase(), duration.as_secs_f64());
        }

        if verbose {
            display_mode_specific_metrics(&validator, *mode);
        }

        // Add a small delay for demo effect
        tokio::time::sleep(std::time::Duration::from_millis(500)).await;
    }

    println!("\n{}", "=".repeat(80));
    println!("DEMO COMPLETED");
    println!("{}", "=".repeat(80));

    Ok(())
}

/// Display detailed metrics for all modes
fn display_detailed_metrics(metrics: &crate::validation::DeploymentModeMetrics) {
    println!("\nSIMULATE MODE METRICS:");
    println!("  Transactions Intercepted: {}", metrics.simulate_mode.transactions_intercepted);
    println!("  Interception Success Rate: {:.2}%", metrics.simulate_mode.interception_success_rate * 100.0);
    println!("  Educational Data Accuracy: {:.2}%", metrics.simulate_mode.educational_data_accuracy * 100.0);
    println!("  UI Response Time: {:.2}ms", metrics.simulate_mode.ui_response_time_ms);
    println!("  Memory Usage: {:.2}MB", metrics.simulate_mode.memory_usage_mb);

    println!("\nSHADOW MODE METRICS:");
    println!("  Fork State Consistency: {:.2}%", metrics.shadow_mode.fork_state_consistency * 100.0);
    println!("  Simulation Accuracy: {:.2}%", metrics.shadow_mode.simulation_accuracy * 100.0);
    println!("  Fork Sync Time: {:.2}ms", metrics.shadow_mode.fork_sync_time_ms);
    println!("  Simulated Transactions: {}", metrics.shadow_mode.simulated_transactions);
    println!("  Performance Score: {:.2}", metrics.shadow_mode.performance_benchmark_score);

    println!("\nSENTINEL MODE METRICS:");
    println!("  Contract Health Success Rate: {:.2}%", metrics.sentinel_mode.contract_health_success_rate * 100.0);
    println!("  Minimal Risk Transaction Success Rate: {:.2}%", metrics.sentinel_mode.minimal_risk_transaction_success_rate * 100.0);
    println!("  Average Transaction Value: ${:.2}", metrics.sentinel_mode.average_transaction_value_usd);
    println!("  Health Checks Performed: {}", metrics.sentinel_mode.health_checks_performed);

    println!("\nLOW-CAPITAL MODE METRICS:");
    println!("  Position Limit Enforcement: {:.2}%", metrics.low_capital_mode.position_limit_enforcement * 100.0);
    println!("  Daily Loss Limit Enforcement: {:.2}%", metrics.low_capital_mode.daily_loss_limit_enforcement * 100.0);
    println!("  Max Position Size: ${:.2}", metrics.low_capital_mode.max_position_size_usd);
    println!("  Max Daily Loss: ${:.2}", metrics.low_capital_mode.max_daily_loss_usd);
    println!("  Trades Executed: {}", metrics.low_capital_mode.trades_executed);

    println!("\nLIVE MODE METRICS:");
    println!("  Production Capability Score: {:.2}", metrics.live_mode.production_capability_score);
    println!("  Strategy Suite Completeness: {:.2}%", metrics.live_mode.strategy_suite_completeness * 100.0);
    println!("  Risk Parameter Compliance: {:.2}%", metrics.live_mode.risk_parameter_compliance * 100.0);
    println!("  System Stability Score: {:.2}", metrics.live_mode.system_stability_score);
    println!("  Opportunities Processed: {}", metrics.live_mode.opportunities_processed);

    println!("\nOVERALL METRICS:");
    println!("  Total Validation Time: {}ms", metrics.overall_metrics.total_validation_time_ms);
    println!("  Overall Success Rate: {:.2}%", metrics.overall_metrics.overall_success_rate * 100.0);
    println!("  Mode Progression Readiness: {:.2}%", metrics.overall_metrics.mode_progression_readiness * 100.0);
    println!("  Configuration Validation Score: {:.2}%", metrics.overall_metrics.configuration_validation_score * 100.0);
    println!("  Security Validation Score: {:.2}%", metrics.overall_metrics.security_validation_score * 100.0);
    println!("  Performance Validation Score: {:.2}%", metrics.overall_metrics.performance_validation_score * 100.0);
}

/// Display metrics for a specific mode
fn display_mode_specific_metrics(validator: &DeploymentModeValidator, mode: RunMode) {
    let metrics = &validator.validation_metrics;
    
    match mode {
        RunMode::Simulate => {
            println!("  Transactions Intercepted: {}", metrics.simulate_mode.transactions_intercepted);
            println!("  Interception Success Rate: {:.2}%", metrics.simulate_mode.interception_success_rate * 100.0);
            println!("  Educational Data Accuracy: {:.2}%", metrics.simulate_mode.educational_data_accuracy * 100.0);
            println!("  UI Response Time: {:.2}ms", metrics.simulate_mode.ui_response_time_ms);
        }
        RunMode::Shadow => {
            println!("  Fork State Consistency: {:.2}%", metrics.shadow_mode.fork_state_consistency * 100.0);
            println!("  Simulation Accuracy: {:.2}%", metrics.shadow_mode.simulation_accuracy * 100.0);
            println!("  Simulated Transactions: {}", metrics.shadow_mode.simulated_transactions);
        }
        RunMode::Sentinel => {
            println!("  Contract Health Success Rate: {:.2}%", metrics.sentinel_mode.contract_health_success_rate * 100.0);
            println!("  Average Transaction Value: ${:.2}", metrics.sentinel_mode.average_transaction_value_usd);
            println!("  Health Checks Performed: {}", metrics.sentinel_mode.health_checks_performed);
        }
        RunMode::LowCapital => {
            println!("  Position Limit Enforcement: {:.2}%", metrics.low_capital_mode.position_limit_enforcement * 100.0);
            println!("  Daily Loss Limit Enforcement: {:.2}%", metrics.low_capital_mode.daily_loss_limit_enforcement * 100.0);
            println!("  Max Position Size: ${:.2}", metrics.low_capital_mode.max_position_size_usd);
        }
        RunMode::Live => {
            println!("  Production Capability Score: {:.2}", metrics.live_mode.production_capability_score);
            println!("  Strategy Suite Completeness: {:.2}%", metrics.live_mode.strategy_suite_completeness * 100.0);
            println!("  Opportunities Processed: {}", metrics.live_mode.opportunities_processed);
        }
    }
}

/// Show usage examples for deployment mode validation
pub fn show_deployment_mode_usage_examples() {
    println!("\n{}", "=".repeat(80));
    println!("DEPLOYMENT MODE VALIDATION USAGE EXAMPLES");
    println!("{}", "=".repeat(80));
    
    println!("\n1. Validate all deployment modes:");
    println!("   cargo run -- validation deployment-mode validate-all");
    println!("   cargo run -- validation deployment-mode validate-all --verbose");
    
    println!("\n2. Validate a specific deployment mode:");
    println!("   cargo run -- validation deployment-mode validate-mode simulate");
    println!("   cargo run -- validation deployment-mode validate-mode shadow --verbose");
    println!("   cargo run -- validation deployment-mode validate-mode sentinel");
    println!("   cargo run -- validation deployment-mode validate-mode low-capital");
    println!("   cargo run -- validation deployment-mode validate-mode live");
    
    println!("\n3. Run deployment mode validation demo:");
    println!("   cargo run -- validation deployment-mode demo");
    println!("   cargo run -- validation deployment-mode demo --verbose");
    
    println!("\n4. Show usage examples:");
    println!("   cargo run -- validation deployment-mode examples");
    
    println!("\n{}", "=".repeat(80));
    println!("DEPLOYMENT MODE DESCRIPTIONS");
    println!("{}", "=".repeat(80));
    
    println!("\nSIMULATE MODE:");
    println!("  - Educational simulation mode");
    println!("  - Connects to live data but never broadcasts transactions");
    println!("  - Perfect for learning how the bot analyzes opportunities");
    println!("  - Validates transaction interception and educational data processing");
    
    println!("\nSHADOW MODE:");
    println!("  - Live simulation with on-chain verification");
    println!("  - Tests transactions on forked blockchain state using Anvil");
    println!("  - Validates profitability without broadcasting to mainnet");
    println!("  - Ensures fork state consistency and simulation accuracy");
    
    println!("\nSENTINEL MODE:");
    println!("  - Live on-chain event monitoring only");
    println!("  - Monitors deployed contracts with minimal capital exposure");
    println!("  - Tests contract functionality with very small transactions");
    println!("  - Validates contract health and monitoring systems");
    
    println!("\nLOW-CAPITAL MODE:");
    println!("  - Live trading with very low risk limits");
    println!("  - First real money mode with hardcoded safety limits");
    println!("  - Maximum $100 position size, $50 daily loss limit");
    println!("  - Kelly fraction capped at 2%");
    
    println!("\nLIVE MODE:");
    println!("  - Full production live trading");
    println!("  - Uses configured risk parameters without hardcoded limits");
    println!("  - Complete strategy suite active");
    println!("  - Only use after thorough testing in all other modes");
    
    println!("\n{}", "=".repeat(80));
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use std::sync::Arc;

    fn create_test_config() -> Arc<Config> {
        Arc::new(Config {
            app_name: "test".to_string(),
            log_level: "info".to_string(),
            chains: std::collections::HashMap::new(),
            strategy: crate::config::StrategyConfig {
                kelly_fraction_cap: 0.02,
                min_profitability_bps: 50,
                enabled_strategies: vec!["SwapScanner".to_string()],
            },
            execution: crate::config::ExecutionConfig::default(),
            secrets: crate::config::Secrets::default(),
            scoring: crate::config::ScoringConfig::default(),
            nats: crate::config::NatsConfig::default(),
            aetheric_resonance: crate::config::AethericResonanceEngineConfig::default(),
        })
    }

    #[tokio::test]
    async fn test_single_mode_validation() {
        let config = create_test_config();
        let result = run_single_mode_validation(config, RunMode::Simulate, false).await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_show_usage_examples() {
        // This should not panic
        show_deployment_mode_usage_examples();
    }
}