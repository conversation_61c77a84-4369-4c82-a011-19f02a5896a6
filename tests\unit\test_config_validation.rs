// Unit Tests for Configuration Validation
// Tests configuration loading, validation, and error handling

use basilisk_bot::config::validation::ConfigValidator;
use rust_decimal_macros::dec;
use std::collections::HashMap;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_configuration_loading() {
        // Test that the configuration can be loaded successfully
        let settings = Settings::new(Some("config/default")).expect("Failed to load configuration");
        
        // Verify critical configuration sections exist
        assert!(settings.execution.default_slippage_tolerance > dec!(0.0));
        assert!(settings.execution.default_slippage_tolerance < dec!(1.0));
        assert!(settings.risk.max_daily_loss_usd > dec!(0.0));
        assert!(settings.risk.kelly_fraction_config > dec!(0.0));
    }

    #[test]
    fn test_chain_configuration_validation() {
        let settings = Settings::new(Some("config/default")).expect("Failed to load configuration");
        
        // Verify Base chain configuration
        let base_config = settings.chains.get(&8453).expect("Base chain should be configured");
        assert_eq!(base_config.name, "Base");
        assert!(base_config.enabled);
        assert!(!base_config.rpc_endpoints.is_empty());
    }

    #[test]
    fn test_risk_parameter_bounds() {
        let settings = Settings::new(Some("config/default")).expect("Failed to load configuration");
        
        // Test risk regime multipliers are within reasonable bounds
        let multipliers = &settings.risk.regime_multipliers;
        
        // High volatility should reduce position sizes
        assert!(multipliers.high_volatility_position_mult < dec!(1.0));
        assert!(multipliers.high_volatility_position_mult > dec!(0.0));
        
        // Bot gas war should significantly reduce position sizes
        assert!(multipliers.bot_gas_war_position_mult < multipliers.high_volatility_position_mult);
        
        // Calm orderly should be baseline (1.0)
        assert_eq!(multipliers.calm_orderly_position_mult, dec!(1.0));
    }

    #[test]
    fn test_gas_configuration_ordering() {
        let settings = Settings::new(Some("config/default")).expect("Failed to load configuration");
        
        // Test that gas fees are in ascending order
        let gas_config = &settings.execution.priority_fee_gwei;
        assert!(gas_config.low_gwei <= gas_config.medium_gwei);
        assert!(gas_config.medium_gwei <= gas_config.high_gwei);
        assert!(gas_config.high_gwei <= gas_config.critical_gwei);
    }

    #[test]
    fn test_scanner_configuration_validation() {
        let settings = Settings::new(Some("config/default")).expect("Failed to load configuration");
        
        // Test scanner configurations are reasonable
        assert!(settings.scanners.gaze_scanner.min_price_deviation_pct > dec!(0.0));
        assert!(settings.scanners.gaze_scanner.min_price_deviation_pct < dec!(1.0));
        
        assert!(settings.scanners.pilot_fish_scanner.min_whale_trade_usd > dec!(1000.0));
        assert!(settings.scanners.pilot_fish_scanner.profit_multiplier > dec!(0.0));
        assert!(settings.scanners.pilot_fish_scanner.profit_multiplier < dec!(1.0));
    }

    #[test]
    fn test_invalid_configuration_handling() {
        // Test handling of invalid configuration values
        let validator = ConfigValidator::new();
        
        // Test invalid slippage tolerance
        let invalid_slippage = dec!(1.5); // 150% - invalid
        assert!(validator.validate_slippage_tolerance(invalid_slippage).is_err());
        
        // Test invalid Kelly fraction
        let invalid_kelly = dec!(2.0); // 200% - invalid
        assert!(validator.validate_kelly_fraction(invalid_kelly).is_err());
        
        // Test invalid gas multiplier
        let invalid_gas_mult = dec!(0.0); // 0% - invalid
        assert!(validator.validate_gas_multiplier(invalid_gas_mult).is_err());
    }
}