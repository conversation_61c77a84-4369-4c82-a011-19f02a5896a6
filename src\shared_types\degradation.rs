// MISSION: Graceful Degradation Framework
// WHY: Allow the system to continue operating in a limited capacity when dependencies fail
// HOW: Define degradation states and fallback behaviors for each component

use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime, UNIX_EPOCH};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DegradationLevel {
    /// Full functionality - all systems operational
    Operational,
    /// Minor degradation - some non-critical features disabled
    MinorDegradation,
    /// Significant degradation - operating in safe mode with reduced functionality
    SafeMode,
    /// Critical degradation - emergency mode, minimal operations only
    Emergency,
    /// Complete shutdown required
    Shutdown,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DegradationState {
    pub level: DegradationLevel,
    pub reason: String,
    pub affected_services: Vec<String>,
    pub fallback_actions: Vec<String>,
    pub timestamp: u64,
    pub auto_recovery_possible: bool,
}

impl DegradationState {
    pub fn new(level: DegradationLevel, reason: String) -> Self {
        Self {
            level,
            reason,
            affected_services: Vec::new(),
            fallback_actions: Vec::new(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            auto_recovery_possible: true,
        }
    }

    pub fn with_affected_services(mut self, services: Vec<String>) -> Self {
        self.affected_services = services;
        self
    }

    pub fn with_fallback_actions(mut self, actions: Vec<String>) -> Self {
        self.fallback_actions = actions;
        self
    }

    pub fn with_auto_recovery(mut self, possible: bool) -> Self {
        self.auto_recovery_possible = possible;
        self
    }

    pub fn age(&self) -> Duration {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        Duration::from_secs(now.saturating_sub(self.timestamp))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityStatus {
    Safe,
    Uncertain,
    Risky,
    Dangerous,
}

impl SecurityStatus {
    pub fn is_safe_to_trade(&self) -> bool {
        matches!(self, SecurityStatus::Safe)
    }

    pub fn risk_penalty(&self) -> f64 {
        match self {
            SecurityStatus::Safe => 0.0,
            SecurityStatus::Uncertain => 0.5,
            SecurityStatus::Risky => 0.8,
            SecurityStatus::Dangerous => 1.0,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataFreshness {
    pub timestamp: u64,
    pub staleness_threshold: Duration,
}

impl DataFreshness {
    pub fn new(staleness_threshold: Duration) -> Self {
        Self {
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            staleness_threshold,
        }
    }

    pub fn is_stale(&self) -> bool {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        let age = Duration::from_secs(now.saturating_sub(self.timestamp));
        age > self.staleness_threshold
    }

    pub fn age(&self) -> Duration {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        Duration::from_secs(now.saturating_sub(self.timestamp))
    }

    pub fn update(&mut self) {
        self.timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread::sleep;

    #[test]
    fn test_degradation_state_creation() {
        let state = DegradationState::new(
            DegradationLevel::SafeMode,
            "RPC endpoints failing".to_string(),
        )
        .with_affected_services(vec!["ExecutionManager".to_string()])
        .with_fallback_actions(vec!["Switch to conservative mode".to_string()]);

        assert_eq!(state.level, DegradationLevel::SafeMode);
        assert_eq!(state.affected_services.len(), 1);
        assert_eq!(state.fallback_actions.len(), 1);
    }

    #[test]
    fn test_data_freshness() {
        // Test by manually setting timestamps to avoid timing issues
        let mut freshness = DataFreshness::new(Duration::from_secs(60));
        assert!(!freshness.is_stale());

        // Manually set timestamp to past to simulate staleness
        freshness.timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
            .saturating_sub(120); // 2 minutes ago
        
        assert!(freshness.is_stale());

        freshness.update();
        assert!(!freshness.is_stale());
    }

    #[test]
    fn test_security_status_risk_penalty() {
        assert_eq!(SecurityStatus::Safe.risk_penalty(), 0.0);
        assert_eq!(SecurityStatus::Uncertain.risk_penalty(), 0.5);
        assert_eq!(SecurityStatus::Risky.risk_penalty(), 0.8);
        assert_eq!(SecurityStatus::Dangerous.risk_penalty(), 1.0);
    }
}