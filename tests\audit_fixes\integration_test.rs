//! AUDIT-FIX: Integration test for the comprehensive testing framework
//! This test verifies that all testing components work together correctly

use super::test_runner::{AuditFixTestRunner, TestRunnerConfig};
use anyhow::Result;

#[tokio::test]
async fn test_comprehensive_validation_framework() -> Result<()> {
    // Create a test configuration with shorter timeouts for testing
    let config = TestRunnerConfig {
        run_unit_tests: true,
        run_integration_tests: true,
        run_edge_case_tests: false, // Skip for faster testing
        run_stress_tests: false,    // Skip for faster testing
        run_performance_benchmarks: false, // Skip for faster testing
        run_regression_tests: true,
        generate_report: false,     // Don't generate file during test
        report_output_path: "target/test_report.json".to_string(),
        max_test_duration_minutes: 5,
    };

    let test_runner = AuditFixTestRunner::new(config);
    
    // Run a subset of tests to verify the framework works
    let results = test_runner.run_comprehensive_tests().await?;
    
    // Verify basic functionality
    assert!(results.summary.total_tests_run > 0, "Should run some tests");
    assert!(results.summary.success_rate >= 0.0, "Success rate should be valid");
    
    // Verify regression tests ran
    assert!(results.regression_test_summary.total_regression_tests > 0, "Should run regression tests");
    
    println!("✅ Comprehensive validation framework test passed");
    println!("   Tests run: {}", results.summary.total_tests_run);
    println!("   Success rate: {:.1}%", results.summary.success_rate * 100.0);
    
    Ok(())
}

#[test]
fn test_validation_framework_components() {
    use super::validation_framework::*;
    use super::test_data_providers::*;
    use super::component_validators::*;

    // Test that we can create the validation framework
    let config = ValidationConfig::default();
    let mut framework = ValidationFramework::new(config);
    
    // Test that we can register providers and validators
    framework.register_test_data_provider(
        "TestMath".to_string(),
        Box::new(MathComponentTestDataProvider),
    );
    
    framework.register_validator(
        "TestMath".to_string(),
        Box::new(MathComponentValidator),
    );
    
    println!("✅ Validation framework components test passed");
}

#[test]
fn test_test_data_providers() {
    use super::test_data_providers::*;
    use super::validation_framework::TestDataProvider;

    // Test math component test data provider
    let math_provider = MathComponentTestDataProvider;
    let scenarios = math_provider.get_test_scenarios();
    assert!(!scenarios.is_empty(), "Should provide test scenarios");
    
    let edge_cases = math_provider.get_edge_cases();
    assert!(!edge_cases.is_empty(), "Should provide edge cases");
    
    // Test execution component test data provider
    let exec_provider = ExecutionComponentTestDataProvider;
    let exec_scenarios = exec_provider.get_test_scenarios();
    assert!(!exec_scenarios.is_empty(), "Should provide execution test scenarios");
    
    println!("✅ Test data providers test passed");
}

#[test]
fn test_component_validators() {
    use super::component_validators::*;
    use super::validation_framework::*;
    use super::test_data_providers::*;

    // Test math component validator
    let math_validator = MathComponentValidator;
    let math_provider = MathComponentTestDataProvider;
    let scenarios = math_provider.get_test_scenarios();
    
    if let Some(scenario) = scenarios.first() {
        let result = math_validator.validate_component(scenario);
        assert!(result.is_ok(), "Validator should handle test scenarios");
    }
    
    // Test execution component validator
    let exec_validator = ExecutionComponentValidator;
    let exec_provider = ExecutionComponentTestDataProvider;
    let exec_scenarios = exec_provider.get_test_scenarios();
    
    if let Some(scenario) = exec_scenarios.first() {
        let result = exec_validator.validate_component(scenario);
        assert!(result.is_ok(), "Execution validator should handle test scenarios");
    }
    
    println!("✅ Component validators test passed");
}

/// Test that demonstrates the complete testing workflow
#[tokio::test]
async fn test_complete_workflow_demonstration() -> Result<()> {
    println!("🚀 Demonstrating complete audit fix testing workflow...");
    
    // Step 1: Create validation framework
    println!("📋 Step 1: Setting up validation framework");
    let config = ValidationConfig {
        max_test_duration_ms: 1000,
        numerical_tolerance: rust_decimal_macros::dec!(0.01),
        stress_test_iterations: 10, // Reduced for demo
        enable_continuous_monitoring: false,
        performance_baseline_ms: 50,
    };
    let mut framework = ValidationFramework::new(config);
    
    // Step 2: Register test data providers
    println!("📊 Step 2: Registering test data providers");
    framework.register_test_data_provider(
        "MathComponents".to_string(),
        Box::new(MathComponentTestDataProvider),
    );
    
    // Step 3: Register validators
    println!("🔍 Step 3: Registering component validators");
    framework.register_validator(
        "MathComponents".to_string(),
        Box::new(MathComponentValidator),
    );
    
    // Step 4: Run validation
    println!("⚡ Step 4: Running validation tests");
    let validation_results = framework.run_comprehensive_validation().await?;
    
    // Step 5: Analyze results
    println!("📈 Step 5: Analyzing results");
    println!("   Components tested: {}", validation_results.component_results.len());
    println!("   Overall success: {}", validation_results.overall_success);
    
    for (component_name, component_result) in &validation_results.component_results {
        println!("   {}: {} scenarios tested", component_name, component_result.scenario_results.len());
        
        let passed = component_result.scenario_results.values().filter(|r| r.passed).count();
        let total = component_result.scenario_results.len();
        println!("     Passed: {}/{} ({:.1}%)", passed, total, (passed as f64 / total as f64) * 100.0);
    }
    
    println!("✅ Complete workflow demonstration passed");
    Ok(())
}

/// Performance test to ensure the testing framework itself is efficient
#[tokio::test]
async fn test_framework_performance() -> Result<()> {
    use std::time::Instant;
    
    let start_time = Instant::now();
    
    // Run a lightweight version of the framework
    let config = ValidationConfig {
        max_test_duration_ms: 500,
        numerical_tolerance: rust_decimal_macros::dec!(0.1),
        stress_test_iterations: 5,
        enable_continuous_monitoring: false,
        performance_baseline_ms: 100,
    };
    
    let mut framework = ValidationFramework::new(config);
    framework.register_test_data_provider(
        "MathComponents".to_string(),
        Box::new(MathComponentTestDataProvider),
    );
    framework.register_validator(
        "MathComponents".to_string(),
        Box::new(MathComponentValidator),
    );
    
    let _results = framework.run_comprehensive_validation().await?;
    
    let duration = start_time.elapsed();
    
    // Framework should complete quickly
    assert!(duration.as_secs() < 10, "Framework should complete within 10 seconds, took: {:?}", duration);
    
    println!("✅ Framework performance test passed ({}ms)", duration.as_millis());
    Ok(())
}

/// Test error handling in the validation framework
#[tokio::test]
async fn test_error_handling() -> Result<()> {
    use super::validation_framework::*;
    
    // Test with empty framework (no providers/validators)
    let config = ValidationConfig::default();
    let framework = ValidationFramework::new(config);
    
    let results = framework.run_comprehensive_validation().await?;
    
    // Should handle empty framework gracefully
    assert!(results.component_results.is_empty(), "Empty framework should have no component results");
    assert!(results.overall_success, "Empty framework should be considered successful");
    
    println!("✅ Error handling test passed");
    Ok(())
}

/// Integration test for the complete test runner
#[tokio::test]
async fn test_complete_test_runner_integration() -> Result<()> {
    // Test with minimal configuration to avoid long-running tests
    let config = TestRunnerConfig {
        run_unit_tests: true,
        run_integration_tests: false,
        run_edge_case_tests: false,
        run_stress_tests: false,
        run_performance_benchmarks: false,
        run_regression_tests: false,
        generate_report: false,
        report_output_path: "target/integration_test_report.json".to_string(),
        max_test_duration_minutes: 1,
    };
    
    let test_runner = AuditFixTestRunner::new(config);
    let results = test_runner.run_comprehensive_tests().await?;
    
    // Verify basic structure
    assert!(!results.test_run_id.is_empty(), "Should have test run ID");
    assert!(!results.timestamp.is_empty(), "Should have timestamp");
    assert!(results.summary.total_tests_run >= 0, "Should have valid test count");
    
    println!("✅ Complete test runner integration test passed");
    println!("   Test Run ID: {}", results.test_run_id);
    println!("   Duration: {:?}", results.total_duration);
    
    Ok(())
}
