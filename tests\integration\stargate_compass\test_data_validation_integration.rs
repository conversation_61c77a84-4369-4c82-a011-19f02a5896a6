// MISSION: Integration Test for TUI Data Validation System
// WHY: Demonstrate and test the comprehensive data validation capabilities
// HOW: End-to-end test showing TUI output validation against on-chain data

"""use super::{
    anvil_client::AnvilClient,
    core::{DataValidationResult, TestResult, TuiTestResult},
    data_validator::TuiDataValidator,
    tui_functionality_tester::TuiFunctionalityTester,
};
use anyhow::Result;
use std::sync::Arc;
""

/// Integration test for TUI data validation system
pub struct TuiDataValidationIntegrationTest {
    anvil_client: Arc<AnvilClient>,
    tui_tester: TuiFunctionalityTester,
    data_validator: Arc<TuiDataValidator>,
    contract_address: Address,
}

impl TuiDataValidationIntegrationTest {
    /// Create new integration test instance
    pub async fn new(anvil_url: String, contract_address_str: String) -> Result<Self> {
        info!("Initializing TUI data validation integration test");
        
        // Parse contract address
        let contract_address = Address::from_str(&contract_address_str)
            .map_err(|e| anyhow::anyhow!("Invalid contract address: {}", e))?;
        
        // Create Anvil client
        let anvil_client = Arc::new(
            AnvilClient::connect_existing(anvil_url.clone(), Some(contract_address)).await?
        );
        
        // Verify Anvil connection
        if !anvil_client.health_check().await? {
            return Err(anyhow::anyhow!("Anvil health check failed"));
        }
        
        // Create TUI functionality tester
        let mut tui_tester = TuiFunctionalityTester::new(anvil_url, contract_address_str);
        
        // Set up data validator for TUI tester
        tui_tester.set_data_validator(anvil_client.clone())?;
        
        // Create standalone data validator
        let data_validator = Arc::new(TuiDataValidator::new(anvil_client.clone(), contract_address));
        
        Ok(Self {
            anvil_client,
            tui_tester,
            data_validator,
            contract_address,
        })
    }
    
    /// Run comprehensive data validation integration test
    pub async fn run_comprehensive_test(&mut self) -> Result<DataValidationTestResults> {
        info!("Starting comprehensive TUI data validation integration test");
        
        let mut test_results = DataValidationTestResults::new();
        
        // Test 1: Balance Display Validation
        info!("Testing balance display validation...");
        match self.test_balance_display_validation().await {
            Ok(result) => {
                test_results.balance_validation_results.push(result);
                info!("✅ Balance display validation completed");
            }
            Err(e) => {
                error!("❌ Balance display validation failed: {}", e);
                test_results.errors.push(format!("Balance validation error: {}", e));
            }
        }
        
        // Test 2: Contract Status Display Validation
        info!("Testing contract status display validation...");
        match self.test_contract_status_validation().await {
            Ok(result) => {
                test_results.contract_status_validation_results.push(result);
                info!("✅ Contract status display validation completed");
            }
            Err(e) => {
                error!("❌ Contract status display validation failed: {}", e);
                test_results.errors.push(format!("Contract status validation error: {}", e));
            }
        }
        
        // Test 3: Transaction Status Validation (if transactions available)
        info!("Testing transaction status validation...");
        match self.test_transaction_status_validation().await {
            Ok(results) => {
                test_results.transaction_validation_results.extend(results);
                info!("✅ Transaction status validation completed");
            }
            Err(e) => {
                warn!("⚠️ Transaction status validation skipped: {}", e);
                test_results.warnings.push(format!("Transaction validation warning: {}", e));
            }
        }
        
        // Test 4: Transaction History Validation
        info!("Testing transaction history validation...");
        match self.test_transaction_history_validation().await {
            Ok(result) => {
                test_results.transaction_history_validation_results.push(result);
                info!("✅ Transaction history validation completed");
            }
            Err(e) => {
                warn!("⚠️ Transaction history validation failed: {}", e);
                test_results.warnings.push(format!("Transaction history validation warning: {}", e));
            }
        }
        
        // Test 5: Comprehensive Output Validation
        info!("Testing comprehensive output validation...");
        match self.test_comprehensive_output_validation().await {
            Ok(results) => {
                test_results.comprehensive_validation_results.extend(results);
                info!("✅ Comprehensive output validation completed");
            }
            Err(e) => {
                error!("❌ Comprehensive output validation failed: {}", e);
                test_results.errors.push(format!("Comprehensive validation error: {}", e));
            }
        }
        
        // Calculate summary
        test_results.calculate_summary();
        
        info!("TUI data validation integration test completed");
        info!("Summary: {} validations passed, {} failed, {} warnings", 
              test_results.passed_validations, 
              test_results.failed_validations, 
              test_results.warnings.len());
        
        Ok(test_results)
    }
    
    /// Test balance display validation
    async fn test_balance_display_validation(&mut self) -> Result<DataValidationResult> {
        info!("Executing balance query command for validation");
        
        // Execute balance query command
        let command_result = self.tui_tester.execute_command("query_balances").await?;
        
        if !command_result.success {
            return Err(anyhow::anyhow!("Balance query command failed: {:?}", command_result.error_message));
        }
        
        // Validate balance display against on-chain data
        let validation_result = self.data_validator
            .validate_balance_display(&command_result.output_captured, self.contract_address)
            .await?;
        
        info!("Balance validation result: matches={}, method={}", 
              validation_result.matches, validation_result.validation_method);
        
        if let Some(error) = &validation_result.error_message {
            warn!("Balance validation issues: {}", error);
        }
        
        Ok(validation_result)
    }
    
    /// Test contract status display validation
    async fn test_contract_status_validation(&mut self) -> Result<DataValidationResult> {
        info!("Executing contract status query command for validation");
        
        // Execute contract status query command
        let command_result = self.tui_tester.execute_command("query_contract_status").await?;
        
        if !command_result.success {
            return Err(anyhow::anyhow!("Contract status query command failed: {:?}", command_result.error_message));
        }
        
        // Validate contract status display against on-chain state
        let validation_result = self.data_validator
            .validate_contract_status_display(&command_result.output_captured)
            .await?;
        
        info!("Contract status validation result: matches={}, method={}", 
              validation_result.matches, validation_result.validation_method);
        
        if let Some(error) = &validation_result.error_message {
            warn!("Contract status validation issues: {}", error);
        }
        
        Ok(validation_result)
    }
    
    /// Test transaction status validation
    async fn test_transaction_status_validation(&mut self) -> Result<Vec<DataValidationResult>> {
        info!("Testing transaction status validation");
        
        // Get recent transaction history to find transactions to validate
        let transaction_history = self.data_validator
            .query_transaction_history(5)
            .await?;
        
        if transaction_history.transactions.is_empty() {
            return Err(anyhow::anyhow!("No transactions found for validation"));
        }
        
        let mut validation_results = Vec::new();
        
        // Test validation for up to 3 recent transactions
        for tx_data in transaction_history.transactions.iter().take(3) {
            info!("Validating transaction status for hash: {:?}", tx_data.hash);
            
            // Create mock TUI output for transaction status
            let mock_tui_output = self.create_mock_transaction_status_output(&tx_data);
            
            // Validate transaction status display
            match self.data_validator
                .validate_transaction_status_display(&mock_tui_output, tx_data.hash)
                .await {
                Ok(result) => {
                    validation_results.push(result);
                    info!("Transaction status validation completed for {:?}", tx_data.hash);
                }
                Err(e) => {
                    warn!("Transaction status validation failed for {:?}: {}", tx_data.hash, e);
                }
            }
        }
        
        if validation_results.is_empty() {
            return Err(anyhow::anyhow!("No transaction status validations completed"));
        }
        
        Ok(validation_results)
    }
    
    /// Test transaction history validation
    async fn test_transaction_history_validation(&mut self) -> Result<DataValidationResult> {
        info!("Testing transaction history validation");
        
        // Create mock TUI output for transaction history
        let transaction_history = self.data_validator
            .query_transaction_history(10)
            .await?;
        
        let mock_tui_output = self.create_mock_transaction_history_output(&transaction_history);
        
        // Validate transaction history display
        let validation_result = self.data_validator
            .validate_transaction_history_display(&mock_tui_output, 10)
            .await?;
        
        info!("Transaction history validation result: matches={}, method={}", 
              validation_result.matches, validation_result.validation_method);
        
        if let Some(error) = &validation_result.error_message {
            warn!("Transaction history validation issues: {}", error);
        }
        
        Ok(validation_result)
    }
    
    /// Test comprehensive output validation
    async fn test_comprehensive_output_validation(&mut self) -> Result<Vec<DataValidationResult>> {
        info!("Testing comprehensive output validation");
        
        // Execute multiple TUI commands to get comprehensive output
        let mut comprehensive_output = String::new();
        
        // Execute balance query
        if let Ok(result) = self.tui_tester.execute_command("query_balances").await {
            comprehensive_output.push_str(&result.output_captured);
            comprehensive_output.push_str("\n---\n");
        }
        
        // Execute contract status query
        if let Ok(result) = self.tui_tester.execute_command("query_contract_status").await {
            comprehensive_output.push_str(&result.output_captured);
            comprehensive_output.push_str("\n---\n");
        }
        
        // Create validation configuration
        let validation_config = ValidationConfig {
            validate_balances: true,
            validate_contract_status: true,
            validate_transaction_history: true,
            addresses_to_check: vec![self.contract_address],
            transactions_to_check: Vec::new(), // Will be populated if transactions exist
            expected_transaction_count: 5,
        };
        
        // Run comprehensive validation
        let validation_results = self.data_validator
            .validate_comprehensive_tui_output(&comprehensive_output, validation_config)
            .await?;
        
        info!("Comprehensive validation completed with {} results", validation_results.len());
        
        for (i, result) in validation_results.iter().enumerate() {
            info!("Validation {}: type={}, matches={}", 
                  i + 1, result.data_type, result.matches);
            
            if let Some(error) = &result.error_message {
                warn!("Validation {} issues: {}", i + 1, error);
            }
        }
        
        Ok(validation_results)
    }
    
    /// Create mock TUI output for transaction status testing
    fn create_mock_transaction_status_output(&self, tx_data: &TransactionStatusData) -> String {
        format!(
            r#"
Transaction Status:
Hash: {:?}
Status: {:?}
Block: {}
Confirmations: {}
Gas Used: {}
            "#,
            tx_data.hash,
            tx_data.status,
            tx_data.block_number.unwrap_or(0),
            tx_data.confirmations,
            tx_data.gas_used.map(|g| g.to_string()).unwrap_or("N/A".to_string())
        )
    }
    
    /// Create mock TUI output for transaction history testing
    fn create_mock_transaction_history_output(&self, history: &TransactionHistoryData) -> String {
        let mut output = String::from("Transaction History:\n");
        
        for (i, tx) in history.transactions.iter().enumerate() {
            output.push_str(&format!(
                "{}. {:?} - {:?} - {} confirmations\n",
                i + 1,
                tx.hash,
                tx.status,
                tx.confirmations
            ));
        }
        
        output.push_str(&format!(
            "\nTotal: {} transactions ({} successful, {} failed)\n",
            history.total_count,
            history.successful_count,
            history.failed_count
        ));
        
        output
    }
    
    /// Get test summary
    pub async fn get_test_summary(&self) -> Result<TestSummary> {
        let contract_state = self.data_validator.query_contract_state().await?;
        let blockchain_state = self.anvil_client.get_blockchain_state().await?;
        
        Ok(TestSummary {
            contract_address: self.contract_address,
            contract_balance: contract_state.balance,
            contract_paused: contract_state.is_paused,
            contract_emergency_stopped: contract_state.emergency_stopped,
            total_transactions: contract_state.total_transactions,
            current_block: blockchain_state.block_number,
            chain_id: blockchain_state.chain_id,
            test_timestamp: chrono::Utc::now(),
        })
    }
}

/// Results from data validation integration test
#[derive(Debug, Clone)]
pub struct DataValidationTestResults {
    pub balance_validation_results: Vec<DataValidationResult>,
    pub contract_status_validation_results: Vec<DataValidationResult>,
    pub transaction_validation_results: Vec<DataValidationResult>,
    pub transaction_history_validation_results: Vec<DataValidationResult>,
    pub comprehensive_validation_results: Vec<DataValidationResult>,
    pub passed_validations: usize,
    pub failed_validations: usize,
    pub total_validations: usize,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

impl DataValidationTestResults {
    pub fn new() -> Self {
        Self {
            balance_validation_results: Vec::new(),
            contract_status_validation_results: Vec::new(),
            transaction_validation_results: Vec::new(),
            transaction_history_validation_results: Vec::new(),
            comprehensive_validation_results: Vec::new(),
            passed_validations: 0,
            failed_validations: 0,
            total_validations: 0,
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }
    
    pub fn calculate_summary(&mut self) {
        let all_results = [
            &self.balance_validation_results,
            &self.contract_status_validation_results,
            &self.transaction_validation_results,
            &self.transaction_history_validation_results,
            &self.comprehensive_validation_results,
        ];
        
        self.total_validations = 0;
        self.passed_validations = 0;
        self.failed_validations = 0;
        
        for results in all_results.iter() {
            for result in results.iter() {
                self.total_validations += 1;
                if result.matches {
                    self.passed_validations += 1;
                } else {
                    self.failed_validations += 1;
                }
            }
        }
    }
    
    pub fn success_rate(&self) -> f64 {
        if self.total_validations > 0 {
            self.passed_validations as f64 / self.total_validations as f64
        } else {
            0.0
        }
    }
}

/// Test summary information
#[derive(Debug, Clone)]
pub struct TestSummary {
    pub contract_address: Address,
    pub contract_balance: ethers::types::U256,
    pub contract_paused: bool,
    pub contract_emergency_stopped: bool,
    pub total_transactions: u64,
    pub current_block: u64,
    pub chain_id: u64,
    pub test_timestamp: chrono::DateTime<chrono::Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_data_validation_integration_creation() {
        // This test verifies that the integration test can be created
        // In a real environment, this would connect to an actual Anvil instance
        
        let result = TuiDataValidationIntegrationTest::new(
            "http://localhost:8545".to_string(),
            "******************************************".to_string(),
        ).await;
        
        // In a test environment without Anvil running, this will fail
        // In production, this should succeed
        match result {
            Ok(_) => println!("✅ Integration test created successfully"),
            Err(e) => println!("⚠️ Integration test creation failed (expected in test env): {}", e),
        }
    }
    
    #[test]
    fn test_data_validation_results_calculation() {
        let mut results = DataValidationTestResults::new();
        
        // Add some mock validation results
        results.balance_validation_results.push(DataValidationResult {
            data_type: "balance_display".to_string(),
            expected_value: "1000 USDC".to_string(),
            actual_value: "1000 USDC".to_string(),
            matches: true,
            validation_method: "direct_comparison".to_string(),
            tolerance: None,
            error_message: None,
        });
        
        results.contract_status_validation_results.push(DataValidationResult {
            data_type: "contract_status".to_string(),
            expected_value: "active".to_string(),
            actual_value: "paused".to_string(),
            matches: false,
            validation_method: "status_check".to_string(),
            tolerance: None,
            error_message: Some("Status mismatch".to_string()),
        });
        
        results.calculate_summary();
        
        assert_eq!(results.total_validations, 2);
        assert_eq!(results.passed_validations, 1);
        assert_eq!(results.failed_validations, 1);
        assert_eq!(results.success_rate(), 0.5);
    }
}