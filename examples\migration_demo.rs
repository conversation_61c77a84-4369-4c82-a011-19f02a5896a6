// Example demonstrating the phased migration strategy
// Run with: cargo run --example migration_demo

use basilisk_bot::config::{Config, Settings, migration::*};
use basilisk_bot::config::migration::helpers::ConfigBridge;
use std::sync::Arc;
use std::env;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔄 Configuration Migration Strategy Demo");
    println!("========================================\n");

    // Initialize migration tracker
    let mut migration_plan = MigrationPlan::new();
    
    println!("📋 Migration Plan Overview:");
    println!("Phase 1: {} ✅ (Complete)", MigrationPhase::CoreInfrastructure.description());
    println!("Phase 2: {}", MigrationPhase::StrategyComponents.description());
    println!("Phase 3: {}", MigrationPhase::ExecutionAndRisk.description());
    println!("Phase 4: {}", MigrationPhase::UserInterface.description());
    println!("Phase 5: {}", MigrationPhase::Cleanup.description());
    println!();

    // Demonstrate Phase 1 (already complete)
    println!("🚀 Phase 1: Core Infrastructure (Already Complete)");
    demonstrate_core_infrastructure();
    migration_plan.execute_phase(MigrationPhase::CoreInfrastructure);
    println!();

    // Demonstrate how components can be gradually migrated
    println!("🔄 Phase 2: Strategy Components Migration");
    demonstrate_component_migration();
    migration_plan.execute_phase(MigrationPhase::StrategyComponents);
    println!();

    // Show adapter pattern in action
    println!("🌉 Adapter Pattern Demonstration");
    demonstrate_adapter_pattern();
    println!();

    // Show environment variable override capabilities
    println!("🌍 Environment Variable Override Demo");
    demonstrate_env_overrides();
    println!();

    println!("🎯 Migration Strategy Benefits:");
    println!("  ✅ Zero downtime migration");
    println!("  ✅ Gradual rollout with rollback capability");
    println!("  ✅ Existing code continues working unchanged");
    println!("  ✅ New features available immediately");
    println!("  ✅ Enhanced validation and error handling");
    println!("  ✅ Better environment variable support");

    Ok(())
}

fn demonstrate_core_infrastructure() {
    println!("  📦 Configuration loading now uses figment with layered approach");
    println!("  📦 Enhanced validation catches configuration errors early");
    println!("  📦 Environment variable support with APP_ prefix");
    println!("  📦 Adapter layer provides backward compatibility");
}

fn demonstrate_component_migration() -> Result<(), Box<dyn std::error::Error>> {
    println!("  🔧 Example: Migrating a strategy scanner component");
    
    // Simulate old component
    struct OldScanner {
        config: Arc<Settings>,
    }
    
    impl OldScanner {
        fn new(config: Arc<Settings>) -> Self {
            helpers::log_component_migration("OldScanner", false);
            Self { config }
        }
        
        fn get_kelly_fraction(&self) -> f64 {
            helpers::get_kelly_fraction(&self.config)
        }
    }
    
    // Simulate new component that can accept either config
    struct NewScanner {
        kelly_fraction: f64,
        max_slippage: u64,
    }
    
    impl helpers::ConfigBridge for NewScanner {
        fn from_old_settings(settings: Arc<Settings>) -> Self {
            helpers::log_component_migration("NewScanner", false);
            Self {
                kelly_fraction: helpers::get_kelly_fraction(&settings),
                max_slippage: helpers::get_max_slippage_bps(&settings),
            }
        }
        
        fn from_new_config(config: Arc<Config>) -> Self {
            helpers::log_component_migration("NewScanner", true);
            Self {
                kelly_fraction: config.strategy.kelly_fraction_cap,
                max_slippage: config.execution.max_slippage_bps,
            }
        }
    }
    
    // Load configuration using new system
    env::set_var("CONFIG_PATH", "config/elegant-minimal.toml");
    
    let settings_result = Config::load().map(|config| Arc::new(config.to_settings()));
    match settings_result {
        Ok(settings_arc) => {
            // Old component still works
            let _old_scanner = OldScanner::new(settings_arc.clone());
            
            // New component can use either approach
            let _new_scanner = NewScanner::from_old_settings(settings_arc);
            
            println!("  ✅ Both old and new components work with same configuration");
            Ok(())
        }
        Err(e) => {
            println!("  ❌ Configuration loading failed: {}", e);
            Err(e.into())
        }
    }
}

fn demonstrate_adapter_pattern() {
    println!("  🌉 The adapter pattern allows seamless transition:");
    
    env::set_var("CONFIG_PATH", "config/elegant-minimal.toml");
    
    // Load with new system
    match Config::load() {
        Ok(new_config) => {
            println!("  📥 Loaded with new Config system");
            println!("    - Kelly fraction: {}", new_config.strategy.kelly_fraction_cap);
            println!("    - Chains: {}", new_config.chains.len());
            
            // Convert to old format
            let old_settings = new_config.to_settings();
            println!("  🔄 Converted to old Settings format");
            println!("    - Kelly fraction: {}", old_settings.strategy.kelly_fraction_cap);
            println!("    - Chains: {}", old_settings.chains.len());
            
            println!("  ✅ Seamless conversion maintains all data");
        }
        Err(e) => {
            println!("  ❌ Failed to demonstrate adapter: {}", e);
        }
    }
}

fn demonstrate_env_overrides() {
    println!("  🌍 Environment variables can override any configuration:");
    
    // Set some environment overrides
    env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "0.15");
    env::set_var("APP_EXECUTION__MAX_SLIPPAGE_BPS", "300");
    env::set_var("CONFIG_PATH", "config/elegant-minimal.toml");
    
    match Config::load() {
        Ok(config) => {
            println!("  📊 Configuration with environment overrides:");
            println!("    - Kelly fraction: {} (overridden from env)", config.strategy.kelly_fraction_cap);
            println!("    - Max slippage: {}bps (overridden from env)", config.execution.max_slippage_bps);
            println!("  ✅ Environment variables take precedence");
        }
        Err(e) => {
            println!("  ❌ Failed to load with overrides: {}", e);
        }
    }
    
    // Clean up
    env::remove_var("APP_STRATEGY__KELLY_FRACTION_CAP");
    env::remove_var("APP_EXECUTION__MAX_SLIPPAGE_BPS");
}
