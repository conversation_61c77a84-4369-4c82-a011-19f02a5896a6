// OROBOROS: SystemMonitor - The Autonomic Nervous System
// WHY: Automated tactical failover for RPC endpoints within active territory
// HOW: Monitor service heartbeats and trigger intelligent RPC failover

use async_nats::Client as NatsClient;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::time::interval;
use tokio_stream::StreamExt;
use tracing::{debug, error, info, warn};

use crate::config::Settings;
use crate::shared_types::{NatsTopics, ServiceStatus};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHeartbeat {
    pub service_name: String,
    pub status: ServiceStatus,
    pub current_rpc_url: Option<String>,
    pub timestamp: u64,
    pub chain_id: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigUpdate {
    pub target_service: String,
    pub new_rpc_url: String,
    pub reason: String,
    pub timestamp: u64,
}

pub struct SystemMonitor {
    nats_client: NatsClient,
    config: Settings,
    service_heartbeats: HashMap<String, ServiceHeartbeat>,
    last_heartbeat_time: HashMap<String, Instant>,
    failover_history: Vec<ConfigUpdate>,
}

impl SystemMonitor {
    pub fn new(nats_client: NatsClient, config: Settings) -> Self {
        Self {
            nats_client,
            config,
            service_heartbeats: HashMap::new(),
            last_heartbeat_time: HashMap::new(),
            failover_history: Vec::new(),
        }
    }

    pub async fn start(&mut self) -> crate::error::Result<()> {
        info!("OROBOROS: SystemMonitor awakening - The autonomic nervous system is online");

        // Subscribe to health topics
        let mut health_subscriber = self.nats_client.subscribe("state.health.*").await
            .map_err(|e| crate::error::BasiliskError::DataIngestion { message: format!("Failed to subscribe to NATS topic: {}", e) })?;

        // Start heartbeat monitoring loop
        let mut monitor_interval = interval(Duration::from_secs(30));

        info!("OROBOROS: Monitoring system health and ready for tactical failover");

        loop {
            tokio::select! {
                // Process incoming heartbeats
                Some(msg) = health_subscriber.next() => {
                    if let Err(e) = self.process_heartbeat(&msg.subject, &msg.payload).await {
                        error!("OROBOROS: Failed to process heartbeat: {}", e);
                    }
                }

                // Periodic health check
                _ = monitor_interval.tick() => {
                    self.check_service_timeouts().await;
                }
            }
        }
    }

    async fn process_heartbeat(
        &mut self,
        subject: &str,
        payload: &[u8],
    ) -> crate::error::Result<()> {
        // Extract service name from subject (e.g., "state.health.gaze" -> "gaze")
        let service_name = subject
            .strip_prefix("state.health.")
            .unwrap_or("unknown")
            .to_string();

        // Parse heartbeat
        let heartbeat: ServiceHeartbeat = serde_json::from_slice(payload)?;

        debug!(
            "OROBOROS: Received heartbeat from {} - Status: {:?}",
            service_name, heartbeat.status
        );

        // Update tracking
        self.service_heartbeats
            .insert(service_name.clone(), heartbeat.clone());
        self.last_heartbeat_time
            .insert(service_name.clone(), Instant::now());

        // Check if this heartbeat indicates an RPC failure
        if let ServiceStatus::Error(error_msg) = &heartbeat.status {
            if error_msg.contains("RPC") || error_msg.contains("Connection") {
                warn!(
                    "OROBOROS: RPC failure detected in {} - {}",
                    service_name, error_msg
                );

                if let Some(current_rpc) = &heartbeat.current_rpc_url {
                    self.trigger_rpc_failover(&service_name, current_rpc, &error_msg)
                        .await?;
                }
            }
        }

        Ok(())
    }

    async fn trigger_rpc_failover(
        &mut self,
        service_name: &str,
        failed_rpc_url: &str,
        error_reason: &str,
    ) -> crate::error::Result<()> {
        info!(
            "OROBOROS: Initiating tactical failover for {} (failed RPC: {})",
            service_name, failed_rpc_url
        );

        // Find next available RPC endpoint
        match self.config.get_next_rpc_url(failed_rpc_url) {
            Some(backup_url) => {
                let config_update = ConfigUpdate {
                    target_service: service_name.to_string(),
                    new_rpc_url: backup_url.clone(),
                    reason: format!("RPC failover: {}", error_reason),
                    timestamp: chrono::Utc::now().timestamp() as u64,
                };

                // Publish targeted config update
                let payload = serde_json::to_vec(&config_update)?;
                let update_topic = format!("control.config.update.{}", service_name);

                self.nats_client
                    .publish(update_topic, payload.into())
                    .await?;

                // Also publish to general config update topic
                self.nats_client
                    .publish(
                        "control.config.update",
                        serde_json::to_vec(&config_update)?.into(),
                    )
                    .await?;

                // Record failover
                self.failover_history.push(config_update);

                info!(
                    "OROBOROS: Tactical failover executed - {} switched to backup RPC: {}",
                    service_name, backup_url
                );

                // Publish system event
                self.publish_system_event(&format!(
                    "OROBOROS TACTICAL FAILOVER: {} switched from {} to {} due to: {}",
                    service_name, failed_rpc_url, backup_url, error_reason
                ))
                .await?;
            }
            None => {
                error!(
                    "OROBOROS: CRITICAL - No backup RPC available for {} after failure of {}",
                    service_name, failed_rpc_url
                );

                self.publish_system_event(&format!(
                    "OROBOROS CRITICAL: {} has no backup RPC after failure of {}",
                    service_name, failed_rpc_url
                ))
                .await?;
            }
        }

        Ok(())
    }

    async fn check_service_timeouts(&mut self) {
        let now = Instant::now();
        let timeout_threshold = Duration::from_secs(120); // 2 minutes

        for (service_name, last_heartbeat) in &self.last_heartbeat_time {
            if now.duration_since(*last_heartbeat) > timeout_threshold {
                warn!(
                    "OROBOROS: Service {} heartbeat timeout - last seen {:.1}s ago",
                    service_name,
                    now.duration_since(*last_heartbeat).as_secs_f64()
                );

                // In a full implementation, this could trigger additional recovery actions
                if let Err(e) = self
                    .publish_system_event(&format!(
                        "OROBOROS WARNING: {} service heartbeat timeout",
                        service_name
                    ))
                    .await
                {
                    error!("Failed to publish timeout warning: {}", e);
                }
            }
        }
    }

    async fn publish_system_event(&self, message: &str) -> crate::error::Result<()> {
        let event = serde_json::json!({
            "source": "OROBOROS",
            "severity": "WARN",
            "message": message,
            "timestamp": chrono::Utc::now().timestamp()
        });

        self.nats_client
            .publish(crate::shared_types::NatsTopics::LOG_EVENTS_GAZE, serde_json::to_vec(&event)?.into())
            .await?;

        Ok(())
    }

    pub fn get_failover_history(&self) -> &Vec<ConfigUpdate> {
        &self.failover_history
    }

    pub fn get_service_status(&self, service_name: &str) -> Option<&ServiceHeartbeat> {
        self.service_heartbeats.get(service_name)
    }
}
