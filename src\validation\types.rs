// src/validation/types.rs

//! Core data types for the validation framework

use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;

/// Status of a validation operation
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ValidationStatus {
    /// Validation passed successfully
    Passed,
    /// Validation failed
    Failed,
    /// Validation completed with warnings
    Warning,
    /// Validation was skipped
    Skipped,
    /// Validation is currently in progress
    InProgress,
}

impl std::fmt::Display for ValidationStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ValidationStatus::Passed => write!(f, "PASSED"),
            ValidationStatus::Failed => write!(f, "FAILED"),
            ValidationStatus::Warning => write!(f, "WARNING"),
            ValidationStatus::Skipped => write!(f, "SKIPPED"),
            ValidationStatus::InProgress => write!(f, "IN_PROGRESS"),
        }
    }
}

/// Error that occurred during validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationError {
    /// Error code for categorization
    pub code: String,
    /// Human-readable error message
    pub message: String,
    /// Component where the error occurred
    pub component: String,
    /// Additional context data
    pub context: HashMap<String, serde_json::Value>,
    /// Timestamp when the error occurred
    pub timestamp: DateTime<Utc>,
}

impl ValidationError {
    /// Create a new validation error
    pub fn new(
        code: impl Into<String>,
        message: impl Into<String>,
        component: impl Into<String>,
    ) -> Self {
        Self {
            code: code.into(),
            message: message.into(),
            component: component.into(),
            context: HashMap::new(),
            timestamp: Utc::now(),
        }
    }

    /// Add context data to the error
    pub fn with_context<T: serde::Serialize>(mut self, key: &str, value: T) -> Self {
        if let Ok(json_value) = serde_json::to_value(value) {
            self.context.insert(key.to_string(), json_value);
        }
        self
    }

    /// Create a mathematical inconsistency error
    pub fn mathematical_inconsistency(
        component: &str,
        expected: Decimal,
        actual: Decimal,
        tolerance: Decimal,
    ) -> Self {
        Self::new(
            "MATH_INCONSISTENCY",
            format!(
                "Mathematical inconsistency: expected {}, got {}, tolerance {}",
                expected, actual, tolerance
            ),
            component,
        )
        .with_context("expected", expected)
        .with_context("actual", actual)
        .with_context("tolerance", tolerance)
    }

    /// Create a performance threshold exceeded error
    pub fn performance_threshold_exceeded(
        metric: &str,
        threshold: f64,
        actual: f64,
    ) -> Self {
        Self::new(
            "PERFORMANCE_THRESHOLD_EXCEEDED",
            format!(
                "Performance threshold exceeded for {}: {} > {}",
                metric, actual, threshold
            ),
            "performance_validator",
        )
        .with_context("metric", metric)
        .with_context("threshold", threshold)
        .with_context("actual", actual)
    }

    /// Create an integration failure error
    pub fn integration_failure(
        component_a: &str,
        component_b: &str,
        error_message: &str,
    ) -> Self {
        Self::new(
            "INTEGRATION_FAILURE",
            format!(
                "Integration failure between {} and {}: {}",
                component_a, component_b, error_message
            ),
            "integration_validator",
        )
        .with_context("component_a", component_a)
        .with_context("component_b", component_b)
        .with_context("error_message", error_message)
    }

    /// Create a data consistency error
    pub fn data_consistency_error(
        data_source: &str,
        inconsistency_type: &str,
        details: &str,
    ) -> Self {
        Self::new(
            "DATA_CONSISTENCY_ERROR",
            format!(
                "Data consistency error in {}: {} - {}",
                data_source, inconsistency_type, details
            ),
            "data_validator",
        )
        .with_context("data_source", data_source)
        .with_context("inconsistency_type", inconsistency_type)
        .with_context("details", details)
    }
}

/// Warning that occurred during validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationWarning {
    /// Warning code for categorization
    pub code: String,
    /// Human-readable warning message
    pub message: String,
    /// Component where the warning occurred
    pub component: String,
    /// Additional context data
    pub context: HashMap<String, serde_json::Value>,
    /// Timestamp when the warning occurred
    pub timestamp: DateTime<Utc>,
}

impl ValidationWarning {
    /// Create a new validation warning
    pub fn new(
        code: impl Into<String>,
        message: impl Into<String>,
        component: impl Into<String>,
    ) -> Self {
        Self {
            code: code.into(),
            message: message.into(),
            component: component.into(),
            context: HashMap::new(),
            timestamp: Utc::now(),
        }
    }

    /// Add context data to the warning
    pub fn with_context<T: serde::Serialize>(mut self, key: &str, value: T) -> Self {
        if let Ok(json_value) = serde_json::to_value(value) {
            self.context.insert(key.to_string(), json_value);
        }
        self
    }
}

/// Configuration for validation framework
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationConfig {
    /// Maximum execution time for a single validation
    pub max_execution_time: Duration,
    /// Whether to continue validation after failures
    pub continue_on_failure: bool,
    /// Tolerance for mathematical comparisons
    pub mathematical_tolerance: Decimal,
    /// Performance thresholds
    pub performance_thresholds: PerformanceThresholds,
    /// Storage configuration
    pub storage_config: StorageConfig,
}

impl Default for ValidationConfig {
    fn default() -> Self {
        Self {
            max_execution_time: Duration::from_secs(300), // 5 minutes
            continue_on_failure: true,
            mathematical_tolerance: Decimal::new(1, 4), // 0.0001
            performance_thresholds: PerformanceThresholds::default(),
            storage_config: StorageConfig::default(),
        }
    }
}

/// Performance thresholds for validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceThresholds {
    /// Maximum latency for opportunity detection (ms)
    pub max_opportunity_detection_latency_ms: u64,
    /// Maximum execution time for trades (ms)
    pub max_trade_execution_time_ms: u64,
    /// Minimum success rate for operations (0.0 to 1.0)
    pub min_success_rate: f64,
    /// Maximum memory usage (MB)
    pub max_memory_usage_mb: f64,
}

impl Default for PerformanceThresholds {
    fn default() -> Self {
        Self {
            max_opportunity_detection_latency_ms: 100,
            max_trade_execution_time_ms: 5000,
            min_success_rate: 0.85,
            max_memory_usage_mb: 1024.0,
        }
    }
}

/// Storage configuration for validation results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    /// Whether to persist results to disk
    pub persist_to_disk: bool,
    /// Directory for storing validation results
    pub storage_directory: String,
    /// Maximum number of results to keep in memory
    pub max_in_memory_results: usize,
    /// Whether to compress stored results
    pub compress_results: bool,
}

impl Default for StorageConfig {
    fn default() -> Self {
        Self {
            persist_to_disk: true,
            storage_directory: "validation_results".to_string(),
            max_in_memory_results: 1000,
            compress_results: true,
        }
    }
}

/// Metrics collected during validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationMetrics {
    /// Total number of validations run
    pub total_validations: u64,
    /// Number of passed validations
    pub passed_validations: u64,
    /// Number of failed validations
    pub failed_validations: u64,
    /// Number of validations with warnings
    pub warning_validations: u64,
    /// Number of skipped validations
    pub skipped_validations: u64,
    /// Average execution time
    pub average_execution_time: Duration,
    /// Success rate (0.0 to 1.0)
    pub success_rate: f64,
    /// Last validation timestamp
    pub last_validation_timestamp: Option<DateTime<Utc>>,
}

impl Default for ValidationMetrics {
    fn default() -> Self {
        Self {
            total_validations: 0,
            passed_validations: 0,
            failed_validations: 0,
            warning_validations: 0,
            skipped_validations: 0,
            average_execution_time: Duration::from_millis(0),
            success_rate: 0.0,
            last_validation_timestamp: None,
        }
    }
}

impl ValidationMetrics {
    /// Update metrics with a new validation result
    pub fn update_with_result<T>(&mut self, result: &crate::validation::ValidationResult<T>) {
        self.total_validations += 1;
        self.last_validation_timestamp = Some(result.timestamp);

        match result.status {
            ValidationStatus::Passed => self.passed_validations += 1,
            ValidationStatus::Failed => self.failed_validations += 1,
            ValidationStatus::Warning => self.warning_validations += 1,
            ValidationStatus::Skipped => self.skipped_validations += 1,
            ValidationStatus::InProgress => {
                // Don't count in-progress validations
                self.total_validations -= 1;
                return;
            }
        }

        // Update success rate
        let successful = self.passed_validations + self.warning_validations;
        self.success_rate = successful as f64 / self.total_validations as f64;

        // Update average execution time (simple moving average)
        let current_avg_ms = self.average_execution_time.as_millis() as f64;
        let new_time_ms = result.execution_time.as_millis() as f64;
        let new_avg_ms = (current_avg_ms * (self.total_validations - 1) as f64 + new_time_ms) 
            / self.total_validations as f64;
        self.average_execution_time = Duration::from_millis(new_avg_ms as u64);
    }
}