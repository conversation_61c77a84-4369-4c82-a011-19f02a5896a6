use futures_util::StreamExt;
// LogIngestor: Specialized service that monitors blockchain events and publishes them to NATS
// This is a refined version of the generic chain_monitor, focused specifically on
// capturing Swap and PoolCreated events across all contracts on the chain.
//
// The service runs two concurrent tasks:
// 1. A task that monitors for Swap events (both V2 and V3 formats) and publishes them to data.chain.logs.raw.swaps.*
// 2. A task that monitors for PoolCreated events and publishes them to data.chain.logs.raw.pools_created
//
// This specialized approach allows strategy modules to subscribe only to the specific event types they need,
// improving performance and reducing noise.

use crate::config::Config;
use crate::shared_types::{DecodedLog, NatsTopics, SwapEvent};
use async_nats::Client as NatsClient;
use ethers::{
    abi::{Abi, Token},
    contract::Contract,
    providers::{Middleware, Provider, ProviderError, Ws},
    types::{Address, Block, BlockNumber, Filter, Log, H256, U256},
};
use serde_json::Value;
use std::collections::HashMap;
use std::error::Error;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::task;
use tokio::time;
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

pub struct LogIngestor {
    chain_id: u64,
    current_ws_url: String,
    config: Arc<Config>,
    nats_client: NatsClient,
}

impl LogIngestor {
    pub fn new(
        chain_id: u64,
        ws_url: String,
        config: Arc<Config>,
        nats_client: NatsClient,
    ) -> Self {
        Self {
            chain_id,
            current_ws_url: ws_url,
            config,
            nats_client,
        }
    }

    pub async fn start(&mut self) -> Result<(), Box<dyn Error>> {
        info!("Starting log ingestor for chain ID {}", self.chain_id);

        // Reconnection loop
        let mut retry_count = 0;
        let max_retries = 10;
        let mut backoff_duration = Duration::from_secs(1);

        loop {
            info!(
                "Connecting to RPC WebSocket for chain ID {} (attempt {})",
                self.chain_id,
                retry_count + 1
            );

            match Provider::<Ws>::connect(&self.current_ws_url).await {
                Ok(ws_provider) => {
                    let provider = Arc::new(ws_provider);
                    info!("Connected to RPC WebSocket for chain ID {}", self.chain_id);

                    // Reset retry count and backoff on successful connection
                    retry_count = 0;
                    backoff_duration = Duration::from_secs(1);

                    // Start both subscriptions concurrently as separate tasks
                    let swaps_provider = provider.clone();
                    let pools_provider = provider.clone();
                    let nats_client_swaps = self.nats_client.clone();
                    let nats_client_pools = self.nats_client.clone();
                    let chain_id = self.chain_id;

                    // Task 1: Subscribe to Swap events
                    let swaps_task = task::spawn(async move {
                        if let Err(e) =
                            subscribe_to_swaps(chain_id, swaps_provider, nats_client_swaps).await
                        {
                            error!("Swap subscription error for chain ID {}: {}", chain_id, e);
                        }
                    });

                    // Task 2: Subscribe to PoolCreated events
                    let pools_task = task::spawn(async move {
                        if let Err(e) =
                            subscribe_to_pool_created(chain_id, pools_provider, nats_client_pools)
                                .await
                        {
                            error!(
                                "PoolCreated subscription error for chain ID {}: {}",
                                chain_id, e
                            );
                        }
                    });

                    // Wait for either task to complete (which would indicate an error)
                    tokio::select! {
                        _ = swaps_task => {
                            warn!("Swap subscription task ended for chain ID {}", self.chain_id);
                        }
                        _ = pools_task => {
                            warn!("PoolCreated subscription task ended for chain ID {}", self.chain_id);
                        }
                    }

                    // If we reach here, one of the subscriptions has ended
                    warn!(
                        "Reconnecting to RPC WebSocket for chain ID {}...",
                        self.chain_id
                    );
                }
                Err(e) => {
                    error!(
                        "Failed to connect to RPC WebSocket for chain ID {}: {}",
                        self.chain_id, e
                    );

                    // TODO: Implement RPC failover logic with the new Config struct
                    warn!("OROBOROS: RPC failover not implemented yet, using exponential backoff");
                    
                    // Increment retry count
                    retry_count += 1;

                    // Check if we've exceeded max retries
                    if retry_count >= max_retries {
                        error!(
                            "Max retries ({}) exceeded for chain ID {}",
                            max_retries, self.chain_id
                        );
                        return Err(format!(
                            "Failed to connect to RPC after {} attempts",
                            max_retries
                        )
                        .into());
                    }
                }
            }

            // Exponential backoff with jitter
            let jitter = rand::random::<u64>() % 1000;
            let backoff_ms = backoff_duration.as_millis() as u64 + jitter;
            info!(
                "Waiting {}ms before reconnecting to RPC for chain ID {}",
                backoff_ms, self.chain_id
            );
            time::sleep(Duration::from_millis(backoff_ms)).await;

            // Increase backoff for next attempt (capped at 30 seconds)
            backoff_duration = std::cmp::min(backoff_duration * 2, Duration::from_secs(30));
        }
    }
}

// Task 1: Subscribe to Swap events across all contracts
async fn subscribe_to_swaps(
    chain_id: u64,
    provider: Arc<Provider<Ws>>,
    nats_client: NatsClient,
) -> Result<(), Box<dyn Error>> {
    // Define generic Swap event signature that matches both V2 and V3 formats
    let swap_event_signature = "Swap(address,address,int256,int256,uint160,uint128,int24)";
    let swap_v2_event_signature = "Swap(address,uint256,uint256,uint256,uint256,address)";

    // Create topics from event signatures
    let swap_topic = ethers::utils::keccak256(swap_event_signature.as_bytes());
    let swap_v2_topic = ethers::utils::keccak256(swap_v2_event_signature.as_bytes());

    // Create a filter for both Uniswap V2 and V3 Swap events
    // Note: We're not setting .address() to listen to all contracts
    let filter = Filter::new().topic0(vec![H256::from(swap_topic), H256::from(swap_v2_topic)]);

    // Subscribe to the filter
    let mut stream = provider.subscribe_logs(&filter).await?;
    info!("Subscribed to Swap events for chain ID {}", chain_id);

    // Process incoming logs
    while let Some(log) = stream.next().await {
        debug!("Received swap log event on chain ID {}", chain_id);

        // Determine which DEX protocol this is from
        if log.topics.len() > 0 {
            let event_signature = log.topics[0];

            // Create a timestamp for the event
            let timestamp = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            // Publish raw swap log to NATS
            let topic = format!("data.chain.logs.raw.swaps.{}", chain_id);
            if let Err(e) = nats_client
                .publish(topic, serde_json::to_string(&log)?.into())
                .await
            {
                error!("Failed to publish raw swap log: {}", e);
            }

            // Process based on event signature
            if event_signature == H256::from(swap_topic) {
                // This is a Uniswap V3 Swap event
                debug!("Processing Uniswap V3 Swap event");
                // Further processing would be implemented in a real system
            } else if event_signature == H256::from(swap_v2_topic) {
                // This is a Uniswap V2 Swap event
                debug!("Processing Uniswap V2 Swap event");
                // Further processing would be implemented in a real system
            }
        }
    }

    warn!("Swap event subscription ended for chain ID {}", chain_id);
    Ok(())
}

// Task 2: Subscribe to PoolCreated events
async fn subscribe_to_pool_created(
    chain_id: u64,
    provider: Arc<Provider<Ws>>,
    nats_client: NatsClient,
) -> Result<(), Box<dyn Error>> {
    // Define Uniswap V3 Factory PoolCreated event signature
    // event PoolCreated(address token0, address token1, uint24 fee, int24 tickSpacing, address pool)
    let pool_created_event_signature = "PoolCreated(address,address,uint24,int24,address)";

    // Create topic from event signature
    let pool_created_topic = ethers::utils::keccak256(pool_created_event_signature.as_bytes());

    // Create a filter for PoolCreated events
    // Note: We're not setting .address() to listen to all factory contracts
    let filter = Filter::new().topic0(H256::from(pool_created_topic));

    // Subscribe to the filter
    let mut stream = provider.subscribe_logs(&filter).await?;
    info!("Subscribed to PoolCreated events for chain ID {}", chain_id);

    // Process incoming logs
    while let Some(log) = stream.next().await {
        debug!("Received PoolCreated log event on chain ID {}", chain_id);

        // Create a timestamp for the event
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        // Publish raw pool created log to NATS
        let topic = "data.chain.logs.raw.pools_created";
        if let Err(e) = nats_client
            .publish(topic, serde_json::to_string(&log)?.into())
            .await
        {
            error!("Failed to publish raw pool created log: {}", e);
        }

        // Further processing would be implemented in a real system
        debug!("New pool created at address: {}", log.address);
    }

    warn!(
        "PoolCreated event subscription ended for chain ID {}",
        chain_id
    );
    Ok(())
}
