// src/validation/framework.rs

//! Main validation framework controller with orchestration capabilities

use crate::validation::{
    ValidationFrameworkResult, ResultsStore
};
use crate::validation::results::{ValidationResult, ValidationResultSet};
use crate::validation::types::{ValidationStatus, ValidationError, ValidationWarning};
use crate::validation::types::{ValidationConfig, ValidationMetrics};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Main validation framework controller
/// 
/// This is the central orchestrator for all validation activities in the system.
/// It coordinates validation phases, manages test execution, interfaces with
/// production system components, and generates comprehensive validation reports.
#[derive(Debug)]
pub struct ValidationFramework {
    /// Configuration for the validation framework
    config: ValidationConfig,
    /// Storage for validation results
    results_store: Arc<ResultsStore>,
    /// Currently running validations
    active_validations: Arc<RwLock<HashMap<String, ActiveValidation>>>,
    /// Semaphore to limit concurrent validations
    concurrency_limiter: Arc<Semaphore>,
    /// Framework metrics
    metrics: Arc<RwLock<FrameworkMetrics>>,
}

/// Information about an active validation
#[derive(Debug, Clone)]
struct ActiveValidation {
    test_id: String,
    test_name: String,
    started_at: Instant,
    started_timestamp: DateTime<Utc>,
}

/// Metrics specific to the validation framework
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FrameworkMetrics {
    /// Total number of validation suites executed
    pub total_suites_executed: u64,
    /// Total number of individual validations executed
    pub total_validations_executed: u64,
    /// Number of currently active validations
    pub active_validations_count: u64,
    /// Average time per validation suite
    pub average_suite_execution_time: Duration,
    /// Framework uptime
    pub framework_uptime: Duration,
    /// Framework start time
    pub framework_start_time: DateTime<Utc>,
    /// Last validation execution time
    pub last_validation_time: Option<DateTime<Utc>>,
}

impl Default for FrameworkMetrics {
    fn default() -> Self {
        Self {
            total_suites_executed: 0,
            total_validations_executed: 0,
            active_validations_count: 0,
            average_suite_execution_time: Duration::from_millis(0),
            framework_uptime: Duration::from_millis(0),
            framework_start_time: Utc::now(),
            last_validation_time: None,
        }
    }
}

impl ValidationFramework {
    /// Create a new validation framework with the given configuration
    pub fn new(config: ValidationConfig) -> ValidationFrameworkResult<Self> {
        let results_store = Arc::new(ResultsStore::new(config.storage_config.clone())?);
        
        // Limit concurrent validations to prevent resource exhaustion
        let max_concurrent = 10; // Could be configurable
        let concurrency_limiter = Arc::new(Semaphore::new(max_concurrent));

        let framework = Self {
            config,
            results_store,
            active_validations: Arc::new(RwLock::new(HashMap::new())),
            concurrency_limiter,
            metrics: Arc::new(RwLock::new(FrameworkMetrics::default())),
        };

        info!("ValidationFramework initialized with max concurrent validations: {}", max_concurrent);
        Ok(framework)
    }

    /// Execute a single validation test
    pub async fn execute_validation<T, F, Fut>(
        &self,
        test_name: impl Into<String>,
        validation_fn: F,
    ) -> ValidationFrameworkResult<ValidationResult<Option<T>>>
    where
        T: Serialize + for<'de> Deserialize<'de> + Send + 'static,
        F: FnOnce() -> Fut + Send,
        Fut: std::future::Future<Output = ValidationFrameworkResult<T>> + Send,
    {
        let test_name = test_name.into();
        let test_id = self.generate_test_id(&test_name);
        
        // Acquire concurrency permit
        let _permit = self.concurrency_limiter.acquire().await
            .map_err(|e| crate::error::BasiliskError::execution_error(format!("Failed to acquire concurrency permit: {}", e)))?;

        // Register active validation
        let active_validation = ActiveValidation {
            test_id: test_id.clone(),
            test_name: test_name.clone(),
            started_at: Instant::now(),
            started_timestamp: Utc::now(),
        };

        {
            let mut active = self.active_validations.write().unwrap();
            active.insert(test_id.clone(), active_validation);
            
            let mut metrics = self.metrics.write().unwrap();
            metrics.active_validations_count = active.len() as u64;
        }

        info!("Starting validation: {} ({})", test_name, test_id);

        // Execute the validation with timeout
        let start_time = Instant::now();
        let result = match tokio::time::timeout(
            self.config.max_execution_time,
            validation_fn()
        ).await {
            Ok(Ok(metrics)) => {
                let execution_time = start_time.elapsed();
                ValidationResult::success(test_id.clone(), test_name.clone(), execution_time, Some(metrics))
            }
            Ok(Err(e)) => {
                let execution_time = start_time.elapsed();
                let error = ValidationError::new(
                    "VALIDATION_EXECUTION_ERROR",
                    format!("Validation execution failed: {}", e),
                    "validation_framework",
                );
                ValidationResult::failure(test_id.clone(), test_name.clone(), execution_time, None, error)
            }
            Err(_) => {
                let execution_time = self.config.max_execution_time;
                let error = ValidationError::new(
                    "VALIDATION_TIMEOUT",
                    format!("Validation timed out after {}ms", execution_time.as_millis()),
                    "validation_framework",
                );
                ValidationResult::failure(test_id.clone(), test_name.clone(), execution_time, None, error)
            }
        };

        // Remove from active validations
        {
            let mut active = self.active_validations.write().unwrap();
            active.remove(&test_id);
            
            let mut metrics = self.metrics.write().unwrap();
            metrics.active_validations_count = active.len() as u64;
            metrics.total_validations_executed += 1;
            metrics.last_validation_time = Some(Utc::now());
        }

        // Store the result
        self.results_store.store_result(&result).await?;

        info!("Completed validation: {} - {} ({}ms)", 
              test_name, result.status, result.execution_time.as_millis());

        Ok(result)
    }

    /// Execute a suite of validation tests
    pub async fn execute_validation_suite<T, F, Fut>(
        &self,
        suite_name: impl Into<String>,
        validations: Vec<(String, F)>,
    ) -> ValidationFrameworkResult<ValidationResultSet<Option<T>>>
    where
        T: Serialize + for<'de> Deserialize<'de> + Send + 'static,
        F: FnOnce() -> Fut + Send,
        Fut: std::future::Future<Output = ValidationFrameworkResult<T>> + Send,
    {
        let suite_name = suite_name.into();
        let suite_start_time = Instant::now();
        
        info!("Starting validation suite: {} ({} tests)", suite_name, validations.len());

        let mut result_set = ValidationResultSet::new(suite_name.clone());

        // Execute validations sequentially or in parallel based on configuration
        for (test_name, validation_fn) in validations {
            let result = self.execute_validation(test_name, validation_fn).await?;
            result_set.add_result(result);

            // Stop on failure if configured to do so
            if !self.config.continue_on_failure && result_set.overall_status() == ValidationStatus::Failed {
                warn!("Stopping validation suite {} due to failure", suite_name);
                break;
            }
        }

        result_set.total_execution_time = suite_start_time.elapsed();

        // Update framework metrics
        {
            let mut metrics = self.metrics.write().unwrap();
            metrics.total_suites_executed += 1;
            
            // Update average suite execution time
            let current_avg = metrics.average_suite_execution_time.as_millis() as f64;
            let new_time = result_set.total_execution_time.as_millis() as f64;
            let new_avg = (current_avg * (metrics.total_suites_executed - 1) as f64 + new_time) 
                / metrics.total_suites_executed as f64;
            metrics.average_suite_execution_time = Duration::from_millis(new_avg as u64);
        }

        // Store the result set
        self.results_store.store_result_set(&result_set).await?;

        info!("Completed validation suite: {} - {} ({}ms)", 
              suite_name, result_set.overall_status(), result_set.total_execution_time.as_millis());

        Ok(result_set)
    }

    /// Get a validation result by test ID
    pub async fn get_validation_result<T: for<'de> Deserialize<'de>>(
        &self,
        test_id: &str,
    ) -> ValidationFrameworkResult<Option<ValidationResult<T>>> {
        self.results_store.get_result(test_id).await
    }

    /// Get a validation result set by suite name
    pub async fn get_validation_result_set<T: for<'de> Deserialize<'de>>(
        &self,
        suite_name: &str,
    ) -> ValidationFrameworkResult<Option<ValidationResultSet<T>>> {
        self.results_store.get_result_set(suite_name).await
    }

    /// Get all stored test IDs
    pub fn get_all_test_ids(&self) -> Vec<String> {
        self.results_store.get_all_test_ids()
    }

    /// Get all stored suite names
    pub fn get_all_suite_names(&self) -> Vec<String> {
        self.results_store.get_all_suite_names()
    }

    /// Get current validation metrics from the results store
    pub fn get_validation_metrics(&self) -> ValidationMetrics {
        self.results_store.get_metrics()
    }

    /// Get framework-specific metrics
    pub fn get_framework_metrics(&self) -> FrameworkMetrics {
        let mut metrics = self.metrics.read().unwrap().clone();
        metrics.framework_uptime = Utc::now()
            .signed_duration_since(metrics.framework_start_time)
            .to_std()
            .unwrap_or(Duration::from_secs(0));
        metrics
    }

    /// Get currently active validations
    pub fn get_active_validations(&self) -> Vec<ActiveValidationInfo> {
        let active = self.active_validations.read().unwrap();
        active.values().map(|av| ActiveValidationInfo {
            test_id: av.test_id.clone(),
            test_name: av.test_name.clone(),
            started_at: av.started_timestamp,
            duration: av.started_at.elapsed(),
        }).collect()
    }

    /// Clear all stored validation results
    pub async fn clear_all_results(&self) -> ValidationFrameworkResult<()> {
        self.results_store.clear_all().await
    }

    /// Get comprehensive framework status
    pub fn get_framework_status(&self) -> FrameworkStatus {
        let validation_metrics = self.get_validation_metrics();
        let framework_metrics = self.get_framework_metrics();
        let active_validations = self.get_active_validations();
        let storage_stats = self.results_store.get_storage_stats();

        FrameworkStatus {
            validation_metrics,
            framework_metrics,
            active_validations,
            storage_stats,
            config: self.config.clone(),
        }
    }

    /// Generate a unique test ID
    fn generate_test_id(&self, test_name: &str) -> String {
        let uuid = Uuid::new_v4();
        let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
        format!("{}_{}_{}",
                test_name.replace(' ', "_").to_lowercase(),
                timestamp,
                &uuid.to_string()[..8])
    }
}

/// Information about an active validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActiveValidationInfo {
    pub test_id: String,
    pub test_name: String,
    pub started_at: DateTime<Utc>,
    pub duration: Duration,
}

/// Comprehensive status of the validation framework
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FrameworkStatus {
    pub validation_metrics: ValidationMetrics,
    pub framework_metrics: FrameworkMetrics,
    pub active_validations: Vec<ActiveValidationInfo>,
    pub storage_stats: crate::validation::store::StorageStats,
    pub config: ValidationConfig,
}

impl std::fmt::Display for FrameworkStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "Validation Framework Status:\n\
             \n\
             Framework Metrics:\n\
             - Uptime: {}s | Suites Executed: {} | Total Validations: {}\n\
             - Active Validations: {} | Avg Suite Time: {}ms\n\
             \n\
             Validation Metrics:\n\
             - Success Rate: {:.1}% | Total: {} | Passed: {} | Failed: {}\n\
             - Avg Execution Time: {}ms\n\
             \n\
             Storage:\n\
             {}",
            self.framework_metrics.framework_uptime.as_secs(),
            self.framework_metrics.total_suites_executed,
            self.framework_metrics.total_validations_executed,
            self.framework_metrics.active_validations_count,
            self.framework_metrics.average_suite_execution_time.as_millis(),
            self.validation_metrics.success_rate * 100.0,
            self.validation_metrics.total_validations,
            self.validation_metrics.passed_validations,
            self.validation_metrics.failed_validations,
            self.validation_metrics.average_execution_time.as_millis(),
            self.storage_stats
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::validation::types::StorageConfig;

    #[tokio::test]
    async fn test_validation_framework_creation() {
        let mut config = ValidationConfig::default();
        config.storage_config.persist_to_disk = false; // Disable disk persistence for tests
        let framework = ValidationFramework::new(config);
        assert!(framework.is_ok());
    }

    #[tokio::test]
    async fn test_single_validation_execution() {
        let mut config = ValidationConfig::default();
        config.storage_config.persist_to_disk = false; // Disable disk persistence for tests
        let framework = ValidationFramework::new(config).unwrap();

        let result = framework.execute_validation("test_validation", || async {
            Ok("test_metrics".to_string())
        }).await;

        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.status, ValidationStatus::Passed);
        assert_eq!(result.metrics, Some("test_metrics".to_string()));
    }

    #[tokio::test]
    async fn test_validation_suite_execution() {
        let mut config = ValidationConfig::default();
        config.storage_config.persist_to_disk = false; // Disable disk persistence for tests
        let framework = ValidationFramework::new(config).unwrap();

        // Test individual validations since we can't easily create a Vec of different closure types
        let result1 = framework.execute_validation("test1", || async { Ok("metrics1".to_string()) }).await;
        let result2 = framework.execute_validation("test2", || async { Ok("metrics2".to_string()) }).await;

        assert!(result1.is_ok());
        assert!(result2.is_ok());
        
        let result1 = result1.unwrap();
        let result2 = result2.unwrap();
        
        assert_eq!(result1.status, ValidationStatus::Passed);
        assert_eq!(result2.status, ValidationStatus::Passed);
        assert_eq!(result1.metrics, Some("metrics1".to_string()));
        assert_eq!(result2.metrics, Some("metrics2".to_string()));
    }

    #[tokio::test]
    async fn test_validation_failure_handling() {
        let mut config = ValidationConfig::default();
        config.storage_config.persist_to_disk = false; // Disable disk persistence for tests
        let framework = ValidationFramework::new(config).unwrap();

        let result: crate::validation::ValidationFrameworkResult<crate::validation::ValidationResult<Option<String>>> = framework.execute_validation("failing_test", || async {
            Err(crate::error::BasiliskError::execution_error("Test failure"))
        }).await;

        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.status, ValidationStatus::Failed);
        assert!(!result.errors.is_empty());
        assert_eq!(result.metrics, None);
    }
}