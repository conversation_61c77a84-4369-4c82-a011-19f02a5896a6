// MISSION: Fractal Analyzer - The Chronos Sieve
// WHY: Multi-dimensional volatility analysis and market character classification
// HOW: Real-time fractal analysis across multiple timeframes with Hurst Exponent

use chrono::{DateTime, Timelike, Utc};
use rust_decimal::prelude::*;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use num_traits::FromPrimitive;
use rustfft::{num_complex::Complex, FftPlanner};
use std::collections::VecDeque;
use tokio_stream::StreamExt;
use tracing::{debug, info, warn};
use crate::error::BasiliskError;
// Wavelets implementation would go here (simplified for now)

// Bayesian Regime Detection and Kalman Filtering
use crate::shared_types::{MarketRegime, MarketCharacter, MarketRegimeState, MarketPhase, TokenPair};
use crate::data::market_hmm::MarketHMM;



/// Price data point for analysis
#[derive(Debug, Clone)]
pub struct PricePoint {
    pub price: Decimal,
    pub timestamp: DateTime<Utc>,
}

// Using MarketRegimeState from shared_types

/// Kalman filter state for price smoothing
#[derive(Debug, Clone)]
pub struct KalmanState {
    pub state_estimate: Decimal,      // Current price estimate
    pub error_covariance: Decimal,    // Estimation uncertainty
    pub process_noise: Decimal,       // Process noise covariance
    pub measurement_noise: Decimal,   // Measurement noise covariance
}

/// The Fractal Analyzer - implements the Chronos Sieve
pub struct FractalAnalyzer {
    price_history_1m: VecDeque<PricePoint>,
    price_history_15m: VecDeque<PricePoint>,
    price_history_1h: VecDeque<PricePoint>,
    max_history_1m: usize,
    max_history_15m: usize,
    max_history_1h: usize,
    // Bayesian regime detection state
    regime_state: Option<MarketRegimeState>,
    // Kalman filter state for price smoothing
    kalman_state: Option<KalmanState>,
    market_hmm: MarketHMM,
}

impl FractalAnalyzer {
    /// Get the 1-hour volatility metric
    pub async fn get_volatility_1h(&self) -> crate::error::Result<Decimal> {
        // If we have enough data points, calculate volatility
        if self.price_history_1h.len() >= 2 {
            let mut returns = Vec::with_capacity(self.price_history_1h.len() - 1);
            let mut prev_price = self.price_history_1h[0].price;
            
            // Calculate log returns
            for i in 1..self.price_history_1h.len() {
                let current_price = self.price_history_1h[i].price;
                if current_price > Decimal::ZERO && prev_price > Decimal::ZERO {
                    // Convert to f64 for ln operation since Decimal doesn't have ln
                    let return_val = if let Some(ratio_f64) = (current_price / prev_price).to_f64() {
                        if let Some(ln_val) = ratio_f64.ln().try_into().ok() {
                            Decimal::from_f64(ln_val).unwrap_or(Decimal::ZERO)
                        } else {
                            Decimal::ZERO
                        }
                    } else {
                        Decimal::ZERO
                    };
                    returns.push(return_val);
                }
                prev_price = current_price;
            }
            
            // Calculate standard deviation of returns
            if !returns.is_empty() {
                let mean = returns.iter().sum::<Decimal>() / Decimal::from(returns.len());
                let variance = returns.iter()
                    .map(|r| (*r - mean) * (*r - mean))
                    .sum::<Decimal>() / Decimal::from(returns.len());
                
                // Return annualized volatility (multiply by sqrt(24*365) for hourly data)
                // Calculate square root using f64 conversion
                let variance_f64 = variance.to_f64().unwrap_or(0.0);
                let annualization_factor = f64::sqrt(24.0 * 365.0);
                return Ok(Decimal::from_f64(variance_f64.sqrt() * annualization_factor).unwrap_or(Decimal::ZERO));
            }
        }
        
        // Default volatility if we don't have enough data
        Ok(dec!(0.5)) // 50% annualized volatility as a default
    }

    pub fn new() -> Self {
        Self {
            price_history_1m: VecDeque::new(),
            price_history_15m: VecDeque::new(),
            price_history_1h: VecDeque::new(),
            max_history_1m: 60,  // 1 hour of 1-minute data
            max_history_15m: 96, // 24 hours of 15-minute data
            max_history_1h: 168, // 1 week of hourly data
            regime_state: None,
            kalman_state: Some(KalmanState {
                state_estimate: Decimal::ZERO,
                error_covariance: Decimal::ONE,
                process_noise: dec!(0.01),      // 1% process noise
                measurement_noise: dec!(0.05),  // 5% measurement noise
            }),
            market_hmm: MarketHMM::new(),
        }
    }

    /// Apply Kalman filtering to smooth price data
    pub fn apply_kalman_filter(&mut self, raw_price: Decimal) -> Result<Decimal, anyhow::Error> {
        // Initialize state if needed
        if self.kalman_state.is_none() {
            self.kalman_state = Some(KalmanState {
                state_estimate: raw_price,
                error_covariance: Decimal::ONE,
                process_noise: dec!(0.01),
                measurement_noise: dec!(0.05),
            });
            return Ok(raw_price);
        }
        
        let state = self.kalman_state.as_mut()
            .ok_or_else(|| anyhow::anyhow!("Kalman filter not initialized - call initialize() first"))?;
        
        // Prediction step
        let predicted_state = state.state_estimate;
        let predicted_error_covariance = state.error_covariance + state.process_noise;
        
        // Update step
        let kalman_gain = predicted_error_covariance / 
                         (predicted_error_covariance + state.measurement_noise);
        
        let updated_state = predicted_state + kalman_gain * (raw_price - predicted_state);
        let updated_error_covariance = (Decimal::ONE - kalman_gain) * predicted_error_covariance;
        
        // Update state
        state.state_estimate = updated_state;
        state.error_covariance = updated_error_covariance;
        
        debug!(
            "KALMAN FILTER: Raw price: {:.4}, Filtered price: {:.4}, Gain: {:.4}",
            raw_price, updated_state, kalman_gain
        );
        
        Ok(updated_state)
    }

    /// Calculate market regime using Bayesian classification
    pub fn calculate_market_regime(&self) -> Result<MarketRegimeState, anyhow::Error> {
        if self.price_history_1m.len() < 30 {
            return Ok(MarketRegimeState {
                regime: MarketRegime::Unknown,
                confidence: Decimal::ZERO,
                state_probabilities: vec![Decimal::ZERO; 5],
            });
        }

        // Calculate features for regime classification
        let volatility = self.calculate_volatility(&self.price_history_1m);
        let returns = self.calculate_returns(&self.price_history_1m)?;
        let volume_trend = self.estimate_volume_trend();
        
        // Simple Bayesian classification based on volatility and returns
        let mut probabilities = vec![Decimal::ZERO; 5];
        
        // CalmOrderly: Low volatility, stable returns
        if volatility < dec!(0.02) && returns.abs() < dec!(0.01) {
            probabilities[0] = dec!(0.7);
        }
        
        // RetailFomoSpike: High positive returns, increasing volume
        if returns > dec!(0.05) && volume_trend > dec!(0.1) {
            probabilities[1] = dec!(0.8);
        }
        
        // BotGasWar: High volatility, erratic returns
        if volatility > dec!(0.05) {
            probabilities[2] = dec!(0.6);
        }
        
        // HighVolatilityCorrection: High negative returns, high volatility
        if returns < dec!(-0.03) && volatility > dec!(0.04) {
            probabilities[3] = dec!(0.9);
        }
        
        // Find the regime with highest probability
        let (regime_index, max_prob) = probabilities
            .iter()
            .enumerate()
            .max_by(|a, b| a.1.cmp(b.1))
            .unwrap_or((4, &Decimal::ZERO));
        
        let regime = match regime_index {
            0 => MarketRegime::CalmOrderly,
            1 => MarketRegime::RetailFomoSpike,
            2 => MarketRegime::BotGasWar,
            3 => MarketRegime::HighVolatilityCorrection,
            _ => MarketRegime::Unknown,
        };
        
        Ok(MarketRegimeState {
            regime,
            confidence: *max_prob,
            state_probabilities: probabilities,
        })
    }

    /// Calculate simple returns for the price history
    fn calculate_returns(&self, history: &VecDeque<PricePoint>) -> Result<Decimal, anyhow::Error> {
        if history.len() < 2 {
            return Ok(Decimal::ZERO);
        }
        
        let first_price = history.front()
            .ok_or_else(|| anyhow::anyhow!("Price history is empty - cannot calculate trend"))?
            .price;
        let last_price = history.back()
            .ok_or_else(|| anyhow::anyhow!("Price history is empty - cannot calculate trend"))?
            .price;
        
        if first_price > Decimal::ZERO {
            Ok((last_price - first_price) / first_price)
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// Estimate volume trend (simplified - would need actual volume data)
    fn estimate_volume_trend(&self) -> Decimal {
        // Simplified volume trend estimation based on price movement frequency
        // In a real implementation, this would use actual volume data
        let price_changes = self.price_history_1m
            .iter()
            .collect::<Vec<_>>()
            .windows(2)
            .map(|window| (window[1].price - window[0].price).abs())
            .sum::<Decimal>();
        
        price_changes / Decimal::new(self.price_history_1m.len() as i64, 0)
    }

    /// Add a new price point and update all timeframes
    pub fn add_price_point(&mut self, price: Decimal, timestamp: DateTime<Utc>) -> Result<(), anyhow::Error> {
        // Apply Kalman filtering to smooth the price
        let filtered_price = self.apply_kalman_filter(price);
        
        let price_point = PricePoint { price: filtered_price?, timestamp };

        // Add to 1-minute history
        self.price_history_1m.push_back(price_point.clone());
        if self.price_history_1m.len() > self.max_history_1m {
            self.price_history_1m.pop_front();
        }

        // Add to 15-minute history (every 15th minute)
        if timestamp.minute() % 15 == 0 {
            self.price_history_15m.push_back(price_point.clone());
            if self.price_history_15m.len() > self.max_history_15m {
                self.price_history_15m.pop_front();
            }
        }

        // Add to 1-hour history (every hour)
        if timestamp.minute() == 0 {
            self.price_history_1h.push_back(price_point);
            if self.price_history_1h.len() > self.max_history_1h {
                self.price_history_1h.pop_front();
            }
        }
        
        Ok(())
    }

    /// Calculate market regime using Hidden Markov Model (updated version)
    pub fn calculate_market_regime_hmm(&mut self) -> MarketRegimeState {
        if self.price_history_1m.len() < 30 {
            // Default to Unknown regime with low confidence if insufficient data
            return MarketRegimeState {
                regime: MarketRegime::Unknown,
                confidence: dec!(0.5),
                state_probabilities: vec![dec!(0.2); 5],
            };
        }

        // Extract features for HMM
        let volatility_1m = self.calculate_volatility(&self.price_history_1m);
        let hurst_exponent = self.calculate_hurst_exponent(&self.price_history_1m);

        // Convert features into simplified observations for the HMM
        // 0=low_vol, 1=high_vol, 2=trending, 3=mean_reverting
        let mut observations = Vec::new();
        if volatility_1m < dec!(0.01) {
            observations.push(0); // Low volatility
        } else if volatility_1m > dec!(0.03) {
            observations.push(1); // High volatility
        } else {
            observations.push(0); // Default to low vol if in between
        }

        if hurst_exponent > dec!(0.55) {
            observations.push(2); // Trending
        } else if hurst_exponent < dec!(0.45) {
            observations.push(3); // Mean-reverting
        } else {
            observations.push(0); // Default to low vol if random walk
        }

        let predicted_regime = self.market_hmm.predict_regime(&observations);

        // For now, confidence and state_probabilities are placeholders
        // In a full HMM implementation, these would come from the Viterbi algorithm's output
        MarketRegimeState {
            regime: predicted_regime,
            confidence: dec!(0.7), // Placeholder
            state_probabilities: vec![dec!(0.2); 5], // Placeholder
        }
    }

    /// Perform complete fractal analysis
    pub async fn analyze(&mut self, nats_client: async_nats::Client) -> Result<Option<crate::shared_types::FractalAnalysisReport>, crate::error::BasiliskError> {
        if self.price_history_1m.len() < 10 {
            debug!("CHRONOS SIEVE: Insufficient data for analysis (need at least 10 points)");
            return Ok(None);
        }

        let volatility_1m = self.calculate_volatility(&self.price_history_1m);
        let volatility_15m = self.calculate_volatility(&self.price_history_15m);
        let volatility_1h = self.calculate_volatility(&self.price_history_1h);

        let hurst_exponent = self.calculate_hurst_exponent(&self.price_history_1m);
        let market_character = self.classify_market_character(hurst_exponent);
        let _market_regime = self.calculate_market_regime();
        let (dominant_cycles_minutes, market_rhythm_stability) = self.calculate_temporal_harmonics();
        let wavelet_features = self.calculate_wavelet_features(&self.price_history_1m);
        
        // Update market regime state
        let regime_state = self.calculate_market_regime_hmm();
        self.regime_state = Some(regime_state.clone());

        // Publish MarketRegimeState to NATS
        let regime_payload = serde_json::to_vec(&regime_state)?;
        nats_client.publish(crate::shared_types::NatsTopics::STATE_MARKET_REGIME_HMM, regime_payload.into()).await?;

        debug!(
            "CHRONOS SIEVE: Analysis complete | Vol 1m: {:.4} | Vol 15m: {:.4} | Vol 1h: {:.4} | Hurst: {:.3} | Character: {:?} | Dominant Cycles: {:?}",
            volatility_1m, volatility_15m, volatility_1h, hurst_exponent, market_character, dominant_cycles_minutes
        );

        let report = crate::shared_types::FractalAnalysisReport {
            timestamp: Utc::now().timestamp() as u64,
            market_regime: MarketRegime::Trending, // Default value, should be determined by analysis
            market_character,
            hurst_exponent,
            volatility_metrics: {
                let mut metrics = std::collections::HashMap::new();
                metrics.insert("1m".to_string(), volatility_1m);
                metrics.insert("15m".to_string(), volatility_15m);
                metrics.insert("1h".to_string(), volatility_1h);
                metrics
            },
            temporal_harmonics: Some(crate::shared_types::TemporalHarmonics {
                dominant_cycles_minutes,
                market_rhythm_stability,
            }),
            market_phase: Some(MarketPhase::Accumulation), // Default value
            volatility_1m,
            volatility_15m,
            volatility_1h,
            daily_cycle_strength: Decimal::from_f64(market_rhythm_stability).unwrap_or_default(),
        };

        // Publish FractalAnalysisReport to NATS
        let payload = serde_json::to_vec(&report)?;
        nats_client.publish(crate::shared_types::NatsTopics::TOPIC_FRACTAL_ANALYSIS_OUTPUT, payload.into()).await?;

        Ok(Some(report))
    }

    /// Calculate dominant market cycles and rhythm stability using FFT
    fn calculate_temporal_harmonics(&self) -> (Vec<(f64, f64)>, f64) {
        if self.price_history_1m.len() < 60 { // Need at least 1 hour of 1-minute data
            return (Vec::new(), 0.0);
        }

        let input_data: Vec<f64> = self
            .price_history_1m
            .iter()
            .map(|p| p.price.to_f64().unwrap_or(0.0))
            .collect();
        
        let n = input_data.len();

        // Apply a Hann window to reduce spectral leakage (anti-aliasing)
        let windowed_data: Vec<f64> = input_data
            .into_iter()
            .enumerate()
            .map(|(i, val)| {
                let hann = 0.5 * (1.0 - (2.0 * std::f64::consts::PI * i as f64 / (n - 1) as f64).cos());
                val * hann
            })
            .collect();

        // AUDIT-FIX: Calculate target length first, then plan FFT for correct size
        // This fixes the critical buffer size mismatch that was causing runtime panics
        let target_len = n.next_power_of_two();
        let mut planner = FftPlanner::new();
        let fft = planner.plan_fft_forward(target_len); // FIXED: Plan for padded size, not original n

        let mut buffer: Vec<Complex<f64>> = windowed_data
            .into_iter()
            .map(|val| Complex::new(val, 0.0))
            .collect();

        // Pad with zeros to target length for FFT efficiency
        buffer.resize(target_len, Complex::new(0.0, 0.0));

        fft.process(&mut buffer);

        let n_fft = buffer.len() as f64;
        let sampling_rate_minutes = 1.0; // 1 sample per minute

        // Calculate power spectral density (PSD)
        let psd: Vec<f64> = buffer.iter().map(|c| c.norm_sqr()).collect();

        let mut frequencies: Vec<(f64, f64)> = Vec::new(); // (period_minutes, power)
        for i in 1..buffer.len() / 2 { // Only consider positive frequencies
            // AUDIT-FIX: Use original data length n for frequency calculation, not n_fft
            let freq = i as f64 * sampling_rate_minutes / n_fft as f64;
            if freq > 0.0 { // Avoid division by zero
                let period_minutes = 1.0 / freq;
                // AUDIT-FIX: Correct PSD normalization - use n_fft, not n_fft * n_fft
                let power = 2.0 * psd[i] / n_fft;
                frequencies.push((period_minutes, power));
            }
        }

        // Sort by power and take top N dominant cycles
        frequencies.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        let dominant_cycles_with_power: Vec<(f64, f64)> = frequencies.iter().take(5).cloned().collect();

        // AUDIT-FIX: Calculate proper market rhythm stability using temporal consistency
        let market_rhythm_stability = self.calculate_rhythm_stability(&dominant_cycles_with_power);

        // Convert power to amplitude for the final report if needed, or keep as power.
        // The report expects (cycle, stability/amplitude). Let's provide amplitude.
        let dominant_cycles_with_amplitude = dominant_cycles_with_power
            .iter()
            .map(|(period, power)| (*period, power.sqrt()))
            .collect();

        (dominant_cycles_with_amplitude, market_rhythm_stability)
    }

    /// Calculate wavelet features from price history (simplified implementation)
    fn calculate_wavelet_features(&self, price_history: &VecDeque<PricePoint>) -> Vec<Decimal> {
        if price_history.len() < 8 { // Minimum data points for meaningful analysis
            return Vec::new();
        }

        let data: Vec<f64> = price_history
            .iter()
            .map(|p| p.price.to_f64().unwrap_or(0.0))
            .collect();

        // Simplified Haar wavelet transform implementation
        let mut features = Vec::new();
        
        // Calculate energy at different scales using simple moving averages and differences
        for scale in [2, 4, 8].iter() {
            if data.len() >= *scale {
                let mut scale_energy = 0.0;
                for i in 0..(data.len() - scale) {
                    let avg1 = data[i..i + scale/2].iter().sum::<f64>() / (scale/2) as f64;
                    let avg2 = data[i + scale/2..i + scale].iter().sum::<f64>() / (scale/2) as f64;
                    let diff = avg2 - avg1;
                    scale_energy += diff * diff;
                }
                features.push(Decimal::from_f64(scale_energy).unwrap_or_default());
            }
        }
        
        features
    }

    /// Calculate volatility for a given timeframe using log returns
    fn calculate_volatility(&self, price_history: &VecDeque<PricePoint>) -> Decimal {
        if price_history.len() < 2 {
            return Decimal::ZERO;
        }

        let mut log_returns = Vec::new();

        for window in price_history.iter().collect::<Vec<_>>().windows(2) {
            let prev_price = window[0].price;
            let curr_price = window[1].price;

            if prev_price > Decimal::ZERO && curr_price > Decimal::ZERO {
                // Calculate log return: ln(P_t / P_{t-1})
                let price_ratio = curr_price / prev_price;
                if let (Some(ratio_f64), Some(log_return_f64)) = (price_ratio.to_f64(), price_ratio.to_f64().map(|x| x.ln())) {
                    if let Some(log_return) = Decimal::from_f64(log_return_f64) {
                        log_returns.push(log_return);
                    }
                }
            }
        }

        if log_returns.is_empty() {
            return Decimal::ZERO;
        }

        // Calculate standard deviation of log returns
        let mean = log_returns.iter().sum::<Decimal>() / Decimal::from(log_returns.len());
        let variance = log_returns
            .iter()
            .map(|&ret| (ret - mean) * (ret - mean))
            .sum::<Decimal>()
            / Decimal::from(log_returns.len());

        if let Some(variance_f64) = variance.to_f64() {
            Decimal::from_f64(variance_f64.sqrt()).unwrap_or(Decimal::ZERO)
        } else {
            Decimal::ZERO
        }
    }

    /// AUDIT-FIX: Calculate Hurst Exponent using R/S analysis with proper data requirements
    fn calculate_hurst_exponent(&self, price_history: &VecDeque<PricePoint>) -> Decimal {
        // AUDIT-FIX: Increase minimum data requirement from 20 to 100 for reliable R/S analysis
        if price_history.len() < 100 {
            return Decimal::new(5, 1); // Default to 0.5 (random walk)
        }

        // Convert prices to log returns
        let mut log_returns = Vec::new();
        for window in price_history.iter().collect::<Vec<_>>().windows(2) {
            let prev_price = window[0].price;
            let curr_price = window[1].price;

            if prev_price > Decimal::ZERO && curr_price > Decimal::ZERO {
                let price_ratio = curr_price / prev_price;
                if let (Some(ratio_f64), Some(log_return_f64)) = (price_ratio.to_f64(), price_ratio.to_f64().map(|x| x.ln())) {
                    if let Some(log_return) = Decimal::from_f64(log_return_f64) {
                        log_returns.push(log_return);
                    }
                }
            }
        }

        if log_returns.len() < 10 {
            return Decimal::new(5, 1);
        }

        // AUDIT-FIX: Calculate R/S statistic for expanded time scale range
        let mut rs_values = Vec::new();
        let mut time_scales = Vec::new();

        // AUDIT-FIX: Use logarithmic scales from 10 to N/4 for better scaling behavior
        let max_scale = log_returns.len() / 4;
        let scales: Vec<usize> = if max_scale > 10 {
            let step = ((max_scale - 10) / 8).max(1); // Ensure step is at least 1
            (10..=max_scale).step_by(step).collect()
        } else {
            vec![10, 15, 20] // Fallback for small datasets
        };

        for n in scales.iter() {
            if log_returns.len() >= *n && *n >= 10 {
                let rs = self.calculate_rs_statistic(&log_returns[0..*n]);
                if rs > Decimal::ZERO {
                    if let (Some(rs_f64), Some(n_f64)) = (rs.to_f64(), Some(*n as f64)) {
                        if let (Some(rs_ln), Some(n_ln)) = (Decimal::from_f64(rs_f64.ln()), Decimal::from_f64(n_f64.ln())) {
                            rs_values.push(rs_ln);
                            time_scales.push(n_ln);
                        }
                    }
                }
            }
        }

        if rs_values.len() < 2 {
            return Decimal::new(5, 1);
        }

        // Calculate slope of log(R/S) vs log(n) - this is the Hurst exponent
        let (hurst, r_squared) = self.calculate_slope_with_r_squared(&time_scales, &rs_values);

        // AUDIT-FIX: Validate R² for slope fit quality
        if r_squared < dec!(0.7) {
            warn!("Low R² ({:.3}) in Hurst calculation, result may be unreliable", r_squared);
        }

        // Clamp to reasonable bounds
        hurst.max(Decimal::new(1, 1)).min(Decimal::new(9, 1)) // 0.1 to 0.9
    }

    /// Calculate R/S statistic for a given series
    fn calculate_rs_statistic(&self, returns: &[Decimal]) -> Decimal {
        if returns.is_empty() {
            return Decimal::ZERO;
        }

        let n = Decimal::from(returns.len());
        let mean = returns.iter().sum::<Decimal>() / n;

        // Calculate cumulative deviations from mean
        let mut cumulative_deviations = Vec::new();
        let mut cumsum = Decimal::ZERO;

        for &ret in returns {
            cumsum += ret - mean;
            cumulative_deviations.push(cumsum);
        }

        // Calculate range
        let max_dev = cumulative_deviations
            .iter()
            .max()
            .copied()
            .unwrap_or(Decimal::ZERO);
        let min_dev = cumulative_deviations
            .iter()
            .min()
            .copied()
            .unwrap_or(Decimal::ZERO);
        let range = max_dev - min_dev;

        // AUDIT-FIX: Calculate unbiased variance using n-1 instead of n
        let variance = if returns.len() > 1 {
            returns
                .iter()
                .map(|&ret| (ret - mean) * (ret - mean))
                .sum::<Decimal>()
                / (n - Decimal::ONE) // Unbiased variance estimator
        } else {
            Decimal::ZERO
        };
        let std_dev = if let Some(variance_f64) = variance.to_f64() {
            Decimal::from_f64(variance_f64.sqrt()).unwrap_or(Decimal::ZERO)
        } else {
            Decimal::ZERO
        };

        if std_dev > Decimal::ZERO {
            range / std_dev
        } else {
            Decimal::ZERO
        }
    }

    /// Calculate slope using simple linear regression
    fn calculate_slope(&self, x_values: &[Decimal], y_values: &[Decimal]) -> Decimal {
        let (slope, _) = self.calculate_slope_with_r_squared(x_values, y_values);
        slope
    }

    /// AUDIT-FIX: Calculate slope with R² validation for fit quality assessment
    fn calculate_slope_with_r_squared(&self, x_values: &[Decimal], y_values: &[Decimal]) -> (Decimal, Decimal) {
        if x_values.len() != y_values.len() || x_values.len() < 2 {
            return (Decimal::new(5, 1), Decimal::ZERO); // Default slope, zero R²
        }

        let n = Decimal::from(x_values.len());
        let sum_x = x_values.iter().sum::<Decimal>();
        let sum_y = y_values.iter().sum::<Decimal>();
        let sum_xy = x_values
            .iter()
            .zip(y_values.iter())
            .map(|(&x, &y)| x * y)
            .sum::<Decimal>();
        let sum_x_squared = x_values.iter().map(|&x| x * x).sum::<Decimal>();
        let sum_y_squared = y_values.iter().map(|&y| y * y).sum::<Decimal>();

        let denominator = n * sum_x_squared - sum_x * sum_x;

        if denominator == Decimal::ZERO {
            return (Decimal::new(5, 1), Decimal::ZERO);
        }

        // Calculate slope
        let slope = (n * sum_xy - sum_x * sum_y) / denominator;

        // Calculate R² (coefficient of determination)
        let mean_y = sum_y / n;
        let ss_tot = y_values.iter()
            .map(|&y| (y - mean_y) * (y - mean_y))
            .sum::<Decimal>();

        if ss_tot == Decimal::ZERO {
            return (slope, Decimal::ONE); // Perfect fit if no variance in y
        }

        let intercept = (sum_y - slope * sum_x) / n;
        let ss_res = x_values.iter()
            .zip(y_values.iter())
            .map(|(&x, &y)| {
                let predicted = slope * x + intercept;
                (y - predicted) * (y - predicted)
            })
            .sum::<Decimal>();

        let r_squared = (ss_tot - ss_res) / ss_tot;
        let r_squared_clamped = r_squared.max(Decimal::ZERO).min(Decimal::ONE);

        (slope, r_squared_clamped)
    }

    /// Classify market character based on Hurst Exponent
    fn classify_market_character(&self, hurst_exponent: Decimal) -> MarketCharacter {
        let threshold_low = Decimal::new(45, 2); // 0.45
        let threshold_high = Decimal::new(55, 2); // 0.55

        if hurst_exponent < threshold_low {
            MarketCharacter::MeanReverting
        } else if hurst_exponent > threshold_high {
            MarketCharacter::Trending
        } else {
            MarketCharacter::RandomWalk
        }
    }

    /// AUDIT-FIX: Calculate daily cycle strength with proper buffer sizing
    fn calculate_daily_cycle_strength(&self) -> Decimal {
        if self.price_history_1h.len() < 24 {
            return Decimal::ZERO;
        }

        let data_len = self.price_history_1h.len();
        // AUDIT-FIX: Use dynamic buffer sizing instead of hardcoded 4320
        // This fixes the critical issue of assuming exactly 180 days of hourly data
        let target_len = data_len.next_power_of_two();

        let mut planner = FftPlanner::new();
        let fft = planner.plan_fft_forward(target_len);

        let mut buffer: Vec<Complex<f64>> = self
            .price_history_1h
            .iter()
            .map(|p| Complex::new(p.price.to_f64().unwrap_or(0.0), 0.0))
            .collect();

        // Pad to target length
        buffer.resize(target_len, Complex::new(0.0, 0.0));

        fft.process(&mut buffer);

        // AUDIT-FIX: Calculate correct daily cycle index for 24-hour period
        // This fixes the incorrect hardcoded calculation of 4320/24=180
        let daily_cycle_index = if data_len >= 24 {
            target_len / 24 // Correct index for 24-hour cycle in padded buffer
        } else {
            1 // Fallback for insufficient data
        };

        if daily_cycle_index < buffer.len() {
            let daily_cycle_magnitude = buffer[daily_cycle_index].norm();

            // Normalize the magnitude to a 0-1 range
            let total_magnitude = buffer.iter().map(|c| c.norm()).sum::<f64>();
            if total_magnitude > 0.0 {
                Decimal::from_f64(daily_cycle_magnitude / total_magnitude).unwrap_or_default()
            } else {
                Decimal::ZERO
            }
        } else {
            Decimal::ZERO
        }
    }

    /// AUDIT-FIX: Calculate proper rhythm stability using temporal consistency
    /// This measures how stable the dominant cycles are over time, not just spectral concentration
    fn calculate_rhythm_stability(&self, dominant_cycles: &[(f64, f64)]) -> f64 {
        if dominant_cycles.is_empty() || self.price_history_1m.len() < 240 {
            return 0.5; // Neutral score for insufficient data
        }

        // Use a sliding window approach to measure cycle consistency
        let window_size = 60; // 1-hour windows
        let step_size = 30; // 30-minute steps
        let mut cycle_periods = Vec::new();

        // Extract the strongest cycle period for analysis
        let target_period = dominant_cycles[0].0;

        // Analyze cycle consistency across multiple windows
        for window_start in (0..self.price_history_1m.len().saturating_sub(window_size)).step_by(step_size) {
            let window_end = (window_start + window_size).min(self.price_history_1m.len());
            let window_data: Vec<f64> = self.price_history_1m
                .range(window_start..window_end)
                .map(|p| p.price.to_f64().unwrap_or(0.0))
                .collect();

            if window_data.len() >= window_size {
                // Find the dominant cycle in this window (simplified)
                if let Some(period) = self.find_dominant_period_in_window(&window_data) {
                    cycle_periods.push(period);
                }
            }
        }

        if cycle_periods.len() < 2 {
            return 0.5; // Not enough windows for stability analysis
        }

        // Calculate coefficient of variation for the cycle periods
        let mean_period: f64 = cycle_periods.iter().sum::<f64>() / cycle_periods.len() as f64;
        let variance: f64 = cycle_periods.iter()
            .map(|p| (p - mean_period).powi(2))
            .sum::<f64>() / cycle_periods.len() as f64;

        let coefficient_of_variation = if mean_period > 0.0 {
            variance.sqrt() / mean_period
        } else {
            1.0 // High instability
        };

        // Convert to stability score (inverse of coefficient of variation)
        let stability = 1.0 / (1.0 + coefficient_of_variation);
        stability.max(0.0).min(1.0) // Clamp to [0,1]
    }

    /// Helper method to find dominant period in a data window
    fn find_dominant_period_in_window(&self, data: &[f64]) -> Option<f64> {
        if data.len() < 16 {
            return None;
        }

        // Simple autocorrelation-based period detection
        let mut max_correlation = 0.0;
        let mut best_period = 0.0;

        for period in 5..=(data.len() / 4) {
            let mut correlation = 0.0;
            let mut count = 0;

            for i in 0..(data.len() - period) {
                correlation += data[i] * data[i + period];
                count += 1;
            }

            if count > 0 {
                correlation /= count as f64;
                if correlation > max_correlation {
                    max_correlation = correlation;
                    best_period = period as f64;
                }
            }
        }

        if max_correlation > 0.1 { // Threshold for meaningful correlation
            Some(best_period)
        } else {
            None
        }
    }

    pub fn generate_insights(report: &crate::shared_types::FractalAnalysisReport) -> String {
        let mut insights = String::new();

        // Insight based on Hurst Exponent
        let hurst = report.hurst_exponent;
        let hurst_desc = if hurst < dec!(0.45) {
            "Mean-reverting: Prices tend to return to their average. Good for range-bound strategies."
        } else if hurst > dec!(0.55) {
            "Trending: Prices tend to continue in their current direction. Good for trend-following strategies."
        } else {
            "Random walk: Prices are unpredictable. Exercise caution."
        };
        insights.push_str(&format!("Hurst Exponent ({:.3}): {}\n", hurst, hurst_desc));

        // Insight based on Volatility
        let vol_1m = report.volatility_1m;
        let vol_15m = report.volatility_15m;
        let vol_1h = report.volatility_1h;

        let vol_desc = if vol_1m > dec!(0.03) || vol_15m > dec!(0.05) || vol_1h > dec!(0.07) {
            "High volatility detected across multiple timeframes. Expect larger price swings."
        } else if vol_1m < dec!(0.005) && vol_15m < dec!(0.01) && vol_1h < dec!(0.02) {
            "Low volatility detected. Market is calm, potential for breakout or continued range."
        } else {
            "Moderate volatility. Market conditions are stable."
        };
        insights.push_str(&format!("Volatility (1m: {}, 15m: {}, 1h: {}): {}\n", vol_1m, vol_15m, vol_1h, vol_desc));

        // Insight based on Market Character
        let market_char_desc = match report.market_character {
            MarketCharacter::Trending => "Market is exhibiting strong trending behavior.",
            MarketCharacter::MeanReverting => "Market is mean-reverting, prices are likely to return to average.",
            MarketCharacter::RandomWalk => "Market is in a random walk state, no clear patterns.",
        };
        insights.push_str(&format!("Market Character: {}\n", market_char_desc));

        // Insight based on Daily Cycle Strength
        let daily_cycle_strength = report.daily_cycle_strength;
        let cycle_desc = if daily_cycle_strength > dec!(0.5) {
            "Strong daily cycle detected. Consider timing trades with daily rhythms."
        } else {
            "Weak daily cycle. Daily patterns are less pronounced."
        };
        insights.push_str(&format!("Daily Cycle Strength ({:.3}): {}\n", daily_cycle_strength, cycle_desc));

        // Example of a strategic suggestion based on combined insights
        let strategic_suggestion = if hurst > dec!(0.6) && vol_1m > dec!(0.02) {
            "Strategic Suggestion: Market is strongly trending and volatile. Focus on trend-following strategies with tight risk management."
        } else if hurst < dec!(0.4) && vol_1m < dec!(0.01) {
            "Strategic Suggestion: Market is mean-reverting and calm. Consider range-bound strategies or waiting for clearer signals."
        } else {
            "Strategic Suggestion: No strong directional or volatility bias. Adhere to baseline strategy parameters."
        };
        insights.push_str(&format!("Strategic Suggestion: {}\n", strategic_suggestion));

        insights
    }

    /// Listens for high-frequency data from NATS and updates the analyzer.
    pub async fn listen_for_data(
        &mut self,
        nats_client: async_nats::Client,
    ) -> Result<(), crate::error::BasiliskError> {
        let mut price_subscriber = nats_client
            .subscribe(crate::shared_types::NatsTopics::DATA_PRICES_ALL)
            .await
            .map_err(|e| crate::error::BasiliskError::DataIngestion { message: format!("Failed to subscribe to NATS topic: {}", e) })?;
        info!(
            "FractalAnalyzer listening on {}",
            crate::shared_types::NatsTopics::DATA_PRICES_ALL
        );

        while let Some(message) = price_subscriber.next().await {
            // Assuming the payload is a simple f64 representing price
            let price: f64 = serde_json::from_slice(&message.payload)?;
            let price = Decimal::from_f64(price).unwrap_or_default();
            let timestamp = Utc::now(); // Use current time for simplicity, or parse from payload if available
            self.add_price_point(price, timestamp);

            // Perform analysis and publish report
            if let Ok(Some(_report)) = self.analyze(nats_client.clone()).await {
                // The analyze function now handles publishing
            }
        }
        Ok(())
    }
}

impl Default for FractalAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl FractalAnalyzer {
    /// Get market phase for a specific token pair
    /// This method analyzes the current market conditions to determine the optimal
    /// phase for market making decisions
    pub async fn get_market_phase(&self, _pair: &TokenPair) -> anyhow::Result<MarketPhase> {
        // For now, we'll use the general market analysis
        // In a full implementation, this would analyze pair-specific data
        
        if self.price_history_1m.len() < 10 {
            return Ok(MarketPhase::Fracture); // Not enough data, be conservative
        }

        let volatility = self.calculate_volatility(&self.price_history_1m);
        let hurst = self.calculate_hurst_exponent(&self.price_history_1m);
        let returns = self.calculate_returns(&self.price_history_1m)?;

        // Phase classification based on volatility and trend characteristics
        let phase = if volatility > dec!(0.05) {
            // High volatility - dangerous for market making
            MarketPhase::Fracture
        } else if hurst > dec!(0.6) && returns > dec!(0.02) {
            // Strong upward trend
            MarketPhase::Expansion
        } else if hurst > dec!(0.6) && returns < dec!(-0.02) {
            // Strong downward trend
            MarketPhase::Contraction
        } else if volatility < dec!(0.02) && hurst.abs() < dec!(0.1) {
            // Low volatility, no strong trend - ideal for market making
            MarketPhase::Equilibrium
        } else {
            // Moderate conditions, still suitable for market making
            MarketPhase::Equilibrium
        };

        Ok(phase)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[test]
    fn test_volatility_calculation() {
        let mut analyzer = FractalAnalyzer::new();

        // Add some test price points
        let base_time = Utc::now();
        let prices = [dec!(100), dec!(102), dec!(98), dec!(105), dec!(95)];

        for (i, price) in prices.iter().enumerate() {
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            analyzer.add_price_point(*price, timestamp);
        }

        // Note: This test would need a NATS client to work properly
        // For now, just test that volatility calculation works
        let volatility = analyzer.calculate_volatility(&analyzer.price_history_1m);
        assert!(volatility > Decimal::ZERO);
    }

    #[test]
    fn test_hurst_exponent_bounds() {
        let mut analyzer = FractalAnalyzer::new();

        // Add trending data
        let base_time = Utc::now();
        for i in 0..25 {
            let price = dec!(100) + Decimal::from(i); // Trending upward
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            analyzer.add_price_point(price, timestamp);
        }

        // Note: This test would need a NATS client to work properly
        // For now, just test that Hurst exponent calculation works
        let hurst = analyzer.calculate_hurst_exponent(&analyzer.price_history_1m);
        assert!(hurst >= dec!(0.1));
        assert!(hurst <= dec!(0.9));
    }

    #[test]
    fn test_market_character_classification() {
        let analyzer = FractalAnalyzer::new();

        assert_eq!(
            analyzer.classify_market_character(dec!(0.3)),
            MarketCharacter::MeanReverting
        );
        assert_eq!(
            analyzer.classify_market_character(dec!(0.5)),
            MarketCharacter::RandomWalk
        );
        assert_eq!(
            analyzer.classify_market_character(dec!(0.7)),
            MarketCharacter::Trending
        );
    }

    #[test]
    fn test_sqrt_precision() {
        let analyzer = FractalAnalyzer::new();

        // Use f64 for sqrt calculation then convert back to Decimal
        let result = Decimal::from_f64(4.0_f64.sqrt()).unwrap_or(Decimal::ZERO);
        assert!((result - dec!(2)).abs() < dec!(0.001));

        let result = Decimal::from_f64(9.0_f64.sqrt()).unwrap_or(Decimal::ZERO);
        assert!((result - dec!(3)).abs() < dec!(0.001));
    }
}

