// src/validation/mod.rs

//! Validation Framework for Live Production Trading
//! 
//! This module provides comprehensive validation capabilities for ensuring correct
//! live production trading functionality in the Zen Geometer autonomous trading system.
//! The framework validates end-to-end operational capability from opportunity scanning
//! to profitable trade execution, with validated mathematical models and robust
//! smart contract integration across multiple blockchain networks.

pub mod framework;
pub mod results;
pub mod types;
pub mod store;
pub mod examples;
pub mod test_data_provider;
pub mod test_data_examples;
pub mod cli;
pub mod mathematical_validator;
pub mod are_validator;
pub mod are_validator_demo;
pub mod opportunity_validator;
pub mod opportunity_validator_cli;
pub mod contract_integration_validator;
pub mod contract_integration_validator_cli;
pub mod contract_integration_validator_demo;
pub mod cross_chain_validator;
pub mod cross_chain_validator_cli;
pub mod cross_chain_validator_demo;
pub mod deployment_mode_validator;
pub mod deployment_mode_validator_cli;

#[cfg(test)]
pub mod tests;

pub use framework::ValidationFramework;
pub use results::{ValidationResult, ValidationResultSet, ValidationStatus, ValidationError, ValidationWarning};
pub use types::*;
pub use store::ResultsStore;
pub use test_data_provider::{
    TestDataProvider, TestDataConfig, MarketScenarioLibrary, OpportunityTemplateLibrary, HistoricalDataProvider,
    TestScenario, MarketConditions, OpportunityTemplate, OpportunityType, ExecutionComplexity,
    ExpectedOutcomes, ValidationCriteria, HistoricalTestData, DataValidationReport,
    NetworkCongestionLevel, LiquidityDistribution, GeometricProperties
};
pub use cli::{ValidationArgs, ValidationCommand, handle_validation_command};
pub use mathematical_validator::{
    MathematicalModelValidator, MathematicalValidationMetrics, HurstExponentMetrics,
    VesicaPiscisMetrics, KellyCriterionMetrics, GoldenRatioMetrics, PathfindingMetrics,
    ToleranceConfig, ReferenceImplementations, TestDataGenerator,
    HurstTestScenario, VesicaTestScenario, KellyTestScenario, GoldenRatioTestScenario, PathfindingTestScenario
};
pub use are_validator::{
    AREValidator, AREValidationMetrics, ChronosSieveMetrics, MandorlaGaugeMetrics,
    NetworkSeismologyMetrics, MultiplicativeScoringMetrics, PillarIntegrationMetrics,
    ARETestScenario, ARETestDataGenerator, MockGeometricScorer
};
pub use opportunity_validator::{
    OpportunityValidator, OpportunityDetectionMetrics, ScannerMetrics, ScannerValidationMetrics,
    ProfitAccuracyMetrics, ScannerPerformanceMetrics, OpportunityQualityMetrics,
    ValidationTestResult, ResourceUsageMetrics
};
pub use contract_integration_validator::{
    ContractIntegrationValidator, ContractIntegrationConfig, ContractAddresses, ContractIntegrationMetrics
};
pub use contract_integration_validator_cli::{
    ContractIntegrationArgs, ContractIntegrationCommand, handle_contract_integration_command
};
pub use contract_integration_validator_demo::{
    run_contract_integration_demo, run_quick_contract_validation, run_framework_integration_demo,
    show_usage_examples
};
pub use cross_chain_validator::{
    CrossChainValidator, CrossChainValidationConfig, CrossChainValidationMetrics,
    ChainValidationConfig, ChainContractAddresses, CapitalManagementConfig,
    StargateConfig, ArbitrageValidationConfig, BridgeValidationConfig,
    HubSpokeMetrics, BaseHubMetrics, DegenExecutionMetrics, StargateIntegrationMetrics,
    ArbitrageProfitabilityMetrics, BridgePredictionMetrics
};
pub use cross_chain_validator_cli::{
    CrossChainValidationArgs, CrossChainValidationCommand, handle_cross_chain_validation_command,
    run_complete_cross_chain_validation_suite
};
pub use cross_chain_validator_demo::{
    run_cross_chain_validation_demo, run_quick_cross_chain_validation, 
    show_cross_chain_usage_examples
};
pub use deployment_mode_validator::{
    DeploymentModeValidator, DeploymentModeMetrics, SimulateModeMetrics, ShadowModeMetrics,
    SentinelModeMetrics, LowCapitalModeMetrics, LiveModeMetrics, OverallDeploymentMetrics
};
pub use deployment_mode_validator_cli::{
    DeploymentModeValidationArgs, DeploymentModeValidationCommand, handle_deployment_mode_validation_command,
    run_complete_deployment_mode_validation, run_single_mode_validation, run_deployment_mode_validation_demo,
    show_deployment_mode_usage_examples
};

use crate::error::BasiliskError;

/// Result type for validation operations
pub type ValidationFrameworkResult<T> = std::result::Result<T, BasiliskError>;