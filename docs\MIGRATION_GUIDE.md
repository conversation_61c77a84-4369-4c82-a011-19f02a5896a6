# Configuration Migration Guide

This guide provides step-by-step instructions for migrating from the old `Settings` system to the new elegant `Config` system.

## Overview

The migration follows a 5-phase approach that allows zero-downtime transition:

1. **Phase 1: Core Infrastructure** ✅ (Complete)
2. **Phase 2: Strategy Components** 
3. **Phase 3: Execution and Risk Management**
4. **Phase 4: User Interface Components**
5. **Phase 5: Cleanup and Old System Removal**

## Phase 1: Core Infrastructure (Complete)

✅ **Already implemented:**
- New `Config` struct with figment-based loading
- Enhanced validation system
- Adapter layer (`Config::to_settings()`)
- Environment variable support with `APP_` prefix
- Main.rs updated to use new system

## Phase 2: Strategy Components

### Migrating Strategy Scanners

**Before (Old System):**
```rust
// src/strategies/scanners/gaze.rs
impl GazeScanner {
    pub fn new(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        config: Arc<Settings>,  // ← Old Settings
    ) -> Self {
        // Access config fields through old structure
        let kelly_fraction = config.risk.kelly_fraction_cap;
        // ...
    }
}
```

**After (New System):**
```rust
// src/strategies/scanners/gaze.rs
use crate::config::{Config, Settings, migration::helpers::ConfigBridge};

impl GazeScanner {
    // Keep old constructor for backward compatibility
    pub fn new(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        config: Arc<Settings>,
    ) -> Self {
        Self::from_old_settings(provider, honeypot_checker, config)
    }
    
    // Add new constructor for new config system
    pub fn new_with_config(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        config: Arc<Config>,
    ) -> Self {
        Self::from_new_config(provider, honeypot_checker, config)
    }
}

impl ConfigBridge for GazeScanner {
    fn from_old_settings(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        settings: Arc<Settings>
    ) -> Self {
        // Extract values using helper functions
        let kelly_fraction = helpers::get_kelly_fraction(&settings);
        let max_slippage = helpers::get_max_slippage_bps(&settings);
        
        // Build scanner with extracted values
        Self::build(provider, honeypot_checker, kelly_fraction, max_slippage)
    }
    
    fn from_new_config(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        config: Arc<Config>
    ) -> Self {
        // Direct access to new config structure
        let kelly_fraction = config.strategy.kelly_fraction_cap;
        let max_slippage = config.execution.max_slippage_bps;
        
        Self::build(provider, honeypot_checker, kelly_fraction, max_slippage)
    }
}

impl GazeScanner {
    fn build(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        kelly_fraction: f64,
        max_slippage: u64,
    ) -> Self {
        // Common construction logic
        Self {
            provider,
            honeypot_checker,
            kelly_fraction,
            max_slippage,
            // ... other fields
        }
    }
}
```

### Migration Steps for Each Component

1. **Add ConfigBridge trait implementation**
2. **Create helper constructor methods**
3. **Extract common construction logic**
4. **Test both old and new paths**
5. **Update calling code gradually**

## Phase 3: Execution and Risk Management

### Environment Variable Migration

**Old approach:**
```rust
let private_key = std::env::var("BASILISK_EXECUTION_PRIVATE_KEY")?;
```

**New approach:**
```rust
// Configuration automatically loads from APP_SECRETS__PRIVATE_KEYS__BASE
let private_key = config.secrets.private_keys.get("base")
    .or_else(|| std::env::var("BASE_PRIVATE_KEY").ok())
    .ok_or_else(|| anyhow::anyhow!("Private key not found"))?;
```

## Phase 4: User Interface Components

### CLI Handler Migration

**Before:**
```rust
fn show_config(settings: &Settings) {
    println!("Kelly fraction: {}", settings.risk.kelly_fraction_cap);
}
```

**After:**
```rust
fn show_config(config: &Config) {
    println!("Kelly fraction: {}", config.strategy.kelly_fraction_cap);
}

// Adapter for backward compatibility
fn show_config_legacy(settings: &Settings) {
    let kelly_fraction = helpers::get_kelly_fraction(settings);
    println!("Kelly fraction: {}", kelly_fraction);
}
```

## Environment Variable Reference

The new system supports hierarchical environment variables:

```bash
# Strategy configuration
export APP_STRATEGY__KELLY_FRACTION_CAP=0.25
export APP_STRATEGY__MIN_PROFITABILITY_BPS=50

# Execution configuration  
export APP_EXECUTION__MAX_SLIPPAGE_BPS=500
export APP_EXECUTION__GAS_LIMIT_MULTIPLIER=1.2

# Chain-specific configuration
export APP_CHAINS__8453__RPC_URL="https://mainnet.base.org"
export APP_CHAINS__8453__PRIVATE_KEY_ENV_VAR="BASE_PRIVATE_KEY"

# Secrets (loaded into secrets struct)
export APP_SECRETS__API_KEYS__BINANCE="your_api_key"
export APP_SECRETS__PRIVATE_KEYS__BASE="0x..."
```

## Testing Migration

```rust
#[cfg(test)]
mod migration_tests {
    use super::*;
    
    #[test]
    fn test_component_migration() {
        // Load config with new system
        let new_config = Arc::new(Config::load().unwrap());
        let old_settings = Arc::new(new_config.to_settings());
        
        // Test both constructors produce equivalent results
        let scanner_old = GazeScanner::new(provider.clone(), checker.clone(), old_settings);
        let scanner_new = GazeScanner::new_with_config(provider, checker, new_config);
        
        assert_eq!(scanner_old.kelly_fraction, scanner_new.kelly_fraction);
    }
}
```

## Rollback Strategy

If issues arise during migration:

1. **Set environment variable:** `USE_OLD_CONFIG=true`
2. **Revert to old constructor calls**
3. **Fix issues in new system**
4. **Resume migration**

## Benefits After Migration

- ✅ **Type-safe configuration** with compile-time checks
- ✅ **Enhanced validation** catches errors early
- ✅ **Environment variable support** for all config fields
- ✅ **Layered configuration** (base → environment → env vars)
- ✅ **Better error messages** with context
- ✅ **Centralized configuration** management

## Next Steps

1. Start with Phase 2: Migrate strategy scanners
2. Update tests to use new configuration
3. Gradually migrate remaining components
4. Remove old Settings system in Phase 5
