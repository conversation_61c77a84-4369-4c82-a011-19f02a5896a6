// src/bin/validate_config.rs
// Configuration validation utility for deployment system

use anyhow::Result;
use clap::{Arg, Command};
use std::fs;
use std::path::Path;

fn main() -> Result<()> {
    let matches = Command::new("validate_config")
        .version("1.0.0")
        .about("Validate deployment configuration")
        .arg(
            Arg::new("config")
                .long("config")
                .short('c')
                .help("Path to configuration file")
                .required(true)
        )
        .get_matches();

    let config_path = matches.get_one::<String>("config").unwrap();
    
    println!("Validating configuration file: {}", config_path);
    
    // Check if file exists
    if !Path::new(config_path).exists() {
        eprintln!("❌ Configuration file not found: {}", config_path);
        std::process::exit(1);
    }
    
    // Try to read and parse the TOML file
    match fs::read_to_string(config_path) {
        Ok(content) => {
            match toml::from_str::<toml::Value>(&content) {
                Ok(_) => {
                    println!("✅ Configuration file is valid TOML");
                    
                    // Basic validation checks
                    let config: toml::Value = toml::from_str(&content)?;
                    
                    // Check for required sections
                    let required_sections = [
                        "deployment",
                        "traffic_routing", 
                        "feature_flags",
                        "health_check_config",
                        "monitoring_config"
                    ];
                    
                    let mut missing_sections = Vec::new();
                    for section in &required_sections {
                        if !config.get(section).is_some() {
                            missing_sections.push(*section);
                        }
                    }
                    
                    if !missing_sections.is_empty() {
                        eprintln!("⚠️  Missing configuration sections: {:?}", missing_sections);
                        eprintln!("   This may cause deployment issues but TOML is valid");
                    }
                    
                    println!("✅ Configuration validation passed");
                    Ok(())
                }
                Err(e) => {
                    eprintln!("❌ Invalid TOML syntax: {}", e);
                    std::process::exit(1);
                }
            }
        }
        Err(e) => {
            eprintln!("❌ Failed to read configuration file: {}", e);
            std::process::exit(1);
        }
    }
}