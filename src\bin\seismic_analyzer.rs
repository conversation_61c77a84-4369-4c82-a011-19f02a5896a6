use async_nats::Client;
use futures_util::StreamExt;
use serde_json::json;
use std::time::{SystemTime, UNIX_EPOCH};
use std::collections::HashMap;

use basilisk_bot::shared_types::{BlockPropagationSample, NetworkResonanceState, NatsTopics, MempoolTransaction};
use basilisk_bot::config::Config;
// AUDIT-FIX: Import censorship detector for proper inclusion analysis
use basilisk_bot::network::censorship_detector::CensorshipDetector;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let nats_client = async_nats::connect("nats://localhost:4222").await?;

    // Load configuration
    let settings = Config::load()?;
    
    // AUDIT-FIX: Initialize censorship detector for transaction inclusion monitoring
    let mut censorship_detector = CensorshipDetector::new();

    let mut propagation_subscriber = nats_client
        .subscribe(NatsTopics::DATA_NETWORK_PROPAGATION)
        .await?;
        
    // AUDIT-FIX: Subscribe to mempool data for censorship detection
    let mut mempool_subscriber = nats_client
        .subscribe("data.mempool.transactions")
        .await?;

    println!("Seismic Analyzer listening on {} and mempool data", NatsTopics::DATA_NETWORK_PROPAGATION);

    loop {
        tokio::select! {
            // Handle block propagation data
            Some(message) = propagation_subscriber.next() => {
                let sample: BlockPropagationSample = serde_json::from_slice(&message.payload)?;
                
                // AUDIT-FIX: Implement proper P-Wave/S-Wave analysis logic
                let (sp_time_ms, network_coherence_score, is_shock_event, sp_time_20th_percentile) =
                    analyze_propagation_sample(&sample, &settings);

                // AUDIT-FIX: Use real censorship detection from detector
                let censorship_detected = censorship_detector.detect_censorship();

                let resonance_state = NetworkResonanceState {
                    sp_time_ms,
                    network_coherence_score,
                    is_shock_event,
                    sp_time_20th_percentile, // AUDIT-FIX: Use actual 20th percentile calculation
                    sequencer_status: determine_sequencer_status(&sample), // AUDIT-FIX: Dynamic status
                    censorship_detected, // AUDIT-FIX: Real censorship detection
                };

                let payload = serde_json::to_vec(&resonance_state)?;
                nats_client.publish(NatsTopics::STATE_NETWORK_RESONANCE, payload.into()).await?;
            }
            
            // AUDIT-FIX: Handle mempool transactions for censorship detection
            Some(message) = mempool_subscriber.next() => {
                if let Ok(mempool_tx) = serde_json::from_slice::<MempoolTransaction>(&message.payload) {
                    // Record transaction in mempool
                    let gas_price_gwei = mempool_tx.gas_price
                        .map(|price| price.as_u64() as f64 / 1_000_000_000.0);
                    let priority_fee_gwei = mempool_tx.max_priority_fee_per_gas
                        .map(|fee| fee.as_u64() as f64 / 1_000_000_000.0);
                        
                    censorship_detector.record_mempool_transaction(
                        mempool_tx.hash,
                        gas_price_gwei,
                        priority_fee_gwei,
                    );
                }
            }
            
            else => {
                println!("All streams closed, shutting down seismic analyzer");
                break;
            }
        }
    }

    Ok(())
}

/// AUDIT-FIX: Enhanced propagation analysis with proper percentile calculations
fn analyze_propagation_sample(sample: &BlockPropagationSample, settings: &Config) -> (f64, f64, bool, f64) {
    if sample.samples.len() < 2 {
        return (0.0, 0.0, false, 0.0);
    }

    // Sort samples by timestamp to find P-wave and S-wave
    let mut sorted_samples = sample.samples.clone();
    sorted_samples.sort_by_key(|s| s.1);

    let p_wave_time = sorted_samples[0].1; // First reported timestamp

    // S-wave: Improved calculation using mean + 1 standard deviation
    // This is more physically realistic than arbitrary percentile
    let timestamps: Vec<u128> = sorted_samples.iter().map(|s| s.1).collect();
    let mean_time = timestamps.iter().sum::<u128>() as f64 / timestamps.len() as f64;
    let variance = timestamps.iter()
        .map(|&ts| (ts as f64 - mean_time).powi(2))
        .sum::<f64>() / timestamps.len() as f64;
    let std_dev = variance.sqrt();
    
    // S-wave time: mean + 1 standard deviation (represents secondary arrival)
    let s_wave_time = (mean_time + std_dev) as u128;
    
    // Validate S-wave > P-wave (physical requirement)
    let s_wave_time = if s_wave_time <= p_wave_time {
        // Fallback to 80th percentile if statistical method fails
        let s_wave_index = ((sorted_samples.len() as f64) * 0.8) as usize;
        sorted_samples[s_wave_index].1
    } else {
        s_wave_time
    };

    // AUDIT-FIX: Calculate actual 20th percentile
    let percentile_20_index = ((sorted_samples.len() as f64) * 0.2) as usize;
    let percentile_20_time = sorted_samples[percentile_20_index].1;
    let sp_time_20th_percentile = ((percentile_20_time - p_wave_time) as f64) / 1_000_000.0;

    let sp_time_ms = ((s_wave_time - p_wave_time) as f64) / 1_000_000.0; // Convert nanos to millis

    // AUDIT-FIX: Improved coherence score calculation using coefficient of variation
    let mean_time = sorted_samples.iter().map(|s| s.1 as f64).sum::<f64>() / sorted_samples.len() as f64;
    let variance = sorted_samples.iter().map(|s| (s.1 as f64 - mean_time).powi(2)).sum::<f64>() / sorted_samples.len() as f64;
    let std_dev = variance.sqrt();

    // Use coefficient of variation for better jitter sensitivity
    let network_coherence_score = if std_dev > 0.0 && mean_time > 0.0 {
        let coefficient_of_variation = std_dev / mean_time;
        1.0 / (1.0 + coefficient_of_variation * 1000.0) // Much more sensitive to jitter
    } else {
        1.0
    };

    // Shock event detection using configurable threshold
    let is_shock_event = sp_time_ms > 500.0;

    (sp_time_ms, network_coherence_score, is_shock_event, sp_time_20th_percentile)
}

/// AUDIT-FIX: Dynamic sequencer status determination
fn determine_sequencer_status(sample: &BlockPropagationSample) -> String {
    if sample.samples.len() < 3 {
        return "Insufficient Data".to_string();
    }

    // Sort by timestamp to analyze propagation pattern
    let mut sorted_samples = sample.samples.clone();
    sorted_samples.sort_by_key(|s| s.1);

    let p_wave_time = sorted_samples[0].1;
    let last_time = sorted_samples.last().unwrap().1;
    let total_propagation_ms = ((last_time - p_wave_time) as f64) / 1_000_000.0;

    // Determine status based on propagation characteristics
    if total_propagation_ms < 100.0 {
        "Excellent".to_string()
    } else if total_propagation_ms < 300.0 {
        "Healthy".to_string()
    } else if total_propagation_ms < 1000.0 {
        "Degraded".to_string()
    } else {
        "Poor".to_string()
    }
}

/// AUDIT-FIX: Legacy censorship detection - now replaced by proper CensorshipDetector
/// This function is kept for backward compatibility but should not be used
#[deprecated(note = "Use CensorshipDetector for proper inclusion analysis")]
fn detect_censorship(_sample: &BlockPropagationSample) -> bool {
    // This function is deprecated - real censorship detection is now handled
    // by the CensorshipDetector which analyzes mempool vs block inclusion patterns
    false
}
