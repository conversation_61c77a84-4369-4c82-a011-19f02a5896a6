// Production integration example for the elegant configuration system
// Shows how to integrate the new config system in a real trading component

use basilisk_bot::config::{Config, Settings, migration::helpers};
use std::sync::Arc;
use std::env;

// Example trading strategy that uses the new configuration system
struct ZenGeometerStrategy {
    config: Arc<Config>,
    kelly_fraction: f64,
    max_slippage_bps: u64,
    enabled_chains: Vec<u64>,
}

impl ZenGeometerStrategy {
    /// Create strategy with new Config system (preferred)
    pub fn new(config: Arc<Config>) -> Result<Self, Box<dyn std::error::Error>> {
        // Validate configuration before using
        config.validate()?;
        
        // Extract strategy-specific parameters
        let kelly_fraction = config.strategy.kelly_fraction_cap;
        let max_slippage_bps = config.execution.max_slippage_bps;
        let enabled_chains: Vec<u64> = config.chains.keys().copied().collect();
        
        // Verify strategy is enabled
        if !config.strategy.enabled_strategies.contains(&"zen_geometer".to_string()) {
            return Err("zen_geometer strategy not enabled in configuration".into());
        }
        
        Ok(Self {
            config,
            kelly_fraction,
            max_slippage_bps,
            enabled_chains,
        })
    }
    
    /// Legacy constructor for backward compatibility
    pub fn from_settings(settings: Arc<Settings>) -> Result<Self, Box<dyn std::error::Error>> {
        // Convert old Settings to new Config for internal use
        // This is a temporary bridge during migration
        let kelly_fraction = helpers::get_kelly_fraction(&settings);
        let max_slippage_bps = helpers::get_max_slippage_bps(&settings);
        let enabled_chains: Vec<u64> = settings.chains.keys().copied().collect();
        
        // Create a minimal Config for internal use
        // In practice, you'd load the full Config and use the adapter
        let config = Arc::new(Config::load()?);
        
        Ok(Self {
            config,
            kelly_fraction,
            max_slippage_bps,
            enabled_chains,
        })
    }
    
    /// Execute trading logic with configuration-aware parameters
    pub async fn execute_trade(&self, chain_id: u64, amount_usd: f64) -> Result<(), Box<dyn std::error::Error>> {
        // Check if chain is configured
        let chain_config = self.config.chains.get(&chain_id)
            .ok_or_else(|| format!("Chain {} not configured", chain_id))?;
        
        // Calculate position size using Kelly criterion
        let kelly_position_size = amount_usd * self.kelly_fraction;
        
        println!("🚀 Executing trade on {}", chain_config.name);
        println!("  💰 Amount: ${:.2}", amount_usd);
        println!("  📊 Kelly position size: ${:.2}", kelly_position_size);
        println!("  🎯 Max slippage: {}bps", self.max_slippage_bps);
        println!("  🔗 RPC: {}", chain_config.rpc_url);
        
        // Simulate trade execution
        if kelly_position_size > 10000.0 {
            return Err("Position size exceeds safety limits".into());
        }
        
        println!("  ✅ Trade executed successfully");
        Ok(())
    }
    
    /// Get chain-specific configuration
    pub fn get_chain_config(&self, chain_id: u64) -> Option<&basilisk_bot::config::ChainConfig> {
        self.config.chains.get(&chain_id)
    }
    
    /// Check if strategy should execute based on current configuration
    pub fn should_execute(&self) -> bool {
        // Environment-aware execution logic
        let env = env::var("APP_ENV").unwrap_or_else(|_| "local".into());
        
        match env.as_str() {
            "production" => {
                // More conservative in production
                self.kelly_fraction <= 0.5 && self.max_slippage_bps <= 300
            }
            "staging" => {
                // Moderate restrictions in staging
                self.kelly_fraction <= 0.75 && self.max_slippage_bps <= 500
            }
            _ => {
                // Development/local - more permissive
                self.kelly_fraction <= 1.0 && self.max_slippage_bps <= 1000
            }
        }
    }
}

// Example risk manager that uses configuration
struct RiskManager {
    config: Arc<Config>,
    daily_loss_limit: f64,
    position_size_limit: f64,
}

impl RiskManager {
    pub fn new(config: Arc<Config>) -> Result<Self, Box<dyn std::error::Error>> {
        config.validate()?;
        
        // Extract risk parameters from configuration
        let daily_loss_limit = 1000.0; // Could be configurable
        let position_size_limit = config.strategy.kelly_fraction_cap * 10000.0; // Example calculation
        
        Ok(Self {
            config,
            daily_loss_limit,
            position_size_limit,
        })
    }
    
    pub fn check_trade_risk(&self, amount: f64, chain_id: u64) -> Result<(), Box<dyn std::error::Error>> {
        // Check position size limits
        if amount > self.position_size_limit {
            return Err(format!("Trade amount ${:.2} exceeds position limit ${:.2}", 
                              amount, self.position_size_limit).into());
        }
        
        // Check chain-specific limits
        if let Some(chain_config) = self.config.chains.get(&chain_id) {
            let max_gas_cost = (chain_config.max_gas_price as f64) * 21000.0 / 1e18; // Rough estimate
            if max_gas_cost > amount * 0.1 {
                return Err("Gas costs too high relative to trade size".into());
            }
        }
        
        println!("✅ Risk check passed for ${:.2} trade on chain {}", amount, chain_id);
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🏭 Production Integration Example");
    println!("=================================\n");
    
    // Set up production environment
    env::set_var("CONFIG_PATH", "config/elegant-production.toml");
    env::set_var("APP_ENV", "production");
    
    // Load configuration with new system
    println!("📥 Loading production configuration...");
    let config = Arc::new(Config::load()?);
    
    // Validate configuration
    config.validate()?;
    println!("✅ Configuration validated successfully");
    
    // Create trading components
    println!("\n🔧 Initializing trading components...");
    let strategy = ZenGeometerStrategy::new(config.clone())?;
    let risk_manager = RiskManager::new(config.clone())?;
    
    // Check if strategy should execute
    if !strategy.should_execute() {
        println!("⚠️ Strategy execution disabled due to configuration constraints");
        return Ok(());
    }
    
    println!("✅ All components initialized successfully");
    
    // Simulate trading workflow
    println!("\n💼 Simulating trading workflow...");
    
    let trade_amount = 5000.0;
    let chain_id = 8453; // Base
    
    // Risk check
    risk_manager.check_trade_risk(trade_amount, chain_id)?;
    
    // Execute trade
    strategy.execute_trade(chain_id, trade_amount).await?;
    
    println!("\n🎉 Production integration example completed successfully!");
    println!("\n📋 Key Benefits Demonstrated:");
    println!("  ✅ Type-safe configuration access");
    println!("  ✅ Environment-aware validation");
    println!("  ✅ Chain-specific parameter extraction");
    println!("  ✅ Risk management integration");
    println!("  ✅ Backward compatibility during migration");
    
    Ok(())
}
