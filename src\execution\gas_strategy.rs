// PHASE 3 ENHANCEMENT: Advanced gas strategy with competitive intelligence and adaptive bidding

use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use tracing::{debug, info, warn};
use crate::error::BasiliskError;

use crate::shared_types::{NetworkResonanceState, MarketRegime};
use crate::config::ExecutionConfig;

/// Advanced gas strategy with competitive intelligence
#[derive(Debug, Clone)]
pub struct GasStrategy {
    pub config: ExecutionConfig,
}

impl GasStrategy {
    /// Create a new gas strategy with the given configuration
    pub fn new(config: ExecutionConfig) -> Self {
        Self { config }
    }

    /// PHASE 3 ENHANCEMENT: Advanced gas strategy with competitive intelligence and adaptive bidding
    /// Implements Golden Ratio bidding and MEV-aware gas optimization
    pub fn calculate_optimal_gas_price(
        &self,
        network_state: &Option<NetworkResonanceState>,
        market_regime: &MarketRegime,
    ) -> Decimal {
        let base_gas_price = Decimal::from(self.config.max_priority_fee.unwrap_or(2000000000)) / dec!(1_000_000_000); // Convert from wei to Gwei
        let mut multiplier = dec!(1.0);

        // PHASE 3 ENHANCEMENT: Golden Ratio bidding for competitive advantage
        let golden_ratio_multiplier = dec!(1.618); // Golden Ratio for optimal competitive positioning

        // PHASE 3 ENHANCEMENT: Advanced network state analysis
        if let Some(state) = network_state {
            // Multi-factor network congestion analysis
            let congestion_score = calculate_network_congestion_score(state);
            
            // If network is congested or experiencing shock, use adaptive multiplier
            if state.is_shock_event || state.network_coherence_score < 0.5 {
                let shock_multiplier = Decimal::try_from(self.config.gas_multiplier_network_shock.unwrap_or(1.5)).unwrap_or(dec!(1.5));
                multiplier *= shock_multiplier * congestion_score;
                info!("ADVANCED GAS: Network shock detected, adaptive multiplier: {:.3}", multiplier);
            }

            // PHASE 3 ENHANCEMENT: S-P time analysis with percentile-based scaling
            let sp_time_ratio = state.sp_time_ms / state.sp_time_20th_percentile.max(1.0);
            if sp_time_ratio > 2.0 {
                let ratio_decimal = Decimal::try_from(sp_time_ratio - 1.0).unwrap_or(dec!(1.0));
                let sp_multiplier = dec!(1.0) + (ratio_decimal * dec!(0.2));
                multiplier *= sp_multiplier;
                debug!("ADVANCED GAS: High S-P time ratio {:.2}, multiplier: {:.3}", sp_time_ratio, multiplier);
            }

            // PHASE 3 ENHANCEMENT: Censorship resistance adjustment
            if state.censorship_detected {
                multiplier *= dec!(1.5); // Increase gas to bypass censorship
                warn!("ADVANCED GAS: Censorship detected, increasing gas for resistance");
            }
        }

        // PHASE 3 ENHANCEMENT: Advanced regime-based gas strategy
        match market_regime {
            MarketRegime::BotGasWar => {
                // Use Golden Ratio for competitive advantage in gas wars
                let bot_war_multiplier = Decimal::try_from(self.config.gas_multiplier_bot_gas_war.unwrap_or(2.0)).unwrap_or(dec!(2.0));
                multiplier *= bot_war_multiplier * golden_ratio_multiplier;
                info!("ADVANCED GAS: Bot gas war - Golden Ratio bidding: {:.3}", multiplier);
            },
            MarketRegime::RetailFomoSpike => {
                // Moderate increase during retail FOMO
                let fomo_multiplier = Decimal::try_from(self.config.gas_multiplier_retail_fomo.unwrap_or(1.1)).unwrap_or(dec!(1.1));
                multiplier *= fomo_multiplier;
                debug!("ADVANCED GAS: Retail FOMO spike, conservative increase: {:.3}", multiplier);
            },
            MarketRegime::HighVolatilityCorrection => {
                // Aggressive gas during volatility for fast execution
                let volatility_multiplier = Decimal::try_from(self.config.gas_multiplier_high_volatility.unwrap_or(1.05)).unwrap_or(dec!(1.05));
                multiplier *= volatility_multiplier * dec!(1.2);
                debug!("ADVANCED GAS: High volatility, aggressive execution: {:.3}", multiplier);
            },
            MarketRegime::CalmOrderly => {
                // Optimize for cost efficiency in calm markets
                multiplier *= dec!(0.9); // Slight reduction for cost optimization
                debug!("ADVANCED GAS: Calm market, cost optimization: {:.3}", multiplier);
            },
            _ => {},
        }

        // PHASE 3 ENHANCEMENT: Dynamic gas price calculation with competitive intelligence
        let optimal_gas_price = calculate_competitive_gas_price(base_gas_price, multiplier);
        
        // PHASE 3 ENHANCEMENT: Intelligent capping with market awareness
        let max_reasonable_gas = base_gas_price * dec!(10.0); // 10x cap instead of 5x for competitive situations
        let final_gas_price = optimal_gas_price.min(max_reasonable_gas);

        info!(
            "ADVANCED GAS STRATEGY: Base: {:.2} Gwei | Multiplier: {:.3} | Optimal: {:.2} Gwei | Final: {:.2} Gwei",
            base_gas_price, multiplier, optimal_gas_price, final_gas_price
        );

        final_gas_price
    }

    /// PHASE 3 ENHANCEMENT: Calculate MEV-aware gas price for sensitive opportunities
    pub fn calculate_mev_aware_gas_price(
        &self,
        base_gas_price: Decimal,
        opportunity_value_usd: Decimal,
        is_mev_sensitive: bool,
    ) -> Decimal {
        if !is_mev_sensitive {
            return base_gas_price;
        }

        // Calculate gas price as percentage of opportunity value (max 5%)
        let value_based_gas = opportunity_value_usd * dec!(0.05) / dec!(2000.0); // Assuming $2000/ETH
        let competitive_gas = base_gas_price * dec!(2.0); // 2x base for MEV protection

        // Use the higher of value-based or competitive gas pricing
        let mev_gas_price = value_based_gas.max(competitive_gas);
        
        info!(
            "MEV-AWARE GAS: Opportunity: ${:.2} | Value-based: {:.2} Gwei | Competitive: {:.2} Gwei | Selected: {:.2} Gwei",
            opportunity_value_usd, value_based_gas, competitive_gas, mev_gas_price
        );

        mev_gas_price
    }
}

/// PHASE 3 ENHANCEMENT: Calculate network congestion score for adaptive gas pricing
fn calculate_network_congestion_score(state: &NetworkResonanceState) -> Decimal {
    let coherence_factor = dec!(1.0) - Decimal::try_from(state.network_coherence_score).unwrap_or(dec!(0.5));
    let sp_time_factor = Decimal::try_from(state.sp_time_ms / 100.0).unwrap_or(dec!(0.5)); // Normalize to 0-1 range
    
    // Combine factors with weights
    let congestion_score = (coherence_factor * dec!(0.6)) + (sp_time_factor * dec!(0.4));
    
    // Ensure score is between 1.0 and 3.0
    dec!(1.0) + (congestion_score * dec!(2.0))
}

/// PHASE 3 ENHANCEMENT: Competitive gas price calculation with Golden Ratio optimization
fn calculate_competitive_gas_price(base_gas_price: Decimal, multiplier: Decimal) -> Decimal {
    // Apply Golden Ratio for optimal competitive positioning
    let golden_ratio = dec!(1.618);
    
    // Use Golden Ratio to find the sweet spot between cost and competitiveness
    let competitive_price = base_gas_price * multiplier;
    let golden_adjustment = if multiplier > dec!(2.0) {
        // In highly competitive situations, use Golden Ratio for precise positioning
        competitive_price / golden_ratio + (competitive_price * dec!(0.382)) // 0.382 is 1/golden_ratio
    } else {
        competitive_price
    };
    
    golden_adjustment
}
