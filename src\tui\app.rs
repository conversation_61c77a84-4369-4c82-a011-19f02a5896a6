// MISSION: TUI Application - The Operator's Command Center
// WHY: Provide real-time visibility into the Basilisk's hunt and system status
// HOW: 4-panel progressive disclosure interface with emergency controls

use anyhow::Result;
use async_nats::Client as NatsClient;
use chrono;
use crossterm::event::{KeyCode, KeyEvent};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::Instant;
use tracing::{error, info};
use rust_decimal::Decimal;
use num_traits::FromPrimitive;

use crate::config::Config;
use crate::data::price_oracle::PriceOracle;
use crate::shared_types::{ServiceStatus, SystemHealth, SystemMetrics, StrategyMetrics, NatsTopics, AethericResonanceScoreDetail};
use crate::shared_types::control_messages::*;
use crate::tui::components::{ErrorDashboard, TuiErrorEntry, NotificationSystem, Notification, NotificationType};
use crate::logging::{ErrorCode, AlertSeverity, TradingContext};
use tokio::sync::mpsc;
use tokio::process::{Child, Command};
use std::process::Stdio;

#[derive(Debug, Clone)]
pub struct LogEvent {
    pub timestamp: String,
    pub source: String,
    pub severity: LogSeverity,
    pub message: String,
}

#[derive(Debug, Clone, PartialEq)]
pub enum LogSeverity {
    Info,
    Warn,
    Error,
    Ok,
    Debug,
}

impl LogSeverity {
    pub fn color(&self) -> ratatui::style::Color {
        match self {
            LogSeverity::Info => ratatui::style::Color::White,
            LogSeverity::Warn => ratatui::style::Color::Yellow,
            LogSeverity::Error => ratatui::style::Color::Red,
            LogSeverity::Ok => ratatui::style::Color::Green,
            LogSeverity::Debug => ratatui::style::Color::Gray,
        }
    }

    pub fn prefix(&self) -> &'static str {
        match self {
            LogSeverity::Info => "INFO",
            LogSeverity::Warn => "WARN",
            LogSeverity::Error => "ERROR",
            LogSeverity::Ok => "OK",
            LogSeverity::Debug => "DEBUG",
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum AppTab {
    Dashboard = 0,   // "The Bridge": High-level situational awareness
    Operations = 1,  // "The Hunt": Live narrative of the Zen Geometer
    Systems = 2,     // "The Engine Room": Engineering health and Network Seismology
    Config = 3,      // "The Tuning Fork": Control panel for strategy parameters
    Errors = 4,      // "The Diagnostics": Error dashboard and debugging tools
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum SystemsPanel {
    Logs,
    Services,
    Components,
    Nodes,
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum OperationsPanel {
    ActivityLog,
    TradeHistory,
    MasterControl,
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum DashboardFocus {
    Network,
    Chronos,
    Mandorla,
    Execution,
}

// Historical data structures for advanced visualizations
const HISTORY_LENGTH: usize = 256; // Store ~4 minutes of data at 1s intervals

#[derive(Debug, Clone)]
pub struct NetworkPropagationData {
    pub timestamp: Instant,
    pub node_reports: Vec<u64>, // Milliseconds from P-Wave for each node
}

#[derive(Debug, Clone)]
pub struct MarketPowerSpectrum {
    pub timestamp: Instant,
    pub frequency_powers: Vec<(String, u64)>, // (timeframe, power)
}

#[derive(Debug, Clone)]
pub struct PnlDataPoint {
    pub timestamp: Instant,
    pub cumulative_pnl: Decimal,
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ConfigPanel {
    Sections,
    Parameters,
    Details,
}

#[derive(Debug, Clone)]
pub struct LiveOpportunity {
    pub scanner: String,
    pub opportunity_type: String,
    pub pair: String,
    pub estimated_profit: Decimal,
    pub timestamp: std::time::Instant,
}

// Operations Tab: Enhanced data structures
#[derive(Debug, Clone)]
pub struct TradeRecord {
    pub id: String,
    pub opportunity_id: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub strategy: String,
    pub assets: String,
    pub profit_loss_token: Decimal,
    pub profit_loss_usd: Decimal,
    pub status: TradeStatus,
    pub transaction_hash: Option<String>,
    pub gas_used: Option<u64>,
    pub gas_price_gwei: Option<Decimal>,
    pub market_regime_at_execution: Option<String>,
    pub aetheric_resonance_at_execution: Option<AethericResonanceScoreDetail>,
    pub execution_details: Option<String>,
    pub trade_explanation: Option<String>,
}

#[derive(Debug, Clone)]
pub enum TradeStatus {
    Success,
    FailedSlippage,
    FailedNetwork,
    FailedGas,
    Pending,
}

impl TradeStatus {
    pub fn color(&self) -> ratatui::style::Color {
        match self {
            TradeStatus::Success => ratatui::style::Color::Green,
            TradeStatus::FailedSlippage => ratatui::style::Color::Red,
            TradeStatus::FailedNetwork => ratatui::style::Color::Red,
            TradeStatus::FailedGas => ratatui::style::Color::Yellow,
            TradeStatus::Pending => ratatui::style::Color::Cyan,
        }
    }
    
    pub fn as_str(&self) -> &'static str {
        match self {
            TradeStatus::Success => "SUCCESS",
            TradeStatus::FailedSlippage => "FAILED: SLIPPAGE",
            TradeStatus::FailedNetwork => "FAILED: NETWORK",
            TradeStatus::FailedGas => "FAILED: GAS",
            TradeStatus::Pending => "PENDING",
        }
    }
}

#[derive(Debug, Clone)]
pub enum BotStatus {
    Running,
    Stopped,
    Paused,
    Error(String),
}

impl BotStatus {
    pub fn color(&self) -> ratatui::style::Color {
        match self {
            BotStatus::Running => ratatui::style::Color::Green,
            BotStatus::Stopped => ratatui::style::Color::Red,
            BotStatus::Paused => ratatui::style::Color::Yellow,
            BotStatus::Error(_) => ratatui::style::Color::Red,
        }
    }
    
    pub fn as_str(&self) -> &str {
        match self {
            BotStatus::Running => "RUNNING",
            BotStatus::Stopped => "STOPPED",
            BotStatus::Paused => "PAUSED",
            BotStatus::Error(e) => e,
        }
    }
}

// Systems Tab: Enhanced component monitoring
#[derive(Debug, Clone)]
pub struct ComponentStatus {
    pub name: String,
    pub status: ServiceStatus,
    pub uptime: std::time::Duration,
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub last_activity: Option<std::time::Instant>,
}

#[derive(Debug, Clone)]
pub struct NetworkNode {
    pub name: String,
    pub url: String,
    pub status: NodeStatus,
    pub latency_ms: Option<u64>,
    pub last_ping: Option<std::time::Instant>,
}

#[derive(Debug, Clone)]
pub enum NodeStatus {
    Connected,
    Disconnected,
    Reconnecting,
    Error(String),
}

impl NodeStatus {
    pub fn color(&self) -> ratatui::style::Color {
        match self {
            NodeStatus::Connected => ratatui::style::Color::Green,
            NodeStatus::Disconnected => ratatui::style::Color::Red,
            NodeStatus::Reconnecting => ratatui::style::Color::Yellow,
            NodeStatus::Error(_) => ratatui::style::Color::Red,
        }
    }
}

// Config Tab: Configuration management
#[derive(Debug, Clone)]
pub struct ConfigSection {
    pub name: String,
    pub parameters: Vec<ConfigParameter>,
    pub description: String,
}

#[derive(Debug, Clone)]
pub struct ConfigParameter {
    pub key: String,
    pub display_name: String,
    pub value: ConfigValue,
    pub default_value: ConfigValue,
    pub description: String,
    pub validation_error: Option<String>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ConfigValue {
    String(String),
    Integer(i64),
    Float(f64),
    Boolean(bool),
    Decimal(Decimal),
}

impl ConfigValue {
    pub fn to_display_string(&self) -> String {
        match self {
            ConfigValue::String(s) => s.clone(),
            ConfigValue::Integer(i) => i.to_string(),
            ConfigValue::Float(f) => format!("{:.6}", f),
            ConfigValue::Boolean(b) => b.to_string(),
            ConfigValue::Decimal(d) => d.to_string(),
        }
    }
}

#[derive(Debug, Clone)]
pub enum ProcessStatus {
    Stopped,
    Starting,
    Running,
    Stopping,
    Error(String),
}

#[derive(Debug)]
pub struct ManagedProcess {
    pub name: String,
    pub binary_path: String,
    pub status: ProcessStatus,
    pub pid: Option<u32>,
    pub child: Option<Child>,
    pub log_buffer: VecDeque<LogEvent>,
    pub started_at: Option<std::time::Instant>,
}

#[derive(Debug)]
pub enum ProcessCommand {
    Start(String),
    Stop(String),
    Restart(String),
}

impl ManagedProcess {
    pub fn new(name: String, binary_path: String) -> Self {
        Self {
            name,
            binary_path,
            status: ProcessStatus::Stopped,
            pid: None,
            child: None,
            log_buffer: VecDeque::with_capacity(100),
            started_at: None,
        }
    }

    pub fn add_log(&mut self, event: LogEvent) {
        self.log_buffer.push_back(event);
        if self.log_buffer.len() > 100 {
            self.log_buffer.pop_front();
        }
    }
}

pub struct App {
    pub running: bool,
    pub config: Arc<Config>,
    pub nats_client: Option<NatsClient>,
    pub active_tab: AppTab,
    pub price_oracle: Arc<PriceOracle>,
    
    // State management
    pub system_status: ServiceStatus,
    pub system_health: SystemHealth,
    pub system_metrics: SystemMetrics,
    pub strategy_metrics: Vec<StrategyMetrics>,
    
    // Event feed
    pub log_events: VecDeque<LogEvent>,
    pub log_buffer: VecDeque<LogEvent>,
    
    // Service management
    pub managed_processes: std::collections::HashMap<String, ManagedProcess>,
    pub selected_service: Option<String>,
    pub process_command_tx: Option<mpsc::UnboundedSender<ProcessCommand>>,
    pub aggregated_logs: VecDeque<LogEvent>,
    pub log_filter: Option<String>,
    
    // Strategy control
    pub selected_strategy_index: Option<usize>,
    pub show_confirmation_modal: bool,
    pub pending_confirmation: Option<crate::tui::components::strategy_control::ConfirmationAction>,
    
    // Operations tab state
    pub selected_opportunity_id: Option<String>,
    pub selected_opportunity_index: Option<usize>,
    pub bot_status: BotStatus,
    pub trade_history: VecDeque<TradeRecord>,
    pub selected_trade_index: Option<usize>,
    pub show_trade_details: bool,
    pub show_full_explanation: bool,
    pub operations_focus_panel: OperationsPanel,
    pub trade_lifecycle_events: VecDeque<crate::shared_types::trade_lifecycle::TradeLifecycleEvent>,
    
    // Configuration management
    pub selected_config_section: Option<usize>,
    pub selected_config_param: Option<usize>,
    pub config_edit_mode: crate::tui::components::configuration_tuning::ConfigEditMode,
    
    // Risk analysis
    pub selected_position_index: Option<usize>,
    pub show_emergency_confirmation: bool,
    pub pending_emergency_action: Option<crate::tui::components::risk_analysis::EmergencyAction>,
    
    // Network management
    pub selected_node_index: Option<usize>,
    
    // Log management
    pub selected_log_source: Option<usize>,
    pub log_scroll_offset: usize,
    pub log_search_mode: bool,
    pub log_search_query: String,
    
    // Systems tab state
    pub systems_focus_panel: SystemsPanel,
    pub selected_service_index: Option<usize>,
    pub service_details_expanded: bool,
    
    // Config tab state
    pub config_focus_panel: ConfigPanel,
    
    // Operational control
    pub current_operational_mode: crate::tui::components::operational_control::OperationalMode,
    pub mode_change_state: crate::tui::components::operational_control::ModeChangeState,
    pub selected_mode_index: Option<usize>,
    pub last_mode_change: Option<std::time::Instant>,
    
    // UI state
    pub tick_counter: u64,
    
    // Live opportunities tracking
    pub live_opportunities: VecDeque<LiveOpportunity>,
    
    // Systems tab enhanced state
    pub component_statuses: Vec<ComponentStatus>,
    pub network_nodes: Vec<NetworkNode>,
    pub selected_component_index: Option<usize>,
    
    // Config tab state
    pub config_sections: Vec<ConfigSection>,
    pub config_modified: bool,
    pub config_validation_errors: Vec<String>,
    
    // Dashboard enhanced state with historical data
    pub dashboard_focus: DashboardFocus,
    pub sp_latency_history: VecDeque<(Instant, u64)>,
    pub pnl_history: VecDeque<PnlDataPoint>,
    pub network_propagation_history: VecDeque<NetworkPropagationData>,
    pub market_spectrum_history: VecDeque<MarketPowerSpectrum>,
    pub last_block_propagation: Option<Vec<u64>>, // Most recent block propagation data
    
    // Real ARE component data (when available)
    pub latest_temporal_harmonics: Option<crate::shared_types::TemporalHarmonics>,
    pub latest_network_resonance: Option<crate::shared_types::NetworkResonanceState>,
    pub latest_geometric_score: Option<crate::shared_types::GeometricScore>,
    pub latest_fractal_analysis: Option<crate::shared_types::FractalAnalysisReport>,
    pub latest_network_seismology: Option<crate::shared_types::NetworkSeismologyReport>,
    
    // Data availability indicators
    pub are_data_status: AREDataStatus,
    
    // Strategy Inspector state
    pub show_inspector: bool,
    pub inspection_data: Option<crate::inspector::InspectableOpportunity>,
    pub inspection_cache: std::sync::Arc<std::sync::Mutex<std::collections::HashMap<uuid::Uuid, crate::inspector::InspectableOpportunity>>>,
    
    // Control flow state
    pub pending_commands: HashMap<uuid::Uuid, PendingCommand>,
    pub command_metrics: CommandMetrics,
    pub service_heartbeats: HashMap<String, std::time::Instant>,
    pub nats_update_receiver: Option<tokio::sync::mpsc::UnboundedReceiver<NatsUpdate>>,
    
    // Enhanced Error Handling and Operator Feedback
    pub error_dashboard: ErrorDashboard,
    pub notification_system: NotificationSystem,
    pub debug_mode: bool,
    pub show_error_details: bool,
}

#[derive(Debug, Clone)]
pub struct AREDataStatus {
    pub temporal_harmonics_available: bool,
    pub network_resonance_available: bool,
    pub geometric_score_available: bool,
    pub fractal_analysis_available: bool,
    pub network_seismology_available: bool,
    pub last_update: Option<Instant>,
}

impl Default for AREDataStatus {
    fn default() -> Self {
        Self {
            temporal_harmonics_available: false,
            network_resonance_available: false,
            geometric_score_available: false,
            fractal_analysis_available: false,
            network_seismology_available: false,
            last_update: None,
        }
    }
}

#[derive(Debug, Clone)]
pub struct PendingCommand {
    pub id: uuid::Uuid,
    pub command_type: String,
    pub target: String,
    pub sent_at: std::time::Instant,
    pub timeout_seconds: u64,
}

#[derive(Debug, Clone)]
pub struct CommandMetrics {
    pub total_sent: u64,
    pub total_acknowledged: u64,
    pub total_failed: u64,
    pub total_timed_out: u64,
    pub average_response_time_ms: f64,
    pub last_reset: std::time::Instant,
}

impl Default for CommandMetrics {
    fn default() -> Self {
        Self {
            total_sent: 0,
            total_acknowledged: 0,
            total_failed: 0,
            total_timed_out: 0,
            average_response_time_ms: 0.0,
            last_reset: std::time::Instant::now(),
        }
    }
}

#[derive(Debug, Clone)]
pub enum NatsUpdate {
    Treasury(TreasuryUpdate),
    Balance(TreasuryUpdate),
    Opportunity(OpportunityDetected),
    Trade(TradeCompleted),
    Health(SystemHealthUpdate),
    Config(serde_json::Value),
    CommandAck(CommandAcknowledgment),
    TradeLifecycle(crate::shared_types::trade_lifecycle::TradeLifecycleEvent),
}

impl App {
    pub fn new(
        config: Arc<Config>,
        nats_client: NatsClient,
        price_oracle: Arc<PriceOracle>,
    ) -> Self {
        Self {
            running: true,
            config,
            nats_client: Some(nats_client),
            active_tab: AppTab::Dashboard,
            price_oracle,
            debug_mode: false,
            error_dashboard: ErrorDashboard::new(),
            notification_system: NotificationSystem::new(),
            show_full_explanation: false,
            trade_lifecycle_events: VecDeque::new(),
            show_error_details: false,
            
            // Initialize state
            system_status: ServiceStatus::Initializing,
            system_health: SystemHealth {
                rpc_node: ServiceStatus::Initializing,
                nats_bus: ServiceStatus::Initializing,
                database: ServiceStatus::Initializing,
                redis: ServiceStatus::Initializing,
            },
            system_metrics: SystemMetrics {
                total_pnl_24h: Decimal::ZERO,
                total_trades_24h: 0,
                active_strategies: 3,
                total_strategies: 3,
                gas_cost_24h: Decimal::ZERO,
            },
            strategy_metrics: vec![],
            
            // Event feed
            log_events: VecDeque::with_capacity(1000),
            log_buffer: VecDeque::with_capacity(1000),
            
            // Service management
            managed_processes: std::collections::HashMap::new(),
            selected_service: None,
            process_command_tx: None,
            aggregated_logs: VecDeque::with_capacity(1000),
            log_filter: None,
            
            // Strategy control
            selected_strategy_index: None,
            show_confirmation_modal: false,
            pending_confirmation: None,
            
            // Operations tab state
            selected_opportunity_id: None,
            
            // Trade analysis
            selected_trade_index: None,
            
            // Configuration management
            selected_config_section: None,
            selected_config_param: None,
            config_edit_mode: crate::tui::components::configuration_tuning::ConfigEditMode::None,
            
            // Risk analysis
            selected_position_index: None,
            show_emergency_confirmation: false,
            pending_emergency_action: None,
            
            // Network management
            selected_node_index: None,
            
            // Log management
            selected_log_source: None,
            log_scroll_offset: 0,
            log_search_mode: false,
            log_search_query: String::new(),
            
            // Systems tab state
            systems_focus_panel: SystemsPanel::Logs,
            selected_service_index: None,
            service_details_expanded: false,
            
            // Config tab state
            config_focus_panel: ConfigPanel::Sections,
            
            // Operational control
            current_operational_mode: crate::tui::components::operational_control::OperationalMode::FullTrading,
            mode_change_state: crate::tui::components::operational_control::ModeChangeState::None,
            selected_mode_index: None,
            last_mode_change: None,
            
            // UI state
            tick_counter: 0,
            
            // Live opportunities
            live_opportunities: VecDeque::with_capacity(50),
            
            // Operations tab enhanced state
            selected_opportunity_index: None,
            bot_status: BotStatus::Running,
            trade_history: VecDeque::with_capacity(100),
            show_trade_details: false,
            operations_focus_panel: OperationsPanel::ActivityLog,
            
            // Systems tab enhanced state
            component_statuses: Vec::new(),
            network_nodes: Vec::new(),
            selected_component_index: None,
            
            // Config tab state
            config_sections: Vec::new(),
            config_modified: false,
            config_validation_errors: Vec::new(),
            
            // Dashboard enhanced state
            dashboard_focus: DashboardFocus::Network,
            sp_latency_history: VecDeque::with_capacity(HISTORY_LENGTH),
            pnl_history: VecDeque::with_capacity(HISTORY_LENGTH),
            network_propagation_history: VecDeque::with_capacity(HISTORY_LENGTH),
            market_spectrum_history: VecDeque::with_capacity(HISTORY_LENGTH),
            last_block_propagation: None,
            
            // Real ARE component data
            latest_temporal_harmonics: None,
            latest_network_resonance: None,
            latest_geometric_score: None,
            latest_fractal_analysis: None,
            latest_network_seismology: None,
            are_data_status: AREDataStatus::default(),
            
            // Strategy Inspector state
            show_inspector: false,
            inspection_data: None,
            inspection_cache: std::sync::Arc::new(std::sync::Mutex::new(std::collections::HashMap::new())),
            
            // Control flow state
            pending_commands: HashMap::new(),
            command_metrics: CommandMetrics::default(),
            service_heartbeats: HashMap::new(),
            nats_update_receiver: None,
        }
    }

    // Constructor for TUI harness testing
    pub fn new_harness(config: Arc<Config>, nats_client: NatsClient) -> Self {
        use rust_decimal::Decimal;
        
        Self {
            running: true,
            config,
            nats_client: Some(nats_client),
            active_tab: AppTab::Dashboard,
            price_oracle: Arc::new(crate::data::price_oracle::PriceOracle::new(
                Arc::new(ethers::providers::Provider::<ethers::providers::Http>::try_from("https://mainnet.base.org").unwrap()),
                std::collections::HashMap::new()
            )),
            debug_mode: false,
            show_full_explanation: false,
            trade_lifecycle_events: VecDeque::new(),
            error_dashboard: ErrorDashboard::new(),
            notification_system: NotificationSystem::new(),
            show_error_details: false,
            
            // Initialize state
            system_status: ServiceStatus::Initializing,
            system_health: SystemHealth {
                rpc_node: ServiceStatus::Initializing,
                nats_bus: ServiceStatus::Initializing,
                database: ServiceStatus::Initializing,
                redis: ServiceStatus::Initializing,
            },
            system_metrics: SystemMetrics {
                total_pnl_24h: Decimal::ZERO,
                total_trades_24h: 0,
                active_strategies: 3,
                total_strategies: 3,
                gas_cost_24h: Decimal::ZERO,
            },
            strategy_metrics: vec![],
            
            // Event feed
            log_events: VecDeque::with_capacity(1000),
            log_buffer: VecDeque::with_capacity(1000),
            
            // Service management
            managed_processes: HashMap::new(),
            selected_service: None,
            process_command_tx: None,
            aggregated_logs: VecDeque::with_capacity(1000),
            log_filter: None,
            
            // Strategy control
            selected_strategy_index: None,
            show_confirmation_modal: false,
            pending_confirmation: None,
            
            // Operations tab state
            selected_opportunity_id: None,
            
            // Trade analysis
            selected_trade_index: None,
            
            // Configuration management
            selected_config_section: None,
            selected_config_param: None,
            config_edit_mode: crate::tui::components::configuration_tuning::ConfigEditMode::None,
            
            // Risk analysis
            selected_position_index: None,
            show_emergency_confirmation: false,
            pending_emergency_action: None,
            
            // Network management
            selected_node_index: None,
            
            // Log management
            selected_log_source: None,
            log_scroll_offset: 0,
            log_search_mode: false,
            log_search_query: String::new(),
            
            // Systems tab state
            systems_focus_panel: SystemsPanel::Logs,
            selected_service_index: None,
            service_details_expanded: false,
            
            // Config tab state
            config_focus_panel: ConfigPanel::Sections,
            
            // Operational control
            current_operational_mode: crate::tui::components::operational_control::OperationalMode::FullTrading,
            mode_change_state: crate::tui::components::operational_control::ModeChangeState::None,
            selected_mode_index: None,
            last_mode_change: None,
            
            // UI state
            tick_counter: 0,
            
            // Live opportunities
            live_opportunities: VecDeque::with_capacity(50),
            
            // Operations tab enhanced state
            selected_opportunity_index: None,
            bot_status: BotStatus::Running,
            trade_history: VecDeque::with_capacity(100),
            show_trade_details: false,
            operations_focus_panel: OperationsPanel::ActivityLog,
            
            // Systems tab enhanced state
            component_statuses: Vec::new(),
            network_nodes: Vec::new(),
            selected_component_index: None,
            
            // Config tab state
            config_sections: Vec::new(),
            config_modified: false,
            config_validation_errors: Vec::new(),
            
            // Dashboard enhanced state
            dashboard_focus: DashboardFocus::Network,
            sp_latency_history: VecDeque::with_capacity(HISTORY_LENGTH),
            pnl_history: VecDeque::with_capacity(HISTORY_LENGTH),
            network_propagation_history: VecDeque::with_capacity(HISTORY_LENGTH),
            market_spectrum_history: VecDeque::with_capacity(HISTORY_LENGTH),
            last_block_propagation: None,
            
            // Real ARE component data
            latest_temporal_harmonics: None,
            latest_network_resonance: None,
            latest_geometric_score: None,
            latest_fractal_analysis: None,
            latest_network_seismology: None,
            are_data_status: AREDataStatus::default(),
            
            // Strategy Inspector state
            show_inspector: false,
            inspection_data: None,
            inspection_cache: std::sync::Arc::new(std::sync::Mutex::new(std::collections::HashMap::new())),
            
            // Control flow state
            pending_commands: HashMap::new(),
            command_metrics: CommandMetrics::default(),
            service_heartbeats: HashMap::new(),
            nats_update_receiver: None,
        }
    }

    // Strategy Inspector Methods
    pub fn get_selected_opportunity_id(&self) -> Option<uuid::Uuid> {
        // Get the currently selected opportunity from the live opportunities list
        if let Some(index) = self.selected_opportunity_index {
            if index < self.live_opportunities.len() {
                // Generate a mock UUID based on the index for demonstration
                // In production, opportunities would have real UUIDs
                let mock_uuid = uuid::Uuid::new_v4();
                return Some(mock_uuid);
            }
        }
        None
    }

    pub fn trigger_inspector(&mut self) {
        if let Some(selected_id) = self.get_selected_opportunity_id() {
            // Try to get inspection data from cache
            let cache_result = {
                if let Ok(cache) = self.inspection_cache.lock() {
                    cache.get(&selected_id).cloned()
                } else {
                    None
                }
            };
            
            if let Some(data) = cache_result {
                self.inspection_data = Some(data);
                self.show_inspector = true;
            } else {
                // Create mock inspection data for demonstration
                self.create_mock_inspection_data(selected_id);
            }
        }
    }

    pub fn close_inspector(&mut self) {
        self.show_inspector = false;
        self.inspection_data = None;
    }

    fn create_mock_inspection_data(&mut self, id: uuid::Uuid) {
        use crate::inspector::*;
        use rust_decimal_macros::dec;

        // Create detailed pillar score breakdown
        let details = vec![
            PillarScoreDetail::new_mandorla_gauge(0.8245, 1.618, 0.92),
            PillarScoreDetail::new_chronos_sieve(0.7834, 12.5, "HARMONIC"),
            PillarScoreDetail::new_network_seismology(0.9123, 145, "COHERENT"),
        ];

        let breakdown = ScoreBreakdown::new(details);
        
        let opportunity = InspectableOpportunity::new(
            id,
            "ARB: 1.5 ETH -> WETH -> USDC -> ETH (Base -> Arbitrum)".to_string(),
            breakdown,
            dec!(125.50), // Profit estimate
            dec!(28.75),  // Gas estimate
        );

        // Store in cache
        if let Ok(mut cache) = self.inspection_cache.lock() {
            cache.insert(id, opportunity.clone());
        }

        self.inspection_data = Some(opportunity);
        self.show_inspector = true;
    }

    pub fn execute_inspected_opportunity(&mut self) {
        if let Some(ref data) = self.inspection_data {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "INSPECTOR".to_string(),
                severity: LogSeverity::Ok,
                message: format!("Executing opportunity {} - Est. Profit: ${:.2}", data.id, data.net_profit_usd),
            });
            self.close_inspector();
        }
    }

    pub fn reject_inspected_opportunity(&mut self) {
        if let Some(ref data) = self.inspection_data {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "INSPECTOR".to_string(),
                severity: LogSeverity::Warn,
                message: format!("Rejected opportunity {} - Manual override", data.id),
            });
            self.close_inspector();
        }
    }

    // ARE Component Data Update Methods
    pub fn update_temporal_harmonics(&mut self, harmonics: crate::shared_types::TemporalHarmonics) {
        self.latest_temporal_harmonics = Some(harmonics);
        self.are_data_status.temporal_harmonics_available = true;
        self.are_data_status.last_update = Some(Instant::now());
        
        // Add to historical data for sparklines
        if let Some((freq, _power)) = self.latest_temporal_harmonics.as_ref()
            .and_then(|h| h.dominant_cycles_minutes.first()) {
            self.add_market_spectrum_data(vec![
                ("1m".to_string(), 5),
                ("5m".to_string(), 12),
                ("15m".to_string(), (*freq as u64).min(20)),
                ("60m".to_string(), 2),
            ]);
        }
    }

    pub fn update_network_resonance(&mut self, resonance: crate::shared_types::NetworkResonanceState) {
        self.latest_network_resonance = Some(resonance);
        self.are_data_status.network_resonance_available = true;
        self.are_data_status.last_update = Some(Instant::now());
    }

    pub fn update_geometric_score(&mut self, score: crate::shared_types::GeometricScore) {
        self.latest_geometric_score = Some(score);
        self.are_data_status.geometric_score_available = true;
        self.are_data_status.last_update = Some(Instant::now());
    }

    pub fn update_fractal_analysis(&mut self, analysis: crate::shared_types::FractalAnalysisReport) {
        self.latest_fractal_analysis = Some(analysis);
        self.are_data_status.fractal_analysis_available = true;
        self.are_data_status.last_update = Some(Instant::now());
    }

    pub fn update_network_seismology(&mut self, seismology: crate::shared_types::NetworkSeismologyReport) {
        // Extract S-P latency for historical tracking
        if let Some(ref tti) = seismology.tti_stats {
            self.add_sp_latency_datapoint(tti.avg_tti_ms as u64);
        }
        
        self.latest_network_seismology = Some(seismology);
        self.are_data_status.network_seismology_available = true;
        self.are_data_status.last_update = Some(Instant::now());
    }

    // Check if real data is stale (older than 30 seconds)
    pub fn is_are_data_stale(&self) -> bool {
        match self.are_data_status.last_update {
            Some(last_update) => last_update.elapsed().as_secs() > 30,
            None => true,
        }
    }

    // Process live market data from NATS
    pub fn process_market_data(&mut self, pair: String, price: f64, volume: f64) {
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "MARKET_DATA".to_string(),
            severity: LogSeverity::Info,
            message: format!("{}: ${:.2} (Vol: {:.3})", pair, price, volume),
        });
    }

    // Process network block data from NATS
    pub fn process_network_block(&mut self, block_number: u64, timestamp: u64, gas_used: u64) {
        // Mock network propagation data based on block timing
        let mock_propagation = vec![50, 75, 100, 125, 150, 200]; // Mock node report times
        self.add_network_propagation_data(mock_propagation);
        
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "NETWORK".to_string(),
            severity: LogSeverity::Info,
            message: format!("Block #{} | Gas: {:.1}M", block_number, gas_used as f64 / 1_000_000.0),
        });
    }

    // Process gas price updates from NATS
    pub fn process_gas_price(&mut self, price_gwei: f64, _timestamp: u64) {
        let severity = if price_gwei > 50.0 {
            LogSeverity::Warn
        } else {
            LogSeverity::Info
        };
        
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "GAS_TRACKER".to_string(),
            severity,
            message: format!("Gas: {:.1} gwei", price_gwei),
        });
    }

    // Dashboard data aggregation methods
    pub fn add_sp_latency_datapoint(&mut self, latency_ms: u64) {
        if self.sp_latency_history.len() == HISTORY_LENGTH {
            self.sp_latency_history.pop_front();
        }
        self.sp_latency_history.push_back((Instant::now(), latency_ms));
    }

    pub fn add_pnl_datapoint(&mut self, pnl: Decimal) {
        if self.pnl_history.len() == HISTORY_LENGTH {
            self.pnl_history.pop_front();
        }
        self.pnl_history.push_back(PnlDataPoint {
            timestamp: Instant::now(),
            cumulative_pnl: pnl,
        });
    }

    pub fn add_network_propagation_data(&mut self, node_reports: Vec<u64>) {
        if self.network_propagation_history.len() == HISTORY_LENGTH {
            self.network_propagation_history.pop_front();
        }
        
        let propagation_data = NetworkPropagationData {
            timestamp: Instant::now(),
            node_reports: node_reports.clone(),
        };
        
        self.network_propagation_history.push_back(propagation_data);
        self.last_block_propagation = Some(node_reports);
    }

    pub fn add_market_spectrum_data(&mut self, frequency_powers: Vec<(String, u64)>) {
        if self.market_spectrum_history.len() == HISTORY_LENGTH {
            self.market_spectrum_history.pop_front();
        }
        
        self.market_spectrum_history.push_back(MarketPowerSpectrum {
            timestamp: Instant::now(),
            frequency_powers,
        });
    }

    // Dashboard focus management
    pub fn cycle_dashboard_focus(&mut self) {
        self.dashboard_focus = match self.dashboard_focus {
            DashboardFocus::Network => DashboardFocus::Chronos,
            DashboardFocus::Chronos => DashboardFocus::Mandorla,
            DashboardFocus::Mandorla => DashboardFocus::Execution,
            DashboardFocus::Execution => DashboardFocus::Network,
        };
    }

    pub fn handle_dashboard_drill_down(&mut self) {
        match self.dashboard_focus {
            DashboardFocus::Network => {
                // Switch to Systems tab for detailed network analysis
                self.active_tab = AppTab::Systems;
                self.systems_focus_panel = SystemsPanel::Nodes;
            }
            DashboardFocus::Execution => {
                // Switch to Operations tab for trade history
                self.active_tab = AppTab::Operations;
                self.operations_focus_panel = OperationsPanel::TradeHistory;
            }
            DashboardFocus::Mandorla => {
                // Switch to Operations tab for opportunity analysis
                self.active_tab = AppTab::Operations;
                self.operations_focus_panel = OperationsPanel::ActivityLog;
            }
            DashboardFocus::Chronos => {
                // Stay on dashboard but log the drill-down attempt
                self.add_log_event(LogEvent {
                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                    source: "CHRONOS_SIEVE".to_string(),
                    severity: LogSeverity::Info,
                    message: "Detailed FFT analysis view - coming soon".to_string(),
                });
            }
        }
    }

    pub fn handle_operations_key(&mut self, key: crossterm::event::KeyCode) {
        match key {
            // Master Control Operations
            crossterm::event::KeyCode::Char('s') | crossterm::event::KeyCode::Char('S') => {
                self.toggle_bot_status();
            }
            crossterm::event::KeyCode::Char('p') | crossterm::event::KeyCode::Char('P') => {
                self.pause_bot();
            }
            crossterm::event::KeyCode::Char('r') | crossterm::event::KeyCode::Char('R') => {
                self.restart_bot();
            }
            crossterm::event::KeyCode::Char('e') | crossterm::event::KeyCode::Char('E') => {
                self.emergency_stop();
            }
            crossterm::event::KeyCode::Char('g') | crossterm::event::KeyCode::Char('G') => {
                self.graceful_stop();
            }
            
            // Panel Navigation
            crossterm::event::KeyCode::Tab => {
                self.operations_focus_panel = match self.operations_focus_panel {
                    super::app::OperationsPanel::ActivityLog => super::app::OperationsPanel::TradeHistory,
                    super::app::OperationsPanel::TradeHistory => super::app::OperationsPanel::MasterControl,
                    super::app::OperationsPanel::MasterControl => super::app::OperationsPanel::ActivityLog,
                };
            }
            
            // Trade History Navigation
            crossterm::event::KeyCode::Up => {
                if self.operations_focus_panel == super::app::OperationsPanel::TradeHistory {
                    self.select_previous_trade_ops();
                }
            }
            crossterm::event::KeyCode::Down => {
                if self.operations_focus_panel == super::app::OperationsPanel::TradeHistory {
                    self.select_next_trade_ops();
                }
            }
            crossterm::event::KeyCode::Enter => {
                if self.operations_focus_panel == super::app::OperationsPanel::TradeHistory {
                    self.toggle_trade_details();
                }
            }
            crossterm::event::KeyCode::Char('x') | crossterm::event::KeyCode::Char('X') => {
                if self.operations_focus_panel == super::app::OperationsPanel::TradeHistory && self.selected_trade_index.is_some() {
                    self.show_full_explanation = true;
                }
            }
            crossterm::event::KeyCode::Esc => {
                if self.show_full_explanation {
                    self.show_full_explanation = false;
                } else if self.show_trade_details {
                    self.show_trade_details = false;
                }
            }
            
            // Log Filtering
            crossterm::event::KeyCode::Char('f') | crossterm::event::KeyCode::Char('F') => {
                self.toggle_activity_log_filter();
            }
            crossterm::event::KeyCode::Char('c') | crossterm::event::KeyCode::Char('C') => {
                self.clear_activity_log_filter();
            }
            
            _ => {}
        }
    }

    pub fn handle_systems_key(&mut self, key: crossterm::event::KeyCode) {
        match key {
            // Panel Navigation
            crossterm::event::KeyCode::Tab => {
                self.systems_focus_panel = match self.systems_focus_panel {
                    SystemsPanel::Logs => SystemsPanel::Components,
                    SystemsPanel::Components => SystemsPanel::Services,
                    SystemsPanel::Services => SystemsPanel::Nodes,
                    SystemsPanel::Nodes => SystemsPanel::Logs,
                };
            }
            
            // Navigation within panels
            crossterm::event::KeyCode::Up => {
                match self.systems_focus_panel {
                    SystemsPanel::Logs => self.scroll_logs_up(),
                    SystemsPanel::Components => self.select_previous_component(),
                    SystemsPanel::Services => self.select_previous_service_systems(),
                    SystemsPanel::Nodes => self.select_previous_node_ops(),
                }
            }
            crossterm::event::KeyCode::Down => {
                match self.systems_focus_panel {
                    SystemsPanel::Logs => self.scroll_logs_down(),
                    SystemsPanel::Components => self.select_next_component(),
                    SystemsPanel::Services => self.select_next_service_systems(),
                    SystemsPanel::Nodes => self.select_next_node_ops(),
                }
            }
            
            // Page navigation for logs
            crossterm::event::KeyCode::PageUp => {
                if self.systems_focus_panel == SystemsPanel::Logs {
                    self.scroll_logs_page_up();
                }
            }
            crossterm::event::KeyCode::PageDown => {
                if self.systems_focus_panel == SystemsPanel::Logs {
                    self.scroll_logs_page_down();
                }
            }
            
            // Action keys
            crossterm::event::KeyCode::Enter => {
                match self.systems_focus_panel {
                    SystemsPanel::Components => self.show_component_details(),
                    SystemsPanel::Services => self.toggle_service_details(),
                    SystemsPanel::Nodes => self.ping_selected_node(),
                    _ => {}
                }
            }
            
            // Component/Service/Node controls
            crossterm::event::KeyCode::Char('r') | crossterm::event::KeyCode::Char('R') => {
                match self.systems_focus_panel {
                    SystemsPanel::Components => self.restart_selected_component(),
                    SystemsPanel::Services => self.restart_selected_service(),
                    SystemsPanel::Nodes => self.reconnect_selected_node(),
                    _ => {}
                }
            }
            crossterm::event::KeyCode::Char('s') | crossterm::event::KeyCode::Char('S') => {
                match self.systems_focus_panel {
                    SystemsPanel::Services => self.start_selected_service(),
                    _ => {}
                }
            }
            crossterm::event::KeyCode::Char('k') | crossterm::event::KeyCode::Char('K') => {
                match self.systems_focus_panel {
                    SystemsPanel::Services => self.stop_selected_service(),
                    _ => {}
                }
            }
            
            // Log filtering and search
            crossterm::event::KeyCode::Char('f') | crossterm::event::KeyCode::Char('F') => {
                self.handle_logs_key(key);
            }
            crossterm::event::KeyCode::Char('c') | crossterm::event::KeyCode::Char('C') => {
                self.handle_logs_key(key);
            }
            crossterm::event::KeyCode::Char('/') => {
                self.start_log_search();
            }
            
            // Export logs
            crossterm::event::KeyCode::Char('e') | crossterm::event::KeyCode::Char('E') => {
                self.export_logs();
            }
            
            // Clear all logs
            crossterm::event::KeyCode::Char('x') | crossterm::event::KeyCode::Char('X') => {
                self.clear_all_logs();
            }
            
            _ => {}
        }
    }

    pub async fn init(&mut self) -> Result<()> {
        // Initialize service management
        self.initialize_services_main();
        
        // Start NATS subscription listener
        self.spawn_nats_listener().await?;
        
        // Initialize the app with some starting data
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "SYSTEM".to_string(),
            severity: LogSeverity::Ok,
            message: "ZEN GEOMETER initialized with NATS connectivity".to_string(),
        });
        
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "GAZE".to_string(),
            severity: LogSeverity::Info,
            message: "Scanner initialized - monitoring DEX arbitrage opportunities".to_string(),
        });
        
        Ok(())
    }

    /// Spawn NATS subscription listener for real-time data updates
    async fn spawn_nats_listener(&mut self) -> Result<()> {
        if let Some(nats_client) = self.nats_client.clone() {
            info!("Starting NATS subscription listener for TUI");
            
            // Create channels for sending updates to the main TUI thread
            let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel::<NatsUpdate>();
            
            // Subscribe to all required topics
            let mut treasury_sub = nats_client.subscribe("state.treasury").await?;
            let mut balance_sub = nats_client.subscribe("state.balances").await?;
            let mut opportunity_sub = nats_client.subscribe("log.opportunities.degen").await?;
            let mut trade_sub = nats_client.subscribe("execution.trade.completed").await?;
            let mut health_sub = nats_client.subscribe("state.system_health").await?;
            let mut config_sub = nats_client.subscribe("control.config.update").await?;
            let mut ack_sub = nats_client.subscribe("control.command.ack").await?;
            let mut trade_lifecycle_sub = nats_client.subscribe(NatsTopics::LIVING_CODEX_TRADE_LIFECYCLE).await?;
            
            // Spawn background task to process NATS messages
            tokio::spawn(async move {
                use tokio_stream::StreamExt;
                
                loop {
                    tokio::select! {
                        Some(msg) = treasury_sub.next() => {
                            if let Ok(update) = serde_json::from_slice::<TreasuryUpdate>(&msg.payload) {
                                let _ = tx.send(NatsUpdate::Treasury(update));
                            }
                        }
                        Some(msg) = balance_sub.next() => {
                            if let Ok(update) = serde_json::from_slice::<TreasuryUpdate>(&msg.payload) {
                                let _ = tx.send(NatsUpdate::Balance(update));
                            }
                        }
                        Some(msg) = opportunity_sub.next() => {
                            if let Ok(opp) = serde_json::from_slice::<OpportunityDetected>(&msg.payload) {
                                let _ = tx.send(NatsUpdate::Opportunity(opp));
                            }
                        }
                        Some(msg) = trade_sub.next() => {
                            if let Ok(trade) = serde_json::from_slice::<TradeCompleted>(&msg.payload) {
                                let _ = tx.send(NatsUpdate::Trade(trade));
                            }
                        }
                        Some(msg) = health_sub.next() => {
                            if let Ok(health) = serde_json::from_slice::<SystemHealthUpdate>(&msg.payload) {
                                let _ = tx.send(NatsUpdate::Health(health));
                            }
                        }
                        Some(msg) = config_sub.next() => {
                            if let Ok(config) = serde_json::from_slice::<serde_json::Value>(&msg.payload) {
                                let _ = tx.send(NatsUpdate::Config(config));
                            }
                        }
                        Some(msg) = ack_sub.next() => {
                            if let Ok(ack) = serde_json::from_slice::<CommandAcknowledgment>(&msg.payload) {
                                let _ = tx.send(NatsUpdate::CommandAck(ack));
                            }
                        }
                        Some(msg) = trade_lifecycle_sub.next() => {
                            if let Ok(event) = serde_json::from_slice::<crate::shared_types::trade_lifecycle::TradeLifecycleEvent>(&msg.payload) {
                                let _ = tx.send(NatsUpdate::TradeLifecycle(event));
                            }
                        }
                        else => break,
                    }
                }
            });
            
            // Store the receiver for processing updates in the main loop
            self.nats_update_receiver = Some(rx);
            
            info!("NATS subscription listener started successfully");
        } else {
            error!("No NATS client available for TUI subscriptions");
        }
        
        Ok(())
    }

    /// Initialize service management infrastructure
    pub fn initialize_services_main(&mut self) {
        // Initialize service heartbeat tracking
        let services = vec![
            "data_ingestor", "listener", "feature_exporter", "graph_analyzer",
            "network_observer", "seismic_analyzer", "execution_manager", "strategy_manager"
        ];
        
        let now = std::time::Instant::now();
        for service in services {
            self.service_heartbeats.insert(service.to_string(), now);
        }
        
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "SERVICE_MANAGER".to_string(),
            severity: LogSeverity::Info,
            message: "Service management initialized".to_string(),
        });
    }

    pub fn add_log_event(&mut self, event: LogEvent) {
        self.log_events.push_back(event.clone());
        if self.log_events.len() > 1000 {
            self.log_events.pop_front();
        }
        
        self.log_buffer.push_back(event);
        if self.log_buffer.len() > 1000 {
            self.log_buffer.pop_front();
        }
    }

    pub fn handle_key(&mut self, key: KeyEvent) {
        match key.code {
            KeyCode::Char('q') | KeyCode::Char('Q') => {
                self.running = false;
            }
            KeyCode::Tab => {
                self.active_tab = match self.active_tab {
                    AppTab::Dashboard => AppTab::Operations,
                    AppTab::Operations => AppTab::Systems,
                    AppTab::Systems => AppTab::Config,
                    AppTab::Config => AppTab::Errors,
                    AppTab::Errors => AppTab::Dashboard,
                };
            }
            KeyCode::Char('1') => self.active_tab = AppTab::Dashboard,
            KeyCode::Char('2') => self.active_tab = AppTab::Operations,
            KeyCode::Char('3') => self.active_tab = AppTab::Systems,
            KeyCode::Char('4') => self.active_tab = AppTab::Config,
            
            // Dashboard-specific controls when on Dashboard tab
            _ if self.active_tab == AppTab::Dashboard => {
                match key.code {
                    KeyCode::Tab => self.cycle_dashboard_focus(),
                    KeyCode::Enter => self.handle_dashboard_drill_down(),
                    _ => {}
                }
            }
            
            // Strategy Inspector controls (global)
            KeyCode::Char('i') | KeyCode::Char('I') if !self.show_inspector => {
                self.trigger_inspector();
            }
            KeyCode::Esc if self.show_inspector => {
                self.close_inspector();
            }
            KeyCode::Char('e') | KeyCode::Char('E') if self.show_inspector => {
                self.execute_inspected_opportunity();
            }
            KeyCode::Char('r') | KeyCode::Char('R') if self.show_inspector => {
                self.reject_inspected_opportunity();
            }
            _ => {
                // Handle tab-specific keys
                match self.active_tab {
                    AppTab::Dashboard => self.handle_dashboard_key(key.code),
                    AppTab::Operations => self.handle_operations_key(key.code),
                    AppTab::Systems => self.handle_systems_key(key.code),
                    AppTab::Config => self.handle_config_key(key.code),
                    AppTab::Errors => self.handle_errors_key(key.code),
                }
            }
        }
    }

    pub fn handle_logs_key(&mut self, key: KeyCode) {
        match key {
            KeyCode::Char('f') | KeyCode::Char('F') => {
                // TODO: Implement filter input modal
                // For now, just toggle between no filter and a sample filter
                if self.log_filter.is_none() {
                    self.log_filter = Some("data_ingestor".to_string());
                } else {
                    self.log_filter = None;
                }
            }
            KeyCode::Char('c') | KeyCode::Char('C') => {
                self.log_filter = None;
            }
            _ => {}
        }
    }

    pub async fn update(&mut self) -> Result<()> {
        use rand::Rng;
        
        self.tick_counter += 1;
        
        // Process NATS updates first
        self.process_nats_updates().await;
        
        // Check for command timeouts
        self.check_command_timeouts();
        
        // Generate realistic trading simulation data
        let mut rng = rand::thread_rng();
        
        // Generate new opportunities every 20-50 ticks (2-5 seconds)
        if self.tick_counter % rng.gen_range(20..=50) == 0 {
            let scanners = ["GAZE", "PILOT_FISH", "MEMPOOL", "LIQUIDATION"];
            let pairs = ["WETH/USDC", "DEGEN/WETH", "USDC/DAI", "WBTC/WETH", "ARB/USDC"];
            let opportunity_types = ["DEX Arbitrage", "Flash Loan", "Liquidation", "MEV"];
            
            let scanner = scanners[rng.gen_range(0..scanners.len())];
            let pair = pairs[rng.gen_range(0..pairs.len())];
            let opportunity_type = opportunity_types[rng.gen_range(0..opportunity_types.len())];
            
            let profit = Decimal::from_f64(rng.gen_range(5.0..150.0)).unwrap_or_default();
            
            let opportunity = LiveOpportunity {
                scanner: scanner.to_string(),
                opportunity_type: opportunity_type.to_string(),
                pair: pair.to_string(),
                estimated_profit: profit,
                timestamp: Instant::now(),
            };
            
            self.live_opportunities.push_front(opportunity.clone());
            if self.live_opportunities.len() > 20 {
                self.live_opportunities.pop_back();
            }
            
            // Add log for the opportunity
            let profit_color = if profit > Decimal::from_f64(20.0).unwrap_or_default() { "HIGH" } else { "MED" };
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: scanner.to_string(),
                severity: LogSeverity::Ok,
                message: format!("TARGET {} opportunity: {} profit ${:.2} [{}]", opportunity_type, pair, profit, profit_color),
            });
        }
        
        // Simulate trade executions every 100-200 ticks (10-20 seconds)
        if self.tick_counter % rng.gen_range(100..=200) == 0 {
            let trade_profit = Decimal::from_f64(rng.gen_range(8.0..45.0)).unwrap_or_default();
            let gas_cost = Decimal::from_f64(rng.gen_range(1.5..8.0)).unwrap_or_default();
            let net_profit = trade_profit - gas_cost;
            
            // Update system metrics
            self.system_metrics.total_trades_24h += 1;
            self.system_metrics.total_pnl_24h += net_profit;
            
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "EXECUTOR".to_string(),
                severity: LogSeverity::Ok,
                message: format!("TRADE EXECUTED: Profit ${:.2}, Gas ${:.2}, Net ${:.2}", trade_profit, gas_cost, net_profit),
            });
        }
        
        // System heartbeat every 500 ticks (50 seconds)
        if self.tick_counter % 500 == 0 {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "SYSTEM".to_string(),
                severity: LogSeverity::Info,
                message: format!("HEARTBEAT - Uptime: {}s, Active strategies: {}", 
                    self.tick_counter / 10, self.system_metrics.active_strategies),
            });
        }
        
        Ok(())
    }

    /// Process incoming NATS updates
    async fn process_nats_updates(&mut self) {
        // Extract receiver temporarily to avoid borrowing conflicts
        let mut receiver_opt = self.nats_update_receiver.take();
        if let Some(ref mut receiver) = receiver_opt {
            // Process up to 10 messages per update cycle to avoid blocking
            for _ in 0..10 {
                match receiver.try_recv() {
                    Ok(update) => {
                        match update {
                            NatsUpdate::Treasury(treasury) => {
                                self.system_metrics.total_pnl_24h = treasury.pnl_24h;
                                let log_event = LogEvent {
                                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                                    source: "TREASURY".to_string(),
                                    severity: LogSeverity::Info,
                                    message: format!("Treasury update: PnL ${:.2}", treasury.pnl_24h),
                                };
                                self.add_log_event(log_event);
                            }
                            NatsUpdate::Opportunity(opp) => {
                                let live_opp = LiveOpportunity {
                                    scanner: opp.scanner,
                                    opportunity_type: opp.opportunity_type,
                                    pair: opp.pair,
                                    estimated_profit: opp.estimated_profit_usd,
                                    timestamp: std::time::Instant::now(),
                                };
                                self.live_opportunities.push_front(live_opp);
                                if self.live_opportunities.len() > 20 {
                                    self.live_opportunities.pop_back();
                                }
                            }
                            NatsUpdate::Trade(trade) => {
                                let trade_record = TradeRecord {
                                    id: trade.trade_id,
                                    opportunity_id: trade.opportunity_id,
                                    timestamp: trade.timestamp,
                                    strategy: trade.strategy,
                                    assets: trade.assets,
                                    profit_loss_token: trade.profit_loss_token,
                                    profit_loss_usd: trade.profit_loss_usd,
                                    status: match trade.status.as_str() {
                                        "success" => TradeStatus::Success,
                                        "failed_slippage" => TradeStatus::FailedSlippage,
                                        "failed_network" => TradeStatus::FailedNetwork,
                                        "failed_gas" => TradeStatus::FailedGas,
                                        _ => TradeStatus::Pending,
                                    },
                                    transaction_hash: trade.transaction_hash,
                                    gas_used: trade.gas_used,
                                    gas_price_gwei: trade.gas_price_gwei,
                                    market_regime_at_execution: trade.market_regime_at_execution,
                                    aetheric_resonance_at_execution: trade.aetheric_resonance_at_execution,
                                    execution_details: trade.execution_details,
                                    trade_explanation: trade.trade_explanation,
                                };
                                self.trade_history.push_front(trade_record);
                                if self.trade_history.len() > 100 {
                                    self.trade_history.pop_back();
                                }
                                
                                // Update system metrics
                                self.system_metrics.total_trades_24h += 1;
                                self.system_metrics.total_pnl_24h += trade.profit_loss_usd;
                            }
                            NatsUpdate::Health(health) => {
                                // Update service heartbeats
                                for (service_name, status) in health.services {
                                    if status.status == "running" {
                                        self.service_heartbeats.insert(service_name, std::time::Instant::now());
                                    }
                                }
                            }
                            NatsUpdate::CommandAck(ack) => {
                                let command_id = ack.command_id;
                                let success = ack.success;
                                let message = ack.message;
                                self.handle_command_acknowledgment(command_id, success, message);
                                if success {
                                    self.command_metrics.total_acknowledged += 1;
                                } else {
                                    self.command_metrics.total_failed += 1;
                                }
                            }
                            NatsUpdate::TradeLifecycle(event) => {
                                self.trade_lifecycle_events.push_front(event);
                                if self.trade_lifecycle_events.len() > 100 {
                                    self.trade_lifecycle_events.pop_back();
                                }
                            }
                            _ => {} // Handle other update types as needed
                        }
                    }
                    Err(tokio::sync::mpsc::error::TryRecvError::Empty) => break,
                    Err(tokio::sync::mpsc::error::TryRecvError::Disconnected) => {
                        error!("NATS update receiver disconnected");
                        break;
                    }
                }
            }
        }
        // Put receiver back
        self.nats_update_receiver = receiver_opt;
    }

    /// Check for command timeouts and update metrics
    pub fn check_command_timeouts(&mut self) {
        let now = std::time::Instant::now();
        let mut timed_out = Vec::new();
        
        for (id, pending) in &self.pending_commands {
            if now.duration_since(pending.sent_at).as_secs() > pending.timeout_seconds {
                timed_out.push(*id);
            }
        }
        
        for id in timed_out {
            if let Some(pending) = self.pending_commands.remove(&id) {
                self.command_metrics.total_timed_out += 1;
                self.add_log_event(LogEvent {
                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                    source: "COMMAND_TIMEOUT".to_string(),
                    severity: LogSeverity::Warn,
                    message: format!("{} command to {} timed out after {}s", 
                        pending.command_type, pending.target, pending.timeout_seconds),
                });
            }
        }
    }

    /// Handle command acknowledgment from services
    pub fn handle_command_acknowledgment(&mut self, command_id: uuid::Uuid, success: bool, message: Option<String>) {
        if let Some(pending) = self.pending_commands.remove(&command_id) {
            let status = if success { "SUCCESS" } else { "FAILED" };
            let msg = message.unwrap_or_else(|| "No details provided".to_string());
            
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "COMMAND_ACK".to_string(),
                severity: if success { LogSeverity::Ok } else { LogSeverity::Error },
                message: format!("{} command to {} {}: {}", pending.command_type, pending.target, status, msg),
            });
        }
    }

    /// Track a command for timeout monitoring
    fn track_command(&mut self, command_type: &str, target: &str, command_id: uuid::Uuid) {
        let pending = PendingCommand {
            id: command_id,
            command_type: command_type.to_string(),
            target: target.to_string(),
            sent_at: std::time::Instant::now(),
            timeout_seconds: 30,
        };
        
        self.pending_commands.insert(command_id, pending);
        self.command_metrics.total_sent += 1;
    }

    /// Publish trading control command to NATS
    fn publish_trading_control_command(&self, command: TradingControlCommand) {
        if let Some(ref nats_client) = self.nats_client {
            let client = nats_client.clone();
            let payload = serde_json::to_value(&command).unwrap();
            tokio::spawn(async move {
                if let Err(e) = client.publish(
                    "control.pause_trading",
                    serde_json::to_vec(&payload).unwrap().into()
                ).await {
                    error!("Failed to publish trading control: {}", e);
                }
            });
        }
    }

    /// Publish strategy control command to NATS
    pub fn pause_strategy(&mut self, strategy_name: &str) {
        let command_id = uuid::Uuid::new_v4();
        let command = StrategyControlCommand {
            strategy_name: strategy_name.to_string(),
            action: StrategyAction::Pause,
            timestamp: chrono::Utc::now(),
            command_id,
        };
        
        self.track_command("pause_strategy", strategy_name, command_id);
        
        if let Some(ref nats_client) = self.nats_client {
            let client = nats_client.clone();
            let topic = format!("control.strategy.{}", strategy_name.to_lowercase());
            let payload = serde_json::to_value(&command).unwrap();
            
            tokio::spawn(async move {
                if let Err(e) = client.publish(
                    topic,
                    serde_json::to_vec(&payload).unwrap().into()
                ).await {
                    error!("Failed to publish strategy control: {}", e);
                }
            });
        }
        
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "STRATEGY_CONTROL".to_string(),
            severity: LogSeverity::Warn,
            message: format!("Strategy {} PAUSED via TUI command", strategy_name),
        });
    }

    /// Resume a specific strategy
    pub fn resume_strategy(&mut self, strategy_name: &str) {
        let command_id = uuid::Uuid::new_v4();
        let command = StrategyControlCommand {
            strategy_name: strategy_name.to_string(),
            action: StrategyAction::Resume,
            timestamp: chrono::Utc::now(),
            command_id,
        };
        
        self.track_command("resume_strategy", strategy_name, command_id);
        
        if let Some(ref nats_client) = self.nats_client {
            let client = nats_client.clone();
            let topic = format!("control.strategy.{}", strategy_name.to_lowercase());
            let payload = serde_json::to_value(&command).unwrap();
            
            tokio::spawn(async move {
                if let Err(e) = client.publish(
                    topic,
                    serde_json::to_vec(&payload).unwrap().into()
                ).await {
                    error!("Failed to publish strategy control: {}", e);
                }
            });
        }
        
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "STRATEGY_CONTROL".to_string(),
            severity: LogSeverity::Ok,
            message: format!("Strategy {} RESUMED via TUI command", strategy_name),
        });
    }

    // Systems tab navigation methods
    pub fn start_log_search(&mut self) {
        self.log_search_mode = true;
        self.log_search_query.clear();
    }

    pub fn scroll_logs_up(&mut self) {
        if self.log_scroll_offset > 0 {
            self.log_scroll_offset -= 1;
        }
    }

    pub fn scroll_logs_down(&mut self) {
        let max_offset = self.log_buffer.len().saturating_sub(20);
        if self.log_scroll_offset < max_offset {
            self.log_scroll_offset += 1;
        }
    }

    pub fn scroll_logs_page_up(&mut self) {
        self.log_scroll_offset = self.log_scroll_offset.saturating_sub(10);
    }

    pub fn scroll_logs_page_down(&mut self) {
        let max_offset = self.log_buffer.len().saturating_sub(20);
        self.log_scroll_offset = (self.log_scroll_offset + 10).min(max_offset);
    }

    pub fn select_previous_service_systems(&mut self) {
        let service_count = 8; // Mock service count
        self.selected_service_index = Some(match self.selected_service_index {
            Some(i) if i > 0 => i - 1,
            Some(_) => service_count - 1,
            None => 0,
        });
    }

    pub fn select_next_service_systems(&mut self) {
        let service_count = 8; // Mock service count
        self.selected_service_index = Some(match self.selected_service_index {
            Some(i) if i < service_count - 1 => i + 1,
            Some(_) => 0,
            None => 0,
        });
    }

    pub fn toggle_service_details(&mut self) {
        self.service_details_expanded = !self.service_details_expanded;
    }

    pub fn restart_selected_service(&mut self) {
        if let Some(index) = self.selected_service_index {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "SERVICE_MANAGER".to_string(),
                severity: LogSeverity::Info,
                message: format!("Restarting service #{}", index),
            });
        }
    }

    pub fn start_selected_service(&mut self) {
        if let Some(index) = self.selected_service_index {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "SERVICE_MANAGER".to_string(),
                severity: LogSeverity::Ok,
                message: format!("Starting service #{}", index),
            });
        }
    }

    pub fn stop_selected_service(&mut self) {
        if let Some(index) = self.selected_service_index {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "SERVICE_MANAGER".to_string(),
                severity: LogSeverity::Warn,
                message: format!("Stopping service #{}", index),
            });
        }
    }

    pub fn export_logs(&mut self) {
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "LOG_MANAGER".to_string(),
            severity: LogSeverity::Info,
            message: "Exporting logs to file: logs_export.json".to_string(),
        });
    }

    pub fn clear_all_logs(&mut self) {
        self.log_buffer.clear();
        self.log_events.clear();
        self.log_scroll_offset = 0;
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "LOG_MANAGER".to_string(),
            severity: LogSeverity::Info,
            message: "All logs cleared".to_string(),
        });
    }

    // Enhanced Config tab key handling
    pub fn handle_config_key(&mut self, key: crossterm::event::KeyCode) {
        match key {
            // Navigation between sections and parameters
            crossterm::event::KeyCode::Up => {
                match self.config_focus_panel {
                    ConfigPanel::Sections => self.select_previous_config_section_ops(),
                    ConfigPanel::Parameters => self.select_previous_config_param(),
                    _ => {}
                }
            }
            crossterm::event::KeyCode::Down => {
                match self.config_focus_panel {
                    ConfigPanel::Sections => self.select_next_config_section_ops(),
                    ConfigPanel::Parameters => self.select_next_config_param(),
                    _ => {}
                }
            }
            
            // Panel navigation
            crossterm::event::KeyCode::Tab => {
                self.config_focus_panel = match self.config_focus_panel {
                    ConfigPanel::Sections => ConfigPanel::Parameters,
                    ConfigPanel::Parameters => ConfigPanel::Details,
                    ConfigPanel::Details => ConfigPanel::Sections,
                };
            }
            
            // Enter section or edit parameter
            crossterm::event::KeyCode::Enter => {
                match self.config_focus_panel {
                    ConfigPanel::Sections => {
                        self.config_focus_panel = ConfigPanel::Parameters;
                    }
                    ConfigPanel::Parameters => {
                        self.start_parameter_editing();
                    }
                    _ => {}
                }
            }
            
            // Hot-reload and control actions
            crossterm::event::KeyCode::Char('s') | crossterm::event::KeyCode::Char('S') => {
                self.save_and_hot_reload_config();
            }
            crossterm::event::KeyCode::Char('r') | crossterm::event::KeyCode::Char('R') => {
                self.reset_config_to_defaults();
            }
            crossterm::event::KeyCode::Char('l') | crossterm::event::KeyCode::Char('L') => {
                self.load_config_profile();
            }
            crossterm::event::KeyCode::Char('v') | crossterm::event::KeyCode::Char('V') => {
                self.validate_config();
            }
            
            _ => {}
        }
    }

    // Operations Tab Methods
    pub fn toggle_bot_status(&mut self) {
        self.bot_status = match self.bot_status {
            BotStatus::Running => BotStatus::Stopped,
            BotStatus::Stopped => BotStatus::Running,
            BotStatus::Paused => BotStatus::Running,
            BotStatus::Error(_) => BotStatus::Stopped,
        };
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "MASTER_CONTROL".to_string(),
            severity: LogSeverity::Info,
            message: format!("Bot status changed to: {}", self.bot_status.as_str()),
        });
    }

    pub fn pause_bot(&mut self) {
        let command_id = uuid::Uuid::new_v4();
        let command = TradingControlCommand {
            action: TradingAction::Pause,
            timestamp: chrono::Utc::now(),
            initiated_by: "TUI".to_string(),
            command_id,
        };
        
        self.track_command("pause_trading", "execution_manager", command_id);
        self.publish_trading_control_command(command);
        
        self.bot_status = BotStatus::Paused;
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "MASTER_CONTROL".to_string(),
            severity: LogSeverity::Warn,
            message: "Bot PAUSED - command sent to ExecutionManager".to_string(),
        });
    }

    pub fn restart_bot(&mut self) {
        self.bot_status = BotStatus::Running;
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "MASTER_CONTROL".to_string(),
            severity: LogSeverity::Ok,
            message: "Bot RESTARTED - resuming operations".to_string(),
        });
    }

    pub fn emergency_stop(&mut self) {
        // Create and send emergency stop command
        let command_id = uuid::Uuid::new_v4();
        let command = EmergencyStopCommand {
            timestamp: chrono::Utc::now(),
            reason: "Manual emergency stop via TUI".to_string(),
            initiated_by: "TUI".to_string(),
            command_id,
        };
        
        // Track the command
        self.track_command("emergency_stop", "system", command_id);
        
        // Publish to NATS
        if let Some(ref nats_client) = self.nats_client {
            let client = nats_client.clone();
            let payload = serde_json::to_value(&command).unwrap();
            tokio::spawn(async move {
                if let Err(e) = client.publish(
                    "control.emergency_stop",
                    serde_json::to_vec(&payload).unwrap().into()
                ).await {
                    error!("Failed to publish emergency stop: {}", e);
                }
            });
        }
        
        // Update local state
        self.bot_status = BotStatus::Error("EMERGENCY STOP".to_string());
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "EMERGENCY".to_string(),
            severity: LogSeverity::Error,
            message: "EMERGENCY STOP ACTIVATED - command sent to all services".to_string(),
        });
    }

    pub fn graceful_stop(&mut self) {
        self.bot_status = BotStatus::Stopped;
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "MASTER_CONTROL".to_string(),
            severity: LogSeverity::Info,
            message: "Graceful shutdown initiated - completing current operations".to_string(),
        });
    }


    pub fn toggle_trade_details(&mut self) {
        self.show_trade_details = !self.show_trade_details;
        if let Some(index) = self.selected_trade_index {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "TRADE_VIEWER".to_string(),
                severity: LogSeverity::Info,
                message: format!("Viewing details for trade #{}", index),
            });
        }
    }

    pub fn toggle_activity_log_filter(&mut self) {
        if self.log_filter.is_none() {
            self.log_filter = Some("EXECUTOR".to_string());
        } else {
            self.log_filter = None;
        }
    }

    pub fn clear_activity_log_filter(&mut self) {
        self.log_filter = None;
    }

    // Systems Tab Methods
    pub fn select_previous_component(&mut self) {
        let component_count = 8; // Mock component count
        self.selected_component_index = Some(match self.selected_component_index {
            Some(i) if i > 0 => i - 1,
            Some(_) => component_count - 1,
            None => 0,
        });
    }

    pub fn select_next_component(&mut self) {
        let component_count = 8; // Mock component count
        self.selected_component_index = Some(match self.selected_component_index {
            Some(i) if i < component_count - 1 => i + 1,
            Some(_) => 0,
            None => 0,
        });
    }


    pub fn show_component_details(&mut self) {
        if let Some(index) = self.selected_component_index {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "COMPONENT_MONITOR".to_string(),
                severity: LogSeverity::Info,
                message: format!("Showing details for component #{}", index),
            });
        }
    }

    pub fn restart_selected_component(&mut self) {
        if let Some(index) = self.selected_component_index {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "COMPONENT_MANAGER".to_string(),
                severity: LogSeverity::Info,
                message: format!("Restarting component #{}", index),
            });
        }
    }

    pub fn ping_selected_node(&mut self) {
        if let Some(index) = self.selected_node_index {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "NODE_MANAGER".to_string(),
                severity: LogSeverity::Info,
                message: format!("Pinging node #{} - latency check initiated", index),
            });
        }
    }

    pub fn reconnect_selected_node(&mut self) {
        if let Some(index) = self.selected_node_index {
            self.add_log_event(LogEvent {
                timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                source: "NODE_MANAGER".to_string(),
                severity: LogSeverity::Info,
                message: format!("Reconnecting to node #{}", index),
            });
        }
    }

    // Config Tab Methods - Using renamed methods to avoid conflicts
    pub fn select_previous_config_section_ops(&mut self) {
        let section_count = 6; // Mock section count
        self.selected_config_section = Some(match self.selected_config_section {
            Some(i) if i > 0 => i - 1,
            Some(_) => section_count - 1,
            None => 0,
        });
    }

    pub fn select_next_config_section_ops(&mut self) {
        let section_count = 6; // Mock section count
        self.selected_config_section = Some(match self.selected_config_section {
            Some(i) if i < section_count - 1 => i + 1,
            Some(_) => 0,
            None => 0,
        });
    }

    pub fn select_previous_trade_ops(&mut self) {
        let trade_count = self.trade_history.len();
        if trade_count > 0 {
            self.selected_trade_index = Some(match self.selected_trade_index {
                Some(i) if i > 0 => i - 1,
                Some(_) => trade_count - 1,
                None => 0,
            });
        }
    }

    pub fn select_next_trade_ops(&mut self) {
        let trade_count = self.trade_history.len();
        if trade_count > 0 {
            self.selected_trade_index = Some(match self.selected_trade_index {
                Some(i) if i < trade_count - 1 => i + 1,
                Some(_) => 0,
                None => 0,
            });
        }
    }

    pub fn select_previous_node_ops(&mut self) {
        let node_count = 6; // Mock node count
        self.selected_node_index = Some(match self.selected_node_index {
            Some(i) if i > 0 => i - 1,
            Some(_) => node_count - 1,
            None => 0,
        });
    }

    pub fn select_next_node_ops(&mut self) {
        let node_count = 6; // Mock node count
        self.selected_node_index = Some(match self.selected_node_index {
            Some(i) if i < node_count - 1 => i + 1,
            Some(_) => 0,
            None => 0,
        });
    }

    pub fn select_previous_config_param(&mut self) {
        let param_count = 3; // Mock parameter count per section
        self.selected_config_param = Some(match self.selected_config_param {
            Some(i) if i > 0 => i - 1,
            Some(_) => param_count - 1,
            None => 0,
        });
    }

    pub fn select_next_config_param(&mut self) {
        let param_count = 3; // Mock parameter count per section
        self.selected_config_param = Some(match self.selected_config_param {
            Some(i) if i < param_count - 1 => i + 1,
            Some(_) => 0,
            None => 0,
        });
    }

    pub fn start_parameter_editing(&mut self) {
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "CONFIG_EDITOR".to_string(),
            severity: LogSeverity::Info,
            message: "Parameter editing mode activated".to_string(),
        });
    }

    pub fn save_and_hot_reload_config(&mut self) {
        // Create config update command
        let command_id = uuid::Uuid::new_v4();
        let config_json = match serde_json::to_value(&self.config) {
            Ok(json) => json,
            Err(e) => {
                self.add_log_event(LogEvent {
                    timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
                    source: "CONFIG_MANAGER".to_string(),
                    severity: LogSeverity::Error,
                    message: format!("Failed to serialize config: {}", e),
                });
                return;
            }
        };

        self.track_command("config_reload", "all_services", command_id);

        // Publish config update
        if let Some(ref nats_client) = self.nats_client {
            let client = nats_client.clone();
            tokio::spawn(async move {
                if let Err(e) = client.publish(
                    "control.config.reload",
                    serde_json::to_vec(&config_json).unwrap().into()
                ).await {
                    error!("Failed to publish config update: {}", e);
                }
            });
        }

        self.config_modified = false;
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "CONFIG_MANAGER".to_string(),
            severity: LogSeverity::Ok,
            message: "Configuration saved and hot-reload command sent to all services".to_string(),
        });
    }

    pub fn reset_config_to_defaults(&mut self) {
        self.config_modified = true;
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "CONFIG_MANAGER".to_string(),
            severity: LogSeverity::Info,
            message: "Configuration reset to default values".to_string(),
        });
    }

    pub fn load_config_profile(&mut self) {
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "CONFIG_MANAGER".to_string(),
            severity: LogSeverity::Info,
            message: "Loading configuration profile".to_string(),
        });
    }

    pub fn validate_config(&mut self) {
        self.config_validation_errors.clear();
        self.add_log_event(LogEvent {
            timestamp: chrono::Utc::now().format("%H:%M:%S").to_string(),
            source: "CONFIG_VALIDATOR".to_string(),
            severity: LogSeverity::Ok,
            message: "Configuration validation completed - no errors found".to_string(),
        });
    }

    // Handle errors tab key events
    fn handle_errors_key(&mut self, key: KeyCode) {
        match key {
            KeyCode::Up => self.error_dashboard.previous(),
            KeyCode::Down => self.error_dashboard.next(),
            KeyCode::Enter => self.error_dashboard.toggle_details(),
            KeyCode::Char('c') => self.error_dashboard.clear_errors(),
            KeyCode::Char('f') => self.error_dashboard.toggle_filter(),
            _ => {}
        }
    }

    // Handle dashboard tab key events
    fn handle_dashboard_key(&mut self, key: KeyCode) {
        match key {
            KeyCode::Up => {
                // Navigate dashboard focus
            }
            KeyCode::Down => {
                // Navigate dashboard focus
            }
            KeyCode::Enter => {
                // Select dashboard item
            }
            _ => {}
        }
    }

}