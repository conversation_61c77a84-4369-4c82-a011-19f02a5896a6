//! AUDIT-FIX: Performance benchmarks and validation tests - Task 6.4
//! This module provides performance benchmarks for all critical components

use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use std::time::{Duration, Instant};
use tokio::runtime::Runtime;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;

/// Benchmark configuration
pub struct BenchmarkConfig {
    pub iterations: usize,
    pub timeout_ms: u64,
    pub warmup_iterations: usize,
}

impl Default for BenchmarkConfig {
    fn default() -> Self {
        Self {
            iterations: 1000,
            timeout_ms: 5000,
            warmup_iterations: 100,
        }
    }
}

/// Performance requirements for different components
pub struct PerformanceRequirements {
    pub vesica_calculation_max_ms: f64,
    pub fft_calculation_max_ms: f64,
    pub nonce_management_max_ms: f64,
    pub gas_estimation_max_ms: f64,
    pub risk_assessment_max_ms: f64,
    pub scoring_calculation_max_ms: f64,
}

impl Default for PerformanceRequirements {
    fn default() -> Self {
        Self {
            vesica_calculation_max_ms: 1.0,    // 1ms for math calculations
            fft_calculation_max_ms: 50.0,      // 50ms for FFT on reasonable datasets
            nonce_management_max_ms: 5.0,      // 5ms for nonce operations
            gas_estimation_max_ms: 100.0,      // 100ms for gas estimation (network call)
            risk_assessment_max_ms: 10.0,      // 10ms for risk calculations
            scoring_calculation_max_ms: 20.0,  // 20ms for opportunity scoring
        }
    }
}

/// Benchmark results
#[derive(Debug)]
pub struct BenchmarkResults {
    pub component_name: String,
    pub operation_name: String,
    pub avg_duration_ms: f64,
    pub min_duration_ms: f64,
    pub max_duration_ms: f64,
    pub p95_duration_ms: f64,
    pub p99_duration_ms: f64,
    pub throughput_ops_per_sec: f64,
    pub meets_requirements: bool,
}

/// Benchmark suite for mathematical components
pub fn benchmark_math_components(c: &mut Criterion) {
    let mut group = c.benchmark_group("math_components");
    
    // Benchmark Vesica Piscis calculations
    group.bench_function("vesica_piscis_standard", |b| {
        b.iter(|| {
            basilisk_bot::math::vesica::calculate_amount_to_equalize(
                black_box(dec!(1000.0)),
                black_box(dec!(1500.0)),
                black_box(dec!(0.1)),
            )
        })
    });

    // Benchmark with various input sizes
    for size in [10, 100, 1000, 10000].iter() {
        group.bench_with_input(
            BenchmarkId::new("vesica_piscis_varying_reserves", size),
            size,
            |b, &size| {
                let pool_a = Decimal::from(size);
                let pool_b = Decimal::from(size * 2);
                b.iter(|| {
                    basilisk_bot::math::vesica::calculate_amount_to_equalize(
                        black_box(pool_a),
                        black_box(pool_b),
                        black_box(dec!(0.1)),
                    )
                })
            },
        );
    }

    // Benchmark FFT calculations
    let rt = Runtime::new().unwrap();
    group.bench_function("fft_small_dataset", |b| {
        let analyzer = basilisk_bot::data::fractal_analyzer::FractalAnalyzer::new();
        let test_data: Vec<f64> = (0..100).map(|i| (i as f64 * 0.1).sin()).collect();
        
        b.to_async(&rt).iter(|| async {
            analyzer.calculate_temporal_harmonics(black_box(&test_data)).await
        })
    });

    group.bench_function("fft_medium_dataset", |b| {
        let analyzer = basilisk_bot::data::fractal_analyzer::FractalAnalyzer::new();
        let test_data: Vec<f64> = (0..1000).map(|i| (i as f64 * 0.01).sin()).collect();
        
        b.to_async(&rt).iter(|| async {
            analyzer.calculate_temporal_harmonics(black_box(&test_data)).await
        })
    });

    group.finish();
}

/// Benchmark suite for execution components
pub fn benchmark_execution_components(c: &mut Criterion) {
    let mut group = c.benchmark_group("execution_components");
    let rt = Runtime::new().unwrap();

    // Benchmark nonce management
    group.bench_function("nonce_manager_get_next", |b| {
        b.to_async(&rt).iter(|| async {
            let nonce_manager = basilisk_bot::execution::NonceManager::new(
                ethers::types::Address::zero(),
                1
            ).await.unwrap();
            
            black_box(nonce_manager.get_next_nonce().await)
        })
    });

    // Benchmark gas estimation
    group.bench_function("gas_estimator_standard", |b| {
        b.to_async(&rt).iter(|| async {
            let gas_estimator = basilisk_bot::execution::GasEstimator::new(
                ethers::providers::Provider::try_from("https://mainnet.base.org").unwrap(),
                1,
            );
            
            black_box(gas_estimator.estimate_gas_params(
                basilisk_bot::shared_types::GasUrgency::Standard
            ).await)
        })
    });

    // Benchmark circuit breaker operations
    group.bench_function("circuit_breaker_check", |b| {
        b.to_async(&rt).iter(|| async {
            let circuit_breaker = basilisk_bot::execution::CircuitBreaker::new(
                5,
                Duration::from_secs(60),
            );
            
            black_box(circuit_breaker.is_open().await)
        })
    });

    group.finish();
}

/// Benchmark suite for risk management components
pub fn benchmark_risk_components(c: &mut Criterion) {
    let mut group = c.benchmark_group("risk_components");

    // Benchmark Kelly Criterion calculations
    group.bench_function("kelly_criterion_calculation", |b| {
        let kelly = basilisk_bot::risk::kelly::KellyCriterion::new(dec!(0.25));
        
        b.iter(|| {
            kelly.calculate_optimal_fraction(
                black_box(dec!(0.6)),   // win_rate
                black_box(dec!(150.0)), // avg_win
                black_box(dec!(100.0)), // avg_loss
            )
        })
    });

    // Benchmark risk assessment with varying parameters
    for volatility in [0.1, 0.3, 0.5, 0.8].iter() {
        group.bench_with_input(
            BenchmarkId::new("risk_assessment_varying_volatility", volatility),
            volatility,
            |b, &volatility| {
                let kelly = basilisk_bot::risk::kelly::KellyCriterion::new(dec!(0.25));
                b.iter(|| {
                    kelly.calculate_optimal_fraction(
                        black_box(dec!(0.6)),
                        black_box(dec!(150.0)),
                        black_box(dec!(100.0)),
                    )
                })
            },
        );
    }

    group.finish();
}

/// Stress test for high-frequency operations
pub async fn stress_test_high_frequency_operations() -> Result<Vec<BenchmarkResults>, Box<dyn std::error::Error>> {
    let mut results = Vec::new();
    let config = BenchmarkConfig::default();

    // Stress test Vesica Piscis calculations
    let vesica_result = stress_test_vesica_piscis(&config).await?;
    results.push(vesica_result);

    // Stress test nonce management
    let nonce_result = stress_test_nonce_management(&config).await?;
    results.push(nonce_result);

    // Stress test risk calculations
    let risk_result = stress_test_risk_calculations(&config).await?;
    results.push(risk_result);

    Ok(results)
}

/// Stress test Vesica Piscis calculations under high frequency
async fn stress_test_vesica_piscis(config: &BenchmarkConfig) -> Result<BenchmarkResults, Box<dyn std::error::Error>> {
    let mut durations = Vec::with_capacity(config.iterations);
    let requirements = PerformanceRequirements::default();

    // Warmup
    for _ in 0..config.warmup_iterations {
        basilisk_bot::math::vesica::calculate_amount_to_equalize(
            dec!(1000.0),
            dec!(1500.0),
            dec!(0.1),
        );
    }

    // Actual benchmark
    let start_time = Instant::now();
    for i in 0..config.iterations {
        let iteration_start = Instant::now();
        
        // Vary inputs slightly to prevent optimization
        let pool_a = dec!(1000.0) + Decimal::from(i % 100);
        let pool_b = dec!(1500.0) + Decimal::from(i % 150);
        let deviation = dec!(0.1) + Decimal::from(i % 10) / dec!(1000.0);
        
        let _result = basilisk_bot::math::vesica::calculate_amount_to_equalize(
            pool_a,
            pool_b,
            deviation,
        );
        
        let duration = iteration_start.elapsed();
        durations.push(duration.as_secs_f64() * 1000.0); // Convert to milliseconds
    }
    let total_time = start_time.elapsed();

    // Calculate statistics
    durations.sort_by(|a, b| a.partial_cmp(b).unwrap());
    let avg_duration = durations.iter().sum::<f64>() / durations.len() as f64;
    let min_duration = durations[0];
    let max_duration = durations[durations.len() - 1];
    let p95_index = (durations.len() as f64 * 0.95) as usize;
    let p99_index = (durations.len() as f64 * 0.99) as usize;
    let p95_duration = durations[p95_index.min(durations.len() - 1)];
    let p99_duration = durations[p99_index.min(durations.len() - 1)];
    let throughput = config.iterations as f64 / total_time.as_secs_f64();

    Ok(BenchmarkResults {
        component_name: "MathComponents".to_string(),
        operation_name: "vesica_piscis_calculation".to_string(),
        avg_duration_ms: avg_duration,
        min_duration_ms: min_duration,
        max_duration_ms: max_duration,
        p95_duration_ms: p95_duration,
        p99_duration_ms: p99_duration,
        throughput_ops_per_sec: throughput,
        meets_requirements: avg_duration <= requirements.vesica_calculation_max_ms,
    })
}

/// Stress test nonce management under high frequency
async fn stress_test_nonce_management(config: &BenchmarkConfig) -> Result<BenchmarkResults, Box<dyn std::error::Error>> {
    let mut durations = Vec::with_capacity(config.iterations);
    let requirements = PerformanceRequirements::default();

    let nonce_manager = basilisk_bot::execution::NonceManager::new(
        ethers::types::Address::zero(),
        1
    ).await?;

    // Warmup
    for _ in 0..config.warmup_iterations {
        let _ = nonce_manager.get_next_nonce().await;
    }

    // Actual benchmark
    let start_time = Instant::now();
    for _ in 0..config.iterations {
        let iteration_start = Instant::now();
        
        let _nonce = nonce_manager.get_next_nonce().await?;
        
        let duration = iteration_start.elapsed();
        durations.push(duration.as_secs_f64() * 1000.0);
    }
    let total_time = start_time.elapsed();

    // Calculate statistics
    durations.sort_by(|a, b| a.partial_cmp(b).unwrap());
    let avg_duration = durations.iter().sum::<f64>() / durations.len() as f64;
    let min_duration = durations[0];
    let max_duration = durations[durations.len() - 1];
    let p95_index = (durations.len() as f64 * 0.95) as usize;
    let p99_index = (durations.len() as f64 * 0.99) as usize;
    let p95_duration = durations[p95_index.min(durations.len() - 1)];
    let p99_duration = durations[p99_index.min(durations.len() - 1)];
    let throughput = config.iterations as f64 / total_time.as_secs_f64();

    Ok(BenchmarkResults {
        component_name: "ExecutionComponents".to_string(),
        operation_name: "nonce_management".to_string(),
        avg_duration_ms: avg_duration,
        min_duration_ms: min_duration,
        max_duration_ms: max_duration,
        p95_duration_ms: p95_duration,
        p99_duration_ms: p99_duration,
        throughput_ops_per_sec: throughput,
        meets_requirements: avg_duration <= requirements.nonce_management_max_ms,
    })
}

/// Stress test risk calculations under high frequency
async fn stress_test_risk_calculations(config: &BenchmarkConfig) -> Result<BenchmarkResults, Box<dyn std::error::Error>> {
    let mut durations = Vec::with_capacity(config.iterations);
    let requirements = PerformanceRequirements::default();

    let kelly = basilisk_bot::risk::kelly::KellyCriterion::new(dec!(0.25));

    // Warmup
    for _ in 0..config.warmup_iterations {
        kelly.calculate_optimal_fraction(dec!(0.6), dec!(150.0), dec!(100.0));
    }

    // Actual benchmark
    let start_time = Instant::now();
    for i in 0..config.iterations {
        let iteration_start = Instant::now();
        
        // Vary inputs slightly
        let win_rate = dec!(0.5) + Decimal::from(i % 20) / dec!(100.0);
        let avg_win = dec!(100.0) + Decimal::from(i % 50);
        let avg_loss = dec!(80.0) + Decimal::from(i % 40);
        
        let _fraction = kelly.calculate_optimal_fraction(win_rate, avg_win, avg_loss);
        
        let duration = iteration_start.elapsed();
        durations.push(duration.as_secs_f64() * 1000.0);
    }
    let total_time = start_time.elapsed();

    // Calculate statistics
    durations.sort_by(|a, b| a.partial_cmp(b).unwrap());
    let avg_duration = durations.iter().sum::<f64>() / durations.len() as f64;
    let min_duration = durations[0];
    let max_duration = durations[durations.len() - 1];
    let p95_index = (durations.len() as f64 * 0.95) as usize;
    let p99_index = (durations.len() as f64 * 0.99) as usize;
    let p95_duration = durations[p95_index.min(durations.len() - 1)];
    let p99_duration = durations[p99_index.min(durations.len() - 1)];
    let throughput = config.iterations as f64 / total_time.as_secs_f64();

    Ok(BenchmarkResults {
        component_name: "RiskComponents".to_string(),
        operation_name: "kelly_criterion_calculation".to_string(),
        avg_duration_ms: avg_duration,
        min_duration_ms: min_duration,
        max_duration_ms: max_duration,
        p95_duration_ms: p95_duration,
        p99_duration_ms: p99_duration,
        throughput_ops_per_sec: throughput,
        meets_requirements: avg_duration <= requirements.risk_assessment_max_ms,
    })
}

/// Load test for concurrent operations
pub async fn load_test_concurrent_operations() -> Result<Vec<BenchmarkResults>, Box<dyn std::error::Error>> {
    let mut results = Vec::new();
    
    // Test concurrent nonce requests
    let concurrent_nonce_result = load_test_concurrent_nonce_requests().await?;
    results.push(concurrent_nonce_result);
    
    // Test concurrent risk assessments
    let concurrent_risk_result = load_test_concurrent_risk_assessments().await?;
    results.push(concurrent_risk_result);
    
    Ok(results)
}

/// Load test concurrent nonce requests
async fn load_test_concurrent_nonce_requests() -> Result<BenchmarkResults, Box<dyn std::error::Error>> {
    let nonce_manager = basilisk_bot::execution::NonceManager::new(
        ethers::types::Address::zero(),
        1
    ).await?;
    
    let concurrent_requests = 100;
    let start_time = Instant::now();
    
    // Spawn concurrent requests
    let mut handles = Vec::new();
    for _ in 0..concurrent_requests {
        let nm = nonce_manager.clone();
        let handle = tokio::spawn(async move {
            let start = Instant::now();
            let result = nm.get_next_nonce().await;
            (result, start.elapsed())
        });
        handles.push(handle);
    }
    
    // Collect results
    let mut durations = Vec::new();
    let mut successful_requests = 0;
    
    for handle in handles {
        match handle.await {
            Ok((Ok(_), duration)) => {
                durations.push(duration.as_secs_f64() * 1000.0);
                successful_requests += 1;
            },
            _ => {
                // Request failed
            }
        }
    }
    
    let total_time = start_time.elapsed();
    
    // Calculate statistics
    durations.sort_by(|a, b| a.partial_cmp(b).unwrap());
    let avg_duration = if !durations.is_empty() {
        durations.iter().sum::<f64>() / durations.len() as f64
    } else {
        0.0
    };
    
    let throughput = successful_requests as f64 / total_time.as_secs_f64();
    
    Ok(BenchmarkResults {
        component_name: "ExecutionComponents".to_string(),
        operation_name: "concurrent_nonce_requests".to_string(),
        avg_duration_ms: avg_duration,
        min_duration_ms: durations.first().copied().unwrap_or(0.0),
        max_duration_ms: durations.last().copied().unwrap_or(0.0),
        p95_duration_ms: durations.get((durations.len() as f64 * 0.95) as usize).copied().unwrap_or(0.0),
        p99_duration_ms: durations.get((durations.len() as f64 * 0.99) as usize).copied().unwrap_or(0.0),
        throughput_ops_per_sec: throughput,
        meets_requirements: successful_requests == concurrent_requests, // All requests should succeed
    })
}

/// Load test concurrent risk assessments
async fn load_test_concurrent_risk_assessments() -> Result<BenchmarkResults, Box<dyn std::error::Error>> {
    let kelly = basilisk_bot::risk::kelly::KellyCriterion::new(dec!(0.25));
    let concurrent_requests = 1000;
    let start_time = Instant::now();
    
    // Spawn concurrent calculations
    let mut handles = Vec::new();
    for i in 0..concurrent_requests {
        let kelly_clone = kelly.clone();
        let handle = tokio::spawn(async move {
            let start = Instant::now();
            let win_rate = dec!(0.5) + Decimal::from(i % 20) / dec!(100.0);
            let _result = kelly_clone.calculate_optimal_fraction(
                win_rate,
                dec!(150.0),
                dec!(100.0)
            );
            start.elapsed()
        });
        handles.push(handle);
    }
    
    // Collect results
    let mut durations = Vec::new();
    for handle in handles {
        if let Ok(duration) = handle.await {
            durations.push(duration.as_secs_f64() * 1000.0);
        }
    }
    
    let total_time = start_time.elapsed();
    
    // Calculate statistics
    durations.sort_by(|a, b| a.partial_cmp(b).unwrap());
    let avg_duration = durations.iter().sum::<f64>() / durations.len() as f64;
    let throughput = durations.len() as f64 / total_time.as_secs_f64();
    
    Ok(BenchmarkResults {
        component_name: "RiskComponents".to_string(),
        operation_name: "concurrent_risk_assessments".to_string(),
        avg_duration_ms: avg_duration,
        min_duration_ms: durations[0],
        max_duration_ms: durations[durations.len() - 1],
        p95_duration_ms: durations[(durations.len() as f64 * 0.95) as usize],
        p99_duration_ms: durations[(durations.len() as f64 * 0.99) as usize],
        throughput_ops_per_sec: throughput,
        meets_requirements: durations.len() == concurrent_requests, // All calculations should complete
    })
}

criterion_group!(
    benches,
    benchmark_math_components,
    benchmark_execution_components,
    benchmark_risk_components
);
criterion_main!(benches);
