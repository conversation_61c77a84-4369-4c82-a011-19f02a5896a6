// MISSION: TUI Harness - Standalone TUI Testing Environment
// WHY: Provide isolated TUI testing with mock data generation
// HOW: Complete TUI implementation with NATS-based data simulation

use async_nats::Client as NatsClient;
use basilisk_bot::tui::app::App;
use basilisk_bot::config::{Config, Settings};
use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode},
    execute,
    terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
};
use rand::{Rng, SeedableRng};
use rand::rngs::StdRng;
use ratatui::{backend::CrosstermBackend, Terminal};
use serde_json::json;
use std::error::Error;
use std::io;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::time::{interval, sleep};
use tokio_stream::StreamExt;
use tracing::{error, info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // Initialize logging - redirect to file to avoid interfering with TUI
    let log_file = std::fs::File::create("tui_harness.log")?;
    let subscriber = tracing_subscriber::FmtSubscriber::builder()
        .with_max_level(tracing::Level::INFO)
        .with_writer(log_file)
        .finish();
    tracing::subscriber::set_global_default(subscriber)?;

    tracing::info!("ZEN GEOMETER TUI HARNESS - Starting standalone TUI testing environment");

    // Connect to NATS
    let nats_url = std::env::var("NATS_URL").unwrap_or_else(|_| "nats://localhost:4222".to_string());
    tracing::info!("Connecting to NATS server at {}", nats_url);
    let client = async_nats::connect(&nats_url).await?;
    tracing::info!("Connected to NATS server");

    // Load configuration for TUI
    let settings = match Config::load() {
        Ok(config) => Settings::from(config),
        Err(_) => {
            warn!("Could not load simulation config, using defaults");
            Config::default().into()
        }
    };

    // Setup terminal for TUI
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create TUI app
    let mut app = App::new_harness(std::sync::Arc::new(Config::from(settings)), client.clone());

    tracing::info!("TUI initialized - starting mock data generation and UI loop");

    // Start mock data generation in background
    let data_client = client.clone();
    tokio::spawn(async move {
        generate_mock_data(data_client).await;
    });

    // Subscribe to control commands to verify TUI is sending them
    let control_client = client.clone();
    tokio::spawn(async move {
        let mut subscriber = control_client.subscribe("control.>").await.unwrap();
        tracing::info!("Listening for control commands from TUI...");

        while let Some(msg) = subscriber.next().await {
            let subject = msg.subject.to_string();
            let payload = String::from_utf8_lossy(&msg.payload);
            tracing::info!("Received control command: {} -> {}", subject, payload);
        }
    });

    // Initialize app
    if let Err(e) = app.init().await {
        error!("Failed to initialize TUI app: {}", e);
        cleanup_terminal(&mut terminal)?;
        return Err(e.into());
    }

    // Main TUI event loop
    let mut last_tick = std::time::Instant::now();
    let tick_rate = Duration::from_millis(100);

    info!("TUI Harness ready - Use TAB to navigate, Q to quit");

    while app.running {
        // Draw UI
        if let Err(e) = terminal.draw(|f| app.render(f)) {
            error!("Failed to draw TUI: {}", e);
            break;
        }

        // Handle input events
        let timeout = tick_rate
            .checked_sub(last_tick.elapsed())
            .unwrap_or_else(|| Duration::from_secs(0));

        if event::poll(timeout)? {
            if let Event::Key(key) = event::read()? {
                app.handle_key(key);
            }
        }

        // Update app state periodically
        if last_tick.elapsed() >= tick_rate {
            if let Err(e) = app.update().await {
                error!("Failed to update TUI state: {}", e);
            }
            last_tick = std::time::Instant::now();
        }
    }

    // Cleanup and restore terminal
    cleanup_terminal(&mut terminal)?;
    info!("TUI Harness shutdown complete");

    Ok(())
}

fn cleanup_terminal(terminal: &mut Terminal<CrosstermBackend<io::Stdout>>) -> Result<(), Box<dyn Error>> {
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;
    Ok(())
}

async fn generate_mock_data(client: NatsClient) {
    let mut interval = interval(Duration::from_millis(1200)); // Faster updates for more dynamic feel
    let mut rng = StdRng::from_entropy();
    let mut trade_counter = 1;
    let mut opportunity_counter = 1;

    loop {
        interval.tick().await;

        // Generate mock system status
        let system_status = json!({
            "status": "active",
            "mode": "simulation",
            "uptime_seconds": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            "active_strategies": 3,
            "total_strategies": 3
        });

        let status_payload = system_status.to_string();
        if let Err(e) = client
            .publish(basilisk_bot::shared_types::NatsTopics::STATE_SYSTEM_STATUS, status_payload.into())
            .await
        {
            error!("Failed to publish system status: {}", e);
        }

        // Generate mock opportunities with varying market conditions
        if rng.gen_bool(0.7) { // 70% chance of opportunity each cycle
            let pairs = ["WETH/USDC", "DEGEN/WETH", "USDC/DAI", "WBTC/WETH", "ARB/USDC", "OP/WETH"];
            let scanners = ["ZEN_GEOMETER", "PILOT_FISH", "NOMADIC_HUNTER", "GAZE", "MEMPOOL", "LIQUIDATION"];
            let opportunity_types = ["Cross-Chain Arbitrage", "DEX Arbitrage", "Flash Loan", "Liquidation", "MEV Sandwich", "Statistical Arbitrage"];
            
            let pair = pairs[rng.gen_range(0..pairs.len())];
            let scanner = scanners[rng.gen_range(0..scanners.len())];
            let opportunity_type = opportunity_types[rng.gen_range(0..opportunity_types.len())];
            
            // Generate realistic profit ranges based on opportunity type
            let profit = match opportunity_type {
                "Cross-Chain Arbitrage" => rng.gen_range(15.0..250.0),
                "Flash Loan" => rng.gen_range(25.0..500.0),
                "Liquidation" => rng.gen_range(50.0..1000.0),
                "MEV Sandwich" => rng.gen_range(8.0..75.0),
                _ => rng.gen_range(5.0..150.0),
            };

            // Generate Aetheric Resonance Score components
            let chronos_sieve = rng.gen_range(0.2..0.95);
            let mandorla_gauge = rng.gen_range(0.15..0.88);
            let network_seismology = rng.gen_range(0.3..0.92);
            let composite_score = (chronos_sieve + mandorla_gauge + network_seismology) / 3.0;

            let opportunity = json!({
                "id": format!("OPP_{:04}", opportunity_counter),
                "scanner": scanner,
                "opportunity_type": opportunity_type,
                "pair": pair,
                "estimated_profit": profit,
                "gas_estimate": rng.gen_range(2.0..15.0),
                "aetheric_resonance": {
                    "chronos_sieve": chronos_sieve,
                    "mandorla_gauge": mandorla_gauge,
                    "network_seismology": network_seismology,
                    "composite_score": composite_score
                },
                "market_regime": if composite_score > 0.7 { "High Coherence" } else if composite_score > 0.5 { "Moderate Volatility" } else { "Chaotic State" },
                "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs()
            });

            let opp_payload = opportunity.to_string();
            if let Err(e) = client
                .publish(basilisk_bot::shared_types::NatsTopics::DATA_OPPORTUNITIES_DETECTED, opp_payload.into())
                .await
            {
                error!("Failed to publish opportunity: {}", e);
            }

            opportunity_counter += 1;
        }

        // Simulate trade executions based on high-scoring opportunities
        if rng.gen_bool(0.15) { // 15% chance of trade execution each cycle
            let trade_profit = rng.gen_range(12.0..85.0);
            let gas_cost = rng.gen_range(2.5..12.0);
            let net_profit = trade_profit - gas_cost;
            let execution_time_ms = rng.gen_range(1200..4500);
            
            let pairs = ["WETH/USDC", "DEGEN/WETH", "USDC/DAI"];
            let strategies = ["ZEN_GEOMETER", "PILOT_FISH", "NOMADIC_HUNTER"];
            
            let trade_execution = json!({
                "trade_id": format!("T_{:04}", trade_counter),
                "opportunity_id": format!("OPP_{:04}", rng.gen_range(1..opportunity_counter)), // Link to a hypothetical opportunity
                "pair": pairs[rng.gen_range(0..3)],
                "strategy": strategies[rng.gen_range(0..3)],
                "gross_profit": trade_profit,
                "gas_cost": gas_cost,
                "net_profit": net_profit,
                "execution_time_ms": execution_time_ms,
                "block_number": 21000000 + rng.gen_range(1..1000),
                "transaction_hash": format!("0x{:064x}", rng.gen::<u64>()),
                "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
                "market_regime_at_execution": if net_profit > 50.0 { "High Coherence" } else if net_profit > 20.0 { "Moderate Volatility" } else { "Chaotic State" },
                "aetheric_resonance_at_execution": {
                    "chronos_sieve": rng.gen_range(0.6..0.99),
                    "mandorla_gauge": rng.gen_range(0.5..0.95),
                    "network_seismology": rng.gen_range(0.7..0.98),
                    "composite_score": (rng.gen_range(0.6..0.99) + rng.gen_range(0.5..0.95) + rng.gen_range(0.7..0.98)) / 3.0
                },
                "execution_details": format!("Timed transaction broadcast based on optimal network conditions (S-P time: {}ms). Utilized flash loan for capital efficiency.", rng.gen_range(50..500)),
                "trade_explanation": if net_profit > 50.0 {
                    "Executed due to strong Aetheric Resonance Score indicating high market coherence and optimal network conditions. The Zen Geometer strategy identified a robust cross-chain arbitrage path with significant profit potential."
                } else if net_profit > 20.0 {
                    "Trade executed during moderate volatility. The Mandorla Gauge indicated a stable geometric opportunity, and Chronos Sieve confirmed a favorable market cycle, despite some network fluctuations."
                } else {
                    "Opportunistic trade executed in a chaotic market. Network Seismology identified a brief window of low latency, allowing for a quick, low-risk arbitrage. Profit was modest but consistent with market conditions."
                }
            });

            let trade_payload = trade_execution.to_string();
            if let Err(e) = client
                .publish(basilisk_bot::shared_types::NatsTopics::EXECUTION_TRADE_COMPLETED, trade_payload.into())
                .await
            {
                error!("Failed to publish trade execution: {}", e);
            }

            trade_counter += 1;
        }

        // Generate dynamic network seismology data
        if rng.gen_bool(0.4) { // 40% chance of seismology update
            let p_wave = rng.gen_range(800..2500);
            let s_wave = rng.gen_range(3000..6500);
            let sp_time = s_wave - p_wave;
            let coherence = rng.gen_range(0.45..0.95);
            
            let seismology = json!({
                "p_wave_ms": p_wave,
                "s_wave_ms": s_wave,
                "sp_time_ms": sp_time,
                "coherence": coherence,
                "network_state": if coherence > 0.8 { "STABLE" } else if coherence > 0.6 { "MODERATE" } else { "VOLATILE" },
                "recommendation": if coherence > 0.75 { "OPTIMAL_FOR_TRADING" } else if coherence > 0.5 { "PROCEED_WITH_CAUTION" } else { "AVOID_TRADING" },
                "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs()
            });

            let seismology_payload = seismology.to_string();
            if let Err(e) = client
                .publish(basilisk_bot::shared_types::NatsTopics::NETWORK_SEISMOLOGY_UPDATE, seismology_payload.into())
                .await
            {
                error!("Failed to publish seismology data: {}", e);
            }
        }

        // Generate mock service logs with realistic content
        let services = [
            ("zen_geometer", vec![
                "Scanning cross-chain arbitrage opportunities",
                "Aetheric Resonance Score calculated: 0.847",
                "Sacred geometry analysis: Golden ratio detected",
                "Vesica Piscis depth analysis complete",
                "Mandorla gauge indicates deep opportunity pool"
            ]),
            ("data_ingestor", vec![
                "Connected to CEX feed: Coinbase Pro",
                "Processing price data for WETH/USDC",
                "Chain monitor active on Base network",
                "Fractal analyzer detecting market patterns",
                "Price deviation detected: 0.15%",
                "Degen Chain block ingested: +2.3s latency"
            ]),
            ("listener", vec![
                "NATS subscription active on data.>",
                "Processing incoming market data",
                "Message queue healthy: 0ms latency",
                "Received 1,247 messages in last minute"
            ]),
            ("pilot_fish", vec![
                "Following whale transaction: 0x7a2b...",
                "MEV opportunity detected in mempool",
                "Sandwich attack vector identified",
                "Front-running position calculated",
                "Slipstream analysis: Optimal entry point"
            ]),
            ("nomadic_hunter", vec![
                "Hunting liquidation opportunities",
                "Undercollateralized position found",
                "Health factor: 1.02 (CRITICAL)",
                "Liquidation profit estimate: $234.56",
                "Position monitoring: 15 targets active"
            ]),
            ("feature_exporter", vec![
                "Generating SIGINT market analysis",
                "Exporting feature data to JSON",
                "Market regime classification: High Volatility",
                "Analysis complete: 47 features extracted",
                "Fractal dimension calculated: 1.847"
            ]),
            ("graph_analyzer", vec![
                "PageRank analysis running on token graph",
                "Centrality scores updated for 156 tokens",
                "Optimal routing path calculated",
                "Network topology analysis complete",
                "Token correlation matrix updated"
            ]),
            ("network_observer", vec![
                "Monitoring Base sequencer health",
                "Block propagation time: 1.2s",
                "Network congestion: Low",
                "RPC endpoint latency: 45ms",
                "Degen Chain sync status: 99.8%"
            ]),
            ("seismic_analyzer", vec![
                "S-P wave analysis active",
                "Network resonance state: Stable",
                "Temporal harmonics detected",
                "Seismic coherence: 0.87",
                "Axis Mundi alignment: Optimal"
            ]),
            ("risk_manager", vec![
                "Portfolio exposure: $8,450 / $10,000",
                "Kelly fraction adjusted: 0.23",
                "VaR 95%: -$156.78",
                "Position size validation complete",
                "Emergency stop system: ARMED"
            ]),
            ("execution_engine", vec![
                "Gas price oracle updated: 12.5 gwei",
                "MEV relay connection: ACTIVE",
                "Transaction pool: 3 pending",
                "Nonce synchronization complete",
                "Flash loan capacity: $50,000 available"
            ])
        ];

        // Generate 2-3 service logs per cycle
        for _ in 0..rng.gen_range(2..=3) {
            let (service_name, messages) = &services[rng.gen_range(0..services.len())];
            let message = &messages[rng.gen_range(0..messages.len())];
            let severity = match rng.gen_range(0..10) {
                0 => "error",
                1..=2 => "warning", 
                _ => "info"
            };

            let log_event = json!({
                "timestamp": chrono::Utc::now().format("%H:%M:%S%.3f").to_string(),
                "source": service_name,
                "severity": severity,
                "message": message
            });

            let log_payload = log_event.to_string();
            let topic = format!("logs.service.{}", service_name);
            if let Err(e) = client
                .publish(topic, log_payload.into())
                .await
            {
                error!("Failed to publish service log: {}", e);
            }
        }

        // Generate system-level logs occasionally
        if rng.gen_bool(0.3) {
            let system_messages = [
                "System health check completed",
                "Risk parameters within normal range",
                "Memory usage: 245MB / 2GB",
                "Active connections: NATS ✓ RPC ✓ DB ✓",
                "Emergency stop system armed and ready"
            ];
            
            let message = system_messages[rng.gen_range(0..system_messages.len())];
            let log_event = json!({
                "timestamp": chrono::Utc::now().format("%H:%M:%S%.3f").to_string(),
                "source": "SYSTEM",
                "severity": "info",
                "message": message
            });

            let log_payload = log_event.to_string();
            if let Err(e) = client
                .publish(basilisk_bot::shared_types::NatsTopics::LOGS_SYSTEM, log_payload.into())
                .await
            {
                error!("Failed to publish system log: {}", e);
            }
        }

        // Log to file only - don't interfere with TUI
        tracing::info!("Generated mock multi-service data cycle");
    }
}