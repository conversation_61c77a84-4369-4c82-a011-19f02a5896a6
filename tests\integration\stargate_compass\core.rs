// Core trait interfaces and data structures for Stargate Compass integration testing

use ethers::types::{Address, H256, U256};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::{Duration, SystemTime};
use chrono::{DateTime, Utc};
use async_trait::async_trait;
use anyhow::Result;

// ============= CORE TRAIT INTERFACES =============

/// Main integration tester trait - defines the contract for all test components
#[async_trait]
pub trait IntegrationTester {
    /// Execute the integration test and return results
    async fn run_test(&mut self) -> Result<TestResult>;
    
    /// Get the name of this test component
    fn component_name(&self) -> &str;
    
    /// Check if this component is ready to run tests
    async fn is_ready(&self) -> Result<bool>;
    
    /// Setup any required test environment
    async fn setup(&self) -> Result<()>;
    
    /// Cleanup after test execution
    async fn cleanup(&self) -> Result<()>;
}

/// Configuration manager trait - handles dynamic contract address updates
#[async_trait]
pub trait ConfigManager {
    /// Update contract address in configuration files
    async fn update_contract_address(&self, new_address: Address) -> Result<ConfigUpdateResult>;
    
    /// Validate current configuration
    async fn validate_configuration(&self) -> Result<ConfigValidationResult>;
    
    /// Backup current configuration
    async fn backup_configuration(&self) -> Result<()>;
    
    /// Restore configuration from backup
    async fn restore_configuration(&self) -> Result<()>;
    
    /// Get current contract address from configuration
    async fn get_current_contract_address(&self) -> Result<Address>;
}

/// Test reporter trait - handles result aggregation and reporting
pub trait TestReporter {
    /// Add a test result to the report
    fn add_test_result(&mut self, result: TestResult);
    
    /// Generate comprehensive integration test report
    fn generate_report(&self) -> IntegrationTestReport;
    
    /// Analyze failures and provide remediation recommendations
    fn analyze_failures(&self) -> Vec<FailureAnalysis>;
    
    /// Export report to file
    fn export_report(&self, path: &str) -> Result<()>;
}

// ============= SHARED DATA STRUCTURES =============

/// Overall integration test result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationTestResult {
    pub overall_success: bool,
    pub configuration_result: ConfigurationTestResult,
    pub backend_result: BackendTestResult,
    pub tui_result: TuiTestResult,
    pub end_to_end_result: EndToEndTestResult,
    pub execution_time: Duration,
    pub timestamp: DateTime<Utc>,
    pub test_environment: TestEnvironment,
}

/// Individual test component result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestResult {
    pub component_name: String,
    pub success: bool,
    pub execution_time: Duration,
    pub timestamp: DateTime<Utc>,
    pub details: TestDetails,
    pub errors: Vec<TestError>,
    pub warnings: Vec<String>,
}

/// Detailed test execution information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TestDetails {
    Configuration(ConfigurationTestResult),
    Backend(BackendTestResult),
    Tui(TuiTestResult),
    EndToEnd(EndToEndTestResult),
    Anvil(AnvilTestResult),
}

/// Configuration management test results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigurationTestResult {
    pub success: bool,
    pub config_files_updated: Vec<String>,
    pub validation_errors: Vec<String>,
    pub backup_created: bool,
    pub contract_address_updated: bool,
    pub original_address: Option<Address>,
    pub new_address: Option<Address>,
}

/// Backend integration test results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackendTestResult {
    pub success: bool,
    pub execution_manager_tests: Vec<ComponentTestResult>,
    pub execution_dispatcher_tests: Vec<ComponentTestResult>,
    pub strategy_manager_tests: Vec<ComponentTestResult>,
    pub transaction_validations: Vec<TransactionValidationResult>,
    pub contract_interactions: Vec<ContractInteractionResult>,
    pub opportunities_tested: u32,
    pub successful_transactions: u32,
}

/// TUI functionality test results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuiTestResult {
    pub success: bool,
    pub command_tests: Vec<CommandTestResult>,
    pub data_validations: Vec<DataValidationResult>,
    pub ui_state_validations: Vec<UiStateValidationResult>,
    pub contract_interaction_commands: Vec<String>,
    pub successful_commands: u32,
    pub failed_commands: u32,
}

/// End-to-end workflow test results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EndToEndTestResult {
    pub success: bool,
    pub workflow_validations: Vec<WorkflowValidationResult>,
    pub data_pipeline_tests: Vec<DataPipelineTestResult>,
    pub complete_cycles_tested: u32,
    pub successful_cycles: u32,
}

/// Anvil testnet interaction results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnvilTestResult {
    pub success: bool,
    pub connection_established: bool,
    pub contract_deployed: bool,
    pub contract_verified: bool,
    pub blockchain_state_valid: bool,
    pub anvil_url: String,
    pub chain_id: u64,
    pub block_number: u64,
}

/// Individual component test result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentTestResult {
    pub component_name: String,
    pub function_name: String,
    pub success: bool,
    pub execution_time: Duration,
    pub error_message: Option<String>,
    pub test_data: HashMap<String, String>,
}

/// Transaction validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionValidationResult {
    pub transaction_hash: H256,
    pub success: bool,
    pub reverted: bool,
    pub gas_used: U256,
    pub gas_price: U256,
    pub return_values: Vec<String>,
    pub events_emitted: Vec<String>,
    pub validation_errors: Vec<String>,
    pub execution_time: Duration,
}

/// Contract interaction result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContractInteractionResult {
    pub function_name: String,
    pub contract_address: Address,
    pub success: bool,
    pub transaction_hash: Option<H256>,
    pub gas_used: Option<U256>,
    pub error_message: Option<String>,
    pub execution_time: Duration,
    pub input_parameters: HashMap<String, String>,
    pub output_values: HashMap<String, String>,
}

/// TUI command test result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandTestResult {
    pub command: String,
    pub success: bool,
    pub execution_time: Duration,
    pub output_captured: String,
    pub error_message: Option<String>,
    pub exit_code: Option<i32>,
    pub timeout_occurred: bool,
}

/// Data validation result for TUI displays
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataValidationResult {
    pub data_type: String,
    pub expected_value: String,
    pub actual_value: String,
    pub matches: bool,
    pub validation_method: String,
    pub tolerance: Option<Decimal>,
    pub error_message: Option<String>,
}

/// UI state validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiStateValidationResult {
    pub state_name: String,
    pub expected_state: String,
    pub actual_state: String,
    pub valid: bool,
    pub validation_time: DateTime<Utc>,
    pub error_details: Option<String>,
}

/// Workflow validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowValidationResult {
    pub workflow_name: String,
    pub success: bool,
    pub stages_completed: Vec<String>,
    pub stages_failed: Vec<String>,
    pub total_execution_time: Duration,
    pub data_consistency_check: bool,
    pub error_details: Option<String>,
}

/// Data pipeline test result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataPipelineTestResult {
    pub pipeline_name: String,
    pub success: bool,
    pub input_data: HashMap<String, String>,
    pub output_data: HashMap<String, String>,
    pub transformation_valid: bool,
    pub latency_ms: u64,
    pub error_message: Option<String>,
}

// ============= CONFIGURATION TYPES =============

/// Configuration update result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigUpdateResult {
    pub success: bool,
    pub files_modified: Vec<String>,
    pub backup_created: bool,
    pub validation_passed: bool,
    pub error_message: Option<String>,
}

/// Configuration validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigValidationResult {
    pub valid: bool,
    pub config_files_found: Vec<String>,
    pub missing_keys: Vec<String>,
    pub invalid_values: Vec<String>,
    pub warnings: Vec<String>,
    pub contract_address_valid: bool,
}

/// File-specific validation result for update verification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileValidationResult {
    pub file_path: PathBuf,
    pub success: bool,
    pub address_matches: bool,
    pub config_structure_valid: bool,
    pub backup_exists: bool,
    pub file_readable: bool,
    pub errors: Vec<String>,
}

// ============= ERROR HANDLING =============

/// Comprehensive error types for integration testing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IntegrationTestError {
    ConfigurationError {
        config_file: String,
        error_type: ConfigErrorType,
        message: String,
    },
    BackendIntegrationError {
        component: String,
        function: String,
        error_type: BackendErrorType,
        message: String,
    },
    TuiInteractionError {
        command: String,
        error_type: TuiErrorType,
        message: String,
    },
    ContractInteractionError {
        contract_address: Address,
        function: String,
        error_type: ContractErrorType,
        message: String,
    },
    EnvironmentError {
        error_type: EnvironmentErrorType,
        message: String,
    },
    AnvilError {
        error_type: AnvilErrorType,
        message: String,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConfigErrorType {
    FileNotFound,
    InvalidFormat,
    ValidationFailed,
    BackupFailed,
    RestoreFailed,
    AddressInvalid,
    KeyMissing,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackendErrorType {
    ComponentNotFound,
    FunctionNotFound,
    ExecutionFailed,
    ValidationFailed,
    TransactionReverted,
    GasEstimationFailed,
    NonceError,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TuiErrorType {
    CommandNotFound,
    ExecutionTimeout,
    InvalidOutput,
    DataMismatch,
    UiStateInvalid,
    ProcessSpawnFailed,
    OutputParseFailed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContractErrorType {
    DeploymentFailed,
    CallFailed,
    TransactionReverted,
    InvalidAddress,
    AbiDecodingFailed,
    GasLimitExceeded,
    InsufficientBalance,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EnvironmentErrorType {
    AnvilNotAvailable,
    NetworkConnectionFailed,
    DependencyMissing,
    PermissionDenied,
    ResourceExhausted,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnvilErrorType {
    StartupFailed,
    ConnectionFailed,
    ForkFailed,
    StateInvalid,
    BlockchainError,
}

/// Test error with context
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestError {
    pub error_type: IntegrationTestError,
    pub context: String,
    pub timestamp: DateTime<Utc>,
    pub recoverable: bool,
    pub suggested_action: Option<String>,
}

// ============= REPORTING TYPES =============

/// Comprehensive integration test report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationTestReport {
    pub summary: TestSummary,
    pub detailed_results: IntegrationTestResult,
    pub failure_analysis: Vec<FailureAnalysis>,
    pub recommendations: Vec<String>,
    pub environment_info: TestEnvironment,
    pub execution_metadata: ExecutionMetadata,
}

/// Executive summary of test results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestSummary {
    pub overall_status: TestStatus,
    pub total_tests_run: u32,
    pub tests_passed: u32,
    pub tests_failed: u32,
    pub tests_skipped: u32,
    pub critical_failures: u32,
    pub warnings: u32,
    pub total_execution_time: Duration,
    pub production_ready: bool,
}

/// Test execution status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TestStatus {
    Passed,
    Failed,
    PartiallyPassed,
    Skipped,
    Error,
}

/// Failure analysis with remediation steps
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FailureAnalysis {
    pub component: String,
    pub failure_type: String,
    pub description: String,
    pub impact_level: ImpactLevel,
    pub root_cause: Option<String>,
    pub remediation_steps: Vec<String>,
    pub related_failures: Vec<String>,
}

/// Impact level classification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ImpactLevel {
    Critical,    // Blocks production deployment
    High,        // Significant functionality impacted
    Medium,      // Minor functionality impacted
    Low,         // Cosmetic or non-functional issues
    Informational, // No impact, informational only
}

/// Test environment information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestEnvironment {
    pub anvil_version: String,
    pub anvil_url: String,
    pub chain_id: u64,
    pub fork_block_number: Option<u64>,
    pub contract_address: Address,
    pub test_accounts: Vec<Address>,
    pub initial_balances: HashMap<Address, U256>,
    pub system_info: SystemInfo,
}

/// System information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    pub os: String,
    pub arch: String,
    pub rust_version: String,
    pub cargo_version: String,
    pub available_memory_mb: u64,
    pub cpu_cores: u32,
}

/// Test execution metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionMetadata {
    pub test_suite_version: String,
    pub execution_id: String,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub executed_by: String,
    pub command_line_args: Vec<String>,
    pub environment_variables: HashMap<String, String>,
}

// ============= UTILITY FUNCTIONS =============

impl TestResult {
    /// Create a new successful test result
    pub fn success(component_name: String, details: TestDetails) -> Self {
        Self {
            component_name,
            success: true,
            execution_time: Duration::from_secs(0),
            timestamp: Utc::now(),
            details,
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }
    
    /// Create a new failed test result
    pub fn failure(component_name: String, details: TestDetails, errors: Vec<TestError>) -> Self {
        Self {
            component_name,
            success: false,
            execution_time: Duration::from_secs(0),
            timestamp: Utc::now(),
            details,
            errors,
            warnings: Vec::new(),
        }
    }
    
    /// Add execution time to the result
    pub fn with_execution_time(mut self, duration: Duration) -> Self {
        self.execution_time = duration;
        self
    }
    
    /// Add warnings to the result
    pub fn with_warnings(mut self, warnings: Vec<String>) -> Self {
        self.warnings = warnings;
        self
    }
}

impl TestError {
    /// Create a new test error
    pub fn new(error_type: IntegrationTestError, context: String, recoverable: bool) -> Self {
        Self {
            error_type,
            context,
            timestamp: Utc::now(),
            recoverable,
            suggested_action: None,
        }
    }
    
    /// Add a suggested action for error recovery
    pub fn with_suggestion(mut self, action: String) -> Self {
        self.suggested_action = Some(action);
        self
    }
}

impl Default for TestEnvironment {
    fn default() -> Self {
        Self {
            anvil_version: "unknown".to_string(),
            anvil_url: "http://localhost:8545".to_string(),
            chain_id: 8453,
            fork_block_number: None,
            contract_address: Address::zero(),
            test_accounts: Vec::new(),
            initial_balances: HashMap::new(),
            system_info: SystemInfo::default(),
        }
    }
}

impl Default for SystemInfo {
    fn default() -> Self {
        Self {
            os: std::env::consts::OS.to_string(),
            arch: std::env::consts::ARCH.to_string(),
            rust_version: "unknown".to_string(),
            cargo_version: "unknown".to_string(),
            available_memory_mb: 0,
            cpu_cores: 1,
        }
    }
}

/// Generate a unique execution ID for test runs
pub fn generate_execution_id() -> String {
    use std::time::{SystemTime, UNIX_EPOCH};
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();
    format!("integration_test_{}", timestamp)
}

/// Convert Duration to human-readable string
pub fn format_duration(duration: Duration) -> String {
    let secs = duration.as_secs();
    let millis = duration.subsec_millis();
    
    if secs > 0 {
        format!("{}.{:03}s", secs, millis)
    } else {
        format!("{}ms", millis)
    }
}

/// Check if an address is valid (non-zero)
pub fn is_valid_address(address: Address) -> bool {
    address != Address::zero()
}

// New structs for backend_tester.rs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionAnalyzer;

impl ExecutionAnalyzer {
    pub fn new() -> Self {
        Self
    }

    pub async fn analyze_stargate_compass_interactions(&self) -> Result<Vec<String>> {
        Ok(vec!["mock_interaction_method".to_string()])
    }

    pub async fn analyze_dispatcher_methods(&self) -> Result<Vec<String>> {
        Ok(vec!["mock_dispatcher_method".to_string()])
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestOpportunity {
    pub id: String,
    pub opportunity_type: String,
    pub estimated_profit_usd: Decimal,
    pub chain_id: u64,
    pub source_chain: u64,
    pub destination_chain: u64,
    pub token_path: Vec<Address>,
    pub amount_in: U256,
    pub expected_amount_out: U256,
    pub gas_estimate: U256,
    pub deadline: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MockTransaction {
    pub to: Address,
    pub value: U256,
    pub gas_limit: U256,
    pub gas_price: U256,
    pub nonce: U256,
    pub data: Vec<u8>,
    pub opportunity_id: String,
}
