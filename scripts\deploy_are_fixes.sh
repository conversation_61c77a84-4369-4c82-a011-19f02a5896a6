#!/bin/bash
# Phased deployment script for Aetheric Resonance Engine fixes
# This script implements the deployment strategy with validation checkpoints

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/deployment_$(date +%Y%m%d_%H%M%S).log"
CONFIG_FILE="$PROJECT_ROOT/config/deployment.toml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        WARN)  echo -e "${YELLOW}[WARN]${NC} $message" | tee -a "$LOG_FILE" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        DEBUG) echo -e "${BLUE}[DEBUG]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
}

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

# Deployment phases
declare -A PHASES=(
    ["development"]="Development"
    ["core-scoring"]="CoreScoring"
    ["mathematical-components"]="MathematicalComponents"
    ["component-integration"]="ComponentIntegration"
    ["data-quality"]="DataQuality"
    ["configuration-monitoring"]="ConfigurationMonitoring"
    ["full-production"]="FullProduction"
)

# Traffic percentages for each phase
declare -A TRAFFIC_PERCENTAGES=(
    ["development"]="0"
    ["core-scoring"]="5"
    ["mathematical-components"]="15"
    ["component-integration"]="35"
    ["data-quality"]="60"
    ["configuration-monitoring"]="85"
    ["full-production"]="100"
)

# Function to check prerequisites
check_prerequisites() {
    log INFO "Checking deployment prerequisites..."
    
    # Check if Rust is installed
    if ! command -v cargo &> /dev/null; then
        log ERROR "Cargo not found. Please install Rust."
        exit 1
    fi
    
    # Check if project builds
    log INFO "Building project to verify compilation..."
    if ! cargo build --release; then
        log ERROR "Project build failed. Please fix compilation errors before deployment."
        exit 1
    fi
    
    # Check if tests pass
    log INFO "Running tests to verify functionality..."
    if ! cargo test; then
        log ERROR "Tests failed. Please fix test failures before deployment."
        exit 1
    fi
    
    # Check if configuration file exists
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log WARN "Deployment configuration file not found. Creating default configuration..."
        create_default_config
    fi
    
    log INFO "Prerequisites check completed successfully."
}

# Function to create default deployment configuration
create_default_config() {
    mkdir -p "$(dirname "$CONFIG_FILE")"
    cat > "$CONFIG_FILE" << 'EOF'
[deployment]
current_phase = "Development"
auto_rollback_enabled = true
health_check_interval_seconds = 30
failure_threshold = 3

[traffic_routing]
new_implementation_percentage = 0.0
legacy_implementation_percentage = 100.0
canary_enabled = false
canary_percentage = 0.01

[health_checks]
check_interval_seconds = 30
failure_threshold = 3
timeout_seconds = 10
auto_rollback_enabled = true

[rollback]
enabled = true
timeout_seconds = 300
preserve_data = true
strategy = "GradualTrafficShift"

[feature_flags]
# Core scoring engine fixes
scoring_engine_weight_fix = false
neutral_score_fallbacks = false
complete_geometric_score = false

# Mathematical component fixes
hurst_exponent_fix = false
market_rhythm_stability_fix = false
vesica_piscis_negative_fix = false
temporal_harmonics_integration = false
liquidity_centroid_bias_fix = false

# Component integration fixes
network_state_integration = false
asset_centrality_initialization = false
token_registry_integration = false
vesica_piscis_geometric_integration = false

# Data quality and validation fixes
network_coherence_fix = false
censorship_detection = false
sequencer_health_monitoring = false

# Configuration and monitoring fixes
enhanced_configuration_validation = false
graceful_degradation_patterns = false
performance_monitoring = false
enhanced_error_propagation = false
EOF
    log INFO "Created default deployment configuration at $CONFIG_FILE"
}

# Function to run validation checkpoints for a phase
run_validation_checkpoints() {
    local phase=$1
    log INFO "Running validation checkpoints for phase: $phase"
    
    case $phase in
        "development")
            run_checkpoint "configuration_validation"
            run_checkpoint "unit_tests"
            ;;
        "core-scoring")
            run_checkpoint "configuration_validation"
            run_checkpoint "unit_tests"
            run_checkpoint "scoring_engine_validation"
            run_checkpoint "weight_application_validation"
            ;;
        "mathematical-components")
            run_checkpoint "configuration_validation"
            run_checkpoint "unit_tests"
            run_checkpoint "scoring_engine_validation"
            run_checkpoint "mathematical_accuracy"
            run_checkpoint "numerical_stability"
            ;;
        "component-integration")
            run_checkpoint "configuration_validation"
            run_checkpoint "unit_tests"
            run_checkpoint "integration_tests"
            run_checkpoint "data_flow_validation"
            run_checkpoint "network_integration"
            ;;
        "data-quality")
            run_checkpoint "configuration_validation"
            run_checkpoint "unit_tests"
            run_checkpoint "integration_tests"
            run_checkpoint "data_quality_validation"
            run_checkpoint "error_handling_validation"
            ;;
        "configuration-monitoring")
            run_checkpoint "configuration_validation"
            run_checkpoint "unit_tests"
            run_checkpoint "integration_tests"
            run_checkpoint "performance_validation"
            run_checkpoint "monitoring_validation"
            ;;
        "full-production")
            run_checkpoint "configuration_validation"
            run_checkpoint "unit_tests"
            run_checkpoint "integration_tests"
            run_checkpoint "end_to_end_validation"
            run_checkpoint "production_readiness"
            ;;
    esac
}

# Function to run a specific validation checkpoint
run_checkpoint() {
    local checkpoint=$1
    log INFO "Running validation checkpoint: $checkpoint"
    
    case $checkpoint in
        "configuration_validation")
            # Validate configuration files
            if cargo run --bin validate_config; then
                log INFO "Configuration validation passed"
            else
                log ERROR "Configuration validation failed"
                return 1
            fi
            ;;
        "unit_tests")
            # Run unit tests
            if cargo test --lib; then
                log INFO "Unit tests passed"
            else
                log ERROR "Unit tests failed"
                return 1
            fi
            ;;
        "integration_tests")
            # Run integration tests
            if cargo test --test '*'; then
                log INFO "Integration tests passed"
            else
                log ERROR "Integration tests failed"
                return 1
            fi
            ;;
        "scoring_engine_validation")
            # Validate scoring engine fixes
            if cargo test scoring_engine; then
                log INFO "Scoring engine validation passed"
            else
                log ERROR "Scoring engine validation failed"
                return 1
            fi
            ;;
        "weight_application_validation")
            # Validate weight application
            if cargo test weight_application; then
                log INFO "Weight application validation passed"
            else
                log ERROR "Weight application validation failed"
                return 1
            fi
            ;;
        "mathematical_accuracy")
            # Validate mathematical accuracy
            if cargo test mathematical_accuracy; then
                log INFO "Mathematical accuracy validation passed"
            else
                log ERROR "Mathematical accuracy validation failed"
                return 1
            fi
            ;;
        "numerical_stability")
            # Validate numerical stability
            if cargo test numerical_stability; then
                log INFO "Numerical stability validation passed"
            else
                log ERROR "Numerical stability validation failed"
                return 1
            fi
            ;;
        "data_flow_validation")
            # Validate data flow
            if cargo test data_flow; then
                log INFO "Data flow validation passed"
            else
                log ERROR "Data flow validation failed"
                return 1
            fi
            ;;
        "network_integration")
            # Validate network integration
            if cargo test network_integration; then
                log INFO "Network integration validation passed"
            else
                log ERROR "Network integration validation failed"
                return 1
            fi
            ;;
        "data_quality_validation")
            # Validate data quality
            if cargo test data_quality; then
                log INFO "Data quality validation passed"
            else
                log ERROR "Data quality validation failed"
                return 1
            fi
            ;;
        "error_handling_validation")
            # Validate error handling
            if cargo test error_handling; then
                log INFO "Error handling validation passed"
            else
                log ERROR "Error handling validation failed"
                return 1
            fi
            ;;
        "performance_validation")
            # Validate performance
            if cargo test --release performance; then
                log INFO "Performance validation passed"
            else
                log ERROR "Performance validation failed"
                return 1
            fi
            ;;
        "monitoring_validation")
            # Validate monitoring
            if cargo test monitoring; then
                log INFO "Monitoring validation passed"
            else
                log ERROR "Monitoring validation failed"
                return 1
            fi
            ;;
        "end_to_end_validation")
            # Run end-to-end tests
            if cargo test --test e2e; then
                log INFO "End-to-end validation passed"
            else
                log ERROR "End-to-end validation failed"
                return 1
            fi
            ;;
        "production_readiness")
            # Check production readiness
            if "$SCRIPT_DIR/check_production_readiness.sh"; then
                log INFO "Production readiness validation passed"
            else
                log ERROR "Production readiness validation failed"
                return 1
            fi
            ;;
        *)
            log WARN "Unknown validation checkpoint: $checkpoint"
            ;;
    esac
}

# Function to enable feature flags for a phase
enable_phase_features() {
    local phase=$1
    log INFO "Enabling feature flags for phase: $phase"
    
    case $phase in
        "core-scoring")
            enable_feature_flag "scoring_engine_weight_fix"
            enable_feature_flag "neutral_score_fallbacks"
            enable_feature_flag "complete_geometric_score"
            ;;
        "mathematical-components")
            enable_feature_flag "hurst_exponent_fix"
            enable_feature_flag "market_rhythm_stability_fix"
            enable_feature_flag "vesica_piscis_negative_fix"
            enable_feature_flag "temporal_harmonics_integration"
            enable_feature_flag "liquidity_centroid_bias_fix"
            ;;
        "component-integration")
            enable_feature_flag "network_state_integration"
            enable_feature_flag "asset_centrality_initialization"
            enable_feature_flag "token_registry_integration"
            enable_feature_flag "vesica_piscis_geometric_integration"
            ;;
        "data-quality")
            enable_feature_flag "network_coherence_fix"
            enable_feature_flag "censorship_detection"
            enable_feature_flag "sequencer_health_monitoring"
            ;;
        "configuration-monitoring")
            enable_feature_flag "enhanced_configuration_validation"
            enable_feature_flag "graceful_degradation_patterns"
            enable_feature_flag "performance_monitoring"
            enable_feature_flag "enhanced_error_propagation"
            ;;
    esac
}

# Function to enable a specific feature flag
enable_feature_flag() {
    local flag=$1
    log INFO "Enabling feature flag: $flag"
    
    # Update configuration file
    if command -v toml &> /dev/null; then
        toml set "$CONFIG_FILE" "feature_flags.$flag" true
    else
        # Fallback to sed if toml command is not available
        sed -i "s/^$flag = false/$flag = true/" "$CONFIG_FILE"
    fi
}

# Function to update traffic routing
update_traffic_routing() {
    local phase=$1
    local percentage=${TRAFFIC_PERCENTAGES[$phase]}
    
    log INFO "Updating traffic routing: ${percentage}% to new implementation"
    
    # Update configuration file
    if command -v toml &> /dev/null; then
        toml set "$CONFIG_FILE" "traffic_routing.new_implementation_percentage" "0.${percentage}"
        toml set "$CONFIG_FILE" "traffic_routing.legacy_implementation_percentage" "$(echo "scale=2; 1.0 - 0.${percentage}" | bc)"
    else
        # Fallback to sed
        sed -i "s/^new_implementation_percentage = .*/new_implementation_percentage = 0.${percentage}/" "$CONFIG_FILE"
        sed -i "s/^legacy_implementation_percentage = .*/legacy_implementation_percentage = $(echo "scale=2; 1.0 - 0.${percentage}" | bc)/" "$CONFIG_FILE"
    fi
    
    # Restart service to apply new routing
    restart_service
}

# Function to restart the service
restart_service() {
    log INFO "Restarting service to apply configuration changes..."
    
    # Check if systemd service exists
    if systemctl is-active --quiet basilisk-bot; then
        sudo systemctl restart basilisk-bot
        log INFO "Service restarted via systemd"
    else
        # Fallback to process management
        if pgrep -f "basilisk" > /dev/null; then
            pkill -f "basilisk"
            sleep 2
        fi
        
        # Start the service in background
        nohup cargo run --release > "$PROJECT_ROOT/logs/service.log" 2>&1 &
        log INFO "Service started in background"
    fi
}

# Function to monitor health during deployment
monitor_health() {
    local phase=$1
    local duration=${2:-300} # Default 5 minutes
    
    log INFO "Monitoring health for phase: $phase (duration: ${duration}s)"
    
    local start_time=$(date +%s)
    local end_time=$((start_time + duration))
    local consecutive_failures=0
    local failure_threshold=3
    
    while [[ $(date +%s) -lt $end_time ]]; do
        if run_health_check; then
            consecutive_failures=0
            log DEBUG "Health check passed"
        else
            consecutive_failures=$((consecutive_failures + 1))
            log WARN "Health check failed (${consecutive_failures}/${failure_threshold})"
            
            if [[ $consecutive_failures -ge $failure_threshold ]]; then
                log ERROR "Health check failure threshold reached. Triggering rollback."
                return 1
            fi
        fi
        
        sleep 30
    done
    
    log INFO "Health monitoring completed successfully for phase: $phase"
    return 0
}

# Function to run health check
run_health_check() {
    # Check if service is responding
    if ! pgrep -f "basilisk" > /dev/null; then
        log ERROR "Service process not found"
        return 1
    fi
    
    # Check if service is listening on expected port
    if ! netstat -tuln | grep -q ":8080"; then
        log ERROR "Service not listening on expected port"
        return 1
    fi
    
    # Run application-specific health checks
    if cargo run --bin health_check; then
        return 0
    else
        return 1
    fi
}

# Function to deploy to a specific phase
deploy_phase() {
    local phase=$1
    
    log INFO "Starting deployment to phase: $phase"
    
    # Step 1: Run validation checkpoints
    if ! run_validation_checkpoints "$phase"; then
        log ERROR "Validation checkpoints failed for phase: $phase"
        return 1
    fi
    
    # Step 2: Enable feature flags
    enable_phase_features "$phase"
    
    # Step 3: Update traffic routing
    update_traffic_routing "$phase"
    
    # Step 4: Monitor health
    if ! monitor_health "$phase"; then
        log ERROR "Health monitoring failed for phase: $phase. Initiating rollback."
        rollback_deployment
        return 1
    fi
    
    log INFO "Successfully deployed to phase: $phase"
    return 0
}

# Function to rollback deployment
rollback_deployment() {
    log WARN "Initiating deployment rollback..."
    
    # Run rollback script
    if [[ -f "$SCRIPT_DIR/rollback_are_fixes.sh" ]]; then
        "$SCRIPT_DIR/rollback_are_fixes.sh"
    else
        log ERROR "Rollback script not found. Manual intervention required."
        return 1
    fi
}

# Function to get current deployment phase
get_current_phase() {
    if [[ -f "$CONFIG_FILE" ]]; then
        if command -v toml &> /dev/null; then
            toml get "$CONFIG_FILE" "deployment.current_phase" 2>/dev/null || echo "development"
        else
            grep "current_phase" "$CONFIG_FILE" | cut -d'"' -f2 || echo "development"
        fi
    else
        echo "development"
    fi
}

# Function to get next phase
get_next_phase() {
    local current=$1
    
    case $current in
        "development") echo "core-scoring" ;;
        "core-scoring") echo "mathematical-components" ;;
        "mathematical-components") echo "component-integration" ;;
        "component-integration") echo "data-quality" ;;
        "data-quality") echo "configuration-monitoring" ;;
        "configuration-monitoring") echo "full-production" ;;
        "full-production") echo "" ;;
        *) echo "core-scoring" ;;
    esac
}

# Function to show deployment status
show_status() {
    local current_phase=$(get_current_phase)
    local traffic_percentage=${TRAFFIC_PERCENTAGES[$current_phase]:-0}
    
    echo
    echo "=== Aetheric Resonance Engine Deployment Status ==="
    echo "Current Phase: ${PHASES[$current_phase]:-Unknown}"
    echo "Traffic to New Implementation: ${traffic_percentage}%"
    echo "Traffic to Legacy Implementation: $((100 - traffic_percentage))%"
    echo
    
    # Show enabled feature flags
    echo "Enabled Feature Flags:"
    if [[ -f "$CONFIG_FILE" ]]; then
        grep "= true" "$CONFIG_FILE" | grep -v "auto_rollback_enabled\|canary_enabled\|enabled" || echo "  None"
    else
        echo "  Configuration file not found"
    fi
    echo
}

# Main deployment function
main() {
    local command=${1:-"status"}
    
    case $command in
        "init")
            log INFO "Initializing deployment environment..."
            check_prerequisites
            log INFO "Deployment environment initialized successfully."
            ;;
        "deploy")
            local target_phase=${2:-""}
            if [[ -z "$target_phase" ]]; then
                # Deploy to next phase
                local current_phase=$(get_current_phase)
                target_phase=$(get_next_phase "$current_phase")
                
                if [[ -z "$target_phase" ]]; then
                    log INFO "Already at final deployment phase."
                    exit 0
                fi
            fi
            
            log INFO "Deploying to phase: $target_phase"
            check_prerequisites
            
            if deploy_phase "$target_phase"; then
                log INFO "Deployment to phase $target_phase completed successfully."
            else
                log ERROR "Deployment to phase $target_phase failed."
                exit 1
            fi
            ;;
        "rollback")
            log INFO "Rolling back deployment..."
            rollback_deployment
            ;;
        "status")
            show_status
            ;;
        "health")
            log INFO "Running health check..."
            if run_health_check; then
                log INFO "Health check passed."
            else
                log ERROR "Health check failed."
                exit 1
            fi
            ;;
        *)
            echo "Usage: $0 {init|deploy [phase]|rollback|status|health}"
            echo
            echo "Commands:"
            echo "  init                 - Initialize deployment environment"
            echo "  deploy [phase]       - Deploy to next phase or specified phase"
            echo "  rollback            - Rollback to previous phase"
            echo "  status              - Show current deployment status"
            echo "  health              - Run health check"
            echo
            echo "Available phases:"
            echo "  development"
            echo "  core-scoring"
            echo "  mathematical-components"
            echo "  component-integration"
            echo "  data-quality"
            echo "  configuration-monitoring"
            echo "  full-production"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"