// MISSION: Stargate Compass Integration Test Module
// WHY: Organize and expose all integration test components for StargateCompassV1 testing
// HOW: Module structure with public exports and test orchestration

pub mod tui_functionality_tester;
pub mod command_executor;
pub mod output_parser;
pub mod data_validator;
pub mod test_data_validation_integration;
pub mod transaction_command_tester;
pub mod test_transaction_commands;
pub mod test_transaction_command_syntax;
pub mod end_to_end_workflow;
pub mod workflow_result_synthesis;
pub mod test_end_to_end_workflow;
pub mod test_reporter;
pub mod test_result_aggregation_integration;
pub mod failure_diagnostics;
pub mod test_failure_diagnostics_integration;
pub mod integration_test_controller;
pub mod test_execution_pipeline;
pub mod contract_interaction_utils;
pub mod test_contract_interaction_utils;
pub mod contract_interaction_demo;

// Re-export main components for easier access
pub use tui_functionality_tester::{
    TuiContractCommand, 
    TuiCommandResult,
    TuiKeyInput,
    DataValidationResult,
};

pub use command_executor::{
    CommandExecutor,
    CommandConfig,
    CommandExecutionResult,
    KeyInput,
    SpecialKey,
    OutputLine,
    OutputSource,
};

pub use output_parser::{
    TuiOutputParser,
    ParsedTuiOutput,
    LogEntry,
    LogSeverity,
    BalanceData,
    ContractInteraction,
    ContractInteractionType,
    SystemStatus,
    CommandAck,
    ValidationResult,
    ContractData,
};

pub use data_validator::{
    TuiDataValidator,
    ValidationConfig,
    ContractStateData,
    BalanceData as ValidatorBalanceData,
    TransactionStatusData,
    TransactionStatus,
    TransactionHistoryData,
};

pub use transaction_command_tester::{
    TransactionCommandTester,
    TransactionCommandTestResult,
    TransactionVerificationResult,
    EmergencyStopTestResult,
    ErrorMessageValidationResult,
    TransactionTestSuite,
    TransactionTestStatistics,
};

pub use end_to_end_workflow::{
    WorkflowConfig,
    EndToEndWorkflowResult,
    OpportunitySimulationResult,
    BackendExecutionResult,
    TuiValidationResult,
    DataPipelineCoherenceResult,
    ProfitLossValidationResult,
};

pub use workflow_result_synthesis::{
    WorkflowResultSynthesizer,
    SynthesisConfig,
    WorkflowSynthesisResult,
    ComponentAnalysisResult,
    DataFlowAnalysisResult,
    TransactionLifecycleAnalysisResult,
    SystemCoherenceVerificationResult,
    RemediationRecommendation,
    RemediationSeverity,
};

pub use test_reporter::{
    TestResultAggregator,
    TestReportConfiguration,
    ComprehensiveTestReport,
    OverallTestSummary,
    TuiTestAnalysis,
    TransactionTestAnalysis,
    WorkflowTestAnalysis,
    StressTestAnalysis,
    FailureAnalysis,
    PerformanceAnalysis,
    TestRecommendation,
    RecommendationCategory,
    RecommendationPriority,
    TestResultExporter,
};

pub use failure_diagnostics::{
    FailureDiagnosticsEngine,
    DiagnosticConfiguration,
    ComprehensiveFailureDiagnostic,
    TuiFailureDiagnostic,
    TransactionFailureDiagnostic,
    WorkflowFailureDiagnostic,
    CommandFailureDetail,
    FailureCategory,
    FailureSeverity,
    ComponentHealthStatus,
    FailureDiagnosticReporter,
    DiagnosticSummaryStats,
};

pub use integration_test_controller::{
    IntegrationTestController,
    IntegrationTestConfig,
    TestExecutionState,
    TestPhase,
    IntegrationTestResult,
    TestExecutionSummary,
    PerformanceMetrics,
    ConfigurationManager,
    BackendIntegrationTester,
    TuiFunctionalityTester,
    TestReporter,
    EndToEndWorkflowValidator,
};

// Include mock controller for binary execution
pub mod integration_test_controller_mock;
pub use integration_test_controller_mock::*;

pub use test_execution_pipeline::{
    TestExecutionPipeline,
    PipelineConfig,
    ErrorCategory,
    RecoveryStrategy,
    RecoveryAction,
    ExecutionStateTracker,
    RetryPolicyManager,
    LoggingContextManager,
    PipelineExecutionResult,
    ErrorSummary,
    PerformanceSummary,
    RecoverySummary,
    ExecutionError,
    ExecutionWarning,
    RecoveryActionTaken,
};

pub use contract_interaction_utils::{
    ContractInteractionTester,
    ContractCallResult,
    TransactionSimulationResult,
    TransactionExecutionResult,
    ContractStateVerificationResult,
    ContractInteractionTestSuite,
};

pub use test_contract_interaction_utils::{
    ContractInteractionIntegrationTest,
    ContractInteractionTestReport,
    ErrorHandlingTestResult,
};

use anyhow::Result;
use std::time::Duration;
use tracing::{info, warn, error};

/// Integration test orchestrator for TUI functionality
pub struct TuiIntegrationTestOrchestrator {
    pub tui_tester: TuiFunctionalityTester,
    pub output_parser: TuiOutputParser,
    pub anvil_url: String,
    pub contract_address: String,
}

impl TuiIntegrationTestOrchestrator {
    /// Create new test orchestrator
    pub fn new(anvil_url: String, contract_address: String) -> Result<Self> {
        let tui_tester = TuiFunctionalityTester::new(anvil_url.clone(), contract_address.clone());
        let output_parser = TuiOutputParser::new()?;

        Ok(Self {
            tui_tester,
            output_parser,
            anvil_url,
            contract_address,
        })
    }

    /// Run comprehensive TUI functionality tests
    pub async fn run_comprehensive_tests(&mut self) -> Result<TuiTestSuite> {
        info!("Starting comprehensive TUI integration tests for StargateCompassV1");

        let mut test_suite = TuiTestSuite::new();

        // Test 1: Emergency Stop Command
        info!("Testing emergency stop command...");
        match self.tui_tester.execute_command("emergency_stop").await {
            Ok(result) => {
                test_suite.emergency_stop_result = Some(result.clone());
                if result.success {
                    info!("✅ Emergency stop test passed");
                } else {
                    warn!("❌ Emergency stop test failed: {:?}", result.error_message);
                }
            }
            Err(e) => {
                error!("Emergency stop test error: {}", e);
                test_suite.emergency_stop_result = Some(TuiCommandResult {
                    command_name: "emergency_stop".to_string(),
                    success: false,
                    execution_time_ms: 0,
                    output_captured: String::new(),
                    error_message: Some(e.to_string()),
                    contract_interaction_detected: false,
                    data_validation_results: Vec::new(),
                });
            }
        }

        // Test 2: Pause Bot Command
        info!("Testing pause bot command...");
        match self.tui_tester.execute_command("pause_bot").await {
            Ok(result) => {
                test_suite.pause_bot_result = Some(result.clone());
                if result.success {
                    info!("✅ Pause bot test passed");
                } else {
                    warn!("❌ Pause bot test failed: {:?}", result.error_message);
                }
            }
            Err(e) => {
                error!("Pause bot test error: {}", e);
                test_suite.pause_bot_result = Some(TuiCommandResult {
                    command_name: "pause_bot".to_string(),
                    success: false,
                    execution_time_ms: 0,
                    output_captured: String::new(),
                    error_message: Some(e.to_string()),
                    contract_interaction_detected: false,
                    data_validation_results: Vec::new(),
                });
            }
        }

        // Test 3: Restart Bot Command
        info!("Testing restart bot command...");
        match self.tui_tester.execute_command("restart_bot").await {
            Ok(result) => {
                test_suite.restart_bot_result = Some(result.clone());
                if result.success {
                    info!("✅ Restart bot test passed");
                } else {
                    warn!("❌ Restart bot test failed: {:?}", result.error_message);
                }
            }
            Err(e) => {
                error!("Restart bot test error: {}", e);
                test_suite.restart_bot_result = Some(TuiCommandResult {
                    command_name: "restart_bot".to_string(),
                    success: false,
                    execution_time_ms: 0,
                    output_captured: String::new(),
                    error_message: Some(e.to_string()),
                    contract_interaction_detected: false,
                    data_validation_results: Vec::new(),
                });
            }
        }

        // Test 4: Execute Opportunity Command
        info!("Testing execute opportunity command...");
        match self.tui_tester.execute_command("execute_opportunity").await {
            Ok(result) => {
                test_suite.execute_opportunity_result = Some(result.clone());
                if result.success {
                    info!("✅ Execute opportunity test passed");
                } else {
                    warn!("❌ Execute opportunity test failed: {:?}", result.error_message);
                }
            }
            Err(e) => {
                error!("Execute opportunity test error: {}", e);
                test_suite.execute_opportunity_result = Some(TuiCommandResult {
                    command_name: "execute_opportunity".to_string(),
                    success: false,
                    execution_time_ms: 0,
                    output_captured: String::new(),
                    error_message: Some(e.to_string()),
                    contract_interaction_detected: false,
                    data_validation_results: Vec::new(),
                });
            }
        }

        // Test 5: Balance Query Command
        info!("Testing balance query command...");
        match self.tui_tester.execute_command("query_balances").await {
            Ok(result) => {
                test_suite.query_balances_result = Some(result.clone());
                if result.success {
                    info!("✅ Balance query test passed");
                } else {
                    warn!("❌ Balance query test failed: {:?}", result.error_message);
                }
            }
            Err(e) => {
                error!("Balance query test error: {}", e);
                test_suite.query_balances_result = Some(TuiCommandResult {
                    command_name: "query_balances".to_string(),
                    success: false,
                    execution_time_ms: 0,
                    output_captured: String::new(),
                    error_message: Some(e.to_string()),
                    contract_interaction_detected: false,
                    data_validation_results: Vec::new(),
                });
            }
        }

        // Test 6: Contract Status Query
        info!("Testing contract status query...");
        match self.tui_tester.execute_command("query_contract_status").await {
            Ok(result) => {
                test_suite.query_contract_status_result = Some(result.clone());
                if result.success {
                    info!("✅ Contract status query test passed");
                } else {
                    warn!("❌ Contract status query test failed: {:?}", result.error_message);
                }
            }
            Err(e) => {
                error!("Contract status query test error: {}", e);
                test_suite.query_contract_status_result = Some(TuiCommandResult {
                    command_name: "query_contract_status".to_string(),
                    success: false,
                    execution_time_ms: 0,
                    output_captured: String::new(),
                    error_message: Some(e.to_string()),
                    contract_interaction_detected: false,
                    data_validation_results: Vec::new(),
                });
            }
        }

        // Calculate overall results
        test_suite.calculate_summary();

        info!("Comprehensive TUI integration tests completed");
        info!("Results: {}/{} tests passed", test_suite.passed_tests, test_suite.total_tests);

        Ok(test_suite)
    }

    /// Test specific TUI command with custom validation
    pub async fn test_command_with_validation(
        &mut self,
        command_name: &str,
        expected_patterns: Vec<String>,
        timeout_seconds: u64,
    ) -> Result<TuiCommandValidationResult> {
        info!("Testing command '{}' with custom validation", command_name);

        let start_time = std::time::Instant::now();
        
        // Execute the command
        let command_result = self.tui_tester.execute_command(command_name).await?;
        
        // Parse the output
        let parsed_output = self.output_parser.parse_output(&command_result.output_captured)?;
        
        // Validate against expected patterns
        let validation_results = self.output_parser.validate_output(&parsed_output, &expected_patterns);
        
        // Extract contract-specific data
        let contract_data = self.output_parser.extract_contract_data(&parsed_output);
        
        let execution_time = start_time.elapsed();
        let all_patterns_found = validation_results.iter().all(|r| r.found);
        
        Ok(TuiCommandValidationResult {
            command_result,
            parsed_output,
            validation_results,
            contract_data,
            all_patterns_found,
            execution_time,
        })
    }

    /// Run stress test with multiple rapid commands
    pub async fn run_stress_test(&mut self, iterations: usize) -> Result<TuiStressTestResult> {
        info!("Running TUI stress test with {} iterations", iterations);

        let mut stress_result = TuiStressTestResult::new();
        let commands = vec!["pause_bot", "restart_bot", "query_balances"];

        for i in 0..iterations {
            let command = &commands[i % commands.len()];
            
            let start_time = std::time::Instant::now();
            match self.tui_tester.execute_command(command).await {
                Ok(result) => {
                    stress_result.successful_commands += 1;
                    stress_result.total_execution_time += result.execution_time_ms;
                    
                    if result.contract_interaction_detected {
                        stress_result.contract_interactions += 1;
                    }
                }
                Err(e) => {
                    stress_result.failed_commands += 1;
                    stress_result.errors.push(format!("Iteration {}: {}", i, e));
                }
            }
            
            stress_result.total_commands += 1;
            
            // Small delay between commands
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        stress_result.average_execution_time = if stress_result.successful_commands > 0 {
            stress_result.total_execution_time / stress_result.successful_commands as u64
        } else {
            0
        };

        info!("Stress test completed: {}/{} commands successful", 
              stress_result.successful_commands, stress_result.total_commands);

        Ok(stress_result)
    }
}

/// Complete test suite results
#[derive(Debug, Clone)]
pub struct TuiTestSuite {
    pub emergency_stop_result: Option<TuiCommandResult>,
    pub pause_bot_result: Option<TuiCommandResult>,
    pub restart_bot_result: Option<TuiCommandResult>,
    pub execute_opportunity_result: Option<TuiCommandResult>,
    pub query_balances_result: Option<TuiCommandResult>,
    pub query_contract_status_result: Option<TuiCommandResult>,
    pub total_tests: usize,
    pub passed_tests: usize,
    pub failed_tests: usize,
    pub total_execution_time: u64,
    pub contract_interactions_detected: usize,
}

impl TuiTestSuite {
    pub fn new() -> Self {
        Self {
            emergency_stop_result: None,
            pause_bot_result: None,
            restart_bot_result: None,
            execute_opportunity_result: None,
            query_balances_result: None,
            query_contract_status_result: None,
            total_tests: 0,
            passed_tests: 0,
            failed_tests: 0,
            total_execution_time: 0,
            contract_interactions_detected: 0,
        }
    }

    pub fn calculate_summary(&mut self) {
        let results = vec![
            &self.emergency_stop_result,
            &self.pause_bot_result,
            &self.restart_bot_result,
            &self.execute_opportunity_result,
            &self.query_balances_result,
            &self.query_contract_status_result,
        ];

        self.total_tests = results.len();
        self.passed_tests = 0;
        self.failed_tests = 0;
        self.total_execution_time = 0;
        self.contract_interactions_detected = 0;

        for result_opt in results {
            if let Some(result) = result_opt {
                if result.success {
                    self.passed_tests += 1;
                } else {
                    self.failed_tests += 1;
                }
                
                self.total_execution_time += result.execution_time_ms;
                
                if result.contract_interaction_detected {
                    self.contract_interactions_detected += 1;
                }
            }
        }
    }

    pub fn success_rate(&self) -> f64 {
        if self.total_tests > 0 {
            self.passed_tests as f64 / self.total_tests as f64
        } else {
            0.0
        }
    }

    pub fn average_execution_time(&self) -> f64 {
        if self.total_tests > 0 {
            self.total_execution_time as f64 / self.total_tests as f64
        } else {
            0.0
        }
    }
}

/// Command validation result with detailed analysis
#[derive(Debug)]
pub struct TuiCommandValidationResult {
    pub command_result: TuiCommandResult,
    pub parsed_output: ParsedTuiOutput,
    pub validation_results: Vec<ValidationResult>,
    pub contract_data: ContractData,
    pub all_patterns_found: bool,
    pub execution_time: Duration,
}

/// Stress test results
#[derive(Debug)]
pub struct TuiStressTestResult {
    pub total_commands: usize,
    pub successful_commands: usize,
    pub failed_commands: usize,
    pub total_execution_time: u64,
    pub average_execution_time: u64,
    pub contract_interactions: usize,
    pub errors: Vec<String>,
}

impl TuiStressTestResult {
    pub fn new() -> Self {
        Self {
            total_commands: 0,
            successful_commands: 0,
            failed_commands: 0,
            total_execution_time: 0,
            average_execution_time: 0,
            contract_interactions: 0,
            errors: Vec::new(),
        }
    }

    pub fn success_rate(&self) -> f64 {
        if self.total_commands > 0 {
            self.successful_commands as f64 / self.total_commands as f64
        } else {
            0.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_orchestrator_creation() {
        let orchestrator = TuiIntegrationTestOrchestrator::new(
            "http://localhost:8545".to_string(),
            "0x1234567890123456789012345678901234567890".to_string(),
        );
        
        assert!(orchestrator.is_ok());
    }

    #[test]
    fn test_test_suite_calculations() {
        let mut suite = TuiTestSuite::new();
        
        // Add some mock results
        suite.emergency_stop_result = Some(TuiCommandResult {
            command_name: "emergency_stop".to_string(),
            success: true,
            execution_time_ms: 1000,
            output_captured: String::new(),
            error_message: None,
            contract_interaction_detected: true,
            data_validation_results: Vec::new(),
        });
        
        suite.pause_bot_result = Some(TuiCommandResult {
            command_name: "pause_bot".to_string(),
            success: false,
            execution_time_ms: 500,
            output_captured: String::new(),
            error_message: Some("Test error".to_string()),
            contract_interaction_detected: false,
            data_validation_results: Vec::new(),
        });
        
        suite.calculate_summary();
        
        assert_eq!(suite.total_tests, 6); // All 6 test slots
        assert_eq!(suite.passed_tests, 1);
        assert_eq!(suite.failed_tests, 1);
        assert_eq!(suite.contract_interactions_detected, 1);
        assert_eq!(suite.success_rate(), 1.0 / 6.0);
    }
}