// MISSION: Test Execution Pipeline with Robust Error Handling
// WHY: Provide resilient test execution with retry logic, partial completion, and comprehensive logging
// HOW: Implement retry mechanisms, error recovery, result persistence, and detailed logging

use super::*;
use anyhow::{Result, anyhow, Context};
use std::sync::Arc;
use std::time::{Duration, Instant};
use std::collections::HashMap;
use tracing::{info, warn, error, debug, trace};
use tokio::time::{timeout, sleep};
use serde::{Serialize, Deserialize};
use std::path::PathBuf;

/// Test execution pipeline with robust error handling and retry logic
pub struct TestExecutionPipeline {
    /// Pipeline configuration
    config: PipelineConfig,
    /// Error recovery strategies
    recovery_strategies: HashMap<ErrorCategory, RecoveryStrategy>,
    /// Test result persistence manager
    persistence_manager: Arc<TestResultPersistenceManager>,
    /// Execution state tracker
    state_tracker: ExecutionStateTracker,
    /// Retry policy manager
    retry_manager: RetryPolicyManager,
    /// Logging context manager
    logging_context: LoggingContextManager,
}

/// Pipeline configuration for error handling and execution
#[derive(Debug, Clone)]
pub struct PipelineConfig {
    /// Maximum retry attempts for transient failures
    pub max_retry_attempts: u32,
    /// Base delay between retry attempts
    pub base_retry_delay: Duration,
    /// Maximum delay between retry attempts (for exponential backoff)
    pub max_retry_delay: Duration,
    /// Maximum delay between retry attempts (for exponential backoff)
    pub exponential_backoff: bool,
    /// Timeout for individual test operations
    pub operation_timeout: Duration,
    /// Whether to continue execution on non-critical failures
    pub continue_on_failure: bool,
    /// Whether to persist intermediate results
    pub persist_intermediate_results: bool,
    /// Directory for result persistence
    pub persistence_directory: PathBuf,
    /// Whether to enable detailed logging
    pub detailed_logging: bool,
    /// Log level for pipeline operations
    pub log_level: LogLevel,
    /// Whether to enable performance monitoring
    pub performance_monitoring: bool,
}

/// Error categories for targeted recovery strategies
#[derive(Debug, Clone, Hash, PartialEq, Eq)]
pub enum ErrorCategory {
    /// Network connectivity issues
    NetworkError,
    /// Timeout-related failures
    TimeoutError,
    /// Configuration-related errors
    ConfigurationError,
    /// Contract interaction failures
    ContractError,
    /// TUI interaction failures
    TuiError,
    /// File system operation failures
    FileSystemError,
    /// Resource exhaustion errors
    ResourceError,
    /// Unknown or unclassified errors
    UnknownError,
}

/// Recovery strategies for different error categories
#[derive(Debug, Clone)]
pub struct RecoveryStrategy {
    /// Maximum retry attempts for this error category
    pub max_retries: u32,
    /// Delay between retry attempts
    pub retry_delay: Duration,
    /// Whether to use exponential backoff
    pub exponential_backoff: bool,
    /// Whether this error is considered critical
    pub is_critical: bool,
    /// Custom recovery actions
    pub recovery_actions: Vec<RecoveryAction>,
}

/// Recovery actions that can be taken for specific errors
#[derive(Debug, Clone)]
pub enum RecoveryAction {
    /// Wait for a specified duration
    Wait(Duration),
    /// Reset connection or state
    Reset,
    /// Restart a component
    Restart(String),
    /// Skip the failing operation
    Skip,
    /// Use alternative approach
    Alternative(String),
    /// Clean up resources
    Cleanup,
}
/// Test result persistence manager for recovery and analysis
pub struct TestResultPersistenceManager {
    /// Base directory for persistence
    base_directory: PathBuf,
    /// Whether persistence is enabled
    enabled: bool,
}

/// Execution state tracker for monitoring and recovery
#[derive(Debug, Clone)]
pub struct ExecutionStateTracker {
    /// Current execution phase
    pub current_phase: String,
    /// Phase start time
    pub phase_start_time: Option<Instant>,
    /// Total execution time
    pub total_execution_time: Duration,
    /// Number of retries performed
    pub retry_count: u32,
    /// Errors encountered
    pub errors_encountered: Vec<ExecutionError>,
    /// Warnings generated
    pub warnings_generated: Vec<ExecutionWarning>,
    /// Performance metrics
    pub performance_metrics: HashMap<String, Duration>,
    /// Recovery actions taken
    pub recovery_actions_taken: Vec<RecoveryActionTaken>,
}

/// Retry policy manager for intelligent retry logic
pub struct RetryPolicyManager {
    /// Retry policies by error category
    policies: HashMap<ErrorCategory, RetryPolicy>,
    /// Global retry configuration
    global_config: GlobalRetryConfig,
}

/// Retry policy for specific error categories
#[derive(Debug, Clone)]
pub struct RetryPolicy {
    /// Maximum retry attempts
    pub max_attempts: u32,
    /// Base delay between attempts
    pub base_delay: Duration,
    /// Maximum delay (for exponential backoff)
    pub max_delay: Duration,
    /// Backoff strategy
    pub backoff_strategy: BackoffStrategy,
    /// Jitter configuration
    pub jitter: JitterConfig,
}

/// Backoff strategies for retry attempts
#[derive(Debug, Clone)]
pub enum BackoffStrategy {
    /// Fixed delay between attempts
    Fixed,
    /// Linear increase in delay
    Linear,
    /// Exponential increase in delay
    Exponential,
    /// Custom backoff function
    Custom(fn(u32) -> Duration),
}

/// Jitter configuration to avoid thundering herd
#[derive(Debug, Clone)]
pub struct JitterConfig {
    /// Whether jitter is enabled
    pub enabled: bool,
    /// Maximum jitter percentage (0.0 to 1.0)
    pub max_percentage: f64,
}

/// Global retry configuration
#[derive(Debug, Clone)]
pub struct GlobalRetryConfig {
    /// Global maximum retry attempts
    pub global_max_attempts: u32,
    /// Global timeout for all retry attempts
    pub global_timeout: Duration,
    /// Whether to fail fast on critical errors
    pub fail_fast_on_critical: bool,
}

/// Logging context manager for structured logging
pub struct LoggingContextManager {
    /// Current logging context
    context: HashMap<String, String>,
    /// Log level
    log_level: LogLevel,
    /// Whether structured logging is enabled
    structured_logging: bool,
}

/// Log levels for pipeline operations
#[derive(Debug, Clone, PartialEq)]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
}
/// Execution error with context and recovery information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionError {
    /// Error category
    pub category: String,
    /// Error message
    pub message: String,
    /// Error context
    pub context: HashMap<String, String>,
    /// Timestamp when error occurred
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Phase when error occurred
    pub phase: String,
    /// Whether error is recoverable
    pub recoverable: bool,
    /// Recovery attempts made
    pub recovery_attempts: u32,
}

/// Execution warning with context
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionWarning {
    /// Warning message
    pub message: String,
    /// Warning context
    pub context: HashMap<String, String>,
    /// Timestamp when warning occurred
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Phase when warning occurred
    pub phase: String,
}

/// Recovery action taken during execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryActionTaken {
    /// Action type
    pub action_type: String,
    /// Action description
    pub description: String,
    /// Timestamp when action was taken
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Whether action was successful
    pub successful: bool,
    /// Additional context
    pub context: HashMap<String, String>,
}

/// Pipeline execution result with comprehensive error information
#[derive(Debug, Clone)]
pub struct PipelineExecutionResult {
    /// Overall success status
    pub success: bool,
    /// Partial completion status
    pub partial_completion: bool,
    /// Phases completed successfully
    pub completed_phases: Vec<String>,
    /// Phases that failed
    pub failed_phases: Vec<String>,
    /// Phases that were skipped
    pub skipped_phases: Vec<String>,
    /// Total execution time
    pub total_execution_time: Duration,
    /// Error summary
    pub error_summary: ErrorSummary,
    /// Performance summary
    pub performance_summary: PerformanceSummary,
    /// Recovery summary
    pub recovery_summary: RecoverySummary,
}

/// Error summary for pipeline execution
#[derive(Debug, Clone)]
pub struct ErrorSummary {
    /// Total errors encountered
    pub total_errors: u32,
    /// Critical errors
    pub critical_errors: u32,
    /// Recoverable errors
    pub recoverable_errors: u32,
    /// Errors by category
    pub errors_by_category: HashMap<String, u32>,
    /// Most common error
    pub most_common_error: Option<String>,
}

/// Performance summary for pipeline execution
#[derive(Debug, Clone)]
pub struct PerformanceSummary {
    /// Total execution time
    pub total_time: Duration,
    /// Time by phase
    pub time_by_phase: HashMap<String, Duration>,
    /// Slowest phase
    pub slowest_phase: Option<String>,
    /// Average retry time
    pub average_retry_time: Duration,
    /// Total retry time
    pub total_retry_time: Duration,
}

/// Recovery summary for pipeline execution
#[derive(Debug, Clone)]
pub struct RecoverySummary {
    /// Total recovery attempts
    pub total_recovery_attempts: u32,
    /// Successful recoveries
    pub successful_recoveries: u32,
    /// Failed recoveries
    pub failed_recoveries: u32,
    /// Recovery actions by type
    pub recovery_actions_by_type: HashMap<String, u32>,
    /// Most effective recovery action
    pub most_effective_recovery: Option<String>,
}
impl Default for PipelineConfig {
    fn default() -> Self {
        Self {
            max_retry_attempts: 3,
            base_retry_delay: Duration::from_secs(2),
            max_retry_delay: Duration::from_secs(30),
            exponential_backoff: true,
            operation_timeout: Duration::from_secs(300),
            continue_on_failure: true,
            persist_intermediate_results: true,
            persistence_directory: PathBuf::from("./test_results"),
            detailed_logging: true,
            log_level: LogLevel::Info,
            performance_monitoring: true,
        }
    }
}

impl Default for ExecutionStateTracker {
    fn default() -> Self {
        Self {
            current_phase: "initialization".to_string(),
            phase_start_time: None,
            total_execution_time: Duration::ZERO,
            retry_count: 0,
            errors_encountered: Vec::new(),
            warnings_generated: Vec::new(),
            performance_metrics: HashMap::new(),
            recovery_actions_taken: Vec::new(),
        }
    }
}

impl TestExecutionPipeline {
    /// Create new test execution pipeline
    pub fn new(config: PipelineConfig) -> Result<Self> {
        let persistence_manager = Arc::new(TestResultPersistenceManager::new(
            config.persistence_directory.clone(),
            config.persist_intermediate_results,
        )?);

        let recovery_strategies = Self::create_default_recovery_strategies();
        let retry_manager = RetryPolicyManager::new()?;
        let logging_context = LoggingContextManager::new(config.log_level.clone());

        Ok(Self {
            config,
            recovery_strategies,
            persistence_manager,
            state_tracker: ExecutionStateTracker::default(),
            retry_manager,
            logging_context,
        })
    }

    /// Execute test pipeline with comprehensive error handling
    pub async fn execute_with_error_handling<F, T>(&mut self, 
        phase_name: &str,
        operation: F
    ) -> Result<T>
    where
        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T>> + Send>> + Send + Sync,
        T: Send + 'static,
    {
        info!("🔄 Starting pipeline phase: {}", phase_name);
        
        self.state_tracker.current_phase = phase_name.to_string();
        self.state_tracker.phase_start_time = Some(Instant::now());
        
        let mut last_error = None;
        let error_category = self.classify_error_category(phase_name);
        let recovery_strategy = self.get_recovery_strategy(&error_category);
        
        for attempt in 1..=recovery_strategy.max_retries {
            self.logging_context.add_context("attempt", &attempt.to_string());
            self.logging_context.add_context("phase", phase_name);
            
            debug!("Executing {} (attempt {}/{})", phase_name, attempt, recovery_strategy.max_retries);
            
            let operation_start = Instant::now();
            
            // Execute operation with timeout
            let operation_future = operation();
            let result = timeout(self.config.operation_timeout, operation_future).await;
            
            let operation_duration = operation_start.elapsed();
            self.state_tracker.performance_metrics.insert(
                format!("{}_{}", phase_name, attempt),
                operation_duration
            );
            
            match result {
                Ok(Ok(success_result)) => {
                    info!("✅ Phase {} completed successfully on attempt {}", phase_name, attempt);
                    self.record_successful_execution(phase_name, attempt, operation_duration);
                    return Ok(success_result);
                }
                Ok(Err(e)) => {
                    warn!("⚠️ Phase {} failed on attempt {}: {}", phase_name, attempt, e);
                    last_error = Some(e);
                    
                    // Record error
                    self.record_execution_error(phase_name, &last_error.as_ref().unwrap(), attempt);
                    
                    // Apply recovery strategy if not the last attempt
                    if attempt < recovery_strategy.max_retries {
                        if let Err(recovery_error) = self.apply_recovery_strategy(&recovery_strategy, attempt).await {
                            warn!("Recovery strategy failed: {}", recovery_error);
                        }
                    }
                }
                Err(_) => {
                    let timeout_error = anyhow!("Operation timed out after {:?}", self.config.operation_timeout);
                    warn!("⏰ Phase {} timed out on attempt {}", phase_name, attempt);
                    last_error = Some(timeout_error);
                    
                    // Record timeout error
                    self.record_execution_error(phase_name, &last_error.as_ref().unwrap(), attempt);
                    
                    // Apply recovery strategy if not the last attempt
                    if attempt < recovery_strategy.max_retries {
                        if let Err(recovery_error) = self.apply_recovery_strategy(&recovery_strategy, attempt).await {
                            warn!("Recovery strategy failed: {}", recovery_error);
                        }
                    }
                }
            }
        }
        
        // All retry attempts exhausted
        let final_error = last_error.unwrap_or_else(|| anyhow!("Unknown error in phase {}", phase_name));
        error!("❌ Phase {} failed after {} attempts", phase_name, recovery_strategy.max_retries);
        
        // Check if we should continue on failure
        if self.config.continue_on_failure && !recovery_strategy.is_critical {
            warn!("⚠️ Continuing execution despite failure in non-critical phase: {}", phase_name);
            self.record_phase_skipped(phase_name, final_error.to_string());
            return Err(final_error);
        }
        
        Err(final_error)
    }

    /// Apply recovery strategy for failed operations
    async fn apply_recovery_strategy(&mut self, strategy: &RecoveryStrategy, attempt: u32) -> Result<()> {
        debug!("Applying recovery strategy for attempt {}", attempt);
        
        // Calculate delay with backoff and jitter
        let delay = self.calculate_retry_delay(strategy, attempt);
        
        // Apply recovery actions
        for action in &strategy.recovery_actions {
            if let Err(e) = self.execute_recovery_action(action).await {
                warn!("Recovery action failed: {}", e);
            }
        }
        
        // Wait before retry
        if delay > Duration::ZERO {
            debug!("Waiting {:?} before retry", delay);
            sleep(delay).await;
        }
        
        Ok(())
    }

    /// Execute a specific recovery action
    async fn execute_recovery_action(&mut self, action: &RecoveryAction) -> Result<()> {
        let action_start = Instant::now();
        let action_description = format!("{:?}", action);
        
        debug!("Executing recovery action: {}", action_description);
        
        let result = match action {
            RecoveryAction::Wait(duration) => {
                sleep(*duration).await;
                Ok(())
            }
            RecoveryAction::Reset => {
                // Implementation would reset relevant state
                debug!("Resetting component state");
                Ok(())
            }
            RecoveryAction::Restart(component) => {
                // Implementation would restart the specified component
                debug!("Restarting component: {}", component);
                Ok(())
            }
            RecoveryAction::Skip => {
                debug!("Skipping failed operation");
                Ok(())
            }
            RecoveryAction::Alternative(approach) => {
                debug!("Using alternative approach: {}", approach);
                Ok(())
            }
            RecoveryAction::Cleanup => {
                // Implementation would clean up resources
                debug!("Cleaning up resources");
                Ok(())
            }
        };
        
        let action_duration = action_start.elapsed();
        let successful = result.is_ok();
        
        self.state_tracker.recovery_actions_taken.push(RecoveryActionTaken {
            action_type: action_description.clone(),
            description: action_description,
            timestamp: chrono::Utc::now(),
            successful,
            context: HashMap::new(),
        });
        
        if successful {
            debug!("✅ Recovery action completed successfully in {:?}", action_duration);
        } else {
            warn!("❌ Recovery action failed after {:?}", action_duration);
        }
        
        result
    }

    /// Calculate retry delay with backoff and jitter
    fn calculate_retry_delay(&self, strategy: &RecoveryStrategy, attempt: u32) -> Duration {
        let base_delay = strategy.retry_delay;
        
        let delay = if strategy.exponential_backoff {
            let multiplier = 2_u32.pow(attempt.saturating_sub(1));
            base_delay * multiplier
        } else {
            base_delay
        };
        
        // Apply jitter to avoid thundering herd
        let jitter_factor = fastrand::f64() * 0.1; // 10% jitter
        let jittered_delay = delay.mul_f64(1.0 + jitter_factor);
        
        // Cap at maximum delay
        std::cmp::min(jittered_delay, self.config.max_retry_delay)
    }

    /// Classify error category based on phase and error type
    fn classify_error_category(&self, phase_name: &str) -> ErrorCategory {
        match phase_name.to_lowercase().as_str() {
            name if name.contains("network") || name.contains("connection") => ErrorCategory::NetworkError,
            name if name.contains("config") => ErrorCategory::ConfigurationError,
            name if name.contains("contract") => ErrorCategory::ContractError,
            name if name.contains("tui") => ErrorCategory::TuiError,
            name if name.contains("file") || name.contains("persistence") => ErrorCategory::FileSystemError,
            _ => ErrorCategory::UnknownError,
        }
    }

    /// Get recovery strategy for error category
    fn get_recovery_strategy(&self, category: &ErrorCategory) -> &RecoveryStrategy {
        self.recovery_strategies.get(category)
            .unwrap_or_else(|| self.recovery_strategies.get(&ErrorCategory::UnknownError).unwrap())
    }    
/// Create default recovery strategies
    fn create_default_recovery_strategies() -> HashMap<ErrorCategory, RecoveryStrategy> {
        let mut strategies = HashMap::new();
        
        // Network error strategy
        strategies.insert(ErrorCategory::NetworkError, RecoveryStrategy {
            max_retries: 5,
            retry_delay: Duration::from_secs(2),
            exponential_backoff: true,
            is_critical: false,
            recovery_actions: vec![
                RecoveryAction::Wait(Duration::from_secs(1)),
                RecoveryAction::Reset,
            ],
        });
        
        // Timeout error strategy
        strategies.insert(ErrorCategory::TimeoutError, RecoveryStrategy {
            max_retries: 3,
            retry_delay: Duration::from_secs(5),
            exponential_backoff: true,
            is_critical: false,
            recovery_actions: vec![
                RecoveryAction::Wait(Duration::from_secs(2)),
            ],
        });
        
        // Configuration error strategy
        strategies.insert(ErrorCategory::ConfigurationError, RecoveryStrategy {
            max_retries: 2,
            retry_delay: Duration::from_secs(1),
            exponential_backoff: false,
            is_critical: true,
            recovery_actions: vec![
                RecoveryAction::Reset,
            ],
        });
        
        // Contract error strategy
        strategies.insert(ErrorCategory::ContractError, RecoveryStrategy {
            max_retries: 3,
            retry_delay: Duration::from_secs(3),
            exponential_backoff: true,
            is_critical: false,
            recovery_actions: vec![
                RecoveryAction::Wait(Duration::from_secs(1)),
                RecoveryAction::Reset,
            ],
        });
        
        // TUI error strategy
        strategies.insert(ErrorCategory::TuiError, RecoveryStrategy {
            max_retries: 4,
            retry_delay: Duration::from_secs(2),
            exponential_backoff: true,
            is_critical: false,
            recovery_actions: vec![
                RecoveryAction::Cleanup,
                RecoveryAction::Restart("tui".to_string()),
            ],
        });
        
        // File system error strategy
        strategies.insert(ErrorCategory::FileSystemError, RecoveryStrategy {
            max_retries: 2,
            retry_delay: Duration::from_secs(1),
            exponential_backoff: false,
            is_critical: false,
            recovery_actions: vec![
                RecoveryAction::Cleanup,
            ],
        });
        
        // Unknown error strategy (fallback)
        strategies.insert(ErrorCategory::UnknownError, RecoveryStrategy {
            max_retries: 2,
            retry_delay: Duration::from_secs(2),
            exponential_backoff: false,
            is_critical: false,
            recovery_actions: vec![
                RecoveryAction::Wait(Duration::from_secs(1)),
            ],
        });
        
        strategies
    }

    /// Record successful execution
    fn record_successful_execution(&mut self, phase_name: &str, attempt: u32, duration: Duration) {
        if let Some(phase_start) = self.state_tracker.phase_start_time {
            let total_phase_duration = phase_start.elapsed();
            self.state_tracker.performance_metrics.insert(
                format!("{}_total", phase_name),
                total_phase_duration
            );
        }
        
        debug!("Phase {} completed in {:?} (attempt {})", phase_name, duration, attempt);
    }

    /// Record execution error
    fn record_execution_error(&mut self, phase_name: &str, error: &anyhow::Error, attempt: u32) {
        let execution_error = ExecutionError {
            category: format!("{:?}", self.classify_error_category(phase_name)),
            message: error.to_string(),
            context: self.logging_context.get_context(),
            timestamp: chrono::Utc::now(),
            phase: phase_name.to_string(),
            recoverable: attempt < self.config.max_retry_attempts,
            recovery_attempts: attempt,
        };
        
        self.state_tracker.errors_encountered.push(execution_error);
        self.state_tracker.retry_count += 1;
    }

    /// Record phase skipped due to failure
    fn record_phase_skipped(&mut self, phase_name: &str, reason: String) {
        let warning = ExecutionWarning {
            message: format!("Phase {} skipped: {}", phase_name, reason),
            context: self.logging_context.get_context(),
            timestamp: chrono::Utc::now(),
            phase: phase_name.to_string(),
        };
        
        self.state_tracker.warnings_generated.push(warning);
    }

    /// Generate pipeline execution result
    pub fn generate_execution_result(&self) -> PipelineExecutionResult {
        let error_summary = self.generate_error_summary();
        let performance_summary = self.generate_performance_summary();
        let recovery_summary = self.generate_recovery_summary();
        
        PipelineExecutionResult {
            success: error_summary.critical_errors == 0,
            partial_completion: error_summary.total_errors > 0 && error_summary.critical_errors == 0,
            completed_phases: self.get_completed_phases(),
            failed_phases: self.get_failed_phases(),
            skipped_phases: self.get_skipped_phases(),
            total_execution_time: self.state_tracker.total_execution_time,
            error_summary,
            performance_summary,
            recovery_summary,
        }
    }

    /// Generate error summary
    fn generate_error_summary(&self) -> ErrorSummary {
        let total_errors = self.state_tracker.errors_encountered.len() as u32;
        let critical_errors = self.state_tracker.errors_encountered.iter()
            .filter(|e| !e.recoverable)
            .count() as u32;
        let recoverable_errors = total_errors - critical_errors;
        
        let mut errors_by_category = HashMap::new();
        for error in &self.state_tracker.errors_encountered {
            *errors_by_category.entry(error.category.clone()).or_insert(0) += 1;
        }
        
        let most_common_error = errors_by_category.iter()
            .max_by_key(|(_, count)| *count)
            .map(|(category, _)| category.clone());
        
        ErrorSummary {
            total_errors,
            critical_errors,
            recoverable_errors,
            errors_by_category,
            most_common_error,
        }
    }

    /// Generate performance summary
    fn generate_performance_summary(&self) -> PerformanceSummary {
        let total_time = self.state_tracker.total_execution_time;
        let time_by_phase = self.state_tracker.performance_metrics.clone();
        
        let slowest_phase = time_by_phase.iter()
            .max_by_key(|(_, duration)| *duration)
            .map(|(phase, _)| phase.clone());
        
        let retry_times: Vec<Duration> = time_by_phase.iter()
            .filter(|(key, _)| key.contains("_") && !key.contains("_total"))
            .map(|(_, duration)| *duration)
            .collect();
        
        let total_retry_time = retry_times.iter().sum();
        let average_retry_time = if retry_times.is_empty() {
            Duration::ZERO
        } else {
            total_retry_time / retry_times.len() as u32
        };
        
        PerformanceSummary {
            total_time,
            time_by_phase,
            slowest_phase,
            average_retry_time,
            total_retry_time,
        }
    }

    /// Generate recovery summary
    fn generate_recovery_summary(&self) -> RecoverySummary {
        let total_recovery_attempts = self.state_tracker.recovery_actions_taken.len() as u32;
        let successful_recoveries = self.state_tracker.recovery_actions_taken.iter()
            .filter(|action| action.successful)
            .count() as u32;
        let failed_recoveries = total_recovery_attempts - successful_recoveries;
        
        let mut recovery_actions_by_type = HashMap::new();
        for action in &self.state_tracker.recovery_actions_taken {
            *recovery_actions_by_type.entry(action.action_type.clone()).or_insert(0) += 1;
        }
        
        let most_effective_recovery = recovery_actions_by_type.iter()
            .max_by_key(|(_, count)| *count)
            .map(|(action_type, _)| action_type.clone());
        
        RecoverySummary {
            total_recovery_attempts,
            successful_recoveries,
            failed_recoveries,
            recovery_actions_by_type,
            most_effective_recovery,
        }
    }

    /// Get completed phases
    fn get_completed_phases(&self) -> Vec<String> {
        // Implementation would track completed phases
        Vec::new()
    }

    /// Get failed phases
    fn get_failed_phases(&self) -> Vec<String> {
        self.state_tracker.errors_encountered.iter()
            .filter(|e| !e.recoverable)
            .map(|e| e.phase.clone())
            .collect()
    }

    /// Get skipped phases
    fn get_skipped_phases(&self) -> Vec<String> {
        self.state_tracker.warnings_generated.iter()
            .filter(|w| w.message.contains("skipped"))
            .map(|w| w.phase.clone())
            .collect()
    }

    /// Persist intermediate results
    pub async fn persist_intermediate_results(&self, phase_name: &str, results: &serde_json::Value) -> Result<()> {
        if self.config.persist_intermediate_results {
            self.persistence_manager.persist_phase_results(phase_name, results).await?;
        }
        Ok(())
    }

    /// Recover from persisted results
    pub async fn recover_from_persisted_results(&self, phase_name: &str) -> Result<Option<serde_json::Value>> {
        if self.config.persist_intermediate_results {
            self.persistence_manager.recover_phase_results(phase_name).await
        } else {
            Ok(None)
        }
    }
}