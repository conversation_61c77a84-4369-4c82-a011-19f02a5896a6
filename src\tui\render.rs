// MISSION: T<PERSON> Render - Mission Control Interface
// WHY: Implement the 4-tab "Mission Control" interface for the Zen Geometer
// HOW: Consolidated rendering functions for Dashboard, Operations, Systems, and Config tabs

use ratatui::{
    layout::{Constraint, Direction, Layout, Rect, Alignment},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, List, ListItem, Paragraph, Tabs, Wrap, Table, Row, Cell, Gauge, Sparkline, BarChart, Clear},
    Frame,
};

use super::app::{App, AppTab, LogSeverity, SystemsPanel, TradeRecord};
use num_traits::ToPrimitive;
use rust_decimal::Decimal;

impl App {
    pub fn render(&mut self, f: &mut Frame) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Length(3), Constraint::Min(0)].as_ref())
            .split(f.size());

        // Render tabs
        self.render_tabs(f, chunks[0]);

        // Render content based on active tab
        match self.active_tab {
            AppTab::Dashboard => self.render_dashboard(f, chunks[1]),
            AppTab::Operations => self.render_operations(f, chunks[1]),
            AppTab::Systems => self.render_systems(f, chunks[1]),
            AppTab::Config => self.render_config(f, chunks[1]),
            AppTab::Errors => self.render_errors(f, chunks[1]),
        }

        // Render Strategy Inspector modal if active
        if self.show_inspector {
            self.render_inspector_modal(f);
        }
    }

    fn render_tabs(&self, f: &mut Frame, area: Rect) {
        let titles = vec![
            "1:Dashboard",  // "The Bridge": High-level situational awareness
            "2:Operations", // "The Hunt": Live narrative of the Zen Geometer
            "3:Systems",    // "The Engine Room": Engineering health and Network Seismology
            "4:Config",     // "The Tuning Fork": Control panel for strategy parameters
        ];
        let tabs = Tabs::new(titles)
            .block(Block::default().borders(Borders::ALL).title("ZEN GEOMETER - Mission Control"))
            .style(Style::default().fg(Color::White))
            .highlight_style(Style::default().fg(Color::Yellow))
            .select(self.active_tab as usize);
        f.render_widget(tabs, area);
    }

    // Tab 1: Dashboard - "The Bridge" - Aetheric Resonance Dashboard
    fn render_dashboard(&self, f: &mut Frame, area: Rect) {
        let main_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Min(0), Constraint::Length(5)].as_ref())
            .split(area);

        // Top: 2x2 Grid of Aetheric Resonance Widgets
        let grid_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Percentage(50), Constraint::Percentage(50)].as_ref())
            .split(main_chunks[0]);

        let top_row = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([Constraint::Percentage(50), Constraint::Percentage(50)].as_ref())
            .split(grid_chunks[0]);

        let bottom_row = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([Constraint::Percentage(50), Constraint::Percentage(50)].as_ref())
            .split(grid_chunks[1]);

        // Aetheric Resonance Engine Widgets
        self.render_network_seismology_widget(f, top_row[0]);
        self.render_chronos_sieve_widget(f, top_row[1]);
        self.render_mandorla_gauge_widget(f, bottom_row[0]);
        self.render_execution_pnl_widget(f, bottom_row[1]);

        // Bottom: Master Control & Status Footer
        self.render_master_control_footer(f, main_chunks[1]);
    }

    // Tab 2: Operations - "The Hunt" - The Cockpit
    fn render_operations(&self, f: &mut Frame, area: Rect) {
        let main_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Length(5), Constraint::Min(0)].as_ref())
            .split(area);

        // Top: Master Control Panel
        self.render_master_control_panel(f, main_chunks[0]);

        let content_chunks = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([Constraint::Percentage(70), Constraint::Percentage(30)].as_ref())
            .split(main_chunks[1]);

        let left_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Percentage(40), Constraint::Percentage(30), Constraint::Percentage(30)].as_ref())
            .split(content_chunks[0]);

        // Top Left: Live Activity Log
        self.render_live_activity_log(f, left_chunks[0]);
        
        // Middle Left: Trade History Panel
        self.render_trade_history_panel(f, left_chunks[1]);

        // Bottom Left: Trade Lifecycle Events Panel
        self.render_trade_lifecycle_panel(f, left_chunks[2]);
        
        // Right Panel: Current State Dashboard
        self.render_current_state_dashboard(f, content_chunks[1]);
    }

    // Tab 3: Systems - "The Engine Room"
    fn render_systems(&self, f: &mut Frame, area: Rect) {
        let main_chunks = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([Constraint::Percentage(60), Constraint::Percentage(40)].as_ref())
            .split(area);

        let left_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Percentage(60), Constraint::Percentage(40)].as_ref())
            .split(main_chunks[0]);

        let right_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Percentage(40), Constraint::Percentage(30), Constraint::Percentage(30)].as_ref())
            .split(main_chunks[1]);

        // Main Left: Component Status Matrix
        self.render_component_status_matrix(f, left_chunks[0]);
        
        // Bottom Left: Live Log Viewer
        self.render_log_viewer_panel(f, left_chunks[1]);
        
        // Top Right: Key Performance Indicators
        self.render_kpi_panel(f, right_chunks[0]);
        
        // Middle Right: Network Seismology
        self.render_network_seismology_panel(f, right_chunks[1]);
        
        // Bottom Right: Node Connectivity Manager
        self.render_node_connectivity_panel(f, right_chunks[2]);
    }

    // Tab 4: Config - "The Control Panel"
    fn render_config(&self, f: &mut Frame, area: Rect) {
        let main_chunks = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([Constraint::Percentage(40), Constraint::Percentage(60)].as_ref())
            .split(area);

        let right_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Percentage(70), Constraint::Percentage(30)].as_ref())
            .split(main_chunks[1]);

        // Left Panel: Configuration Tree View
        self.render_config_tree_view(f, main_chunks[0]);
        
        // Top Right: Parameter Editor
        self.render_parameter_editor(f, right_chunks[0]);
        
        // Bottom Right: Hot-Reload Control & Validation
        self.render_config_controls(f, right_chunks[1]);
    }

    // Aetheric Resonance Dashboard Widget Implementations
    fn render_network_seismology_widget(&self, f: &mut Frame, area: Rect) {
        // Hybrid Data System: Use real data when available, fallback to mock
        let (sp_latency_ms, network_state, state_color, is_real_data) = 
            if let Some(ref seismology) = self.latest_network_seismology {
                // Real data from Network Seismology component
                let latency = if let Some(ref tti) = seismology.tti_stats {
                    tti.avg_tti_ms as u64
                } else {
                    150 // Default if no TTI data
                };
                let coherence_score = seismology.coherence.coherence_score;
                let (state, color) = if coherence_score > 0.8 {
                    ("COHERENT", Color::Green)
                } else if coherence_score > 0.5 {
                    ("STRESSED", Color::Yellow)
                } else {
                    ("SHOCKWAVE", Color::Red)
                };
                (latency, state, color, true)
            } else {
                // Fallback to mock data with historical data if available
                let latency = self.sp_latency_history
                    .back()
                    .map(|(_, latency)| *latency)
                    .unwrap_or(152);
                let (state, color) = match latency {
                    0..=100 => ("COHERENT", Color::Green),
                    101..=250 => ("STRESSED", Color::Yellow),
                    _ => ("SHOCKWAVE", Color::Red),
                };
                (latency, state, color, false)
            };

        // Determine if this widget is focused
        let border_style = if self.dashboard_focus == super::app::DashboardFocus::Network {
            Style::default().fg(Color::Blue).add_modifier(Modifier::BOLD)
        } else {
            Style::default()
        };

        let widget_area = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Length(4), Constraint::Min(0)].as_ref())
            .split(area);

        // Top section: S-P Latency and State with data source indicator
        let data_indicator = if is_real_data {
            ("●", Color::Green, "LIVE")
        } else if self.is_are_data_stale() {
            ("●", Color::Red, "MOCK")
        } else {
            ("●", Color::Yellow, "CACHED")
        };

        let content = vec![
            Line::from(vec![
                Span::styled("S-P Latency: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("{}ms ", sp_latency_ms), Style::default().fg(Color::White)),
                Span::styled(data_indicator.0, Style::default().fg(data_indicator.1)),
                Span::styled(data_indicator.2, Style::default().fg(data_indicator.1).add_modifier(Modifier::ITALIC)),
            ]),
            Line::from(vec![
                Span::styled("State: ", Style::default().fg(Color::Gray)),
                Span::styled(network_state, Style::default().fg(state_color).add_modifier(Modifier::BOLD)),
            ]),
        ];

        let paragraph = Paragraph::new(content)
            .block(Block::default().borders(Borders::ALL).title("🌐 NETWORK SEISMOLOGY").border_style(border_style))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, widget_area[0]);

        // Bottom section: Block Propagation Histogram
        if let Some(ref propagation_data) = self.last_block_propagation {
            // Create histogram buckets for block propagation
            let mut buckets = vec![0u64; 6]; // 50ms buckets: 0-50, 50-100, 100-150, 150-200, 200-250, 250+
            
            for &report_time in propagation_data {
                let bucket_index = ((report_time / 50).min(5)) as usize;
                buckets[bucket_index] += 1;
            }

            let bar_data: Vec<(&str, u64)> = vec![
                ("50", buckets[0]),
                ("100", buckets[1]),
                ("150", buckets[2]),
                ("200", buckets[3]),
                ("250", buckets[4]),
                ("300+", buckets[5]),
            ];

            let barchart = BarChart::default()
                .block(Block::default().borders(Borders::TOP).title("Block Propagation (ms)"))
                .data(&bar_data)
                .bar_width(3)
                .bar_style(Style::default().fg(Color::Cyan))
                .value_style(Style::default().fg(Color::White));
            f.render_widget(barchart, widget_area[1]);
        } else {
            // Fallback sparkline for S-P latency trend
            let sparkline_data: Vec<u64> = self.sp_latency_history
                .iter()
                .map(|(_, latency)| *latency)
                .collect();
            
            let sparkline = Sparkline::default()
                .block(Block::default().borders(Borders::TOP).title("S-P Latency Trend"))
                .data(&sparkline_data)
                .style(Style::default().fg(Color::Cyan));
            f.render_widget(sparkline, widget_area[1]);
        }
    }

    fn render_chronos_sieve_widget(&self, f: &mut Frame, area: Rect) {
        // Hybrid Data System: Use real Temporal Harmonics when available
        let (market_rhythm, rhythm_color, dominant_cycle, is_real_data) = 
            if let Some(ref harmonics) = self.latest_temporal_harmonics {
                // Real data from Chronos Sieve (Fractal Analyzer)
                let stability = harmonics.market_rhythm_stability;
                let rhythm = if stability > 0.8 {
                    "HARMONIC"
                } else if stability > 0.5 {
                    "STABLE"
                } else {
                    "CHAOTIC"
                };
                let color = if stability > 0.8 {
                    Color::Green
                } else if stability > 0.5 {
                    Color::Yellow
                } else {
                    Color::Red
                };
                let cycle = if let Some((freq, _power)) = harmonics.dominant_cycles_minutes.first() {
                    format!("{:.1} min", freq)
                } else {
                    "N/A".to_string()
                };
                (rhythm, color, cycle, true)
            } else {
                // Fallback to mock data
                ("HARMONIC", Color::Green, "12.5 min".to_string(), false)
            };
        
        // Determine if this widget is focused
        let border_style = if self.dashboard_focus == super::app::DashboardFocus::Chronos {
            Style::default().fg(Color::Blue).add_modifier(Modifier::BOLD)
        } else {
            Style::default()
        };

        let widget_area = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Length(3), Constraint::Min(0)].as_ref())
            .split(area);

        // Top section: Market Rhythm and Stability with data source indicator
        let data_indicator = if is_real_data {
            ("●", Color::Green, "LIVE")
        } else {
            ("●", Color::Red, "MOCK")
        };

        let content = vec![
            Line::from(vec![
                Span::styled("Rhythm: ", Style::default().fg(Color::Gray)),
                Span::styled(market_rhythm, Style::default().fg(rhythm_color).add_modifier(Modifier::BOLD)),
                Span::styled(" ", Style::default()),
                Span::styled(data_indicator.0, Style::default().fg(data_indicator.1)),
                Span::styled(data_indicator.2, Style::default().fg(data_indicator.1).add_modifier(Modifier::ITALIC)),
            ]),
            Line::from(vec![
                Span::styled("Cycle: ", Style::default().fg(Color::Gray)),
                Span::styled(&dominant_cycle, Style::default().fg(Color::White)),
            ]),
        ];

        let paragraph = Paragraph::new(content)
            .block(Block::default().borders(Borders::ALL).title("⌛ CHRONOS SIEVE").border_style(border_style))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, widget_area[0]);

        // Bottom section: Market Power Spectrum
        let power_spectrum = if let Some(spectrum) = self.market_spectrum_history.back() {
            spectrum.frequency_powers.clone()
        } else {
            // Mock power spectrum data
            vec![
                ("1m".to_string(), 5),
                ("5m".to_string(), 12),
                ("15m".to_string(), 6),
                ("60m".to_string(), 2),
            ]
        };

        let bar_data: Vec<(&str, u64)> = power_spectrum.iter()
            .map(|(timeframe, power)| (timeframe.as_str(), *power))
            .collect();

        let barchart = BarChart::default()
            .block(Block::default().borders(Borders::TOP).title("Power Spectrum"))
            .data(&bar_data)
            .bar_width(4)
            .bar_style(Style::default().fg(Color::Yellow))
            .value_style(Style::default().fg(Color::White));
        f.render_widget(barchart, widget_area[1]);
    }

    fn render_mandorla_gauge_widget(&self, f: &mut Frame, area: Rect) {
        // Mock Mandorla Gauge data - Vesica Piscis opportunity analysis
        let opp_flow_per_min = self.live_opportunities.len() * 5; // Estimate based on current opportunities
        let avg_geo_score = 0.82;

        // Determine if this widget is focused
        let border_style = if self.dashboard_focus == super::app::DashboardFocus::Mandorla {
            Style::default().fg(Color::Blue).add_modifier(Modifier::BOLD)
        } else {
            Style::default()
        };

        // Enhanced score distribution based on opportunity quality
        let score_distribution = vec![
            ("Low", 2),
            ("Med", 5),
            ("High", 8),
            ("Excellent", 3),
        ];

        let widget_area = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Length(4), Constraint::Min(0)].as_ref())
            .split(area);

        let content = vec![
            Line::from(vec![
                Span::styled("Opp. Flow: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("{}/min", opp_flow_per_min), Style::default().fg(Color::White)),
            ]),
            Line::from(vec![
                Span::styled("Avg. Geo Score: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("{:.2}", avg_geo_score), Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)),
            ]),
        ];

        let paragraph = Paragraph::new(content)
            .block(Block::default().borders(Borders::ALL).title("💠 MANDORLA GAUGE").border_style(border_style))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, widget_area[0]);

        // Enhanced score distribution visualization
        let bar_data: Vec<(&str, u64)> = score_distribution.iter()
            .map(|(label, count)| (*label, *count as u64))
            .collect();
        
        let barchart = BarChart::default()
            .block(Block::default().borders(Borders::TOP).title("Score Distribution"))
            .data(&bar_data)
            .bar_width(4)
            .bar_style(Style::default().fg(Color::Magenta))
            .value_style(Style::default().fg(Color::White));
        f.render_widget(barchart, widget_area[1]);
    }

    fn render_execution_pnl_widget(&self, f: &mut Frame, area: Rect) {
        // Use actual system metrics for PNL data
        let session_pnl_usd = self.system_metrics.total_pnl_24h;
        let session_pnl_eth = session_pnl_usd / rust_decimal::Decimal::new(2500, 0); // Mock ETH price at $2500
        
        let total_trades = self.system_metrics.total_trades_24h;
        let successful_trades = (total_trades as f64 * 0.94) as u32; // Mock 94% success rate
        let success_rate = if total_trades > 0 { 
            (successful_trades as f64 / total_trades as f64 * 100.0) as u32 
        } else { 
            0 
        };

        let pnl_color = if session_pnl_usd >= rust_decimal::Decimal::ZERO {
            Color::Green
        } else {
            Color::Red
        };

        // Determine if this widget is focused
        let border_style = if self.dashboard_focus == super::app::DashboardFocus::Execution {
            Style::default().fg(Color::Blue).add_modifier(Modifier::BOLD)
        } else {
            Style::default()
        };

        let widget_area = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Length(4), Constraint::Min(0)].as_ref())
            .split(area);

        let content = vec![
            Line::from(vec![
                Span::styled("Session PNL: ", Style::default().fg(Color::Gray)),
                Span::styled(
                    format!("${:.2} (ETH: {:.3})", session_pnl_usd, session_pnl_eth), 
                    Style::default().fg(pnl_color).add_modifier(Modifier::BOLD)
                ),
            ]),
            Line::from(vec![
                Span::styled("Success Rate: ", Style::default().fg(Color::Gray)),
                Span::styled(
                    format!("{}% ({}/{})", success_rate, successful_trades, total_trades), 
                    Style::default().fg(Color::Green)
                ),
            ]),
        ];

        let paragraph = Paragraph::new(content)
            .block(Block::default().borders(Borders::ALL).title("📈 EXECUTION & PNL").border_style(border_style))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, widget_area[0]);

        // PNL Sparkline - Historical P&L trend
        let pnl_sparkline_data: Vec<u64> = if !self.pnl_history.is_empty() {
            self.pnl_history
                .iter()
                .map(|point| {
                    // Convert Decimal to u64 for sparkline (add offset to handle negatives)
                    let pnl_f64 = point.cumulative_pnl.to_f64().unwrap_or(0.0);
                    ((pnl_f64 + 1000.0).max(0.0) as u64).min(100) // Normalize to 0-100 range
                })
                .collect()
        } else {
            // Mock PNL trend data
            vec![50, 52, 48, 55, 60, 58, 62, 65]
        };

        let sparkline = Sparkline::default()
            .block(Block::default().borders(Borders::TOP).title("PNL Trend"))
            .data(&pnl_sparkline_data)
            .style(Style::default().fg(pnl_color));
        f.render_widget(sparkline, widget_area[1]);
    }

    fn render_master_control_footer(&self, f: &mut Frame, area: Rect) {
        let uptime_hours = self.tick_counter / 36000; // Mock uptime calculation
        let uptime_minutes = (self.tick_counter % 36000) / 600;

        let status_color = self.bot_status.color();
        let status_text = self.bot_status.as_str();

        let footer_content = vec![
            Line::from(vec![
                Span::styled("BOT: ", Style::default().fg(Color::Gray)),
                Span::styled(status_text, Style::default().fg(status_color).add_modifier(Modifier::BOLD)),
                Span::styled(" | Uptime: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("{}h {}m", uptime_hours, uptime_minutes), Style::default().fg(Color::White)),
                Span::styled(" | Active Strategy: ", Style::default().fg(Color::Gray)),
                Span::styled("Zen Geometer", Style::default().fg(Color::Cyan)),
            ]),
            Line::from(vec![
                Span::styled("[ ", Style::default().fg(Color::Gray)),
                Span::styled("(S)", Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)),
                Span::styled("top ] [ ", Style::default().fg(Color::Gray)),
                Span::styled("(P)", Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)),
                Span::styled("ause ] [ ", Style::default().fg(Color::Gray)),
                Span::styled("(R)", Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)),
                Span::styled("estart ] [ ", Style::default().fg(Color::Gray)),
                Span::styled("(E)", Style::default().fg(Color::Red).add_modifier(Modifier::BOLD)),
                Span::styled("mergency Stop ]", Style::default().fg(Color::Gray)),
            ]),
        ];

        let paragraph = Paragraph::new(footer_content)
            .block(Block::default().borders(Borders::ALL).title("⚡ MASTER CONTROL & STATUS"))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, area);
    }

    // Dashboard Panel Implementations (Legacy - keeping for compatibility)
    fn render_key_metrics_panel(&self, f: &mut Frame, area: Rect) {
        let metrics_text = vec![
            Line::from(vec![
                Span::styled("24h P&L: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("${:.2}", self.system_metrics.total_pnl_24h), 
                    Style::default().fg(if self.system_metrics.total_pnl_24h >= rust_decimal::Decimal::ZERO { Color::Green } else { Color::Red })),
            ]),
            Line::from(vec![
                Span::styled("Trades: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("{}", self.system_metrics.total_trades_24h), Style::default().fg(Color::White)),
            ]),
            Line::from(vec![
                Span::styled("Active Strategies: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("{}/{}", self.system_metrics.active_strategies, self.system_metrics.total_strategies), 
                    Style::default().fg(Color::Cyan)),
            ]),
            Line::from(vec![
                Span::styled("Gas Cost 24h: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("${:.2}", self.system_metrics.gas_cost_24h), Style::default().fg(Color::Yellow)),
            ]),
        ];

        let paragraph = Paragraph::new(metrics_text)
            .block(Block::default().borders(Borders::ALL).title("📊 Key Metrics"))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, area);
    }

    fn render_system_status_panel(&self, f: &mut Frame, area: Rect) {
        let status_color = |status: &crate::shared_types::ServiceStatus| match status {
            crate::shared_types::ServiceStatus::Running => Color::Green,
            crate::shared_types::ServiceStatus::Stopped => Color::Red,
            crate::shared_types::ServiceStatus::Stopping => Color::Yellow,
            crate::shared_types::ServiceStatus::Warning(_) => Color::Yellow,
            crate::shared_types::ServiceStatus::Error(_) => Color::Red,
            crate::shared_types::ServiceStatus::Initializing => Color::Cyan,
        };

        let status_text = vec![
            Line::from(vec![
                Span::styled("RPC Node: ", Style::default().fg(Color::Gray)),
                Span::styled("●", Style::default().fg(status_color(&self.system_health.rpc_node))),
            ]),
            Line::from(vec![
                Span::styled("NATS Bus: ", Style::default().fg(Color::Gray)),
                Span::styled("●", Style::default().fg(status_color(&self.system_health.nats_bus))),
            ]),
            Line::from(vec![
                Span::styled("Database: ", Style::default().fg(Color::Gray)),
                Span::styled("●", Style::default().fg(status_color(&self.system_health.database))),
            ]),
            Line::from(vec![
                Span::styled("Redis: ", Style::default().fg(Color::Gray)),
                Span::styled("●", Style::default().fg(status_color(&self.system_health.redis))),
            ]),
        ];

        let paragraph = Paragraph::new(status_text)
            .block(Block::default().borders(Borders::ALL).title("🔧 System Health"))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, area);
    }

    fn render_portfolio_overview_panel(&self, f: &mut Frame, area: Rect) {
        let risk_text = vec![
            Line::from(vec![
                Span::styled("Portfolio Value: ", Style::default().fg(Color::Gray)),
                Span::styled("$12,450.00", Style::default().fg(Color::Green)),
            ]),
            Line::from(vec![
                Span::styled("Max Drawdown: ", Style::default().fg(Color::Gray)),
                Span::styled("2.1%", Style::default().fg(Color::Yellow)),
            ]),
            Line::from(vec![
                Span::styled("Kelly Fraction: ", Style::default().fg(Color::Gray)),
                Span::styled("0.25", Style::default().fg(Color::White)),
            ]),
            Line::from(vec![
                Span::styled("Position Size: ", Style::default().fg(Color::Gray)),
                Span::styled("$1,000", Style::default().fg(Color::Cyan)),
            ]),
            Line::from(vec![
                Span::styled("Risk Level: ", Style::default().fg(Color::Gray)),
                Span::styled("LOW", Style::default().fg(Color::Green)),
            ]),
        ];

        let paragraph = Paragraph::new(risk_text)
            .block(Block::default().borders(Borders::ALL).title("⚖️ Risk Metrics"))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, area);
    }

    // Operations Panel Implementations - "The Cockpit"
    fn render_master_control_panel(&self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([Constraint::Percentage(30), Constraint::Percentage(40), Constraint::Percentage(30)].as_ref())
            .split(area);

        // Bot Status
        let status_text = vec![
            Line::from(vec![
                Span::styled("STATUS: ", Style::default().fg(Color::Gray)),
                Span::styled(self.bot_status.as_str(), Style::default().fg(self.bot_status.color()).add_modifier(Modifier::BOLD)),
            ]),
        ];
        let status_widget = Paragraph::new(status_text)
            .block(Block::default().borders(Borders::ALL).title("🎛️ Master Control"));
        f.render_widget(status_widget, chunks[0]);

        // Control Buttons (simulated)
        let controls_text = vec![
            Line::from("S: Start/Stop  P: Pause  R: Restart"),
            Line::from("E: Emergency Stop  G: Graceful Stop"),
        ];
        let controls_widget = Paragraph::new(controls_text)
            .block(Block::default().borders(Borders::ALL).title("🔧 Controls"))
            .wrap(Wrap { trim: true });
        f.render_widget(controls_widget, chunks[1]);

        // Quick Stats
        let stats_text = vec![
            Line::from(vec![
                Span::styled("Uptime: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("{}m", self.tick_counter / 600), Style::default().fg(Color::White)),
            ]),
        ];
        let stats_widget = Paragraph::new(stats_text)
            .block(Block::default().borders(Borders::ALL).title("📊 Stats"));
        f.render_widget(stats_widget, chunks[2]);
    }

    fn render_live_activity_log(&self, f: &mut Frame, area: Rect) {
        let logs: Vec<ListItem> = self.log_buffer
            .iter()
            .take(area.height.saturating_sub(2) as usize)
            .map(|log| {
                let severity_color = match log.severity {
                    super::app::LogSeverity::Info => Color::White,
                    super::app::LogSeverity::Ok => Color::Green,
                    super::app::LogSeverity::Warn => Color::Yellow,
                    super::app::LogSeverity::Error => Color::Red,
                    super::app::LogSeverity::Debug => Color::Gray,
                };
                
                ListItem::new(Line::from(vec![
                    Span::styled(format!("{} ", log.timestamp), Style::default().fg(Color::Gray)),
                    Span::styled(format!("[{}] ", log.source), Style::default().fg(Color::Cyan)),
                    Span::styled(&log.message, Style::default().fg(severity_color)),
                ]))
            })
            .collect();

        let list = List::new(logs)
            .block(Block::default().borders(Borders::ALL).title("📡 Live Activity Log"))
            .style(Style::default().fg(Color::White));
        f.render_widget(list, area);
    }

    fn render_trade_history_panel(&self, f: &mut Frame, area: Rect) {
        let header = Row::new(vec!["Time", "Strategy", "Assets", "P&L", "Status", "Market Regime", "ARE Score", "Explanation"])
            .style(Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD));

        let rows: Vec<Row> = self.trade_history
            .iter()
            .take(area.height.saturating_sub(3) as usize) // Adjust take based on available height
            .map(|trade| {
                let are_score_display = trade.aetheric_resonance_at_execution
                    .as_ref()
                    .map_or("N/A".to_string(), |are| format!("{:.2}", are.composite_score));
                
                let explanation_display = trade.trade_explanation
                    .as_ref()
                    .map_or("N/A".to_string(), |exp| {
                        // Truncate explanation to fit in cell, add ellipsis if truncated
                        let max_len = 25; // Adjusted max length for better fit
                        if exp.chars().count() > max_len {
                            format!("{}...", exp.chars().take(max_len).collect::<String>())
                        } else {
                            exp.clone()
                        }
                    });

                Row::new(vec![
                    Cell::from(trade.timestamp.format("%H:%M:%S").to_string()),
                    Cell::from(trade.strategy.clone()),
                    Cell::from(trade.assets.clone()),
                    Cell::from(format!("${:.2}", trade.profit_loss_usd)),
                    Cell::from(trade.status.as_str()),
                    Cell::from(trade.market_regime_at_execution.clone().unwrap_or_else(|| "N/A".to_string())),
                    Cell::from(are_score_display),
                    Cell::from(explanation_display),
                ])
                .style(Style::default().fg(trade.status.color()))
            })
            .collect();

        let table = Table::new(rows)
            .header(header)
            .block(Block::default().borders(Borders::ALL).title("📈 Trade History"))
            .widths(&[
                Constraint::Length(8),   // Time
                Constraint::Length(12),  // Strategy
                Constraint::Length(12),  // Assets
                Constraint::Length(8),   // P&L
                Constraint::Length(10),  // Status
                Constraint::Length(15),  // Market Regime
                Constraint::Length(10),  // ARE Score
                Constraint::Min(28),     // Explanation (adjusted for new max_len)
            ])
            .column_spacing(1);
        f.render_widget(table, area);

        // Render detailed trade view if show_trade_details is true and a trade is selected
        if self.show_trade_details {
            if let Some(selected_index) = self.selected_trade_index {
                if let Some(trade) = self.trade_history.get(selected_index) {
                    self.render_trade_details_modal(f, trade);
                }
            }
        }

        // Render full explanation modal if show_full_explanation is true and a trade is selected
        if self.show_full_explanation {
            if let Some(selected_index) = self.selected_trade_index {
                if let Some(trade) = self.trade_history.get(selected_index) {
                    self.render_trade_explanation_modal(f, trade);
                }
            }
        }
    }

    fn render_trade_details_modal(&self, f: &mut Frame, trade: &TradeRecord) {
        let area = self.centered_rect(80, 70, f.size());
        f.render_widget(Clear, area); // Clear the background

        let block = Block::default()
            .title(format!(" 📊 Trade Details: {} ", trade.id))
            .borders(Borders::ALL)
            .border_style(Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD));
        f.render_widget(block, area);

        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .margin(1)
            .constraints([
                Constraint::Length(1), // Basic info
                Constraint::Length(1), // Profit/Loss
                Constraint::Length(1), // Tx Hash
                Constraint::Length(1), // Market Regime
                Constraint::Length(5), // ARE Score Breakdown (increased height)
                Constraint::Min(0),    // Execution Details
            ].as_ref()) // Removed Trade Explanation from here, moved to separate modal
            .split(area);

        let basic_info = Line::from(vec![
            Span::styled("Time: ", Style::default().fg(Color::Gray)),
            Span::styled(trade.timestamp.format("%Y-%m-%d %H:%M:%S").to_string(), Style::default().fg(Color::White)),
            Span::styled(" | Strategy: ", Style::default().fg(Color::Gray)),
            Span::styled(trade.strategy.clone(), Style::default().fg(Color::Cyan)),
            Span::styled(" | Assets: ", Style::default().fg(Color::Gray)),
            Span::styled(trade.assets.clone(), Style::default().fg(Color::White)),
        ]);
        f.render_widget(Paragraph::new(basic_info), chunks[0]);

        // Mock ETH price for gas cost conversion
        let eth_price_usd = Decimal::new(2500, 0); 
        let gas_cost_usd = trade.gas_price_gwei.map_or(Decimal::ZERO, |gwei| {
            // Assuming 21000 gas limit for a simple transfer, convert gwei to ETH, then to USD
            // This is a simplification; actual gas cost depends on gas_used * gas_price
            let gas_used_mock = Decimal::new(21000, 0); // Example gas used
            let gas_cost_eth = (gwei * gas_used_mock) / Decimal::new(1_000_000_000, 0); // Gwei to ETH
            gas_cost_eth * eth_price_usd
        });
        let net_profit = trade.profit_loss_usd - gas_cost_usd;

        let profit_info = Line::from(vec![
            Span::styled("Gross Profit: ", Style::default().fg(Color::Gray)),
            Span::styled(format!("${:.2}", trade.profit_loss_usd), Style::default().fg(Color::Green)),
            Span::styled(" | Gas Cost: ", Style::default().fg(Color::Gray)),
            Span::styled(format!("${:.2}", gas_cost_usd), Style::default().fg(Color::Yellow)),
            Span::styled(" | Net Profit: ", Style::default().fg(Color::Gray)),
            Span::styled(format!("${:.2}", net_profit), Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)),
        ]);
        f.render_widget(Paragraph::new(profit_info), chunks[1]);

        let tx_hash_info = Line::from(vec![
            Span::styled("Tx Hash: ", Style::default().fg(Color::Gray)),
            Span::styled(trade.transaction_hash.clone().unwrap_or_else(|| "N/A".to_string()), Style::default().fg(Color::White)),
            Span::styled(" | Status: ", Style::default().fg(Color::Gray)),
            Span::styled(trade.status.as_str(), Style::default().fg(trade.status.color())),
        ]);
        f.render_widget(Paragraph::new(tx_hash_info), chunks[2]);

        let market_regime_info = Line::from(vec![
            Span::styled("Market Regime: ", Style::default().fg(Color::Gray)),
            Span::styled(trade.market_regime_at_execution.clone().unwrap_or_else(|| "N/A".to_string()), Style::default().fg(Color::White)),
        ]);
        f.render_widget(Paragraph::new(market_regime_info), chunks[3]);

        let are_breakdown_text = if let Some(ref are) = trade.aetheric_resonance_at_execution {
            vec![
                Line::from(Span::styled("ARE Breakdown:", Style::default().fg(Color::Gray))),
                Line::from(vec![
                    Span::styled(format!("  Chronos Sieve: {:.2}", are.chronos_sieve), Style::default().fg(Color::White)),
                ]),
                Line::from(vec![
                    Span::styled(format!("  Mandorla Gauge: {:.2}", are.mandorla_gauge), Style::default().fg(Color::White)),
                ]),
                Line::from(vec![
                    Span::styled(format!("  Network Seismology: {:.2}", are.network_seismology), Style::default().fg(Color::White)),
                ]),
                Line::from(vec![
                    Span::styled(format!("  Composite Score: {:.2}", are.composite_score), Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)),
                ]),
            ]
        } else {
            vec![Line::from(Span::styled("ARE Breakdown: N/A", Style::default().fg(Color::Gray)))]
        };
        f.render_widget(Paragraph::new(are_breakdown_text), chunks[4]);

        let execution_details_text = vec![
            Line::from(Span::styled("Execution Details:", Style::default().fg(Color::Gray))),
            Line::from(trade.execution_details.clone().unwrap_or_else(|| "N/A".to_string())),
        ];
        f.render_widget(Paragraph::new(execution_details_text).wrap(Wrap { trim: false }), chunks[5]);
    }

    fn render_trade_explanation_modal(&self, f: &mut Frame, trade: &TradeRecord) {
        let area = self.centered_rect(90, 80, f.size());
        f.render_widget(Clear, area); // Clear the background

        let block = Block::default()
            .title(format!(" 📚 Trade Explanation: {} ", trade.id))
            .borders(Borders::ALL)
            .border_style(Style::default().fg(Color::Magenta).add_modifier(Modifier::BOLD));
        f.render_widget(block, area);

        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .margin(1)
            .constraints([
                Constraint::Length(1), // Title
                Constraint::Min(0),    // Explanation content
                Constraint::Length(1), // Close instruction
            ].as_ref())
            .split(area);

        let title_text = Line::from(vec![
            Span::styled("Strategy: ", Style::default().fg(Color::Gray)),
            Span::styled(trade.strategy.clone(), Style::default().fg(Color::Cyan)),
            Span::styled(" | Assets: ", Style::default().fg(Color::Gray)),
            Span::styled(trade.assets.clone(), Style::default().fg(Color::White)),
        ]);
        f.render_widget(Paragraph::new(title_text), chunks[0]);

        let explanation_content = vec![
            Line::from(Span::styled("Trade Explanation:", Style::default().fg(Color::Gray).add_modifier(Modifier::BOLD))),
            Line::from(trade.trade_explanation.clone().unwrap_or_else(|| "No detailed explanation available.".to_string())),
            Line::from(""),
            Line::from(Span::styled("Execution Details:", Style::default().fg(Color::Gray).add_modifier(Modifier::BOLD))),
            Line::from(trade.execution_details.clone().unwrap_or_else(|| "No detailed execution information available.".to_string())),
        ];
        f.render_widget(Paragraph::new(explanation_content).wrap(Wrap { trim: false }), chunks[1]);

        let close_instruction = Line::from(Span::styled("Press ESC to close", Style::default().fg(Color::DarkGray).add_modifier(Modifier::ITALIC)));
        f.render_widget(Paragraph::new(close_instruction).alignment(Alignment::Center), chunks[2]);
    }

    fn render_trade_lifecycle_panel(&self, f: &mut Frame, area: Rect) {
        let events: Vec<ListItem> = self.trade_lifecycle_events
            .iter()
            .rev() // Display newest events first
            .take(area.height.saturating_sub(2) as usize)
            .map(|event| {
                let (icon, status_text) = event.status.status_indicator();
                let message = &event.educational_context;
                
                ListItem::new(Line::from(vec![
                    Span::styled(format!("{} ", event.formatted_timestamp()), Style::default().fg(Color::DarkGray)),
                    Span::styled(format!("{} ", icon), Style::default().fg(Color::White)),
                    Span::styled(format!("[{}] ", status_text), Style::default().fg(Color::Cyan)),
                    Span::styled(message, Style::default().fg(Color::White)),
                ]))
            })
            .collect();

        let list = List::new(events)
            .block(Block::default().borders(Borders::ALL).title("📚 Trade Lifecycle Events"))
            .style(Style::default().fg(Color::White));
        f.render_widget(list, area);
    }

    fn render_current_state_dashboard(&self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Percentage(40), Constraint::Percentage(30), Constraint::Percentage(30)].as_ref())
            .split(area);

        // Market Regime
        let regime_text = vec![
            Line::from(vec![
                Span::styled("Market Regime: ", Style::default().fg(Color::Gray)),
                Span::styled("TRENDING", Style::default().fg(Color::Green)),
            ]),
            Line::from(vec![
                Span::styled("Active Strategy: ", Style::default().fg(Color::Gray)),
                Span::styled("Zen Geometer", Style::default().fg(Color::Cyan)),
            ]),
        ];
        let regime_widget = Paragraph::new(regime_text)
            .block(Block::default().borders(Borders::ALL).title("🧭 Market State"));
        f.render_widget(regime_widget, chunks[0]);

        // Wallet Health
        let wallet_text = vec![
            Line::from(vec![
                Span::styled("ETH: ", Style::default().fg(Color::Gray)),
                Span::styled("2.45", Style::default().fg(Color::White)),
            ]),
            Line::from(vec![
                Span::styled("USDC: ", Style::default().fg(Color::Gray)),
                Span::styled("5,234.12", Style::default().fg(Color::White)),
            ]),
        ];
        let wallet_widget = Paragraph::new(wallet_text)
            .block(Block::default().borders(Borders::ALL).title("💰 Wallet"));
        f.render_widget(wallet_widget, chunks[1]);

        // Performance Gauge
        let performance_ratio = (self.system_metrics.total_pnl_24h.to_f64().unwrap_or(0.0) / 1000.0).max(0.0).min(1.0);
        let gauge = Gauge::default()
            .block(Block::default().borders(Borders::ALL).title("📊 Performance"))
            .gauge_style(Style::default().fg(Color::Green))
            .ratio(performance_ratio);
        f.render_widget(gauge, chunks[2]);
    }

    // Systems Panel Implementations - "The Engine Room"
    fn render_component_status_matrix(&self, f: &mut Frame, area: Rect) {
        let header = Row::new(vec!["Component", "Status", "Uptime", "CPU%", "Memory%", "Activity"])
            .style(Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD));

        // Mock component data based on the analytical pillars from GEMINI.md
        let components = vec![
            ("Data Ingestor", "RUNNING", "2h 34m", "12.3", "45.2", "Active"),
            ("Chronos Sieve", "RUNNING", "2h 34m", "8.7", "32.1", "Analyzing"),
            ("Mandorla Gauge", "RUNNING", "2h 34m", "15.2", "28.9", "Calculating"),
            ("Network Seismology", "RUNNING", "2h 34m", "5.4", "18.7", "Monitoring"),
            ("Strategy Manager", "RUNNING", "2h 34m", "22.1", "67.3", "Evaluating"),
            ("Execution Manager", "RUNNING", "2h 34m", "18.9", "41.5", "Ready"),
            ("Risk Manager", "RUNNING", "2h 34m", "7.2", "25.8", "Monitoring"),
            ("MEV Protector", "RUNNING", "2h 34m", "9.8", "33.4", "Scanning"),
        ];

        let rows: Vec<Row> = components
            .iter()
            .enumerate()
            .map(|(i, (name, status, uptime, cpu, memory, activity))| {
                let style = if Some(i) == self.selected_component_index {
                    Style::default().bg(Color::Blue).fg(Color::White)
                } else {
                    Style::default()
                };

                let status_color = match *status {
                    "RUNNING" => Color::Green,
                    "ERROR" => Color::Red,
                    "STALLED" => Color::Yellow,
                    _ => Color::Gray,
                };

                Row::new(vec![
                    Cell::from(*name),
                    Cell::from(*status).style(Style::default().fg(status_color)),
                    Cell::from(*uptime),
                    Cell::from(*cpu),
                    Cell::from(*memory),
                    Cell::from(*activity),
                ])
                .style(style)
            })
            .collect();

        let table = Table::new(rows)
            .header(header)
            .block(Block::default().borders(Borders::ALL).title("🔧 Component Status Matrix"))
            .widths(&[
                Constraint::Length(16),
                Constraint::Length(8),
                Constraint::Length(8),
                Constraint::Length(6),
                Constraint::Length(8),
                Constraint::Min(10),
            ]);
        f.render_widget(table, area);
    }

    fn render_kpi_panel(&self, f: &mut Frame, area: Rect) {
        let kpi_text = vec![
            Line::from(vec![
                Span::styled("Network S-P Time: ", Style::default().fg(Color::Gray)),
                Span::styled("33.4ms", Style::default().fg(Color::Green)),
            ]),
            Line::from(vec![
                Span::styled("Resonance State: ", Style::default().fg(Color::Gray)),
                Span::styled("HARMONIC", Style::default().fg(Color::Cyan)),
            ]),
            Line::from(vec![
                Span::styled("Temporal Cycle: ", Style::default().fg(Color::Gray)),
                Span::styled("BULL_TREND", Style::default().fg(Color::Green)),
            ]),
            Line::from(vec![
                Span::styled("Data Ingestion: ", Style::default().fg(Color::Gray)),
                Span::styled("1,247 msg/s", Style::default().fg(Color::White)),
            ]),
            Line::from(vec![
                Span::styled("NATS Connection: ", Style::default().fg(Color::Gray)),
                Span::styled("HEALTHY", Style::default().fg(Color::Green)),
            ]),
            Line::from(vec![
                Span::styled("RPC Latency: ", Style::default().fg(Color::Gray)),
                Span::styled("12.3ms", Style::default().fg(Color::Green)),
            ]),
        ];

        let paragraph = Paragraph::new(kpi_text)
            .block(Block::default().borders(Borders::ALL).title("📊 Key Performance Indicators"))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, area);
    }

    fn render_node_connectivity_panel(&self, f: &mut Frame, area: Rect) {
        let header = Row::new(vec!["Node", "Status", "Latency"])
            .style(Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD));

        // Mock network nodes
        let nodes = vec![
            ("Base RPC", "CONNECTED", "12ms"),
            ("Arbitrum RPC", "CONNECTED", "18ms"),
            ("Degen RPC", "CONNECTED", "25ms"),
            ("WebSocket Feed", "CONNECTED", "8ms"),
            ("MEV Relay", "CONNECTED", "15ms"),
            ("Backup RPC", "DISCONNECTED", "N/A"),
        ];

        let rows: Vec<Row> = nodes
            .iter()
            .enumerate()
            .map(|(i, (name, status, latency))| {
                let style = if Some(i) == self.selected_node_index {
                    Style::default().bg(Color::Blue).fg(Color::White)
                } else {
                    Style::default()
                };

                let status_color = match *status {
                    "CONNECTED" => Color::Green,
                    "DISCONNECTED" => Color::Red,
                    "RECONNECTING" => Color::Yellow,
                    _ => Color::Gray,
                };

                Row::new(vec![
                    Cell::from(*name),
                    Cell::from(*status).style(Style::default().fg(status_color)),
                    Cell::from(*latency),
                ])
                .style(style)
            })
            .collect();

        let table = Table::new(rows)
            .header(header)
            .block(Block::default().borders(Borders::ALL).title("🌐 Node Connectivity"))
            .widths(&[
                Constraint::Length(14),
                Constraint::Length(12),
                Constraint::Min(8),
            ]);
        f.render_widget(table, area);
    }

    fn render_log_viewer_panel(&self, f: &mut Frame, area: Rect) {
        let logs: Vec<ListItem> = self.log_buffer
            .iter()
            .skip(self.log_scroll_offset)
            .take(area.height.saturating_sub(2) as usize)
            .map(|log| {
                ListItem::new(Line::from(vec![
                    Span::styled(format!("{} ", log.timestamp), Style::default().fg(Color::Gray)),
                    Span::styled(format!("[{}] ", log.severity.prefix()), Style::default().fg(log.severity.color())),
                    Span::styled(format!("{}: ", log.source), Style::default().fg(Color::Cyan)),
                    Span::styled(&log.message, Style::default().fg(Color::White)),
                ]))
            })
            .collect();

        let title = if let Some(ref filter) = self.log_filter {
            format!("📋 System Logs (Filter: {})", filter)
        } else {
            "📋 System Logs".to_string()
        };

        let list = List::new(logs)
            .block(Block::default().borders(Borders::ALL).title(title.as_str()))
            .style(Style::default().fg(Color::White));
        f.render_widget(list, area);
    }

    fn render_network_seismology_panel(&self, f: &mut Frame, area: Rect) {
        let seismology_text = vec![
            Line::from(vec![
                Span::styled("P-Wave: ", Style::default().fg(Color::Gray)),
                Span::styled("12.3ms", Style::default().fg(Color::Green)),
            ]),
            Line::from(vec![
                Span::styled("S-Wave: ", Style::default().fg(Color::Gray)),
                Span::styled("45.7ms", Style::default().fg(Color::Yellow)),
            ]),
            Line::from(vec![
                Span::styled("S-P Time: ", Style::default().fg(Color::Gray)),
                Span::styled("33.4ms", Style::default().fg(Color::White)),
            ]),
            Line::from(vec![
                Span::styled("Block Height: ", Style::default().fg(Color::Gray)),
                Span::styled("19,234,567", Style::default().fg(Color::Cyan)),
            ]),
        ];

        let paragraph = Paragraph::new(seismology_text)
            .block(Block::default().borders(Borders::ALL).title("🌊 Network Seismology"))
            .wrap(Wrap { trim: true });
        f.render_widget(paragraph, area);
    }

    fn render_service_management_panel(&self, f: &mut Frame, area: Rect) {
        let services = vec![
            ("Data Ingestor", "●", Color::Green),
            ("Price Oracle", "●", Color::Green),
            ("Strategy Manager", "●", Color::Green),
            ("Execution Engine", "●", Color::Yellow),
            ("Risk Manager", "●", Color::Green),
            ("MEV Protector", "●", Color::Green),
        ];

        let service_items: Vec<ListItem> = services
            .iter()
            .enumerate()
            .map(|(i, (name, status, color))| {
                let style = if Some(i) == self.selected_service_index {
                    Style::default().bg(Color::Blue).fg(Color::White)
                } else {
                    Style::default()
                };
                
                ListItem::new(Line::from(vec![
                    Span::styled(*status, Style::default().fg(*color)),
                    Span::styled(format!(" {}", name), Style::default().fg(Color::White)),
                ])).style(style)
            })
            .collect();

        let list = List::new(service_items)
            .block(Block::default().borders(Borders::ALL).title("⚙️ Services"))
            .style(Style::default().fg(Color::White));
        f.render_widget(list, area);
    }

    // Config Panel Implementations - "The Control Panel"
    fn render_config_tree_view(&self, f: &mut Frame, area: Rect) {
        // Mock configuration sections
        let config_sections = vec![
            ("📊 [strategy]", "Strategy configuration"),
            ("⚡ [execution]", "Execution parameters"),
            ("🌐 [data_sources]", "Data source settings"),
            ("⚖️ [risk]", "Risk management"),
            ("🔗 [chains]", "Blockchain networks"),
            ("🔧 [system]", "System settings"),
        ];

        let items: Vec<ListItem> = config_sections
            .iter()
            .enumerate()
            .map(|(i, (name, desc))| {
                let style = if Some(i) == self.selected_config_section {
                    Style::default().bg(Color::Blue).fg(Color::White)
                } else {
                    Style::default()
                };
                
                ListItem::new(Line::from(vec![
                    Span::styled(*name, Style::default().fg(Color::Cyan)),
                    Span::styled(format!(" - {}", desc), Style::default().fg(Color::Gray)),
                ])).style(style)
            })
            .collect();

        let list = List::new(items)
            .block(Block::default().borders(Borders::ALL).title("🗂️ Configuration Sections"))
            .style(Style::default().fg(Color::White));
        f.render_widget(list, area);
    }

    fn render_parameter_editor(&self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Length(3), Constraint::Min(0)].as_ref())
            .split(area);

        // Parameter header
        let selected_section = self.selected_config_section.unwrap_or(0);
        let section_names = ["strategy", "execution", "data_sources", "risk", "chains", "system"];
        let section_name = section_names.get(selected_section).unwrap_or(&"unknown");
        
        let header_text = vec![
            Line::from(vec![
                Span::styled("Section: ", Style::default().fg(Color::Gray)),
                Span::styled(*section_name, Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)),
            ]),
        ];
        let header_widget = Paragraph::new(header_text)
            .block(Block::default().borders(Borders::ALL).title("📝 Parameter Editor"));
        f.render_widget(header_widget, chunks[0]);

        // Mock parameters based on selected section
        let parameters = match selected_section {
            0 => vec![ // strategy
                ("min_resonance_threshold", "0.75", "Minimum score to execute trade (0.0-1.0)"),
                ("max_position_size_usd", "1000.0", "Maximum position size in USD"),
                ("enabled_strategies", "zen_geometer,pilot_fish", "Comma-separated strategy list"),
            ],
            1 => vec![ // execution
                ("gas_price_multiplier", "1.2", "Gas price multiplier for transactions"),
                ("slippage_tolerance", "0.005", "Maximum allowed slippage (0.5%)"),
                ("max_retry_attempts", "3", "Maximum transaction retry attempts"),
            ],
            2 => vec![ // data_sources
                ("base_rpc_url", "https://mainnet.base.org", "Base network RPC endpoint"),
                ("websocket_enabled", "true", "Enable WebSocket data feeds"),
                ("refresh_interval_ms", "100", "Data refresh interval in milliseconds"),
            ],
            _ => vec![
                ("parameter_1", "value_1", "Description for parameter 1"),
                ("parameter_2", "value_2", "Description for parameter 2"),
            ],
        };

        let param_items: Vec<ListItem> = parameters
            .iter()
            .enumerate()
            .map(|(i, (key, value, desc))| {
                let style = if Some(i) == self.selected_config_param {
                    Style::default().bg(Color::Blue).fg(Color::White)
                } else {
                    Style::default()
                };
                
                ListItem::new(vec![
                    Line::from(vec![
                        Span::styled(format!("{}: ", key), Style::default().fg(Color::Cyan)),
                        Span::styled(*value, Style::default().fg(Color::White).add_modifier(Modifier::BOLD)),
                    ]),
                    Line::from(vec![
                        Span::styled(format!("  {}", desc), Style::default().fg(Color::Gray)),
                    ]),
                ]).style(style)
            })
            .collect();

        let param_list = List::new(param_items)
            .block(Block::default().borders(Borders::ALL))
            .style(Style::default().fg(Color::White));
        f.render_widget(param_list, chunks[1]);
    }

    fn render_config_controls(&self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([Constraint::Percentage(50), Constraint::Percentage(50)].as_ref())
            .split(area);

        // Hot-Reload Controls
        let controls_text = vec![
            Line::from("Available Actions:"),
            Line::from(""),
            Line::from("S: Save & Hot-Reload"),
            Line::from("R: Reset to Defaults"),
            Line::from("L: Load Profile"),
            Line::from("V: Validate Config"),
        ];
        let controls_widget = Paragraph::new(controls_text)
            .block(Block::default().borders(Borders::ALL).title("🔄 Hot-Reload Control"))
            .wrap(Wrap { trim: true });
        f.render_widget(controls_widget, chunks[0]);

        // Validation Status
        let validation_color = if self.config_modified {
            Color::Yellow
        } else {
            Color::Green
        };

        let validation_text = vec![
            Line::from(vec![
                Span::styled("Status: ", Style::default().fg(Color::Gray)),
                Span::styled(
                    if self.config_modified { "MODIFIED" } else { "CLEAN" },
                    Style::default().fg(validation_color).add_modifier(Modifier::BOLD)
                ),
            ]),
            Line::from(""),
            Line::from(vec![
                Span::styled("Errors: ", Style::default().fg(Color::Gray)),
                Span::styled(
                    format!("{}", self.config_validation_errors.len()),
                    Style::default().fg(if self.config_validation_errors.is_empty() { Color::Green } else { Color::Red })
                ),
            ]),
            Line::from(""),
            Line::from("Profile: active.toml"),
        ];
        let validation_widget = Paragraph::new(validation_text)
            .block(Block::default().borders(Borders::ALL).title("✅ Validation & Status"))
            .wrap(Wrap { trim: true });
        f.render_widget(validation_widget, chunks[1]);
    }

    // Strategy Inspector Modal Implementation
    fn render_inspector_modal(&self, f: &mut Frame) {
        if let Some(ref data) = self.inspection_data {
            // Create centered modal area (80% width, 70% height)
            let area = self.centered_rect(80, 70, f.size());
            
            // Clear the background
            f.render_widget(Clear, area);
            
            // Main modal block
            let block = Block::default()
                .title(" 🔍 Strategy Inspector - Aetheric Resonance Analysis ")
                .borders(Borders::ALL)
                .border_style(Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD));
            f.render_widget(block, area);

            // Split modal into sections
            let chunks = Layout::default()
                .direction(Direction::Vertical)
                .margin(1)
                .constraints([
                    Constraint::Length(4),  // Header info
                    Constraint::Min(0),     // Score breakdown table
                    Constraint::Length(6),  // Footer with summary
                ].as_ref())
                .split(area);

            // Header: Opportunity Summary
            self.render_inspector_header(f, chunks[0], data);
            
            // Main: Score Breakdown Table
            self.render_inspector_breakdown(f, chunks[1], data);
            
            // Footer: Summary and Controls
            self.render_inspector_footer(f, chunks[2], data);
        }
    }

    fn render_inspector_header(&self, f: &mut Frame, area: Rect, data: &crate::inspector::InspectableOpportunity) {
        let header_text = vec![
            Line::from(vec![
                Span::styled("ID: ", Style::default().fg(Color::Gray)),
                Span::styled(data.id.to_string(), Style::default().fg(Color::White)),
            ]),
            Line::from(vec![
                Span::styled("Summary: ", Style::default().fg(Color::Gray)),
                Span::styled(&data.opportunity_summary, Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)),
            ]),
            Line::from(vec![
                Span::styled("Status: ", Style::default().fg(Color::Gray)),
                Span::styled(data.execution_status.as_str(), Style::default().fg(data.execution_status.color()).add_modifier(Modifier::BOLD)),
                Span::styled(" | Risk Level: ", Style::default().fg(Color::Gray)),
                Span::styled(data.risk_level(), Style::default().fg(match data.risk_level() {
                    "LOW" => Color::Green,
                    "MEDIUM" => Color::Yellow,
                    "HIGH" => Color::Red,
                    _ => Color::White,
                })),
            ]),
        ];

        let header = Paragraph::new(header_text)
            .block(Block::default().borders(Borders::ALL).title("📋 Opportunity Details"))
            .wrap(Wrap { trim: true });
        f.render_widget(header, area);
    }

    fn render_inspector_breakdown(&self, f: &mut Frame, area: Rect, data: &crate::inspector::InspectableOpportunity) {
        let header = Row::new(vec!["Pillar", "Metric", "Value", "Score", "Weight", "Explanation"])
            .style(Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD));

        let rows: Vec<Row> = data.breakdown.details.iter().map(|detail| {
            Row::new(vec![
                Cell::from(detail.pillar_name.clone()),
                Cell::from(detail.metric_name.clone()),
                Cell::from(detail.metric_value.clone()),
                Cell::from(format!("{:.4}", detail.score_contribution)),
                Cell::from(format!("{:.1}%", detail.weight * 100.0)),
                Cell::from(detail.explanation.clone()),
            ])
            .style(Style::default().fg(Color::White))
        }).collect();

        let table = Table::new(rows)
            .header(header)
            .block(Block::default().borders(Borders::ALL).title("⚖️ Score Breakdown Analysis"))
            .widths(&[
                Constraint::Length(18),  // Pillar
                Constraint::Length(16),  // Metric
                Constraint::Length(12),  // Value
                Constraint::Length(8),   // Score
                Constraint::Length(8),   // Weight
                Constraint::Min(20),     // Explanation
            ])
            .column_spacing(1);

        f.render_widget(table, area);
    }

    fn render_inspector_footer(&self, f: &mut Frame, area: Rect, data: &crate::inspector::InspectableOpportunity) {
        let chunks = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([Constraint::Percentage(50), Constraint::Percentage(50)].as_ref())
            .split(area);

        // Left: Financial Summary
        let financial_text = vec![
            Line::from(vec![
                Span::styled("Final Score: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("{:.4}", data.final_score), Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)),
                Span::styled(" | Confidence: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("{:.1}%", data.breakdown.confidence_level * 100.0), Style::default().fg(Color::Green)),
            ]),
            Line::from(vec![
                Span::styled("Profit Est: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("${:.2}", data.profit_estimate_usd), Style::default().fg(Color::Green)),
                Span::styled(" | Gas Est: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("${:.2}", data.gas_estimate_usd), Style::default().fg(Color::Yellow)),
            ]),
            Line::from(vec![
                Span::styled("Net Profit: ", Style::default().fg(Color::Gray)),
                Span::styled(format!("${:.2}", data.net_profit_usd), Style::default().fg(
                    if data.net_profit_usd > rust_decimal::Decimal::ZERO { Color::Green } else { Color::Red }
                ).add_modifier(Modifier::BOLD)),
            ]),
        ];

        let financial = Paragraph::new(financial_text)
            .block(Block::default().borders(Borders::ALL).title("💰 Financial Analysis"))
            .wrap(Wrap { trim: true });
        f.render_widget(financial, chunks[0]);

        // Right: Controls and Risk Factors
        let mut control_lines = vec![
            Line::from("Controls: [ESC] Close | [E] Execute | [R] Reject"),
            Line::from(""),
        ];

        if !data.breakdown.risk_factors.is_empty() {
            control_lines.push(Line::from(vec![
                Span::styled("⚠️ Risk Factors:", Style::default().fg(Color::Red).add_modifier(Modifier::BOLD)),
            ]));
            for risk in &data.breakdown.risk_factors {
                control_lines.push(Line::from(vec![
                    Span::styled("• ", Style::default().fg(Color::Red)),
                    Span::styled(risk, Style::default().fg(Color::White)),
                ]));
            }
        }

        if !data.breakdown.opportunity_strengths.is_empty() {
            control_lines.push(Line::from(vec![
                Span::styled("✅ Strengths:", Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)),
            ]));
            for strength in &data.breakdown.opportunity_strengths {
                control_lines.push(Line::from(vec![
                    Span::styled("• ", Style::default().fg(Color::Green)),
                    Span::styled(strength, Style::default().fg(Color::White)),
                ]));
            }
        }

        let controls = Paragraph::new(control_lines)
            .block(Block::default().borders(Borders::ALL).title("🎛️ Controls & Analysis"))
            .wrap(Wrap { trim: true });
        f.render_widget(controls, chunks[1]);
    }

    // Helper function to create centered rectangle
    fn centered_rect(&self, percent_x: u16, percent_y: u16, r: Rect) -> Rect {
        let popup_layout = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Percentage((100 - percent_y) / 2),
                Constraint::Percentage(percent_y),
                Constraint::Percentage((100 - percent_y) / 2),
            ])
            .split(r);

        Layout::default()
            .direction(Direction::Horizontal)
            .constraints([
                Constraint::Percentage((100 - percent_x) / 2),
                Constraint::Percentage(percent_x),
                Constraint::Percentage((100 - percent_x) / 2),
            ])
            .split(popup_layout[1])[1]
    }

    // Render errors tab
    fn render_errors(&mut self, f: &mut Frame, area: Rect) {
        self.error_dashboard.render(f, area);
    }
}