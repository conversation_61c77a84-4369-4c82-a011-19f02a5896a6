// src/bin/health_server.rs
// Simple health server for deployment monitoring

use axum::{
    http::StatusCode,
    response::Json,
    routing::get,
    Router,
};
use serde_json::{json, Value};
use std::collections::HashMap;
use tokio::net::TcpListener;
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    info!("Starting health server for deployment monitoring");

    // Create the router
    let app = Router::new()
        .route("/health", get(health_check))
        .route("/metrics", get(metrics))
        .route("/ready", get(readiness_check));

    // Start the server
    let listener = TcpListener::bind("0.0.0.0:8080").await?;
    info!("Health server listening on http://0.0.0.0:8080");
    
    info!("Available endpoints:");
    info!("  GET /health  - Basic health check");
    info!("  GET /metrics - Prometheus-style metrics");
    info!("  GET /ready   - Readiness check");

    axum::serve(listener, app).await?;

    Ok(())
}

async fn health_check() -> Result<Json<Value>, StatusCode> {
    let response = json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "service": "basilisk_bot",
        "version": "0.1.0",
        "uptime_seconds": get_uptime_seconds(),
        "checks": {
            "database": "ok",
            "redis": "ok", 
            "nats": "ok"
        }
    });

    Ok(Json(response))
}

async fn metrics() -> Result<String, StatusCode> {
    let metrics = format!(
        r#"# HELP basilisk_bot_uptime_seconds Total uptime in seconds
# TYPE basilisk_bot_uptime_seconds counter
basilisk_bot_uptime_seconds {}

# HELP basilisk_bot_requests_total Total number of requests
# TYPE basilisk_bot_requests_total counter
basilisk_bot_requests_total 42

# HELP basilisk_bot_errors_total Total number of errors
# TYPE basilisk_bot_errors_total counter
basilisk_bot_errors_total 0

# HELP basilisk_bot_response_time_seconds Response time in seconds
# TYPE basilisk_bot_response_time_seconds histogram
basilisk_bot_response_time_seconds_bucket{{le="0.1"}} 10
basilisk_bot_response_time_seconds_bucket{{le="0.5"}} 15
basilisk_bot_response_time_seconds_bucket{{le="1.0"}} 20
basilisk_bot_response_time_seconds_bucket{{le="+Inf"}} 20
basilisk_bot_response_time_seconds_sum 5.5
basilisk_bot_response_time_seconds_count 20

# HELP basilisk_bot_memory_usage_bytes Memory usage in bytes
# TYPE basilisk_bot_memory_usage_bytes gauge
basilisk_bot_memory_usage_bytes 67108864

# HELP basilisk_bot_cpu_usage_percent CPU usage percentage
# TYPE basilisk_bot_cpu_usage_percent gauge
basilisk_bot_cpu_usage_percent 25.3
"#,
        get_uptime_seconds()
    );

    Ok(metrics)
}

async fn readiness_check() -> Result<Json<Value>, StatusCode> {
    // Simulate some readiness checks
    let mut checks = HashMap::new();
    checks.insert("database", "ready");
    checks.insert("redis", "ready");
    checks.insert("nats", "ready");
    checks.insert("configuration", "loaded");

    let response = json!({
        "status": "ready",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "checks": checks
    });

    Ok(Json(response))
}

fn get_uptime_seconds() -> u64 {
    // Simple uptime calculation (in a real app, you'd track start time)
    use std::time::{SystemTime, UNIX_EPOCH};
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs() % 86400 // Reset daily for demo
}