// src/validation/cross_chain_validator_cli.rs

//! CLI interface for Cross-Chain Execution Validation Framework

use crate::validation::{
    CrossChainValidator, CrossChainValidationConfig, ValidationFramework, ValidationConfig
};
use crate::error::BasiliskError;
use clap::{Args, Subcommand};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::{error, info, warn};

/// CLI arguments for cross-chain validation
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct CrossChainValidationArgs {
    /// Configuration file path
    #[arg(long, short = 'c')]
    pub config: Option<PathBuf>,
    
    /// Base chain RPC URL
    #[arg(long)]
    pub base_rpc: Option<String>,
    
    /// Degen chain RPC URL  
    #[arg(long)]
    pub degen_rpc: Option<String>,
    
    /// Base Anvil port
    #[arg(long, default_value = "8545")]
    pub base_port: u16,
    
    /// Degen Anvil port
    #[arg(long, default_value = "8546")]
    pub degen_port: u16,
    
    /// Skip Anvil startup (use existing instances)
    #[arg(long)]
    pub skip_anvil: bool,
    
    /// Output format (json, table, summary)
    #[arg(long, default_value = "table")]
    pub output: String,
    
    /// Save results to file
    #[arg(long)]
    pub save_results: Option<PathBuf>,
}

/// Cross-chain validation commands
#[derive(Debug, Clone, Subcommand)]
pub enum CrossChainValidationCommand {
    /// Validate Hub and Spoke architecture
    HubSpoke {
        #[command(flatten)]
        args: CrossChainValidationArgs,
    },
    /// Validate Base L2 settlement hub
    BaseHub {
        #[command(flatten)]
        args: CrossChainValidationArgs,
    },
    /// Validate Degen Chain L3 execution venue
    DegenExecution {
        #[command(flatten)]
        args: CrossChainValidationArgs,
    },
    /// Validate Stargate bridge integration
    StargateIntegration {
        #[command(flatten)]
        args: CrossChainValidationArgs,
    },
    /// Validate cross-chain arbitrage profitability
    ArbitrageProfitability {
        #[command(flatten)]
        args: CrossChainValidationArgs,
    },
    /// Validate bridge fee and slippage prediction
    BridgePrediction {
        #[command(flatten)]
        args: CrossChainValidationArgs,
    },
    /// Run complete cross-chain validation suite
    Complete {
        #[command(flatten)]
        args: CrossChainValidationArgs,
    },
}

/// Handle cross-chain validation commands
pub async fn handle_cross_chain_validation_command(
    command: CrossChainValidationCommand,
) -> Result<(), BasiliskError> {
    match command {
        CrossChainValidationCommand::HubSpoke { args } => {
            run_hub_spoke_validation(args).await
        }
        CrossChainValidationCommand::BaseHub { args } => {
            run_base_hub_validation(args).await
        }
        CrossChainValidationCommand::DegenExecution { args } => {
            run_degen_execution_validation(args).await
        }
        CrossChainValidationCommand::StargateIntegration { args } => {
            run_stargate_integration_validation(args).await
        }
        CrossChainValidationCommand::ArbitrageProfitability { args } => {
            run_arbitrage_profitability_validation(args).await
        }
        CrossChainValidationCommand::BridgePrediction { args } => {
            run_bridge_prediction_validation(args).await
        }
        CrossChainValidationCommand::Complete { args } => {
            run_complete_cross_chain_validation_suite(args).await
        }
    }
}

/// Run Hub and Spoke architecture validation
pub async fn run_hub_spoke_validation(args: CrossChainValidationArgs) -> Result<(), BasiliskError> {
    info!("Starting Hub and Spoke architecture validation");

    let config = load_or_create_config(args.clone()).await?;
    let mut validator = CrossChainValidator::new(config);

    if !args.skip_anvil {
        validator.start_anvil_environments().await?;
    }

    let result = validator.validate_hub_spoke_architecture().await?;
    
    display_validation_result(&result, &args.output)?;
    
    if let Some(save_path) = args.save_results {
        save_validation_result(&result, &save_path)?;
    }

    if !args.skip_anvil {
        validator.stop_anvil_environments().await?;
    }

    match result.status {
        crate::validation::ValidationStatus::Passed => {
            info!("✅ Hub and Spoke architecture validation PASSED");
            Ok(())
        }
        crate::validation::ValidationStatus::Warning => {
            warn!("⚠️  Hub and Spoke architecture validation completed with WARNINGS");
            Ok(())
        }
        _ => {
            error!("❌ Hub and Spoke architecture validation FAILED");
            Err(BasiliskError::execution_error("Validation failed"))
        }
    }
}

/// Run Base L2 settlement hub validation
pub async fn run_base_hub_validation(args: CrossChainValidationArgs) -> Result<(), BasiliskError> {
    info!("Starting Base L2 settlement hub validation");

    let config = load_or_create_config(args.clone()).await?;
    let mut validator = CrossChainValidator::new(config);

    if !args.skip_anvil {
        validator.start_anvil_environments().await?;
    }

    let result = validator.validate_base_settlement_hub().await?;
    
    display_validation_result(&result, &args.output)?;
    
    if let Some(save_path) = args.save_results {
        save_validation_result(&result, &save_path)?;
    }

    if !args.skip_anvil {
        validator.stop_anvil_environments().await?;
    }

    match result.status {
        crate::validation::ValidationStatus::Passed => {
            info!("✅ Base L2 settlement hub validation PASSED");
            Ok(())
        }
        crate::validation::ValidationStatus::Warning => {
            warn!("⚠️  Base L2 settlement hub validation completed with WARNINGS");
            Ok(())
        }
        _ => {
            error!("❌ Base L2 settlement hub validation FAILED");
            Err(BasiliskError::execution_error("Validation failed"))
        }
    }
}

/// Run Degen Chain L3 execution venue validation
pub async fn run_degen_execution_validation(args: CrossChainValidationArgs) -> Result<(), BasiliskError> {
    info!("Starting Degen Chain L3 execution venue validation");

    let config = load_or_create_config(args.clone()).await?;
    let mut validator = CrossChainValidator::new(config);

    if !args.skip_anvil {
        validator.start_anvil_environments().await?;
    }

    let result = validator.validate_degen_execution_venue().await?;
    
    display_validation_result(&result, &args.output)?;
    
    if let Some(save_path) = args.save_results {
        save_validation_result(&result, &save_path)?;
    }

    if !args.skip_anvil {
        validator.stop_anvil_environments().await?;
    }

    match result.status {
        crate::validation::ValidationStatus::Passed => {
            info!("✅ Degen Chain L3 execution venue validation PASSED");
            Ok(())
        }
        crate::validation::ValidationStatus::Warning => {
            warn!("⚠️  Degen Chain L3 execution venue validation completed with WARNINGS");
            Ok(())
        }
        _ => {
            error!("❌ Degen Chain L3 execution venue validation FAILED");
            Err(BasiliskError::execution_error("Validation failed"))
        }
    }
}

/// Run Stargate bridge integration validation
pub async fn run_stargate_integration_validation(args: CrossChainValidationArgs) -> Result<(), BasiliskError> {
    info!("Starting Stargate bridge integration validation");

    let config = load_or_create_config(args.clone()).await?;
    let mut validator = CrossChainValidator::new(config);

    if !args.skip_anvil {
        validator.start_anvil_environments().await?;
    }

    let result = validator.validate_stargate_bridge_integration().await?;
    
    display_validation_result(&result, &args.output)?;
    
    if let Some(save_path) = args.save_results {
        save_validation_result(&result, &save_path)?;
    }

    if !args.skip_anvil {
        validator.stop_anvil_environments().await?;
    }

    match result.status {
        crate::validation::ValidationStatus::Passed => {
            info!("✅ Stargate bridge integration validation PASSED");
            Ok(())
        }
        crate::validation::ValidationStatus::Warning => {
            warn!("⚠️  Stargate bridge integration validation completed with WARNINGS");
            Ok(())
        }
        _ => {
            error!("❌ Stargate bridge integration validation FAILED");
            Err(BasiliskError::execution_error("Validation failed"))
        }
    }
}

/// Run cross-chain arbitrage profitability validation
pub async fn run_arbitrage_profitability_validation(args: CrossChainValidationArgs) -> Result<(), BasiliskError> {
    info!("Starting cross-chain arbitrage profitability validation");

    let config = load_or_create_config(args.clone()).await?;
    let mut validator = CrossChainValidator::new(config);

    if !args.skip_anvil {
        validator.start_anvil_environments().await?;
    }

    let result = validator.validate_cross_chain_arbitrage_profitability().await?;
    
    display_validation_result(&result, &args.output)?;
    
    if let Some(save_path) = args.save_results {
        save_validation_result(&result, &save_path)?;
    }

    if !args.skip_anvil {
        validator.stop_anvil_environments().await?;
    }

    match result.status {
        crate::validation::ValidationStatus::Passed => {
            info!("✅ Cross-chain arbitrage profitability validation PASSED");
            Ok(())
        }
        crate::validation::ValidationStatus::Warning => {
            warn!("⚠️  Cross-chain arbitrage profitability validation completed with WARNINGS");
            Ok(())
        }
        _ => {
            error!("❌ Cross-chain arbitrage profitability validation FAILED");
            Err(BasiliskError::execution_error("Validation failed"))
        }
    }
}

/// Run bridge fee and slippage prediction validation
pub async fn run_bridge_prediction_validation(args: CrossChainValidationArgs) -> Result<(), BasiliskError> {
    info!("Starting bridge fee and slippage prediction validation");

    let config = load_or_create_config(args.clone()).await?;
    let mut validator = CrossChainValidator::new(config);

    if !args.skip_anvil {
        validator.start_anvil_environments().await?;
    }

    let result = validator.validate_bridge_fee_slippage_prediction().await?;
    
    display_validation_result(&result, &args.output)?;
    
    if let Some(save_path) = args.save_results {
        save_validation_result(&result, &save_path)?;
    }

    if !args.skip_anvil {
        validator.stop_anvil_environments().await?;
    }

    match result.status {
        crate::validation::ValidationStatus::Passed => {
            info!("✅ Bridge fee and slippage prediction validation PASSED");
            Ok(())
        }
        crate::validation::ValidationStatus::Warning => {
            warn!("⚠️  Bridge fee and slippage prediction validation completed with WARNINGS");
            Ok(())
        }
        _ => {
            error!("❌ Bridge fee and slippage prediction validation FAILED");
            Err(BasiliskError::execution_error("Validation failed"))
        }
    }
}

/// Run complete cross-chain validation suite
pub async fn run_complete_cross_chain_validation_suite(args: CrossChainValidationArgs) -> Result<(), BasiliskError> {
    info!("🚀 Starting complete cross-chain validation suite");

    let config = load_or_create_config(args.clone()).await?;
    let mut validator = CrossChainValidator::new(config);

    if !args.skip_anvil {
        info!("Starting Anvil environments for complete validation suite");
        validator.start_anvil_environments().await?;
    }

    let mut all_passed = true;
    let mut results = Vec::new();

    // Run all validation tests sequentially
    info!("Running validation: Hub and Spoke Architecture");
    match validator.validate_hub_spoke_architecture().await {
        Ok(result) => {
            let passed = matches!(result.status, crate::validation::ValidationStatus::Passed);
            if !passed { all_passed = false; }
            info!("✓ Hub and Spoke Architecture - {} ({}ms)", result.status, result.execution_time.as_millis());
            results.push(("Hub and Spoke Architecture", result));
        }
        Err(e) => {
            error!("✗ Hub and Spoke Architecture - ERROR: {}", e);
            all_passed = false;
        }
    }

    info!("Running validation: Base L2 Settlement Hub");
    match validator.validate_base_settlement_hub().await {
        Ok(result) => {
            let passed = matches!(result.status, crate::validation::ValidationStatus::Passed);
            if !passed { all_passed = false; }
            info!("✓ Base L2 Settlement Hub - {} ({}ms)", result.status, result.execution_time.as_millis());
            results.push(("Base L2 Settlement Hub", result));
        }
        Err(e) => {
            error!("✗ Base L2 Settlement Hub - ERROR: {}", e);
            all_passed = false;
        }
    }

    info!("Running validation: Degen Chain L3 Execution Venue");
    match validator.validate_degen_execution_venue().await {
        Ok(result) => {
            let passed = matches!(result.status, crate::validation::ValidationStatus::Passed);
            if !passed { all_passed = false; }
            info!("✓ Degen Chain L3 Execution Venue - {} ({}ms)", result.status, result.execution_time.as_millis());
            results.push(("Degen Chain L3 Execution Venue", result));
        }
        Err(e) => {
            error!("✗ Degen Chain L3 Execution Venue - ERROR: {}", e);
            all_passed = false;
        }
    }

    info!("Running validation: Stargate Bridge Integration");
    match validator.validate_stargate_bridge_integration().await {
        Ok(result) => {
            let passed = matches!(result.status, crate::validation::ValidationStatus::Passed);
            if !passed { all_passed = false; }
            info!("✓ Stargate Bridge Integration - {} ({}ms)", result.status, result.execution_time.as_millis());
            results.push(("Stargate Bridge Integration", result));
        }
        Err(e) => {
            error!("✗ Stargate Bridge Integration - ERROR: {}", e);
            all_passed = false;
        }
    }

    info!("Running validation: Cross-Chain Arbitrage Profitability");
    match validator.validate_cross_chain_arbitrage_profitability().await {
        Ok(result) => {
            let passed = matches!(result.status, crate::validation::ValidationStatus::Passed);
            if !passed { all_passed = false; }
            info!("✓ Cross-Chain Arbitrage Profitability - {} ({}ms)", result.status, result.execution_time.as_millis());
            results.push(("Cross-Chain Arbitrage Profitability", result));
        }
        Err(e) => {
            error!("✗ Cross-Chain Arbitrage Profitability - ERROR: {}", e);
            all_passed = false;
        }
    }

    info!("Running validation: Bridge Fee and Slippage Prediction");
    match validator.validate_bridge_fee_slippage_prediction().await {
        Ok(result) => {
            let passed = matches!(result.status, crate::validation::ValidationStatus::Passed);
            if !passed { all_passed = false; }
            info!("✓ Bridge Fee and Slippage Prediction - {} ({}ms)", result.status, result.execution_time.as_millis());
            results.push(("Bridge Fee and Slippage Prediction", result));
        }
        Err(e) => {
            error!("✗ Bridge Fee and Slippage Prediction - ERROR: {}", e);
            all_passed = false;
        }
    }

    // Display summary
    display_validation_suite_summary(&results, &args.output)?;

    if let Some(save_path) = args.save_results {
        save_validation_suite_results(&results, &save_path)?;
    }

    if !args.skip_anvil {
        validator.stop_anvil_environments().await?;
    }

    if all_passed {
        info!("🎉 Complete cross-chain validation suite PASSED");
        Ok(())
    } else {
        error!("❌ Complete cross-chain validation suite FAILED");
        Err(BasiliskError::execution_error("Validation suite failed"))
    }
}

// Helper functions

async fn load_or_create_config(args: CrossChainValidationArgs) -> Result<CrossChainValidationConfig, BasiliskError> {
    if let Some(config_path) = args.config {
        // Load from file
        let config_str = std::fs::read_to_string(&config_path)
            .map_err(|e| BasiliskError::execution_error(format!("Failed to read config file: {}", e)))?;
        
        let config: CrossChainValidationConfig = toml::from_str(&config_str)
            .map_err(|e| BasiliskError::execution_error(format!("Failed to parse config: {}", e)))?;
        
        Ok(config)
    } else {
        // Create default config with CLI overrides
        let mut config = CrossChainValidationConfig::default();
        
        if let Some(base_rpc) = args.base_rpc {
            config.base_config.fork_rpc_url = base_rpc;
        }
        
        if let Some(degen_rpc) = args.degen_rpc {
            config.degen_config.fork_rpc_url = degen_rpc;
        }
        
        config.base_config.anvil_port = args.base_port;
        config.degen_config.anvil_port = args.degen_port;
        
        Ok(config)
    }
}

fn display_validation_result(
    result: &crate::validation::ValidationResult<crate::validation::CrossChainValidationMetrics>,
    output_format: &str,
) -> Result<(), BasiliskError> {
    match output_format {
        "json" => {
            let json = serde_json::to_string_pretty(result)
                .map_err(|e| BasiliskError::execution_error(format!("JSON serialization failed: {}", e)))?;
        println!("{}", json);
        }
        "summary" => {
        println!("Test: {} - {} ({}ms)", 
                     result.test_name, result.status, result.execution_time.as_millis());
            if !result.errors.is_empty() {
            println!("Errors: {}", result.errors.len());
            }
            if !result.warnings.is_empty() {
            println!("Warnings: {}", result.warnings.len());
            }
        }
        "table" | _ => {
            display_table_result(result)?;
        }
    }
    Ok(())
}

fn display_table_result(
    result: &crate::validation::ValidationResult<crate::validation::CrossChainValidationMetrics>,
) -> Result<(), BasiliskError> {
    println!("\n╔══════════════════════════════════════════════════════════════╗");
    println!("║                    Cross-Chain Validation Result             ║");
    println!("╠══════════════════════════════════════════════════════════════╣");
    println!("║ Test: {:<54} ║", result.test_name);
    println!("║ Status: {:<51} ║", result.status);
    println!("║ Execution Time: {:<43} ms ║", result.execution_time.as_millis());
    println!("║ Timestamp: {:<48} ║", result.timestamp.format("%Y-%m-%d %H:%M:%S UTC"));
    
    if !result.errors.is_empty() {
    println!("╠══════════════════════════════════════════════════════════════╣");
    println!("║ Errors ({})                                                   ║", result.errors.len());
        for error in &result.errors {
        println!("║ • {:<57} ║", truncate_string(&error.message, 57));
        }
    }
    
    if !result.warnings.is_empty() {
    println!("╠══════════════════════════════════════════════════════════════╣");
    println!("║ Warnings ({})                                                 ║", result.warnings.len());
        for warning in &result.warnings {
        println!("║ • {:<57} ║", truncate_string(&warning.message, 57));
        }
    }
    
    println!("╚══════════════════════════════════════════════════════════════╝\n");
    Ok(())
}

fn display_validation_suite_summary(
    results: &[(& str, crate::validation::ValidationResult<crate::validation::CrossChainValidationMetrics>)],
    output_format: &str,
) -> Result<(), BasiliskError> {
    match output_format {
        "json" => {
            let json = serde_json::to_string_pretty(results)
                .map_err(|e| BasiliskError::execution_error(format!("JSON serialization failed: {}", e)))?;
        println!("{}", json);
        }
        _ => {
        println!("\n╔══════════════════════════════════════════════════════════════╗");
        println!("║              Cross-Chain Validation Suite Summary            ║");
        println!("╠══════════════════════════════════════════════════════════════╣");
            
            let mut passed = 0;
            let mut warnings = 0;
            let mut failed = 0;
            let mut total_time = std::time::Duration::from_millis(0);
            
            for (test_name, result) in results {
                let status_symbol = match result.status {
                    crate::validation::ValidationStatus::Passed => { passed += 1; "✅" }
                    crate::validation::ValidationStatus::Warning => { warnings += 1; "⚠️ " }
                    crate::validation::ValidationStatus::Failed => { failed += 1; "❌" }
                    _ => "❓"
                };
                
                total_time += result.execution_time;
                
            println!("║ {} {:<50} ({:>4}ms) ║", 
                         status_symbol, 
                         truncate_string(test_name, 50),
                         result.execution_time.as_millis());
            }
            
        println!("╠══════════════════════════════════════════════════════════════╣");
        println!("║ Summary: {} passed, {} warnings, {} failed                    ║", passed, warnings, failed);
        println!("║ Total execution time: {:<38} ms ║", total_time.as_millis());
        println!("╚══════════════════════════════════════════════════════════════╝\n");
        }
    }
    Ok(())
}

fn save_validation_result(
    result: &crate::validation::ValidationResult<crate::validation::CrossChainValidationMetrics>,
    save_path: &PathBuf,
) -> Result<(), BasiliskError> {
    let json = serde_json::to_string_pretty(result)
        .map_err(|e| BasiliskError::execution_error(format!("JSON serialization failed: {}", e)))?;
    
    std::fs::write(save_path, json)
        .map_err(|e| BasiliskError::execution_error(format!("Failed to save results: {}", e)))?;
    
    info!("Results saved to: {}", save_path.display());
    Ok(())
}

fn save_validation_suite_results(
    results: &[(& str, crate::validation::ValidationResult<crate::validation::CrossChainValidationMetrics>)],
    save_path: &PathBuf,
) -> Result<(), BasiliskError> {
    let json = serde_json::to_string_pretty(results)
        .map_err(|e| BasiliskError::execution_error(format!("JSON serialization failed: {}", e)))?;
    
    std::fs::write(save_path, json)
        .map_err(|e| BasiliskError::execution_error(format!("Failed to save results: {}", e)))?;
    
    info!("Suite results saved to: {}", save_path.display());
    Ok(())
}

fn truncate_string(s: &str, max_len: usize) -> String {
    if s.len() <= max_len {
        s.to_string()
    } else {
        format!("{}...", &s[..max_len.saturating_sub(3)])
    }
}