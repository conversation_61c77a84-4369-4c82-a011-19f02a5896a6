// Contract interaction testing utilities for StargateCompassV1
// Provides direct contract method calls, state verification, and transaction simulation

use super::core::*;
use crate::integration::stargate_compass::anvil_client::AnvilClient;
use ethers::{
    providers::{Provider, Http, Middleware},
    types::{Address, H256, U256, Bytes, TransactionRequest, TransactionReceipt, Log},
    contract::{Contract, ContractFactory},
    abi::{Abi, Token, Function, Param, ParamType, StateMutability},
    utils::keccak256,
};
use std::sync::Arc;
use anyhow::{Result, anyhow};
use serde_json;
use std::time::{Duration, Instant};
use tracing::{info, warn, error, debug};

/// Contract interaction testing utilities
pub struct ContractInteractionTester {
    pub anvil_client: Arc<AnvilClient>,
    pub contract_address: Address,
    pub contract_abi: Abi,
    pub provider: Arc<Provider<Http>>,
}

impl ContractInteractionTester {
    /// Create new contract interaction tester
    pub async fn new(anvil_client: Arc<AnvilClient>, contract_address: Address) -> Result<Self> {
        let contract_abi = Self::load_stargate_compass_abi()?;
        let provider = anvil_client.provider.clone();
        
        Ok(Self {
            anvil_client,
            contract_address,
            contract_abi,
            provider,
        })
    }
    
    /// Load StargateCompassV1 ABI from the abi directory
    fn load_stargate_compass_abi() -> Result<Abi> {
        let abi_path = "abi/StargateCompassV1.json";
        let abi_content = std::fs::read_to_string(abi_path)
            .map_err(|e| anyhow!("Failed to read ABI file {}: {}", abi_path, e))?;
        
        let abi_json: serde_json::Value = serde_json::from_str(&abi_content)
            .map_err(|e| anyhow!("Failed to parse ABI JSON: {}", e))?;
        
        let abi_array = abi_json["abi"].as_array()
            .ok_or_else(|| anyhow!("ABI field not found or not an array"))?;
        
        let abi: Abi = serde_json::from_value(serde_json::Value::Array(abi_array.clone()))
            .map_err(|e| anyhow!("Failed to deserialize ABI: {}", e))?;
        
        Ok(abi)
    }
    
    /// Call owner() view function
    pub async fn call_owner(&self) -> Result<ContractCallResult> {
        let start_time = Instant::now();
        info!("Calling owner() function on StargateCompassV1 contract");
        
        let function = self.contract_abi.function("owner")
            .map_err(|e| anyhow!("owner function not found in ABI: {}", e))?;
        
        let call_data = function.encode_input(&[])?;
        
        let tx = TransactionRequest::new()
            .to(self.contract_address)
            .data(call_data);
        
        match self.provider.call(&tx.into(), None).await {
            Ok(result) => {
                let decoded = function.decode_output(&result)?;
                let owner_address = decoded[0].clone().into_address()
                    .ok_or_else(|| anyhow!("Failed to decode owner address"))?;
                
                info!("✅ owner() call successful: {}", owner_address);
                
                Ok(ContractCallResult {
                    function_name: "owner".to_string(),
                    success: true,
                    return_values: vec![format!("{:?}", owner_address)],
                    gas_estimate: None,
                    execution_time: start_time.elapsed(),
                    error_message: None,
                    raw_output: Some(result),
                })
            }
            Err(e) => {
                error!("❌ owner() call failed: {}", e);
                Ok(ContractCallResult {
                    function_name: "owner".to_string(),
                    success: false,
                    return_values: Vec::new(),
                    gas_estimate: None,
                    execution_time: start_time.elapsed(),
                    error_message: Some(e.to_string()),
                    raw_output: None,
                })
            }
        }
    }
    
    /// Call AAVE_PROVIDER() view function
    pub async fn call_aave_provider(&self) -> Result<ContractCallResult> {
        let start_time = Instant::now();
        info!("Calling AAVE_PROVIDER() function on StargateCompassV1 contract");
        
        let function = self.contract_abi.function("AAVE_PROVIDER")
            .map_err(|e| anyhow!("AAVE_PROVIDER function not found in ABI: {}", e))?;
        
        let call_data = function.encode_input(&[])?;
        
        let tx = TransactionRequest::new()
            .to(self.contract_address)
            .data(call_data);
        
        match self.provider.call(&tx.into(), None).await {
            Ok(result) => {
                let decoded = function.decode_output(&result)?;
                let provider_address = decoded[0].clone().into_address()
                    .ok_or_else(|| anyhow!("Failed to decode AAVE provider address"))?;
                
                info!("✅ AAVE_PROVIDER() call successful: {}", provider_address);
                
                Ok(ContractCallResult {
                    function_name: "AAVE_PROVIDER".to_string(),
                    success: true,
                    return_values: vec![format!("{:?}", provider_address)],
                    gas_estimate: None,
                    execution_time: start_time.elapsed(),
                    error_message: None,
                    raw_output: Some(result),
                })
            }
            Err(e) => {
                error!("❌ AAVE_PROVIDER() call failed: {}", e);
                Ok(ContractCallResult {
                    function_name: "AAVE_PROVIDER".to_string(),
                    success: false,
                    return_values: Vec::new(),
                    gas_estimate: None,
                    execution_time: start_time.elapsed(),
                    error_message: Some(e.to_string()),
                    raw_output: None,
                })
            }
        }
    }
    
    /// Call STARGATE_ROUTER() view function
    pub async fn call_stargate_router(&self) -> Result<ContractCallResult> {
        let start_time = Instant::now();
        info!("Calling STARGATE_ROUTER() function on StargateCompassV1 contract");
        
        let function = self.contract_abi.function("STARGATE_ROUTER")
            .map_err(|e| anyhow!("STARGATE_ROUTER function not found in ABI: {}", e))?;
        
        let call_data = function.encode_input(&[])?;
        
        let tx = TransactionRequest::new()
            .to(self.contract_address)
            .data(call_data);
        
        match self.provider.call(&tx.into(), None).await {
            Ok(result) => {
                let decoded = function.decode_output(&result)?;
                let router_address = decoded[0].clone().into_address()
                    .ok_or_else(|| anyhow!("Failed to decode Stargate router address"))?;
                
                info!("✅ STARGATE_ROUTER() call successful: {}", router_address);
                
                Ok(ContractCallResult {
                    function_name: "STARGATE_ROUTER".to_string(),
                    success: true,
                    return_values: vec![format!("{:?}", router_address)],
                    gas_estimate: None,
                    execution_time: start_time.elapsed(),
                    error_message: None,
                    raw_output: Some(result),
                })
            }
            Err(e) => {
                error!("❌ STARGATE_ROUTER() call failed: {}", e);
                Ok(ContractCallResult {
                    function_name: "STARGATE_ROUTER".to_string(),
                    success: false,
                    return_values: Vec::new(),
                    gas_estimate: None,
                    execution_time: start_time.elapsed(),
                    error_message: Some(e.to_string()),
                    raw_output: None,
                })
            }
        }
    }
    
    /// Simulate executeRemoteDegenSwap transaction
    pub async fn simulate_execute_remote_degen_swap(
        &self,
        loan_amount: U256,
        remote_calldata: Vec<u8>,
        remote_swap_router: Address,
    ) -> Result<TransactionSimulationResult> {
        let start_time = Instant::now();
        info!("Simulating executeRemoteDegenSwap transaction");
        
        let function = self.contract_abi.function("executeRemoteDegenSwap")
            .map_err(|e| anyhow!("executeRemoteDegenSwap function not found in ABI: {}", e))?;
        
        let params = vec![
            Token::Uint(loan_amount),
            Token::Bytes(remote_calldata.clone()),
            Token::Address(remote_swap_router),
        ];
        
        let call_data = function.encode_input(&params)?;
        
        let tx = TransactionRequest::new()
            .to(self.contract_address)
            .data(call_data)
            .gas(500000u64); // Reasonable gas limit for simulation
        
        // First, estimate gas
        let gas_estimate = match self.provider.estimate_gas(&tx.clone().into(), None).await {
            Ok(gas) => Some(gas),
            Err(e) => {
                warn!("Gas estimation failed: {}", e);
                None
            }
        };
        
        // Try to simulate the call
        match self.provider.call(&tx.into(), None).await {
            Ok(result) => {
                info!("✅ executeRemoteDegenSwap simulation successful");
                
                Ok(TransactionSimulationResult {
                    function_name: "executeRemoteDegenSwap".to_string(),
                    success: true,
                    would_revert: false,
                    gas_estimate,
                    return_data: Some(result),
                    revert_reason: None,
                    execution_time: start_time.elapsed(),
                    state_changes_detected: true, // This function modifies state
                })
            }
            Err(e) => {
                let error_msg = e.to_string();
                let would_revert = error_msg.contains("revert") || error_msg.contains("execution reverted");
                
                warn!("executeRemoteDegenSwap simulation failed: {}", error_msg);
                
                Ok(TransactionSimulationResult {
                    function_name: "executeRemoteDegenSwap".to_string(),
                    success: false,
                    would_revert,
                    gas_estimate,
                    return_data: None,
                    revert_reason: Some(error_msg),
                    execution_time: start_time.elapsed(),
                    state_changes_detected: false,
                })
            }
        }
    }
    
    /// Execute withdraw transaction (owner only)
    pub async fn execute_withdraw(&self, token: Address) -> Result<TransactionExecutionResult> {
        let start_time = Instant::now();
        info!("Executing withdraw transaction for token: {}", token);
        
        let function = self.contract_abi.function("withdraw")
            .map_err(|e| anyhow!("withdraw function not found in ABI: {}", e))?;
        
        let params = vec![Token::Address(token)];
        let call_data = function.encode_input(&params)?;
        
        let tx = TransactionRequest::new()
            .to(self.contract_address)
            .data(call_data)
            .gas(100000u64)
            .from(self.anvil_client.wallet.address());
        
        match self.anvil_client.execute_transaction(tx).await {
            Ok(validation_result) => {
                info!("✅ withdraw transaction executed successfully");
                
                Ok(TransactionExecutionResult {
                    function_name: "withdraw".to_string(),
                    transaction_hash: Some(validation_result.transaction_hash),
                    success: validation_result.success,
                    gas_used: Some(validation_result.gas_used),
                    gas_price: Some(validation_result.gas_price),
                    events_emitted: validation_result.events_emitted,
                    execution_time: start_time.elapsed(),
                    error_message: if validation_result.success { None } else { Some("Transaction reverted".to_string()) },
                })
            }
            Err(e) => {
                error!("❌ withdraw transaction failed: {}", e);
                
                Ok(TransactionExecutionResult {
                    function_name: "withdraw".to_string(),
                    transaction_hash: None,
                    success: false,
                    gas_used: None,
                    gas_price: None,
                    events_emitted: Vec::new(),
                    execution_time: start_time.elapsed(),
                    error_message: Some(e.to_string()),
                })
            }
        }
    }
    
    /// Verify contract state consistency
    pub async fn verify_contract_state(&self) -> Result<ContractStateVerificationResult> {
        let start_time = Instant::now();
        info!("Verifying StargateCompassV1 contract state");
        
        let mut verification_result = ContractStateVerificationResult {
            overall_valid: true,
            checks_performed: Vec::new(),
            inconsistencies_found: Vec::new(),
            verification_time: Duration::default(),
        };
        
        // Check 1: Verify owner is set
        match self.call_owner().await {
            Ok(owner_result) => {
                if owner_result.success && !owner_result.return_values.is_empty() {
                    verification_result.checks_performed.push("Owner address verification".to_string());
                    info!("✅ Owner verification passed");
                } else {
                    verification_result.overall_valid = false;
                    verification_result.inconsistencies_found.push("Owner address not properly set".to_string());
                    warn!("❌ Owner verification failed");
                }
            }
            Err(e) => {
                verification_result.overall_valid = false;
                verification_result.inconsistencies_found.push(format!("Owner check failed: {}", e));
            }
        }
        
        // Check 2: Verify AAVE provider is set
        match self.call_aave_provider().await {
            Ok(provider_result) => {
                if provider_result.success && !provider_result.return_values.is_empty() {
                    verification_result.checks_performed.push("AAVE provider verification".to_string());
                    info!("✅ AAVE provider verification passed");
                } else {
                    verification_result.overall_valid = false;
                    verification_result.inconsistencies_found.push("AAVE provider not properly set".to_string());
                    warn!("❌ AAVE provider verification failed");
                }
            }
            Err(e) => {
                verification_result.overall_valid = false;
                verification_result.inconsistencies_found.push(format!("AAVE provider check failed: {}", e));
            }
        }
        
        // Check 3: Verify Stargate router is set
        match self.call_stargate_router().await {
            Ok(router_result) => {
                if router_result.success && !router_result.return_values.is_empty() {
                    verification_result.checks_performed.push("Stargate router verification".to_string());
                    info!("✅ Stargate router verification passed");
                } else {
                    verification_result.overall_valid = false;
                    verification_result.inconsistencies_found.push("Stargate router not properly set".to_string());
                    warn!("❌ Stargate router verification failed");
                }
            }
            Err(e) => {
                verification_result.overall_valid = false;
                verification_result.inconsistencies_found.push(format!("Stargate router check failed: {}", e));
            }
        }
        
        // Check 4: Verify contract has code
        let code = self.provider.get_code(self.contract_address, None).await?;
        if !code.is_empty() {
            verification_result.checks_performed.push("Contract bytecode verification".to_string());
            info!("✅ Contract bytecode verification passed");
        } else {
            verification_result.overall_valid = false;
            verification_result.inconsistencies_found.push("No bytecode found at contract address".to_string());
            error!("❌ Contract bytecode verification failed");
        }
        
        verification_result.verification_time = start_time.elapsed();
        
        if verification_result.overall_valid {
            info!("✅ Contract state verification completed successfully");
        } else {
            warn!("❌ Contract state verification found {} inconsistencies", 
                  verification_result.inconsistencies_found.len());
        }
        
        Ok(verification_result)
    }
    
    /// Run comprehensive contract interaction test suite
    pub async fn run_comprehensive_test_suite(&self) -> Result<ContractInteractionTestSuite> {
        let start_time = Instant::now();
        info!("Running comprehensive contract interaction test suite");
        
        let mut test_suite = ContractInteractionTestSuite {
            view_function_tests: Vec::new(),
            transaction_simulations: Vec::new(),
            state_verification: None,
            overall_success: true,
            total_tests: 0,
            passed_tests: 0,
            failed_tests: 0,
            execution_time: Duration::default(),
        };
        
        // Test 1: View function calls
        info!("Testing view functions...");
        
        let owner_test = self.call_owner().await?;
        test_suite.view_function_tests.push(owner_test.clone());
        test_suite.total_tests += 1;
        if owner_test.success { test_suite.passed_tests += 1; } else { test_suite.failed_tests += 1; }
        
        let aave_test = self.call_aave_provider().await?;
        test_suite.view_function_tests.push(aave_test.clone());
        test_suite.total_tests += 1;
        if aave_test.success { test_suite.passed_tests += 1; } else { test_suite.failed_tests += 1; }
        
        let router_test = self.call_stargate_router().await?;
        test_suite.view_function_tests.push(router_test.clone());
        test_suite.total_tests += 1;
        if router_test.success { test_suite.passed_tests += 1; } else { test_suite.failed_tests += 1; }
        
        // Test 2: Transaction simulations
        info!("Testing transaction simulations...");
        
        let simulation_test = self.simulate_execute_remote_degen_swap(
            U256::from(1000000), // 1 USDC (6 decimals)
            vec![0x12, 0x34, 0x56, 0x78], // Mock calldata
            Address::zero(), // Mock router address
        ).await?;
        test_suite.transaction_simulations.push(simulation_test.clone());
        test_suite.total_tests += 1;
        if simulation_test.success { test_suite.passed_tests += 1; } else { test_suite.failed_tests += 1; }
        
        // Test 3: State verification
        info!("Testing contract state verification...");
        
        let state_verification = self.verify_contract_state().await?;
        test_suite.state_verification = Some(state_verification.clone());
        test_suite.total_tests += 1;
        if state_verification.overall_valid { test_suite.passed_tests += 1; } else { test_suite.failed_tests += 1; }
        
        // Calculate overall results
        test_suite.overall_success = test_suite.failed_tests == 0;
        test_suite.execution_time = start_time.elapsed();
        
        info!("Contract interaction test suite completed: {}/{} tests passed", 
              test_suite.passed_tests, test_suite.total_tests);
        
        Ok(test_suite)
    }
    
    /// Log comprehensive contract interaction details
    pub fn log_interaction_details(&self, result: &ContractCallResult) {
        info!("=== Contract Interaction Details ===");
        info!("Function: {}", result.function_name);
        info!("Success: {}", result.success);
        info!("Execution Time: {:?}", result.execution_time);
        
        if result.success {
            info!("Return Values: {:?}", result.return_values);
            if let Some(gas) = result.gas_estimate {
                info!("Gas Estimate: {}", gas);
            }
        } else {
            if let Some(error) = &result.error_message {
                error!("Error: {}", error);
            }
        }
        
        info!("=====================================");
    }
}

// ============= DATA STRUCTURES =============

/// Result of a contract function call
#[derive(Debug, Clone)]
pub struct ContractCallResult {
    pub function_name: String,
    pub success: bool,
    pub return_values: Vec<String>,
    pub gas_estimate: Option<U256>,
    pub execution_time: Duration,
    pub error_message: Option<String>,
    pub raw_output: Option<Bytes>,
}

/// Result of a transaction simulation
#[derive(Debug, Clone)]
pub struct TransactionSimulationResult {
    pub function_name: String,
    pub success: bool,
    pub would_revert: bool,
    pub gas_estimate: Option<U256>,
    pub return_data: Option<Bytes>,
    pub revert_reason: Option<String>,
    pub execution_time: Duration,
    pub state_changes_detected: bool,
}

/// Result of a transaction execution
#[derive(Debug, Clone)]
pub struct TransactionExecutionResult {
    pub function_name: String,
    pub transaction_hash: Option<H256>,
    pub success: bool,
    pub gas_used: Option<U256>,
    pub gas_price: Option<U256>,
    pub events_emitted: Vec<String>,
    pub execution_time: Duration,
    pub error_message: Option<String>,
}

/// Contract state verification result
#[derive(Debug, Clone)]
pub struct ContractStateVerificationResult {
    pub overall_valid: bool,
    pub checks_performed: Vec<String>,
    pub inconsistencies_found: Vec<String>,
    pub verification_time: Duration,
}

/// Comprehensive contract interaction test suite
#[derive(Debug, Clone)]
pub struct ContractInteractionTestSuite {
    pub view_function_tests: Vec<ContractCallResult>,
    pub transaction_simulations: Vec<TransactionSimulationResult>,
    pub state_verification: Option<ContractStateVerificationResult>,
    pub overall_success: bool,
    pub total_tests: u32,
    pub passed_tests: u32,
    pub failed_tests: u32,
    pub execution_time: Duration,
}

impl ContractInteractionTestSuite {
    /// Get success rate as percentage
    pub fn success_rate(&self) -> f64 {
        if self.total_tests > 0 {
            (self.passed_tests as f64 / self.total_tests as f64) * 100.0
        } else {
            0.0
        }
    }
    
    /// Generate summary report
    pub fn generate_summary(&self) -> String {
        format!(
            "Contract Interaction Test Suite Summary:\n\
             - Total Tests: {}\n\
             - Passed: {}\n\
             - Failed: {}\n\
             - Success Rate: {:.1}%\n\
             - Execution Time: {:?}\n\
             - Overall Success: {}",
            self.total_tests,
            self.passed_tests,
            self.failed_tests,
            self.success_rate(),
            self.execution_time,
            self.overall_success
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ethers::types::Address;
    use std::str::FromStr;

    #[tokio::test]
    async fn test_abi_loading() {
        let abi_result = ContractInteractionTester::load_stargate_compass_abi();
        assert!(abi_result.is_ok(), "Should be able to load StargateCompassV1 ABI");
        
        if let Ok(abi) = abi_result {
            assert!(abi.function("owner").is_ok(), "ABI should contain owner function");
            assert!(abi.function("AAVE_PROVIDER").is_ok(), "ABI should contain AAVE_PROVIDER function");
            assert!(abi.function("STARGATE_ROUTER").is_ok(), "ABI should contain STARGATE_ROUTER function");
            assert!(abi.function("executeRemoteDegenSwap").is_ok(), "ABI should contain executeRemoteDegenSwap function");
            assert!(abi.function("withdraw").is_ok(), "ABI should contain withdraw function");
        }
    }

    #[test]
    fn test_contract_call_result() {
        let result = ContractCallResult {
            function_name: "test".to_string(),
            success: true,
            return_values: vec!["0x123".to_string()],
            gas_estimate: Some(U256::from(21000)),
            execution_time: Duration::from_millis(100),
            error_message: None,
            raw_output: None,
        };
        
        assert_eq!(result.function_name, "test");
        assert!(result.success);
        assert_eq!(result.return_values.len(), 1);
    }

    #[test]
    fn test_test_suite_success_rate() {
        let mut suite = ContractInteractionTestSuite {
            view_function_tests: Vec::new(),
            transaction_simulations: Vec::new(),
            state_verification: None,
            overall_success: true,
            total_tests: 10,
            passed_tests: 8,
            failed_tests: 2,
            execution_time: Duration::from_secs(1),
        };
        
        assert_eq!(suite.success_rate(), 80.0);
        
        let summary = suite.generate_summary();
        assert!(summary.contains("Total Tests: 10"));
        assert!(summary.contains("Success Rate: 80.0%"));
    }
}