// src/validation/test_data_examples.rs

//! Examples demonstrating the test data provider functionality

use crate::validation::{
    TestDataProvider, TestDataConfig, OpportunityType, MarketConditions,
    NetworkCongestionLevel, LiquidityDistribution
};
use crate::shared_types::MarketRegime;
use chrono::{Utc, Duration as ChronoDuration};
use rust_decimal_macros::dec;
use tracing::{info, error};

/// Example: Basic test data provider usage
pub async fn basic_test_data_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Basic Test Data Provider Example ===\n");
    
    // Create test data provider with default configuration
    let provider = TestDataProvider::new()?;
    
    // Generate a bull market scenario
    let scenario = provider.generate_scenario("bull_market").await?;
    
    println!("Generated Scenario: {}", scenario.name);
    println!("Description: {}", scenario.description);
    println!("Market Regime: {:?}", scenario.market_conditions.regime);
    println!("Volatility: {:.2}%", scenario.market_conditions.volatility * dec!(100));
    println!("Gas Price: {} gwei", scenario.market_conditions.gas_price_gwei);
    println!("Opportunities: {}", scenario.opportunities.len());
    
    // Show opportunity details
    for (i, opportunity) in scenario.opportunities.iter().enumerate() {
        println!("  {}. {} - ${:.2} profit ({})", 
                 i + 1, 
                 opportunity.name, 
                 opportunity.base_profit_usd,
                 if opportunity.requires_flash_loan { "Flash Loan" } else { "Direct" });
    }
    
    println!("Expected Success Rate: {:.1}%", scenario.expected_outcomes.expected_success_rate * 100.0);
    println!("Expected Total Profit: ${:.2}", scenario.expected_outcomes.expected_total_profit_usd);
    
    Ok(())
}

/// Example: Generate comprehensive scenario suite
pub async fn comprehensive_scenario_suite_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n=== Comprehensive Scenario Suite Example ===\n");
    
    let provider = TestDataProvider::new()?;
    let scenarios = provider.generate_scenario_suite().await?;
    
    println!("Generated {} scenarios for comprehensive testing:\n", scenarios.len());
    
    for scenario in scenarios {
        println!("📊 {}", scenario.name);
        println!("   Description: {}", scenario.description);
        println!("   Market Regime: {:?}", scenario.market_conditions.regime);
        println!("   Volatility: {:.2}%", scenario.market_conditions.volatility * dec!(100));
        println!("   Network Congestion: {:?}", scenario.market_conditions.network_congestion);
        println!("   Opportunities: {}", scenario.opportunities.len());
        println!("   Expected Success Rate: {:.1}%", scenario.expected_outcomes.expected_success_rate * 100.0);
        println!("   Expected Profit: ${:.2}", scenario.expected_outcomes.expected_total_profit_usd);
        println!();
    }
    
    Ok(())
}

/// Example: Generate opportunities for specific strategy types
pub async fn strategy_specific_opportunities_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Strategy-Specific Opportunities Example ===\n");
    
    let provider = TestDataProvider::new()?;
    
    // Define market conditions
    let market_conditions = MarketConditions {
        regime: MarketRegime::CalmOrderly,
        volatility: dec!(0.025),
        gas_price_gwei: dec!(25.0),
        network_congestion: NetworkCongestionLevel::Moderate,
        temporal_harmonics: None,
        network_resonance: None,
        liquidity_distribution: LiquidityDistribution::Concentrated,
    };
    
    let strategy_types = vec![
        (OpportunityType::DexArbitrage, "DEX Arbitrage"),
        (OpportunityType::CrossChainArbitrage, "Cross-Chain Arbitrage"),
        (OpportunityType::PilotFish, "Pilot Fish (Whale Following)"),
        (OpportunityType::BasiliskGaze, "Basilisk Gaze (Geometric Analysis)"),
        (OpportunityType::LiquidationOpportunity, "Liquidation"),
    ];
    
    for (strategy_type, strategy_name) in strategy_types {
        println!("🎯 {} Opportunities:", strategy_name);
        
        let opportunities = provider
            .generate_opportunities_for_strategy(strategy_type, 3, &market_conditions)
            .await?;
        
        for (i, opportunity) in opportunities.iter().enumerate() {
            println!("   {}. {}", i + 1, opportunity.name);
            println!("      Profit: ${:.2}", opportunity.base_profit_usd);
            println!("      Complexity: {:?}", opportunity.execution_complexity);
            println!("      Flash Loan Required: {}", opportunity.requires_flash_loan);
            
            if let Some(ref geometric) = opportunity.geometric_properties {
                println!("      Geometric Properties:");
                println!("        - Convexity Ratio: {:.2}", geometric.convexity_ratio);
                println!("        - Liquidity Centroid Bias: {:.2}", geometric.liquidity_centroid_bias);
                println!("        - Vesica Piscis Depth: {:.2}", geometric.vesica_piscis_depth);
            }
            println!();
        }
    }
    
    Ok(())
}

/// Example: Data validation demonstration
pub async fn data_validation_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Data Validation Example ===\n");
    
    let provider = TestDataProvider::new()?;
    
    // Test validation on different scenario types
    let scenario_types = vec!["bull_market", "bear_market", "volatile_market", "stable_market"];
    
    for scenario_type in scenario_types {
        println!("🔍 Validating {} scenario:", scenario_type);
        
        let scenario = provider.generate_scenario(scenario_type).await?;
        let validation_report = provider.validate_test_data(&scenario).await?;
        
        if validation_report.is_valid() {
            println!("   ✅ Validation PASSED");
        } else {
            println!("   ❌ Validation FAILED ({} errors)", validation_report.errors.len());
            for error in &validation_report.errors {
                println!("      - {}", error);
            }
        }
        
        if !validation_report.warnings.is_empty() {
            println!("   ⚠️  {} warnings:", validation_report.warnings.len());
            for warning in &validation_report.warnings {
                println!("      - {}", warning);
            }
        }
        
        println!();
    }
    
    Ok(())
}

/// Example: Historical data generation
pub async fn historical_data_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Historical Data Generation Example ===\n");
    
    let mut config = TestDataConfig::default();
    config.enable_historical_data = true;
    
    let provider = TestDataProvider::with_config(config)?;
    
    // Generate historical data for the past week
    let end_date = Utc::now();
    let start_date = end_date - ChronoDuration::days(7);
    
    println!("Generating historical data from {} to {}", 
             start_date.format("%Y-%m-%d"), 
             end_date.format("%Y-%m-%d"));
    
    let historical_data = provider
        .generate_historical_regression_data(start_date, end_date)
        .await?;
    
    println!("Generated {} historical data points:\n", historical_data.len());
    
    for (i, data_point) in historical_data.iter().enumerate() {
        println!("📅 Day {}: {}", i + 1, data_point.date.format("%Y-%m-%d"));
        println!("   Market Regime: {:?}", data_point.market_conditions.regime);
        println!("   Opportunities Executed: {}", data_point.opportunities_executed);
        println!("   Success Rate: {:.1}%", data_point.success_rate * 100.0);
        println!("   Total Profit: ${:.2}", data_point.total_profit_usd);
        println!("   Avg Execution Time: {}ms", data_point.average_execution_time_ms);
        
        if !data_point.notable_events.is_empty() {
            println!("   Notable Events:");
            for event in &data_point.notable_events {
                println!("     - {}", event);
            }
        }
        println!();
    }
    
    Ok(())
}

/// Example: Custom configuration demonstration
pub async fn custom_configuration_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Custom Configuration Example ===\n");
    
    // Create custom configuration
    let mut config = TestDataConfig::default();
    config.random_seed = Some(12345); // For reproducible results
    config.default_volatility = dec!(0.05); // Higher default volatility
    config.default_gas_price_gwei = dec!(50.0); // Higher gas prices
    config.max_opportunities_per_scenario = 10; // More opportunities
    
    println!("Custom Configuration:");
    println!("  - Random Seed: {:?}", config.random_seed);
    println!("  - Default Volatility: {:.2}%", config.default_volatility * dec!(100));
    println!("  - Default Gas Price: {} gwei", config.default_gas_price_gwei);
    println!("  - Max Opportunities: {}", config.max_opportunities_per_scenario);
    println!();
    
    let provider = TestDataProvider::with_config(config)?;
    
    // Generate a scenario with custom configuration
    let scenario = provider.generate_scenario("volatile_market").await?;
    
    println!("Generated Scenario with Custom Config:");
    println!("  - Name: {}", scenario.name);
    println!("  - Volatility: {:.2}%", scenario.market_conditions.volatility * dec!(100));
    println!("  - Gas Price: {} gwei", scenario.market_conditions.gas_price_gwei);
    println!("  - Opportunities: {}", scenario.opportunities.len());
    
    // Show how opportunities are affected by configuration
    let total_profit: rust_decimal::Decimal = scenario.opportunities
        .iter()
        .map(|op| op.base_profit_usd)
        .sum();
    
    println!("  - Total Opportunity Value: ${:.2}", total_profit);
    println!("  - Average Opportunity Value: ${:.2}", 
             total_profit / rust_decimal::Decimal::from(scenario.opportunities.len()));
    
    Ok(())
}

/// Run all test data provider examples
pub async fn run_all_examples() -> Result<(), Box<dyn std::error::Error>> {
    info!("Running all test data provider examples");
    
    // Run examples in sequence
    basic_test_data_example().await?;
    comprehensive_scenario_suite_example().await?;
    strategy_specific_opportunities_example().await?;
    data_validation_example().await?;
    historical_data_example().await?;
    custom_configuration_example().await?;
    
    println!("🎉 All test data provider examples completed successfully!");
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_basic_example() {
        let result = basic_test_data_example().await;
        assert!(result.is_ok(), "Basic example should run without errors");
    }

    #[tokio::test]
    async fn test_scenario_suite_example() {
        let result = comprehensive_scenario_suite_example().await;
        assert!(result.is_ok(), "Scenario suite example should run without errors");
    }

    #[tokio::test]
    async fn test_strategy_opportunities_example() {
        let result = strategy_specific_opportunities_example().await;
        assert!(result.is_ok(), "Strategy opportunities example should run without errors");
    }

    #[tokio::test]
    async fn test_data_validation_example() {
        let result = data_validation_example().await;
        assert!(result.is_ok(), "Data validation example should run without errors");
    }

    #[tokio::test]
    async fn test_historical_data_example() {
        let result = historical_data_example().await;
        assert!(result.is_ok(), "Historical data example should run without errors");
    }

    #[tokio::test]
    async fn test_custom_config_example() {
        let result = custom_configuration_example().await;
        assert!(result.is_ok(), "Custom configuration example should run without errors");
    }
}