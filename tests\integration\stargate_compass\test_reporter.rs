// MISSION: Test Reporter for Stargate Compass Integration Testing
// WHY: Aggregate, analyze, and report comprehensive test results with actionable insights
// HOW: Collect results from all test components, categorize failures, and generate detailed reports

use anyhow::{Result, Context};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tracing::{info, warn, error, debug};

use super::{
    TuiTestSuite,
    TuiStressTestResult,
    TuiCommandValidationResult,
    TransactionTestSuite,
    TransactionTestStatistics,
    EndToEndWorkflowResult,
    WorkflowSynthesisResult,
    TuiCommandResult,
    TransactionCommandTestResult,
};

#[path = "tui_tester.rs"]
mod tui_tester_module;
#[path = "workflow_result_synthesis.rs"]
mod workflow_result_synthesis_module;
#[path = "transaction_tester.rs"]
mod transaction_tester_module;

// Removed explicit imports to avoid conflicts with super::*
// use tui_tester_module::{TuiTestSuite, TuiStressTestResult, TuiCommandValidationResult, TuiCommandResult};
// use workflow_result_synthesis_module::{EndToEndWorkflowResult, WorkflowSynthesisResult};
// use transaction_tester_module::{TransactionTestSuite, TransactionTestStatistics, TransactionCommandTestResult};


/// Comprehensive test result aggregator and analyzer
#[derive(Debug)]
pub struct TestResultAggregator {
    pub test_session_id: String,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub tui_test_results: Vec<TuiTestSuite>,
    pub transaction_test_results: Vec<TransactionTestSuite>,
    pub workflow_test_results: Vec<EndToEndWorkflowResult>,
    pub synthesis_results: Vec<WorkflowSynthesisResult>,
    pub stress_test_results: Vec<TuiStressTestResult>,
    pub validation_results: Vec<TuiCommandValidationResult>,
    pub configuration: TestReportConfiguration,
}

impl TestResultAggregator {
    /// Create new test result aggregator
    pub fn new(config: TestReportConfiguration) -> Self {
        let session_id = format!("test_session_{}", Utc::now().timestamp());
        
        Self {
            test_session_id: session_id,
            start_time: Utc::now(),
            end_time: None,
            tui_test_results: Vec::new(),
            transaction_test_results: Vec::new(),
            workflow_test_results: Vec::new(),
            synthesis_results: Vec::new(),
            stress_test_results: Vec::new(),
            validation_results: Vec::new(),
            configuration: config,
        }
    }

    /// Add TUI test suite results
    pub fn add_tui_results(&mut self, results: TuiTestSuite) {
        info!("Adding TUI test results: {}/{} passed", results.passed_tests, results.total_tests);
        self.tui_test_results.push(results);
    }

    /// Add transaction test results
    pub fn add_transaction_results(&mut self, results: TransactionTestSuite) {
        info!("Adding transaction test results");
        self.transaction_test_results.push(results);
    }

    /// Add workflow test results
    pub fn add_workflow_results(&mut self, results: EndToEndWorkflowResult) {
        info!("Adding end-to-end workflow results");
        self.workflow_test_results.push(results);
    }

    /// Add synthesis results
    pub fn add_synthesis_results(&mut self, results: WorkflowSynthesisResult) {
        info!("Adding workflow synthesis results");
        self.synthesis_results.push(results);
    }

    /// Add stress test results
    pub fn add_stress_test_results(&mut self, results: TuiStressTestResult) {
        info!("Adding stress test results: {}/{} commands successful", 
              results.successful_commands, results.total_commands);
        self.stress_test_results.push(results);
    }

    /// Add validation results
    pub fn add_validation_results(&mut self, results: TuiCommandValidationResult) {
        info!("Adding command validation results for: {}", results.command_result.command_name);
        self.validation_results.push(results);
    }

    /// Finalize test session and generate comprehensive report
    pub fn finalize_and_generate_report(&mut self) -> Result<ComprehensiveTestReport> {
        self.end_time = Some(Utc::now());
        
        info!("Finalizing test session: {}", self.test_session_id);
        info!("Test duration: {:?}", self.get_total_duration());

        let report = self.generate_comprehensive_report()?;
        
        if self.configuration.save_to_file {
            self.save_report_to_file(&report)?;
        }

        if self.configuration.print_summary {
            self.print_summary(&report);
        }

        Ok(report)
    }

    /// Generate comprehensive test report with analysis
    fn generate_comprehensive_report(&self) -> Result<ComprehensiveTestReport> {
        let mut report = ComprehensiveTestReport {
            session_id: self.test_session_id.clone(),
            start_time: self.start_time,
            end_time: self.end_time.unwrap_or_else(Utc::now),
            total_duration: self.get_total_duration(),
            overall_summary: self.calculate_overall_summary(),
            tui_analysis: self.analyze_tui_results(),
            transaction_analysis: self.analyze_transaction_results(),
            workflow_analysis: self.analyze_workflow_results(),
            stress_test_analysis: self.analyze_stress_test_results(),
            failure_analysis: self.analyze_failures(),
            performance_analysis: self.analyze_performance(),
            recommendations: self.generate_recommendations(),
            trend_analysis: None, // Initialize with None
            detailed_results: DetailedResults {
                tui_results: self.tui_test_results.clone(),
                transaction_results: self.transaction_test_results.clone(),
                workflow_results: self.workflow_test_results.clone(),
                synthesis_results: self.synthesis_results.clone(),
            },
        };

        // Add trend analysis if we have historical data
        if self.configuration.include_trend_analysis {
            report.trend_analysis = Some(self.analyze_trends()?);
        }

        Ok(report)
    }

    /// Calculate overall test summary statistics
    fn calculate_overall_summary(&self) -> OverallTestSummary {
        let mut summary = OverallTestSummary::default();

        // Aggregate TUI test results
        for tui_result in &self.tui_test_results {
            summary.total_tests += tui_result.total_tests;
            summary.passed_tests += tui_result.passed_tests;
            summary.failed_tests += tui_result.failed_tests;
            summary.total_execution_time_ms += tui_result.total_execution_time;
        }

        // Aggregate transaction test results
        for tx_result in &self.transaction_test_results {
            if let Some(stats) = &tx_result.statistics {
                summary.total_tests += stats.total_tests;
                summary.passed_tests += stats.successful_tests;
                summary.failed_tests += stats.failed_tests;
                summary.total_execution_time_ms += stats.total_execution_time_ms;
            }
        }

        // Aggregate workflow test results
        for workflow_result in &self.workflow_test_results {
            summary.total_tests += 1;
            if workflow_result.overall_success {
                summary.passed_tests += 1;
            } else {
                summary.failed_tests += 1;
            }
            summary.total_execution_time_ms += workflow_result.total_execution_time_ms;
        }

        // Calculate derived metrics
        summary.success_rate = if summary.total_tests > 0 {
            summary.passed_tests as f64 / summary.total_tests as f64
        } else {
            0.0
        };

        summary.average_execution_time_ms = if summary.total_tests > 0 {
            summary.total_execution_time_ms as f64 / summary.total_tests as f64
        } else {
            0.0
        };

        summary
    }

    /// Analyze TUI test results
    fn analyze_tui_results(&self) -> TuiTestAnalysis {
        let mut analysis = TuiTestAnalysis::default();

        for tui_result in &self.tui_test_results {
            analysis.total_suites += 1;
            analysis.total_commands += tui_result.total_tests;
            analysis.successful_commands += tui_result.passed_tests;
            analysis.failed_commands += tui_result.failed_tests;
            analysis.contract_interactions += tui_result.contract_interactions_detected;

            // Analyze individual command results
            self.analyze_individual_tui_commands(tui_result, &mut analysis);
        }

        analysis.success_rate = if analysis.total_commands > 0 {
            analysis.successful_commands as f64 / analysis.total_commands as f64
        } else {
            0.0
        };

        analysis
    }

    /// Analyze individual TUI command results
    fn analyze_individual_tui_commands(&self, suite: &TuiTestSuite, analysis: &mut TuiTestAnalysis) {
        let commands = vec![
            ("emergency_stop", &suite.emergency_stop_result),
            ("pause_bot", &suite.pause_bot_result),
            ("restart_bot", &suite.restart_bot_result),
            ("execute_opportunity", &suite.execute_opportunity_result),
            ("query_balances", &suite.query_balances_result),
            ("query_contract_status", &suite.query_contract_status_result),
        ];

        for (command_name, result_opt) in commands {
            if let Some(result) = result_opt {
                let command_stats = analysis.command_statistics
                    .entry(command_name.to_string())
                    .or_insert_with(CommandStatistics::default);

                command_stats.total_executions += 1;
                command_stats.total_execution_time_ms += result.execution_time_ms;

                if result.success {
                    command_stats.successful_executions += 1;
                } else {
                    command_stats.failed_executions += 1;
                    if let Some(error) = &result.error_message {
                        command_stats.error_patterns.push(error.clone());
                    }
                }

                if result.contract_interaction_detected {
                    command_stats.contract_interactions += 1;
                }
            }
        }
    }

    /// Analyze transaction test results
    fn analyze_transaction_results(&self) -> TransactionTestAnalysis {
        let mut analysis = TransactionTestAnalysis::default();

        for tx_result in &self.transaction_test_results {
            analysis.total_suites += 1;

            if let Some(stats) = &tx_result.statistics {
                analysis.total_transactions += stats.total_tests;
                analysis.successful_transactions += stats.successful_tests;
                analysis.failed_transactions += stats.failed_tests;
                analysis.total_execution_time_ms += stats.total_execution_time_ms;
            }

            // Analyze emergency stop tests
            if let Some(emergency_result) = &tx_result.emergency_stop_result {
                analysis.emergency_stop_tests += 1;
                if emergency_result.success {
                    analysis.successful_emergency_stops += 1;
                }
            }

            // Count error message validations
            analysis.error_validations += tx_result.error_message_validations.len();
        }

        analysis.success_rate = if analysis.total_transactions > 0 {
            analysis.successful_transactions as f64 / analysis.total_transactions as f64
        } else {
            0.0
        };

        analysis
    }

    /// Analyze workflow test results
    fn analyze_workflow_results(&self) -> WorkflowTestAnalysis {
        let mut analysis = WorkflowTestAnalysis::default();

        for workflow_result in &self.workflow_test_results {
            analysis.total_workflows += 1;
            analysis.total_execution_time_ms += workflow_result.total_execution_time_ms;

            if workflow_result.overall_success {
                analysis.successful_workflows += 1;
            } else {
                analysis.failed_workflows += 1;
            }

            // Analyze individual components
            if let Some(opportunity_result) = &workflow_result.opportunity_simulation {
                if opportunity_result.success {
                    analysis.successful_opportunity_simulations += 1;
                }
                analysis.total_opportunity_simulations += 1;
            }

            if let Some(backend_result) = &workflow_result.backend_execution {
                if backend_result.success {
                    analysis.successful_backend_executions += 1;
                }
                analysis.total_backend_executions += 1;
            }

            if let Some(tui_result) = &workflow_result.tui_validation {
                if tui_result.success {
                    analysis.successful_tui_validations += 1;
                }
                analysis.total_tui_validations += 1;
            }
        }

        analysis.success_rate = if analysis.total_workflows > 0 {
            analysis.successful_workflows as f64 / analysis.total_workflows as f64
        } else {
            0.0
        };

        analysis
    }

    /// Analyze stress test results
    fn analyze_stress_test_results(&self) -> StressTestAnalysis {
        let mut analysis = StressTestAnalysis::default();

        for stress_result in &self.stress_test_results {
            analysis.total_stress_tests += 1;
            analysis.total_commands += stress_result.total_commands;
            analysis.successful_commands += stress_result.successful_commands;
            analysis.failed_commands += stress_result.failed_commands;
            analysis.total_execution_time_ms += stress_result.total_execution_time;

            // Track peak performance
            if stress_result.success_rate() > analysis.peak_success_rate {
                analysis.peak_success_rate = stress_result.success_rate();
            }

            if stress_result.average_execution_time < analysis.best_average_time_ms || analysis.best_average_time_ms == 0 {
                analysis.best_average_time_ms = stress_result.average_execution_time;
            }

            // Collect error patterns
            for error in &stress_result.errors {
                analysis.error_patterns.push(error.clone());
            }
        }

        analysis.overall_success_rate = if analysis.total_commands > 0 {
            analysis.successful_commands as f64 / analysis.total_commands as f64
        } else {
            0.0
        };

        analysis
    }

    /// Analyze failure patterns and categorize issues
    fn analyze_failures(&self) -> FailureAnalysis {
        let mut analysis = FailureAnalysis::default();

        // Analyze TUI failures
        for tui_result in &self.tui_test_results {
            self.categorize_tui_failures(tui_result, &mut analysis);
        }

        // Analyze transaction failures
        for tx_result in &self.transaction_test_results {
            self.categorize_transaction_failures(tx_result, &mut analysis);
        }

        // Analyze workflow failures
        for workflow_result in &self.workflow_test_results {
            if !workflow_result.overall_success {
                analysis.workflow_failures += 1;
                analysis.failure_categories.entry("workflow_execution".to_string())
                    .and_modify(|count| *count += 1)
                    .or_insert(1);
            }
        }

        analysis
    }

    /// Categorize TUI-specific failures
    fn categorize_tui_failures(&self, suite: &TuiTestSuite, analysis: &mut FailureAnalysis) {
        let commands = vec![
            ("emergency_stop", &suite.emergency_stop_result),
            ("pause_bot", &suite.pause_bot_result),
            ("restart_bot", &suite.restart_bot_result),
            ("execute_opportunity", &suite.execute_opportunity_result),
            ("query_balances", &suite.query_balances_result),
            ("query_contract_status", &suite.query_contract_status_result),
        ];

        for (command_name, result_opt) in commands {
            if let Some(result) = result_opt {
                if !result.success {
                    analysis.tui_command_failures += 1;
                    
                    if let Some(error_msg) = &result.error_message {
                        // Categorize by error type
                        let category = self.categorize_error_message(error_msg);
                        analysis.failure_categories.entry(category)
                            .and_modify(|count| *count += 1)
                            .or_insert(1);

                        // Track specific error patterns
                        analysis.error_patterns.push(ErrorPattern {
                            command: command_name.to_string(),
                            error_message: error_msg.clone(),
                            category: self.categorize_error_message(error_msg),
                            frequency: 1, // Will be aggregated later
                        });
                    }
                }
            }
        }
    }

    /// Categorize transaction-specific failures
    fn categorize_transaction_failures(&self, suite: &TransactionTestSuite, analysis: &mut FailureAnalysis) {
        if let Some(stats) = &suite.statistics {
            analysis.transaction_failures += stats.failed_tests;
        }

        // Analyze emergency stop failures
        if let Some(emergency_result) = &suite.emergency_stop_result {
            if !emergency_result.success {
                analysis.failure_categories.entry("emergency_stop".to_string())
                    .and_modify(|count| *count += 1)
                    .or_insert(1);
            }
        }
    }

    /// Categorize error message into failure type
    fn categorize_error_message(&self, error_msg: &str) -> String {
        let error_lower = error_msg.to_lowercase();
        
        if error_lower.contains("timeout") || error_lower.contains("timed out") {
            "timeout".to_string()
        } else if error_lower.contains("connection") || error_lower.contains("network") {
            "network".to_string()
        } else if error_lower.contains("contract") || error_lower.contains("revert") {
            "contract_interaction".to_string()
        } else if error_lower.contains("parse") || error_lower.contains("format") {
            "parsing".to_string()
        } else if error_lower.contains("permission") || error_lower.contains("unauthorized") {
            "authorization".to_string()
        } else {
            "unknown".to_string()
        }
    }

    /// Analyze performance metrics across all tests
    fn analyze_performance(&self) -> PerformanceAnalysis {
        let mut analysis = PerformanceAnalysis::default();

        // Collect all execution times
        let mut all_execution_times = Vec::new();

        // TUI execution times
        for tui_result in &self.tui_test_results {
            all_execution_times.push(tui_result.average_execution_time());
        }

        // Transaction execution times
        for tx_result in &self.transaction_test_results {
            if let Some(stats) = &tx_result.statistics {
                if stats.total_tests > 0 {
                    all_execution_times.push(stats.total_execution_time_ms as f64 / stats.total_tests as f64);
                }
            }
        }

        // Workflow execution times
        for workflow_result in &self.workflow_test_results {
            all_execution_times.push(workflow_result.total_execution_time_ms as f64);
        }

        if !all_execution_times.is_empty() {
            analysis.average_execution_time_ms = all_execution_times.iter().sum::<f64>() / all_execution_times.len() as f64;
            analysis.min_execution_time_ms = all_execution_times.iter().fold(f64::INFINITY, |a, &b| a.min(b));
            analysis.max_execution_time_ms = all_execution_times.iter().fold(0.0, |a, &b| a.max(b));

            // Calculate percentiles
            let mut sorted_times = all_execution_times.clone();
            sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());
            
            let len = sorted_times.len();
            analysis.p50_execution_time_ms = sorted_times[len / 2];
            analysis.p95_execution_time_ms = sorted_times[(len * 95) / 100];
            analysis.p99_execution_time_ms = sorted_times[(len * 99) / 100];
        }

        // Analyze stress test performance
        for stress_result in &self.stress_test_results {
            if stress_result.total_commands > 0 {
                let throughput = stress_result.total_commands as f64 / (stress_result.total_execution_time as f64 / 1000.0);
                if throughput > analysis.peak_throughput_commands_per_sec {
                    analysis.peak_throughput_commands_per_sec = throughput;
                }
            }
        }

        analysis
    }

    /// Generate actionable recommendations based on test results
    fn generate_recommendations(&self) -> Vec<TestRecommendation> {
        let mut recommendations = Vec::new();

        let overall_summary = self.calculate_overall_summary();
        let failure_analysis = self.analyze_failures();

        // Success rate recommendations
        if overall_summary.success_rate < 0.8 {
            recommendations.push(TestRecommendation {
                category: RecommendationCategory::Reliability,
                priority: RecommendationPriority::High,
                title: "Low Overall Success Rate".to_string(),
                description: format!(
                    "Overall test success rate is {:.1}%, which is below the recommended 80% threshold.",
                    overall_summary.success_rate * 100.0
                ),
                action_items: vec![
                    "Review and fix failing test cases".to_string(),
                    "Investigate root causes of failures".to_string(),
                    "Consider increasing test timeouts if appropriate".to_string(),
                ],
            });
        }

        // Performance recommendations
        let performance_analysis = self.analyze_performance();
        if performance_analysis.average_execution_time_ms > 5000.0 {
            recommendations.push(TestRecommendation {
                category: RecommendationCategory::Performance,
                priority: RecommendationPriority::Medium,
                title: "High Average Execution Time".to_string(),
                description: format!(
                    "Average test execution time is {:.1}ms, which may indicate performance issues.",
                    performance_analysis.average_execution_time_ms
                ),
                action_items: vec![
                    "Profile slow-running tests".to_string(),
                    "Optimize contract interaction patterns".to_string(),
                    "Consider parallel test execution".to_string(),
                ],
            });
        }

        // Failure pattern recommendations
        if failure_analysis.tui_command_failures > 0 {
            recommendations.push(TestRecommendation {
                category: RecommendationCategory::Stability,
                priority: RecommendationPriority::High,
                title: "TUI Command Failures Detected".to_string(),
                description: format!(
                    "{} TUI command failures detected across test suites.",
                    failure_analysis.tui_command_failures
                ),
                action_items: vec![
                    "Review TUI command implementation".to_string(),
                    "Verify command input validation".to_string(),
                    "Check for race conditions in command execution".to_string(),
                ],
            });
        }

        // Contract interaction recommendations
        let tui_analysis = self.analyze_tui_results();
        if tui_analysis.contract_interactions == 0 && tui_analysis.total_commands > 0 {
            recommendations.push(TestRecommendation {
                category: RecommendationCategory::Coverage,
                priority: RecommendationPriority::Medium,
                title: "No Contract Interactions Detected".to_string(),
                description: "Tests completed without detecting contract interactions, which may indicate incomplete test coverage.".to_string(),
                action_items: vec![
                    "Verify contract interaction detection logic".to_string(),
                    "Ensure tests are actually triggering contract calls".to_string(),
                    "Review test environment setup".to_string(),
                ],
            });
        }

        recommendations
    }

    /// Analyze trends (placeholder for historical comparison)
    fn analyze_trends(&self) -> Result<TrendAnalysis> {
        // This would typically compare against historical test data
        // For now, return a basic trend analysis
        Ok(TrendAnalysis {
            success_rate_trend: TrendDirection::Stable,
            performance_trend: TrendDirection::Stable,
            failure_rate_trend: TrendDirection::Stable,
            historical_comparison_available: false,
            trend_period_days: 0,
        })
    }

    /// Get total test session duration
    fn get_total_duration(&self) -> Duration {
        let end_time = self.end_time.unwrap_or_else(Utc::now);
        end_time.signed_duration_since(self.start_time).to_std().unwrap_or_default()
    }

    /// Save report to file
    fn save_report_to_file(&self, report: &ComprehensiveTestReport) -> Result<()> {
        let filename = format!("test_report_{}.json", self.test_session_id);
        let json_content = serde_json::to_string_pretty(report)
            .context("Failed to serialize test report")?;
        
        std::fs::write(&filename, json_content)
            .context(format!("Failed to write report to file: {}", filename))?;
        
        info!("Test report saved to: {}", filename);
        Ok(())
    }

    /// Print summary to console
    fn print_summary(&self, report: &ComprehensiveTestReport) {
        println!("\n🔍 STARGATE COMPASS INTEGRATION TEST REPORT");
        println!("═══════════════════════════════════════════");
        println!("Session ID: {}", report.session_id);
        println!("Duration: {:?}", report.total_duration);
        println!();

        let summary = &report.overall_summary;
        println!("📊 OVERALL RESULTS");
        println!("─────────────────");
        println!("Total Tests: {}", summary.total_tests);
        println!("✅ Passed: {} ({:.1}%)", summary.passed_tests, summary.success_rate * 100.0);
        println!("❌ Failed: {}", summary.failed_tests);
        println!("⏱️  Avg Execution: {:.1}ms", summary.average_execution_time_ms);
        println!();

        // TUI Analysis
        println!("🖥️  TUI TEST ANALYSIS");
        println!("────────────────────");
        println!("Commands Tested: {}", report.tui_analysis.total_commands);
        println!("Success Rate: {:.1}%", report.tui_analysis.success_rate * 100.0);
        println!("Contract Interactions: {}", report.tui_analysis.contract_interactions);
        println!();

        // Transaction Analysis
        println!("💰 TRANSACTION TEST ANALYSIS");
        println!("───────────────────────────");
        println!("Transactions Tested: {}", report.transaction_analysis.total_transactions);
        println!("Success Rate: {:.1}%", report.transaction_analysis.success_rate * 100.0);
        println!("Emergency Stops: {}/{}", report.transaction_analysis.successful_emergency_stops, report.transaction_analysis.emergency_stop_tests);
        println!();

        // Recommendations
        if !report.recommendations.is_empty() {
            println!("💡 RECOMMENDATIONS");
            println!("─────────────────");
            for (i, rec) in report.recommendations.iter().enumerate() {
                println!("{}. {} ({:?})", i + 1, rec.title, rec.priority);
                println!("   {}", rec.description);
            }
            println!();
        }

        println!("Report completed at: {}", Utc::now().format("%Y-%m-%d %H:%M:%S UTC"));
    }
}
/// Configuration for test reporting
#[derive(Debug, Clone)]
pub struct TestReportConfiguration {
    pub save_to_file: bool,
    pub print_summary: bool,
    pub include_detailed_results: bool,
    pub include_trend_analysis: bool,
    pub output_directory: String,
}

impl Default for TestReportConfiguration {
    fn default() -> Self {
        Self {
            save_to_file: true,
            print_summary: true,
            include_detailed_results: true,
            include_trend_analysis: false,
            output_directory: "test_reports".to_string(),
        }
    }
}

/// Comprehensive test report containing all analysis results
#[derive(Debug, Serialize, Deserialize)]
pub struct ComprehensiveTestReport {
    pub session_id: String,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub total_duration: Duration,
    pub overall_summary: OverallTestSummary,
    pub tui_analysis: TuiTestAnalysis,
    pub transaction_analysis: TransactionTestAnalysis,
    pub workflow_analysis: WorkflowTestAnalysis,
    pub stress_test_analysis: StressTestAnalysis,
    pub failure_analysis: FailureAnalysis,
    pub performance_analysis: PerformanceAnalysis,
    pub recommendations: Vec<TestRecommendation>,
    pub trend_analysis: Option<TrendAnalysis>,
    pub detailed_results: DetailedResults,
}

/// Overall test summary statistics
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct OverallTestSummary {
    pub total_tests: usize,
    pub passed_tests: usize,
    pub failed_tests: usize,
    pub success_rate: f64,
    pub total_execution_time_ms: u64,
    pub average_execution_time_ms: f64,
}

/// TUI-specific test analysis
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct TuiTestAnalysis {
    pub total_suites: usize,
    pub total_commands: usize,
    pub successful_commands: usize,
    pub failed_commands: usize,
    pub success_rate: f64,
    pub contract_interactions: usize,
    pub command_statistics: HashMap<String, CommandStatistics>,
}

/// Statistics for individual commands
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CommandStatistics {
    pub total_executions: usize,
    pub successful_executions: usize,
    pub failed_executions: usize,
    pub total_execution_time_ms: u64,
    pub contract_interactions: usize,
    pub error_patterns: Vec<String>,
}

/// Transaction test analysis
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct TransactionTestAnalysis {
    pub total_suites: usize,
    pub total_transactions: usize,
    pub successful_transactions: usize,
    pub failed_transactions: usize,
    pub success_rate: f64,
    pub emergency_stop_tests: usize,
    pub successful_emergency_stops: usize,
    pub error_validations: usize,
    pub total_execution_time_ms: u64,
}

/// Workflow test analysis
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct WorkflowTestAnalysis {
    pub total_workflows: usize,
    pub successful_workflows: usize,
    pub failed_workflows: usize,
    pub success_rate: f64,
    pub total_opportunity_simulations: usize,
    pub successful_opportunity_simulations: usize,
    pub total_backend_executions: usize,
    pub successful_backend_executions: usize,
    pub total_tui_validations: usize,
    pub successful_tui_validations: usize,
    pub total_execution_time_ms: u64,
}

/// Stress test analysis
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct StressTestAnalysis {
    pub total_stress_tests: usize,
    pub total_commands: usize,
    pub successful_commands: usize,
    pub failed_commands: usize,
    pub overall_success_rate: f64,
    pub peak_success_rate: f64,
    pub best_average_time_ms: u64,
    pub total_execution_time_ms: u64,
    pub error_patterns: Vec<String>,
}

/// Failure analysis and categorization
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct FailureAnalysis {
    pub tui_command_failures: usize,
    pub transaction_failures: usize,
    pub workflow_failures: usize,
    pub failure_categories: HashMap<String, usize>,
    pub error_patterns: Vec<ErrorPattern>,
}

/// Error pattern analysis
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorPattern {
    pub command: String,
    pub error_message: String,
    pub category: String,
    pub frequency: usize,
}

/// Performance analysis across all tests
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct PerformanceAnalysis {
    pub average_execution_time_ms: f64,
    pub min_execution_time_ms: f64,
    pub max_execution_time_ms: f64,
    pub p50_execution_time_ms: f64,
    pub p95_execution_time_ms: f64,
    pub p99_execution_time_ms: f64,
    pub peak_throughput_commands_per_sec: f64,
}

/// Test recommendations
#[derive(Debug, Serialize, Deserialize)]
pub struct TestRecommendation {
    pub category: RecommendationCategory,
    pub priority: RecommendationPriority,
    pub title: String,
    pub description: String,
    pub action_items: Vec<String>,
}

/// Recommendation categories
#[derive(Debug, Serialize, Deserialize)]
pub enum RecommendationCategory {
    Reliability,
    Performance,
    Stability,
    Coverage,
    Security,
    Maintainability,
}

/// Recommendation priorities
#[derive(Debug, Serialize, Deserialize)]
pub enum RecommendationPriority {
    Critical,
    High,
    Medium,
    Low,
}

/// Trend analysis (for historical comparison)
#[derive(Debug, Serialize, Deserialize)]
pub struct TrendAnalysis {
    pub success_rate_trend: TrendDirection,
    pub performance_trend: TrendDirection,
    pub failure_rate_trend: TrendDirection,
    pub historical_comparison_available: bool,
    pub trend_period_days: u32,
}

/// Trend direction indicators
#[derive(Debug, Serialize, Deserialize)]
pub enum TrendDirection {
    Improving,
    Stable,
    Declining,
    Unknown,
}

/// Detailed results container
#[derive(Debug, Serialize, Deserialize)]
pub struct DetailedResults {
    pub tui_results: Vec<TuiTestSuite>,
    pub transaction_results: Vec<TransactionTestSuite>,
    pub workflow_results: Vec<EndToEndWorkflowResult>,
    pub synthesis_results: Vec<WorkflowSynthesisResult>,
}

/// Test result export utilities
pub struct TestResultExporter;

impl TestResultExporter {
    /// Export results to JSON format
    pub fn export_to_json(report: &ComprehensiveTestReport, file_path: &str) -> Result<()> {
        let json_content = serde_json::to_string_pretty(report)
            .context("Failed to serialize test report to JSON")?;
        
        std::fs::write(file_path, json_content)
            .context(format!("Failed to write JSON report to: {}", file_path))?;
        
        info!("Test report exported to JSON: {}", file_path);
        Ok(())
    }

    /// Export results to CSV format (summary only)
    pub fn export_summary_to_csv(report: &ComprehensiveTestReport, file_path: &str) -> Result<()> {
        let mut csv_content = String::new();
        csv_content.push_str("Metric,Value\n");
        
        let summary = &report.overall_summary;
        csv_content.push_str(&format!("Session ID,{}\n", report.session_id));
        csv_content.push_str(&format!("Total Tests,{}\n", summary.total_tests));
        csv_content.push_str(&format!("Passed Tests,{}\n", summary.passed_tests));
        csv_content.push_str(&format!("Failed Tests,{}\n", summary.failed_tests));
        csv_content.push_str(&format!("Success Rate,{:.2}\n", summary.success_rate));
        csv_content.push_str(&format!("Average Execution Time (ms),{:.2}\n", summary.average_execution_time_ms));
        csv_content.push_str(&format!("TUI Commands,{}\n", report.tui_analysis.total_commands));
        csv_content.push_str(&format!("TUI Success Rate,{:.2}\n", report.tui_analysis.success_rate));
        csv_content.push_str(&format!("Transaction Tests,{}\n", report.transaction_analysis.total_transactions));
        csv_content.push_str(&format!("Transaction Success Rate,{:.2}\n", report.transaction_analysis.success_rate));
        csv_content.push_str(&format!("Workflow Tests,{}\n", report.workflow_analysis.total_workflows));
        csv_content.push_str(&format!("Workflow Success Rate,{:.2}\n", report.workflow_analysis.success_rate));
        
        std::fs::write(file_path, csv_content)
            .context(format!("Failed to write CSV report to: {}", file_path))?;
        
        info!("Test summary exported to CSV: {}", file_path);
        Ok(())
    }

    /// Generate HTML report
    pub fn export_to_html(report: &ComprehensiveTestReport, file_path: &str) -> Result<()> {
        let html_content = Self::generate_html_report(report);
        
        std::fs::write(file_path, html_content)
            .context(format!("Failed to write HTML report to: {}", file_path))?;
        
        info!("Test report exported to HTML: {}", file_path);
        Ok(())
    }

    /// Generate HTML report content
    fn generate_html_report(report: &ComprehensiveTestReport) -> String {
        format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stargate Compass Integration Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }}
        .summary-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .metric-card {{ background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }}
        .metric-value {{ font-size: 2em; font-weight: bold; color: #007bff; }}
        .metric-label {{ color: #666; font-size: 0.9em; }}
        .section {{ margin-bottom: 30px; }}
        .section h2 {{ color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }}
        .success {{ color: #28a745; }}
        .failure {{ color: #dc3545; }}
        .warning {{ color: #ffc107; }}
        .recommendations {{ background: #fff3cd; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107; }}
        .recommendation {{ margin-bottom: 15px; }}
        .recommendation h4 {{ margin: 0 0 5px 0; color: #856404; }}
        .action-items {{ margin-left: 20px; }}
        .action-items li {{ margin-bottom: 5px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Stargate Compass Integration Test Report</h1>
            <p>Session: {} | Duration: {:?}</p>
            <p>Generated: {}</p>
        </div>

        <div class="summary-grid">
            <div class="metric-card">
                <div class="metric-value {}">{}</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value success">{}</div>
                <div class="metric-label">Passed Tests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value failure">{}</div>
                <div class="metric-label">Failed Tests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value {}">{:.1}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
        </div>

        <div class="section">
            <h2>🖥️ TUI Test Analysis</h2>
            <p><strong>Commands Tested:</strong> {}</p>
            <p><strong>Success Rate:</strong> <span class="{}">{:.1}%</span></p>
            <p><strong>Contract Interactions:</strong> {}</p>
        </div>

        <div class="section">
            <h2>💰 Transaction Test Analysis</h2>
            <p><strong>Transactions Tested:</strong> {}</p>
            <p><strong>Success Rate:</strong> <span class="{}">{:.1}%</span></p>
            <p><strong>Emergency Stops:</strong> {}/{}</p>
        </div>

        <div class="section">
            <h2>🔄 Workflow Test Analysis</h2>
            <p><strong>Workflows Tested:</strong> {}</p>
            <p><strong>Success Rate:</strong> <span class="{}">{:.1}%</span></p>
        </div>

        {}

        <div class="section">
            <h2>📊 Performance Metrics</h2>
            <p><strong>Average Execution Time:</strong> {:.1}ms</p>
            <p><strong>P95 Execution Time:</strong> {:.1}ms</p>
            <p><strong>Peak Throughput:</strong> {:.1} commands/sec</p>
        </div>
    </div>
</body>
</html>
"#,
            report.session_id,
            report.total_duration,
            Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
            if report.overall_summary.total_tests > 0 { "" } else { "warning" },
            report.overall_summary.total_tests,
            report.overall_summary.passed_tests,
            report.overall_summary.failed_tests,
            if report.overall_summary.success_rate >= 0.8 { "success" } else { "failure" },
            report.overall_summary.success_rate * 100.0,
            report.tui_analysis.total_commands,
            if report.tui_analysis.success_rate >= 0.8 { "success" } else { "failure" },
            report.tui_analysis.success_rate * 100.0,
            report.tui_analysis.contract_interactions,
            report.transaction_analysis.total_transactions,
            if report.transaction_analysis.success_rate >= 0.8 { "success" } else { "failure" },
            report.transaction_analysis.success_rate * 100.0,
            report.transaction_analysis.successful_emergency_stops,
            report.transaction_analysis.emergency_stop_tests,
            report.workflow_analysis.total_workflows,
            if report.workflow_analysis.success_rate >= 0.8 { "success" } else { "failure" },
            report.workflow_analysis.success_rate * 100.0,
            Self::generate_recommendations_html(&report.recommendations),
            report.performance_analysis.average_execution_time_ms,
            report.performance_analysis.p95_execution_time_ms,
            report.performance_analysis.peak_throughput_commands_per_sec,
        )
    }

    /// Generate HTML for recommendations section
    fn generate_recommendations_html(recommendations: &[TestRecommendation]) -> String {
        if recommendations.is_empty() {
            return String::new();
        }

        let mut html = String::from(r#"<div class="section"><h2>💡 Recommendations</h2><div class="recommendations">"#);
        
        for rec in recommendations {
            html.push_str(&format!(r#"
                <div class="recommendation">
                    <h4>{} ({:?} Priority)</h4>
                    <p>{}</p>
                    <ul class="action-items">
            "#, rec.title, rec.priority, rec.description));
            
            for action in &rec.action_items {
                html.push_str(&format!("<li>{}</li>", action));
            }
            
            html.push_str("</ul></div>");
        }
        
        html.push_str("</div></div>");
        html
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_aggregator_creation() {
        let config = TestReportConfiguration::default();
        let aggregator = TestResultAggregator::new(config);
        
        assert!(!aggregator.test_session_id.is_empty());
        assert!(aggregator.tui_test_results.is_empty());
        assert!(aggregator.transaction_test_results.is_empty());
    }

    #[test]
    fn test_overall_summary_calculation() {
        let config = TestReportConfiguration::default();
        let mut aggregator = TestResultAggregator::new(config);
        
        // Add mock TUI results
        let mut tui_suite = TuiTestSuite::new();
        tui_suite.total_tests = 6;
        tui_suite.passed_tests = 4;
        tui_suite.failed_tests = 2;
        tui_suite.total_execution_time = 3000;
        
        aggregator.add_tui_results(tui_suite);
        
        let summary = aggregator.calculate_overall_summary();
        assert_eq!(summary.total_tests, 6);
        assert_eq!(summary.passed_tests, 4);
        assert_eq!(summary.failed_tests, 2);
        assert!((summary.success_rate - 0.6667).abs() < 0.001);
    }

    #[test]
    fn test_error_categorization() {
        let config = TestReportConfiguration::default();
        let aggregator = TestResultAggregator::new(config);
        
        assert_eq!(aggregator.categorize_error_message("Connection timeout"), "timeout");
        assert_eq!(aggregator.categorize_error_message("Network error occurred"), "network");
        assert_eq!(aggregator.categorize_error_message("Contract call reverted"), "contract_interaction");
        assert_eq!(aggregator.categorize_error_message("Parse error in response"), "parsing");
        assert_eq!(aggregator.categorize_error_message("Unauthorized access"), "authorization");
        assert_eq!(aggregator.categorize_error_message("Unknown error"), "unknown");
    }

    #[test]
    fn test_recommendation_generation() {
        let config = TestReportConfiguration::default();
        let aggregator = TestResultAggregator::new(config);
        
        let recommendations = aggregator.generate_recommendations();
        
        // Should generate recommendations based on current (empty) state
        // This tests the recommendation logic framework
        assert!(recommendations.len() >= 0); // May be empty for new aggregator
    }
}