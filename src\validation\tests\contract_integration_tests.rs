// src/validation/tests/contract_integration_tests.rs

//! Integration tests for contract integration validator

use crate::validation::{
    ContractIntegrationValidator, ContractIntegrationConfig, ContractAddresses,
    ValidationStatus
};
use ethers::types::Address;
use std::time::Duration;

#[tokio::test]
async fn test_contract_integration_validator_creation() {
    let config = ContractIntegrationConfig::default();
    let validator = ContractIntegrationValidator::new(config);
    
    // Validator should be created successfully
    assert!(validator.anvil_process.is_none());
    assert!(validator.provider.is_none());
}

#[tokio::test]
async fn test_contract_addresses_validation() {
    let addresses = ContractAddresses::default();
    
    // All default addresses should be non-zero
    assert!(!addresses.stargate_compass.is_zero());
    assert!(!addresses.aave_pool.is_zero());
    assert!(!addresses.stargate_router.is_zero());
    assert!(!addresses.uniswap_v3_router.is_zero());
    assert!(!addresses.aerodrome_router.is_zero());
    assert!(!addresses.sushiswap_router.is_zero());
    assert!(!addresses.usdc_token.is_zero());
    assert!(!addresses.weth_token.is_zero());
}

#[tokio::test]
async fn test_contract_integration_config() {
    let config = ContractIntegrationConfig::default();
    
    assert_eq!(config.chain_id, 8453); // Base mainnet
    assert_eq!(config.anvil_port, 8545);
    assert_eq!(config.gas_estimation_tolerance, 0.1);
    assert_eq!(config.simulation_timeout, Duration::from_secs(30));
    assert!(!config.fork_rpc_url.is_empty());
}

#[tokio::test]
async fn test_encode_transfer_call() {
    let config = ContractIntegrationConfig::default();
    let validator = ContractIntegrationValidator::new(config);
    
    let recipient = Address::from_low_u64_be(1);
    let amount = ethers::types::U256::from(1000000);
    
    let calldata = validator.encode_transfer_call(recipient, amount).unwrap();
    
    // Should have function selector (4 bytes) + address (32 bytes) + amount (32 bytes) = 68 bytes
    assert_eq!(calldata.len(), 68);
    
    // First 4 bytes should be the transfer function selector
    let expected_selector = [0xa9, 0x05, 0x9c, 0xbb]; // transfer(address,uint256)
    assert_eq!(&calldata[0..4], expected_selector);
}

#[tokio::test]
async fn test_encode_compass_call() {
    let config = ContractIntegrationConfig::default();
    let validator = ContractIntegrationValidator::new(config);
    
    let loan_amount = ethers::types::U256::from(1000000);
    let remote_calldata = ethers::types::Bytes::from(vec![0x12, 0x34, 0x56, 0x78]);
    let remote_router = Address::from_low_u64_be(2);
    
    let calldata = validator.encode_compass_call(loan_amount, remote_calldata, remote_router).unwrap();
    
    // Should have function selector + encoded parameters
    assert!(calldata.len() > 4);
    
    // First 4 bytes should be the executeRemoteDegenSwap function selector
    // This is a mock test, so we just verify it's not empty and has the selector
    assert!(!calldata.is_empty());
}

// Note: The following tests require Anvil to be available and would be integration tests
// They are commented out to avoid CI failures but can be run manually

/*
#[tokio::test]
#[ignore] // Requires Anvil binary
async fn test_anvil_startup_and_shutdown() {
    let config = ContractIntegrationConfig {
        anvil_startup_timeout: Duration::from_secs(10),
        ..Default::default()
    };
    let mut validator = ContractIntegrationValidator::new(config);
    
    // Start Anvil
    let start_result = validator.start_anvil().await;
    if start_result.is_err() {
        // Skip test if Anvil is not available
        return;
    }
    
    // Verify Anvil is running
    assert!(validator.provider.is_some());
    assert!(validator.anvil_rpc_url.is_some());
    
    // Stop Anvil
    let stop_result = validator.stop_anvil().await;
    assert!(stop_result.is_ok());
    
    // Verify cleanup
    assert!(validator.provider.is_none());
    assert!(validator.anvil_rpc_url.is_none());
}

#[tokio::test]
#[ignore] // Requires Anvil binary and network access
async fn test_contract_addresses_validation_with_anvil() {
    let config = ContractIntegrationConfig {
        anvil_startup_timeout: Duration::from_secs(10),
        ..Default::default()
    };
    let mut validator = ContractIntegrationValidator::new(config);
    
    // Start Anvil
    if validator.start_anvil().await.is_err() {
        // Skip test if Anvil is not available
        return;
    }
    
    // Run contract addresses validation
    let result = validator.validate_contract_addresses().await;
    assert!(result.is_ok());
    
    let validation_result = result.unwrap();
    assert!(matches!(validation_result.status, ValidationStatus::Passed | ValidationStatus::Warning));
    
    if let Some(metrics) = validation_result.metrics {
        assert!(metrics.total_contracts_validated > 0);
        assert!(!metrics.abi_consistency_results.is_empty());
    }
    
    // Stop Anvil
    let _ = validator.stop_anvil().await;
}

#[tokio::test]
#[ignore] // Requires Anvil binary and network access
async fn test_transaction_simulation_validation() {
    let config = ContractIntegrationConfig {
        anvil_startup_timeout: Duration::from_secs(10),
        ..Default::default()
    };
    let mut validator = ContractIntegrationValidator::new(config);
    
    // Start Anvil
    if validator.start_anvil().await.is_err() {
        // Skip test if Anvil is not available
        return;
    }
    
    // Run transaction simulation validation
    let result = validator.validate_transaction_simulation().await;
    assert!(result.is_ok());
    
    let validation_result = result.unwrap();
    assert!(matches!(validation_result.status, ValidationStatus::Passed | ValidationStatus::Warning));
    
    if let Some(metrics) = validation_result.metrics {
        assert!(metrics.total_transactions_simulated > 0);
        assert!(metrics.transaction_simulation_accuracy >= 0.0);
    }
    
    // Stop Anvil
    let _ = validator.stop_anvil().await;
}
*/

#[test]
fn test_contract_integration_metrics_default() {
    let metrics = crate::validation::ContractIntegrationMetrics::default();
    
    assert_eq!(metrics.gas_estimation_accuracy, 0.0);
    assert_eq!(metrics.transaction_simulation_accuracy, 0.0);
    assert_eq!(metrics.contract_state_consistency, 0.0);
    assert_eq!(metrics.error_handling_effectiveness, 0.0);
    assert_eq!(metrics.average_interaction_latency_ms, 0.0);
    assert_eq!(metrics.total_contracts_validated, 0);
    assert_eq!(metrics.total_transactions_simulated, 0);
    assert!(metrics.contract_call_success_rate.is_empty());
    assert!(metrics.abi_consistency_results.is_empty());
}