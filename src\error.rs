use thiserror::Error;
use serde::{Deserialize, Serialize};
use crate::logging::{ErrorCode, AlertSeverity, TraceId};
use rust_decimal::Decimal;

/// Network-related errors that can trigger retries
#[derive(<PERSON><PERSON><PERSON>, Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum NetworkError {
    #[error("RPC timeout after {timeout_ms}ms on endpoint {endpoint}")]
    RpcTimeout { endpoint: String, timeout_ms: u64 },
    
    #[error("RPC connection failed to {endpoint}: {reason}")]
    RpcConnectionFailed { endpoint: String, reason: String },
    
    #[error("Invalid RPC response from {endpoint}: {details}")]
    RpcInvalidResponse { endpoint: String, details: String },
    
    #[error("RPC rate limited on {endpoint}, retry after {retry_after_ms}ms")]
    RpcRateLimited { endpoint: String, retry_after_ms: u64 },
    
    #[error("WebSocket connection lost to {endpoint}")]
    WebSocketDisconnected { endpoint: String },
    
    #[error("Network latency too high: {latency_ms}ms > {threshold_ms}ms")]
    HighLatency { latency_ms: u64, threshold_ms: u64 },
}

impl NetworkError {
    pub fn error_code(&self) -> ErrorCode {
        match self {
            NetworkError::RpcTimeout { .. } => ErrorCode::ERpcTimeout,
            NetworkError::RpcConnectionFailed { .. } => ErrorCode::ERpcConnectionFailed,
            NetworkError::RpcInvalidResponse { .. } => ErrorCode::ERpcInvalidResponse,
            NetworkError::RpcRateLimited { .. } => ErrorCode::ERpcRateLimited,
            NetworkError::WebSocketDisconnected { .. } => ErrorCode::ERpcConnectionFailed,
            NetworkError::HighLatency { .. } => ErrorCode::ENetworkLatencyHigh,
        }
    }
    
    pub fn is_retryable(&self) -> bool {
        matches!(self, 
            NetworkError::RpcTimeout { .. } |
            NetworkError::RpcConnectionFailed { .. } |
            NetworkError::RpcRateLimited { .. } |
            NetworkError::WebSocketDisconnected { .. }
        )
    }
}

/// Data provider and oracle errors
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum DataProviderError {
    #[error("Data source {data_source} unavailable: {reason}")]
    SourceUnavailable { data_source: String, reason: String },
    
    #[error("Stale data from {data_source}: age {age_ms}ms > threshold {threshold_ms}ms")]
    StaleData { data_source: String, age_ms: u64, threshold_ms: u64 },
    
    #[error("Data validation failed for {data_source}: {details}")]
    ValidationFailed { data_source: String, details: String },
    
    #[error("Price oracle failure for {asset}: {reason}")]
    PriceOracleFailure { asset: String, reason: String },
    
    #[error("Insufficient data points for {metric}: got {actual}, need {required}")]
    InsufficientData { metric: String, actual: u32, required: u32 },
}

impl DataProviderError {
    pub fn error_code(&self) -> ErrorCode {
        match self {
            DataProviderError::SourceUnavailable { .. } => ErrorCode::EDataSourceUnavailable,
            DataProviderError::StaleData { .. } => ErrorCode::EDataStale,
            DataProviderError::ValidationFailed { .. } => ErrorCode::EDataValidationFailed,
            DataProviderError::PriceOracleFailure { .. } => ErrorCode::EPriceOracleFailure,
            DataProviderError::InsufficientData { .. } => ErrorCode::EDataValidationFailed,
        }
    }
}

/// Execution-related errors
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionError {
    #[error("Insufficient liquidity for {token_path:?}: available {available_usd}, required {required_usd}")]
    InsufficientLiquidity { 
        token_path: Vec<String>, 
        available_usd: Decimal, 
        required_usd: Decimal 
    },
    
    #[error("High slippage detected: {actual_percent}% > {threshold_percent}% for path {token_path:?}")]
    HighSlippage { 
        token_path: Vec<String>, 
        actual_percent: Decimal, 
        threshold_percent: Decimal 
    },
    
    #[error("Gas estimation failed for {operation}: {reason}")]
    GasEstimationFailed { operation: String, reason: String },
    
    #[error("Transaction reverted: {tx_hash} - {reason}")]
    TransactionReverted { tx_hash: String, reason: String },
    
    #[error("Transaction timeout: {tx_hash} after {timeout_ms}ms")]
    TransactionTimeout { tx_hash: String, timeout_ms: u64 },
    
    #[error("Nonce conflict: expected {expected}, got {actual}")]
    NonceConflict { expected: u64, actual: u64 },
    
    #[error("MEV attack detected on {tx_hash}: {attack_type}")]
    MevAttackDetected { tx_hash: String, attack_type: String },
    
    #[error("Wallet access failed: {reason}")]
    WalletAccessFailed { reason: String },

    #[error("Trading halted: {0}")]
    TradingHalted(String),

    #[error("Nonce management error: {0}")]
    NonceManagement(String),

    #[error("Validation failed: {0}")]
    ValidationFailed(String),

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("RPC error: {0}")]
    RpcError(String),

    #[error("Pre-flight check failed: {0}")]
    PreFlightCheckFailed(String),

    #[error("Slippage calculation failed: {0}")]
    SlippageCalculationFailed(String),

    #[error("Transaction simulation failed: {reason}")]
    SimulationFailed { reason: String },

    #[error("Transaction submission failed: {reason}")]
    SubmissionFailed { reason: String },

    #[error("Slippage exceeded: expected {expected}%, actual {actual}%")]
    SlippageExceeded {
        expected: rust_decimal::Decimal,
        actual: rust_decimal::Decimal,
    },

    #[error("Deadline exceeded: transaction took {duration_ms}ms")]
    DeadlineExceeded { duration_ms: u64 },
}

impl ExecutionError {
    pub fn error_code(&self) -> ErrorCode {
        match self {
            ExecutionError::InsufficientLiquidity { .. } => ErrorCode::EInsufficientLiquidity,
            ExecutionError::HighSlippage { .. } => ErrorCode::EHighSlippage,
            ExecutionError::GasEstimationFailed { .. } => ErrorCode::EGasEstimationFailed,
            ExecutionError::TransactionReverted { .. } => ErrorCode::ETransactionReverted,
            ExecutionError::TransactionTimeout { .. } => ErrorCode::ETransactionTimeout,
            ExecutionError::NonceConflict { .. } => ErrorCode::ENonceConflict,
            ExecutionError::MevAttackDetected { .. } => ErrorCode::EMevAttackDetected,
            ExecutionError::WalletAccessFailed { .. } => ErrorCode::EWalletAccessFailed,
            ExecutionError::TradingHalted(_) => ErrorCode::ESystemOverloaded,
            ExecutionError::NonceManagement(_) => ErrorCode::ENonceConflict,
            ExecutionError::ValidationFailed(_) => ErrorCode::EDataValidationFailed,
            ExecutionError::InvalidInput(_) => ErrorCode::EDataValidationFailed,
            ExecutionError::RpcError(_) => ErrorCode::ERpcConnectionFailed,
            ExecutionError::PreFlightCheckFailed(_) => ErrorCode::EDataValidationFailed,
            ExecutionError::SlippageCalculationFailed(_) => ErrorCode::EHighSlippage,
            ExecutionError::SimulationFailed { .. } => ErrorCode::ETransactionReverted,
            ExecutionError::SubmissionFailed { .. } => ErrorCode::ETransactionReverted,
            ExecutionError::SlippageExceeded { .. } => ErrorCode::EHighSlippage,
            ExecutionError::DeadlineExceeded { .. } => ErrorCode::ETransactionTimeout,
        }
    }
}

/// Strategy and trading logic errors
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum StrategyError {
    #[error("Opportunity expired: {opportunity_id} after {age_ms}ms")]
    OpportunityExpired { opportunity_id: String, age_ms: u64 },
    
    #[error("Honeypot detected for {token_address}: {reason}")]
    HoneypotDetected { token_address: String, reason: String },
    
    #[error("Insufficient profit: {actual_usd} < {threshold_usd} for {opportunity_id}")]
    InsufficientProfit { 
        opportunity_id: String, 
        actual_usd: Decimal, 
        threshold_usd: Decimal 
    },
    
    #[error("No profitable path found from {token_in} to {token_out}")]
    PathNotFound { token_in: String, token_out: String },
    
    #[error("Risk limit exceeded: {metric} = {actual} > {limit}")]
    RiskLimitExceeded { metric: String, actual: Decimal, limit: Decimal },
    
    #[error("Circuit breaker tripped for {component}: {reason}")]
    CircuitBreakerTripped { component: String, reason: String },

    #[error("Scanner error in {scanner}: {message}")]
    Scanner { scanner: String, message: String },

    #[error("Opportunity validation failed: {reason}")]
    OpportunityValidation { reason: String },

    #[error("Pathfinding failed: {reason}")]
    Pathfinding { reason: String },

    #[error("Pricing calculation failed: {reason}")]
    Pricing { reason: String },
}

impl StrategyError {
    pub fn error_code(&self) -> ErrorCode {
        match self {
            StrategyError::OpportunityExpired { .. } => ErrorCode::EOpportunityExpired,
            StrategyError::HoneypotDetected { .. } => ErrorCode::EHoneypotDetected,
            StrategyError::InsufficientProfit { .. } => ErrorCode::EInsufficientProfit,
            StrategyError::PathNotFound { .. } => ErrorCode::EPathNotFound,
            StrategyError::RiskLimitExceeded { .. } => ErrorCode::EPositionSizeExceeded,
            StrategyError::CircuitBreakerTripped { .. } => ErrorCode::ECircuitBreakerTripped,
            StrategyError::Scanner { .. } => ErrorCode::EDataValidationFailed,
            StrategyError::OpportunityValidation { .. } => ErrorCode::EOpportunityExpired,
            StrategyError::Pathfinding { .. } => ErrorCode::EPathNotFound,
            StrategyError::Pricing { .. } => ErrorCode::EPriceOracleFailure,
        }
    }
}

/// Critical system errors that require immediate attention
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum CriticalError {
    #[error("Critical configuration error: {field} - {details}")]
    ConfigError { field: String, details: String },
    
    #[error("System overloaded: {metric} = {value} > {threshold}")]
    SystemOverloaded { metric: String, value: Decimal, threshold: Decimal },
    
    #[error("Unrecoverable error in {component}: {details}")]
    UnrecoverableError { component: String, details: String },
    
    #[error("Security violation detected: {violation_type} - {details}")]
    SecurityViolation { violation_type: String, details: String },
}

impl CriticalError {
    pub fn error_code(&self) -> ErrorCode {
        match self {
            CriticalError::ConfigError { .. } => ErrorCode::ECriticalConfigError,
            CriticalError::SystemOverloaded { .. } => ErrorCode::ESystemOverloaded,
            CriticalError::UnrecoverableError { .. } => ErrorCode::EUnrecoverableError,
            CriticalError::SecurityViolation { .. } => ErrorCode::ESecurityViolation,
        }
    }
}

/// Enhanced context for error reporting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    pub trace_id: TraceId,
    pub component: String,
    pub function: String,
    pub opportunity_id: Option<String>,
    pub chain_id: Option<u64>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub additional_data: std::collections::HashMap<String, serde_json::Value>,
}

impl ErrorContext {
    pub fn new(component: &str, function: &str) -> Self {
        Self {
            trace_id: TraceId::new(),
            component: component.to_string(),
            function: function.to_string(),
            opportunity_id: None,
            chain_id: None,
            timestamp: chrono::Utc::now(),
            additional_data: std::collections::HashMap::new(),
        }
    }
    
    pub fn with_opportunity(mut self, opportunity_id: &str) -> Self {
        self.opportunity_id = Some(opportunity_id.to_string());
        self.trace_id = TraceId::from_opportunity_id(opportunity_id);
        self
    }
    
    pub fn with_chain(mut self, chain_id: u64) -> Self {
        self.chain_id = Some(chain_id);
        self
    }
    
    pub fn with_data<T: serde::Serialize>(mut self, key: &str, value: T) -> Self {
        if let Ok(json_value) = serde_json::to_value(value) {
            self.additional_data.insert(key.to_string(), json_value);
        }
        self
    }
}

/// Central error types for the Basilisk Bot with enhanced context
#[derive(Error, Debug)]
pub enum BasiliskError {
    #[error("Network error: {0}")]
    Network(#[from] NetworkError),
    
    #[error("Data provider error: {0}")]
    DataProvider(#[from] DataProviderError),
    
    #[error("Execution error: {0}")]
    Execution(#[from] ExecutionError),
    
    #[error("Strategy error: {0}")]
    Strategy(#[from] StrategyError),
    
    #[error("Critical error: {0}")]
    Critical(#[from] CriticalError),
    
    // Legacy errors for backward compatibility
    #[error("Configuration error: {0}")]
    Config(#[from] config::ConfigError),
    
    #[error("Configuration error: {0}")]
    ConfigError(String),
    
    #[error("Invalid configuration: {field} - {value}")]
    InvalidConfig { field: String, value: String },
    
    #[error("File error: {path} - {message}")]
    FileError { path: String, message: String },
    
    #[error("Parse error: {message}")]
    ParseError { message: String },
    
    #[error("Serialization error: {message}")]
    SerializeError { message: String },
    
    #[error("URL parse error: {0}")]
    UrlParseError(#[from] url::ParseError),

    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("Redis error: {0}")]
    Redis(#[from] redis::RedisError),

    #[error("NATS error: {0}")]
    Nats(#[from] async_nats::Error),
    
    #[error("NATS connect error: {0}")]
    NatsConnect(String),

    #[error("RPC provider error while communicating with endpoint {endpoint_url}: {source}")]
    RpcError {
        endpoint_url: String,
        #[source]
        source: ethers::providers::ProviderError,
    },

    #[error("RPC provider error: {0}")]
    Rpc(#[from] ethers::providers::ProviderError),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("WebSocket error: {0}")]
    WebSocket(#[from] tokio_tungstenite::tungstenite::Error),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("NATS publish error: {0}")]
    NatsPublish(String),

    #[error("Risk management error: {message}")]
    Risk { message: String },

    #[error("Data ingestion error: {message}")]
    DataIngestion { message: String },

    #[error("Security check failed: {message}")]
    Security { message: String },

    #[error("Invalid state transition: {from} -> {to}")]
    InvalidStateTransition { from: String, to: String },

    #[error("Invalid opportunity type: {opportunity_type}, supported: {supported_types:?}")]
    InvalidOpportunityType { 
        opportunity_type: String,
        supported_types: Vec<String>
    },

    #[error("Timeout error: {operation}")]
    Timeout { operation: String },

    #[error("Insufficient liquidity: required {required_usd} USD, available {available_usd} USD")]
    InsufficientLiquidity {
        required_usd: rust_decimal::Decimal,
        available_usd: rust_decimal::Decimal,
    },

    #[error("Market conditions unfavorable: {reason}")]
    UnfavorableMarket { reason: String },

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("MEV error: {0}")]
    Mev(String),

    #[error("System time error: {0}")]
    SystemTimeError(String),

    #[error("Invalid address: {0}")]
    InvalidAddress(String),

    #[error("Stale data detected: {data_type} is {age_seconds}s old, threshold is {threshold_seconds}s")]
    StaleData {
        data_type: String,
        age_seconds: u64,
        threshold_seconds: u64,
    },

    #[error("Service degraded: {service_name} - {reason}")]
    ServiceDegraded {
        service_name: String,
        reason: String,
    },

    #[error("Circuit breaker open for {service}: {reason}")]
    CircuitBreakerOpen {
        service: String,
        reason: String,
    },

    #[error("External API error: {api_name} - {status_code:?}: {message}")]
    ExternalApiError {
        api_name: String,
        status_code: Option<u16>,
        message: String,
    },

    #[error("Transaction simulation failed: {reason}")]
    SimulationFailed {
        reason: String,
    },

    #[error("Nonce critically stuck: nonce {nonce}, attempts {attempts}")]
    NonceCriticallyStuck {
        nonce: u64,
        attempts: u32,
    },

    #[error("Numeric conversion error: {0}")]
    NumericConversionError(String),

    #[error("Lock acquisition failed: {0}")]
    LockAcquisitionFailed(String),
}

/// Specialized error types for different modules
#[derive(Error, Debug)]
pub enum ConfigError {
    #[error("Missing required configuration: {field}")]
    MissingField { field: String },

    #[error("Invalid configuration value for {field}: {value}")]
    InvalidValue { field: String, value: String },

    #[error("Configuration file not found: {path}")]
    FileNotFound { path: String },
}

#[derive(Error, Debug)]
pub enum RiskError {
    #[error("Position size exceeds limit: {size} > {limit}")]
    PositionSizeExceeded {
        size: rust_decimal::Decimal,
        limit: rust_decimal::Decimal,
    },

    #[error("Drawdown limit exceeded: {current}% > {limit}%")]
    DrawdownExceeded {
        current: rust_decimal::Decimal,
        limit: rust_decimal::Decimal,
    },

    #[error("Volatility too high: {volatility} > {threshold}")]
    VolatilityTooHigh {
        volatility: rust_decimal::Decimal,
        threshold: rust_decimal::Decimal,
    },

    #[error("Correlation risk detected: {assets:?}")]
    CorrelationRisk { assets: Vec<String> },
}

#[derive(Error, Debug)]
pub enum DataError {
    #[error("Data source unavailable: {0}")]
    SourceUnavailable(String),

    #[error("Data validation failed: {field} = {value}")]
    ValidationFailed { field: String, value: String },

    #[error("Data parsing failed: {format}")]
    ParsingFailed { format: String },

    #[error("Stale data detected: age {age_seconds}s > threshold {threshold_seconds}s")]
    StaleData {
        age_seconds: u64,
        threshold_seconds: u64,
    },
}

// Enhanced error modules
pub mod enhanced;
pub mod recovery;
pub mod metrics;
pub mod integration;
pub mod circuit_breaker;
pub mod propagation;

pub use enhanced::{
    ErrorContext as EnhancedErrorContext, EnhancedError, RetryManager, RetryConfig, 
    AlertManager, AlertMessage, AlertChannelConfig, AlertChannelType,
    ErrorHandler
};
pub use recovery::{
    RecoveryManager, FallbackStrategy, RecoveryAction
};
pub use circuit_breaker::{
    CircuitBreaker, CircuitBreakerConfig, CircuitState, CircuitBreakerError,
    CircuitBreakerRegistry
};
pub use propagation::{
    ErrorPropagationManager, default_alert_channels
};
pub use metrics::{
    ErrorMetricsCollector, ErrorMetrics, ErrorPattern, ErrorTrend,
    HealthReport, ErrorDashboard, ErrorImpactAnalyzer, TradingImpactReport
};
pub use integration::{
    IntegratedErrorSystem, IntegratedErrorConfig, SystemHealthStatus,
    ErrorHandlingStatistics, SystemAlert
};

/// Result type aliases for convenience
pub type Result<T> = std::result::Result<T, BasiliskError>;
pub type ConfigResult<T> = std::result::Result<T, ConfigError>;
pub type StrategyResult<T> = std::result::Result<T, StrategyError>;
pub type ExecutionResult<T> = std::result::Result<T, ExecutionError>;
pub type RiskResult<T> = std::result::Result<T, RiskError>;
pub type DataResult<T> = std::result::Result<T, DataError>;

/// Helper functions for creating common errors
impl BasiliskError {
    pub fn strategy(message: impl Into<String>) -> Self {
        Self::Strategy(StrategyError::Scanner { 
            scanner: "unknown".to_string(),
            message: message.into(),
        })
    }

    pub fn execution_error(message: impl Into<String>) -> Self {
        Self::Execution(ExecutionError::PreFlightCheckFailed(message.into()))
    }

    pub fn risk(message: impl Into<String>) -> Self {
        Self::Risk {
            message: message.into(),
        }
    }

    pub fn data_ingestion(message: impl Into<String>) -> Self {
        Self::DataIngestion {
            message: message.into(),
        }
    }

    pub fn security(message: impl Into<String>) -> Self {
        Self::Security {
            message: message.into(),
        }
    }

    pub fn timeout(operation: impl Into<String>) -> Self {
        Self::Timeout {
            operation: operation.into(),
        }
    }
}

impl StrategyError {
    pub fn scanner(scanner: impl Into<String>, message: impl Into<String>) -> Self {
        Self::Scanner {
            scanner: scanner.into(),
            message: message.into(),
        }
    }

    pub fn validation(reason: impl Into<String>) -> Self {
        Self::OpportunityValidation {
            reason: reason.into(),
        }
    }

    pub fn pathfinding(reason: impl Into<String>) -> Self {
        Self::Pathfinding {
            reason: reason.into(),
        }
    }

    pub fn pricing(reason: impl Into<String>) -> Self {
        Self::Pricing {
            reason: reason.into(),
        }
    }
}

impl ExecutionError {
    pub fn simulation_failed(reason: impl Into<String>) -> Self {
        Self::SimulationFailed {
            reason: reason.into(),
        }
    }

    pub fn submission_failed(reason: impl Into<String>) -> Self {
        Self::SubmissionFailed {
            reason: reason.into(),
        }
    }

    pub fn gas_estimation_failed(reason: impl Into<String>) -> Self {
        Self::GasEstimationFailed {
            operation: "unknown".to_string(),
            reason: reason.into(),
        }
    }
}

impl DataError {
    pub fn source_unavailable(source: impl Into<String>) -> Self {
        Self::SourceUnavailable(source.into())
    }

    pub fn validation_failed(field: impl Into<String>, value: impl Into<String>) -> Self {
        Self::ValidationFailed {
            field: field.into(),
            value: value.into(),
        }
    }

    pub fn parsing_failed(format: impl Into<String>) -> Self {
        Self::ParsingFailed {
            format: format.into(),
        }
    }
}

// Additional From implementations for common error types
impl From<async_nats::PublishError> for BasiliskError {
    fn from(err: async_nats::PublishError) -> Self {
        BasiliskError::NatsPublish(err.to_string())
    }
}

impl From<Box<dyn std::error::Error>> for BasiliskError {
    fn from(err: Box<dyn std::error::Error>) -> Self {
        BasiliskError::Execution(ExecutionError::PreFlightCheckFailed(err.to_string()))
    }
}

impl From<String> for BasiliskError {
    fn from(err: String) -> Self {
        BasiliskError::Strategy(StrategyError::Scanner { 
            scanner: "string_conversion".to_string(),
            message: err 
        })
    }
}

impl From<chrono::ParseError> for BasiliskError {
    fn from(err: chrono::ParseError) -> Self {
        BasiliskError::Strategy(StrategyError::Scanner { 
            scanner: "chrono_parser".to_string(),
            message: format!("Date parsing error: {}", err) 
        })
    }
}

impl From<crate::execution::nonce_manager::NonceManagerError> for BasiliskError {
    fn from(err: crate::execution::nonce_manager::NonceManagerError) -> Self {
        BasiliskError::Execution(ExecutionError::NonceManagement(err.to_string()))
    }
}

impl From<anyhow::Error> for BasiliskError {
    fn from(err: anyhow::Error) -> Self {
        BasiliskError::Execution(ExecutionError::PreFlightCheckFailed(err.to_string()))
    }
}
