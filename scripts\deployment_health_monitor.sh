#!/bin/bash
# Deployment health monitoring script for Aetheric Resonance Engine fixes
# This script continuously monitors deployment health and triggers alerts/rollbacks

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/health_monitor_$(date +%Y%m%d_%H%M%S).log"
CONFIG_FILE="$PROJECT_ROOT/config/deployment.toml"
PID_FILE="$PROJECT_ROOT/logs/health_monitor.pid"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        WARN)  echo -e "${YELLOW}[WARN]${NC} $message" | tee -a "$LOG_FILE" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        DEBUG) echo -e "${BLUE}[DEBUG]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
}

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

# Alert thresholds (can be overridden by config)
ERROR_RATE_THRESHOLD=5.0
RESPONSE_TIME_THRESHOLD=1000
MEMORY_USAGE_THRESHOLD=80.0
CPU_USAGE_THRESHOLD=80.0
DISK_USAGE_THRESHOLD=85.0

# Monitoring configuration
MONITORING_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
CONSECUTIVE_FAILURES_THRESHOLD=3
AUTO_ROLLBACK_ENABLED=true

# Load configuration from file
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        if command -v toml &> /dev/null; then
            ERROR_RATE_THRESHOLD=$(toml get "$CONFIG_FILE" "monitoring_config.alert_thresholds.error_rate_percentage" 2>/dev/null || echo "$ERROR_RATE_THRESHOLD")
            RESPONSE_TIME_THRESHOLD=$(toml get "$CONFIG_FILE" "monitoring_config.alert_thresholds.response_time_ms" 2>/dev/null || echo "$RESPONSE_TIME_THRESHOLD")
            MEMORY_USAGE_THRESHOLD=$(toml get "$CONFIG_FILE" "monitoring_config.alert_thresholds.memory_usage_percentage" 2>/dev/null || echo "$MEMORY_USAGE_THRESHOLD")
            CPU_USAGE_THRESHOLD=$(toml get "$CONFIG_FILE" "monitoring_config.alert_thresholds.cpu_usage_percentage" 2>/dev/null || echo "$CPU_USAGE_THRESHOLD")
            DISK_USAGE_THRESHOLD=$(toml get "$CONFIG_FILE" "monitoring_config.alert_thresholds.disk_usage_percentage" 2>/dev/null || echo "$DISK_USAGE_THRESHOLD")
            MONITORING_INTERVAL=$(toml get "$CONFIG_FILE" "monitoring_config.metrics_collection_interval_seconds" 2>/dev/null || echo "$MONITORING_INTERVAL")
            AUTO_ROLLBACK_ENABLED=$(toml get "$CONFIG_FILE" "emergency.auto_rollback_on_critical_alerts" 2>/dev/null || echo "$AUTO_ROLLBACK_ENABLED")
        fi
    fi
    
    log INFO "Loaded monitoring configuration:"
    log INFO "  Error rate threshold: ${ERROR_RATE_THRESHOLD}%"
    log INFO "  Response time threshold: ${RESPONSE_TIME_THRESHOLD}ms"
    log INFO "  Memory usage threshold: ${MEMORY_USAGE_THRESHOLD}%"
    log INFO "  CPU usage threshold: ${CPU_USAGE_THRESHOLD}%"
    log INFO "  Disk usage threshold: ${DISK_USAGE_THRESHOLD}%"
    log INFO "  Monitoring interval: ${MONITORING_INTERVAL}s"
    log INFO "  Auto rollback enabled: $AUTO_ROLLBACK_ENABLED"
}

# Function to collect system metrics
collect_system_metrics() {
    local metrics_file="$PROJECT_ROOT/logs/metrics_$(date +%Y%m%d_%H%M%S).json"
    
    # CPU usage
    local cpu_usage
    if command -v top &> /dev/null; then
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | cut -d',' -f1 || echo "0")
    else
        cpu_usage="0"
    fi
    
    # Memory usage
    local memory_usage
    if command -v free &> /dev/null; then
        memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}' || echo "0")
    else
        memory_usage="0"
    fi
    
    # Disk usage
    local disk_usage
    if command -v df &> /dev/null; then
        disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1 || echo "0")
    else
        disk_usage="0"
    fi
    
    # Application metrics
    local error_rate=$(get_error_rate)
    local response_time=$(get_response_time)
    local requests_per_second=$(get_requests_per_second)
    
    # Create metrics JSON
    cat > "$metrics_file" << EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "system": {
        "cpu_usage_percentage": $cpu_usage,
        "memory_usage_percentage": $memory_usage,
        "disk_usage_percentage": $disk_usage
    },
    "application": {
        "error_rate_percentage": $error_rate,
        "avg_response_time_ms": $response_time,
        "requests_per_second": $requests_per_second
    }
}
EOF
    
    echo "$metrics_file"
}

# Function to get error rate
get_error_rate() {
    # In a real implementation, this would query application logs or metrics
    # For now, simulate by checking recent log entries
    local error_count=0
    local total_count=100
    
    if [[ -f "$PROJECT_ROOT/logs/service.log" ]]; then
        # Count errors in last 100 log lines
        error_count=$(tail -100 "$PROJECT_ROOT/logs/service.log" | grep -i "error\|failed\|exception" | wc -l || echo "0")
        total_count=$(tail -100 "$PROJECT_ROOT/logs/service.log" | wc -l || echo "100")
    fi
    
    if [[ $total_count -eq 0 ]]; then
        echo "0"
    else
        echo "scale=2; $error_count * 100 / $total_count" | bc
    fi
}

# Function to get response time
get_response_time() {
    # Test response time to health endpoint
    if command -v curl &> /dev/null; then
        local start_time=$(date +%s%3N)
        if curl -f -s http://localhost:8080/health > /dev/null 2>&1; then
            local end_time=$(date +%s%3N)
            echo $((end_time - start_time))
        else
            echo "5000" # Return high value if endpoint is not responding
        fi
    else
        echo "0"
    fi
}

# Function to get requests per second
get_requests_per_second() {
    # In a real implementation, this would query application metrics
    # For simulation, return a reasonable value
    echo "$(shuf -i 50-200 -n 1)"
}

# Function to run health checks
run_health_checks() {
    local health_status=0
    
    # Check if service is running (look for basilisk or health_server)
    if ! pgrep -f "basilisk\|health_server" > /dev/null; then
        log ERROR "Service process not found (looking for basilisk or health_server)"
        health_status=1
    fi
    
    # Check health endpoint
    if ! timeout "$HEALTH_CHECK_TIMEOUT" curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log ERROR "Health endpoint check failed"
        health_status=1
    fi
    
    # Check metrics endpoint
    if ! timeout "$HEALTH_CHECK_TIMEOUT" curl -f http://localhost:8080/metrics > /dev/null 2>&1; then
        log ERROR "Metrics endpoint check failed"
        health_status=1
    fi
    
    # Check database connectivity (if applicable)
    if command -v psql &> /dev/null; then
        if ! timeout "$HEALTH_CHECK_TIMEOUT" psql -h localhost -U basilisk_bot -d basilisk_db -c "SELECT 1;" > /dev/null 2>&1; then
            log WARN "Database connectivity check failed"
            # Don't fail health check for database issues, just warn
        fi
    fi
    
    # Check Redis connectivity (if applicable)
    if command -v redis-cli &> /dev/null; then
        if ! timeout "$HEALTH_CHECK_TIMEOUT" redis-cli ping > /dev/null 2>&1; then
            log WARN "Redis connectivity check failed"
            # Don't fail health check for Redis issues, just warn
        fi
    fi
    
    return $health_status
}

# Function to analyze metrics and trigger alerts
analyze_metrics() {
    local metrics_file=$1
    local alerts_triggered=0
    
    if [[ ! -f "$metrics_file" ]]; then
        log ERROR "Metrics file not found: $metrics_file"
        return 1
    fi
    
    # Parse metrics (simplified JSON parsing)
    local cpu_usage=$(grep "cpu_usage_percentage" "$metrics_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
    local memory_usage=$(grep "memory_usage_percentage" "$metrics_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
    local disk_usage=$(grep "disk_usage_percentage" "$metrics_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
    local error_rate=$(grep "error_rate_percentage" "$metrics_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
    local response_time=$(grep "avg_response_time_ms" "$metrics_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
    
    # Check thresholds
    if (( $(echo "$error_rate > $ERROR_RATE_THRESHOLD" | bc -l) )); then
        log ERROR "ALERT: High error rate detected: ${error_rate}% (threshold: ${ERROR_RATE_THRESHOLD}%)"
        send_alert "high_error_rate" "Error rate: ${error_rate}%" "critical"
        alerts_triggered=1
    fi
    
    if (( $(echo "$response_time > $RESPONSE_TIME_THRESHOLD" | bc -l) )); then
        log WARN "ALERT: High response time detected: ${response_time}ms (threshold: ${RESPONSE_TIME_THRESHOLD}ms)"
        send_alert "high_response_time" "Response time: ${response_time}ms" "warning"
        alerts_triggered=1
    fi
    
    if (( $(echo "$memory_usage > $MEMORY_USAGE_THRESHOLD" | bc -l) )); then
        log WARN "ALERT: High memory usage detected: ${memory_usage}% (threshold: ${MEMORY_USAGE_THRESHOLD}%)"
        send_alert "high_memory_usage" "Memory usage: ${memory_usage}%" "warning"
        alerts_triggered=1
    fi
    
    if (( $(echo "$cpu_usage > $CPU_USAGE_THRESHOLD" | bc -l) )); then
        log WARN "ALERT: High CPU usage detected: ${cpu_usage}% (threshold: ${CPU_USAGE_THRESHOLD}%)"
        send_alert "high_cpu_usage" "CPU usage: ${cpu_usage}%" "warning"
        alerts_triggered=1
    fi
    
    if (( $(echo "$disk_usage > $DISK_USAGE_THRESHOLD" | bc -l) )); then
        log ERROR "ALERT: High disk usage detected: ${disk_usage}% (threshold: ${DISK_USAGE_THRESHOLD}%)"
        send_alert "high_disk_usage" "Disk usage: ${disk_usage}%" "critical"
        alerts_triggered=1
    fi
    
    return $alerts_triggered
}

# Function to send alerts
send_alert() {
    local alert_type=$1
    local message=$2
    local severity=$3
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    
    # Log alert
    log ERROR "ALERT [$severity]: $alert_type - $message"
    
    # Write alert to file
    local alert_file="$PROJECT_ROOT/logs/alerts.log"
    echo "[$timestamp] [$severity] $alert_type: $message" >> "$alert_file"
    
    # Send webhook notification (if configured)
    local webhook_url=""
    if [[ -f "$CONFIG_FILE" ]] && command -v toml &> /dev/null; then
        webhook_url=$(toml get "$CONFIG_FILE" "emergency.emergency_contact_webhook" 2>/dev/null | tr -d '"' || echo "")
    fi
    
    if [[ -n "$webhook_url" ]] && command -v curl &> /dev/null; then
        local payload="{\"alert_type\":\"$alert_type\",\"message\":\"$message\",\"severity\":\"$severity\",\"timestamp\":\"$timestamp\"}"
        curl -X POST -H "Content-Type: application/json" -d "$payload" "$webhook_url" > /dev/null 2>&1 || true
    fi
    
    # Send email notification (if configured)
    # This would require additional configuration and mail setup
    
    # Trigger auto-rollback for critical alerts
    if [[ "$severity" == "critical" ]] && [[ "$AUTO_ROLLBACK_ENABLED" == "true" ]]; then
        log WARN "Critical alert detected, triggering auto-rollback"
        trigger_auto_rollback "$alert_type" "$message"
    fi
}

# Function to trigger automatic rollback
trigger_auto_rollback() {
    local alert_type=$1
    local message=$2
    
    log WARN "Triggering automatic rollback due to: $alert_type - $message"
    
    # Get current phase
    local current_phase="development"
    if [[ -f "$CONFIG_FILE" ]] && command -v toml &> /dev/null; then
        current_phase=$(toml get "$CONFIG_FILE" "deployment.current_phase" 2>/dev/null | tr -d '"' || echo "development")
    fi
    
    # Determine rollback target (previous phase)
    local rollback_target=""
    case $current_phase in
        "core-scoring") rollback_target="development" ;;
        "mathematical-components") rollback_target="core-scoring" ;;
        "component-integration") rollback_target="mathematical-components" ;;
        "data-quality") rollback_target="component-integration" ;;
        "configuration-monitoring") rollback_target="data-quality" ;;
        "full-production") rollback_target="configuration-monitoring" ;;
        *) rollback_target="development" ;;
    esac
    
    if [[ -n "$rollback_target" ]]; then
        log WARN "Initiating automatic rollback from $current_phase to $rollback_target"
        
        # Execute rollback
        if "$SCRIPT_DIR/rollback_are_fixes.sh" rollback "$rollback_target"; then
            log INFO "Automatic rollback completed successfully"
            send_alert "auto_rollback_success" "Rollback from $current_phase to $rollback_target completed" "info"
        else
            log ERROR "Automatic rollback failed"
            send_alert "auto_rollback_failure" "Rollback from $current_phase to $rollback_target failed" "critical"
        fi
    else
        log WARN "No rollback target available for current phase: $current_phase"
    fi
}

# Function to start monitoring daemon
start_monitoring() {
    # Check if already running
    if [[ -f "$PID_FILE" ]] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
        log ERROR "Health monitor is already running (PID: $(cat "$PID_FILE"))"
        return 1
    fi
    
    log INFO "Starting deployment health monitoring daemon"
    
    # Load configuration
    load_config
    
    # Start monitoring loop in background
    (
        echo $$ > "$PID_FILE"
        trap 'rm -f "$PID_FILE"; exit' INT TERM EXIT
        
        local consecutive_failures=0
        
        while true; do
            log DEBUG "Running health monitoring cycle"
            
            # Collect metrics
            local metrics_file
            metrics_file=$(collect_system_metrics)
            
            # Run health checks
            if run_health_checks; then
                log DEBUG "Health checks passed"
                consecutive_failures=0
            else
                log WARN "Health checks failed"
                consecutive_failures=$((consecutive_failures + 1))
                
                if [[ $consecutive_failures -ge $CONSECUTIVE_FAILURES_THRESHOLD ]]; then
                    log ERROR "Health checks failed $consecutive_failures consecutive times"
                    send_alert "consecutive_health_failures" "Health checks failed $consecutive_failures times" "critical"
                fi
            fi
            
            # Analyze metrics
            if analyze_metrics "$metrics_file"; then
                log DEBUG "Metrics analysis completed with alerts"
            else
                log DEBUG "Metrics analysis completed without alerts"
            fi
            
            # Clean up old metrics files (keep last 24 hours)
            find "$PROJECT_ROOT/logs" -name "metrics_*.json" -mtime +1 -delete 2>/dev/null || true
            
            # Wait for next cycle
            sleep "$MONITORING_INTERVAL"
        done
    ) &
    
    local monitor_pid=$!
    echo $monitor_pid > "$PID_FILE"
    
    log INFO "Health monitoring daemon started (PID: $monitor_pid)"
    return 0
}

# Function to stop monitoring daemon
stop_monitoring() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log INFO "Stopping health monitoring daemon (PID: $pid)"
            kill "$pid"
            rm -f "$PID_FILE"
            log INFO "Health monitoring daemon stopped"
        else
            log WARN "Health monitoring daemon not running (stale PID file)"
            rm -f "$PID_FILE"
        fi
    else
        log WARN "Health monitoring daemon not running (no PID file)"
    fi
}

# Function to show monitoring status
show_status() {
    echo
    echo "=== Deployment Health Monitor Status ==="
    
    if [[ -f "$PID_FILE" ]] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
        echo "Status: Running (PID: $(cat "$PID_FILE"))"
    else
        echo "Status: Stopped"
    fi
    
    echo "Configuration:"
    echo "  Error rate threshold: ${ERROR_RATE_THRESHOLD}%"
    echo "  Response time threshold: ${RESPONSE_TIME_THRESHOLD}ms"
    echo "  Monitoring interval: ${MONITORING_INTERVAL}s"
    echo "  Auto rollback enabled: $AUTO_ROLLBACK_ENABLED"
    echo
    
    # Show recent alerts
    local alert_file="$PROJECT_ROOT/logs/alerts.log"
    if [[ -f "$alert_file" ]]; then
        echo "Recent Alerts (last 10):"
        tail -10 "$alert_file" | sed 's/^/  /'
    else
        echo "Recent Alerts: None"
    fi
    echo
}

# Function to test alerts
test_alerts() {
    log INFO "Testing alert system"
    
    send_alert "test_alert" "This is a test alert" "info"
    
    log INFO "Test alert sent"
}

# Main function
main() {
    local command=${1:-"status"}
    
    case $command in
        "start")
            start_monitoring
            ;;
        "stop")
            stop_monitoring
            ;;
        "restart")
            stop_monitoring
            sleep 2
            start_monitoring
            ;;
        "status")
            load_config
            show_status
            ;;
        "test")
            load_config
            test_alerts
            ;;
        "check")
            load_config
            log INFO "Running one-time health check"
            if run_health_checks; then
                log INFO "Health check passed"
            else
                log ERROR "Health check failed"
                exit 1
            fi
            ;;
        "metrics")
            load_config
            log INFO "Collecting current metrics"
            local metrics_file
            metrics_file=$(collect_system_metrics)
            log INFO "Metrics collected: $metrics_file"
            cat "$metrics_file"
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|test|check|metrics}"
            echo
            echo "Commands:"
            echo "  start    - Start health monitoring daemon"
            echo "  stop     - Stop health monitoring daemon"
            echo "  restart  - Restart health monitoring daemon"
            echo "  status   - Show monitoring status"
            echo "  test     - Test alert system"
            echo "  check    - Run one-time health check"
            echo "  metrics  - Collect and display current metrics"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"