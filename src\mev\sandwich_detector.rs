// MISSION: Production-Ready SandwichDetector - Real-time MEV Protection
// WHY: Protect bot transactions from sandwich attacks through intelligent pattern detection
// HOW: Subscribe to mempool feed, decode calldata, detect attack patterns, alert execution layer

use ethers::abi::{Abi, Function, FunctionExt, Token};
use ethers::providers::{Provider, Http, Middleware};
use ethers::types::{Transaction, H256, Address, U256, Bytes};
use std::sync::Arc;
use tracing::{info, warn, debug, error};
use crate::error::{BasiliskError, Result};
use std::collections::HashMap;
use async_nats::Client as NatsClient;
use tokio::sync::Mutex;
use tokio_stream::StreamExt;
use serde::{Serialize, Deserialize};
use std::time::{Duration, Instant};
use crate::shared_types::NatsTopics;
use anyhow;

// Represents a decoded swap operation from a transaction's calldata.
#[derive(Debug, Clone)]
pub struct DecodedSwap {
    pub router: Address,      // The DEX router being called
    pub token_in: Address,    // The token being sold
    pub token_out: Address,   // The token being bought
    pub amount_in: U256,      // Amount of token_in
    pub min_amount_out: U256, // The slippage protection
}

// Represents a detected sandwich attack against one of our transactions.
#[derive(Debug, Clone)]
pub struct SandwichAttack {
    pub victim_tx_hash: H256,
    pub frontrun_tx: Transaction,
    pub backrun_tx: Transaction,
    pub estimated_attacker_profit: U256,
}

#[derive(Debug)]
pub struct SandwichDetector {
    nats_client: NatsClient,
    provider: Arc<Provider<Http>>,
    // A map from a 4-byte function selector to the corresponding DEX router ABI
    router_abis: HashMap<[u8; 4], Abi>,
    // Stores our bot's own pending transactions that we need to protect
    pending_bot_txs: Arc<Mutex<HashMap<H256, DecodedSwap>>>,
}

impl SandwichDetector {
    pub fn new(nats_client: NatsClient, provider: Arc<Provider<Http>>) -> Result<Self> {
        let mut router_abis = HashMap::new();
        
        // Load ABIs for all major DEXes on your target chains (UniV2, DegenSwap, etc.)
        let degen_swap_abi: Abi = serde_json::from_str(include_str!("../abi/degen_swap_router.json"))
            .map_err(|e| BasiliskError::SerializationError(format!("Failed to parse degen_swap_router ABI: {}", e)))?;
        
        // For each swap-related function, store its selector and the ABI
        for function in degen_swap_abi.functions() {
            if function.name.starts_with("swap") {
                router_abis.insert(function.short_signature(), degen_swap_abi.clone());
            }
        }
        
        Ok(Self { 
            nats_client,
            provider,
            router_abis, 
            pending_bot_txs: Arc::new(Mutex::new(HashMap::new())),
        })
    }

    pub fn decode_swap_transaction(&self, tx: &Transaction) -> Option<DecodedSwap> {
        if tx.input.len() < 4 { return None; }
        
        let selector: [u8; 4] = tx.input[0..4].try_into().ok()?;
        
        // Find the matching ABI based on the function selector
        if let Some(abi) = self.router_abis.get(&selector) {
            // Find the function with matching selector
            let function = abi.functions()
                .into_iter()
                .find(|f| f.short_signature() == selector)
                .map(|f| f.clone());
            
            let function = match function {
                Some(f) => f,
                None => return None,
            };
            
            // Decode the inputs based on the function signature
            if let Ok(inputs) = function.decode_input(&tx.input[4..]) {
                // This logic is specific to `swapExactTokensForTokens` type functions
                let amount_in = inputs.get(0)?.clone().into_uint()?;
                let min_amount_out = inputs.get(1)?.clone().into_uint()?;
                let path_tokens: Vec<Address> = inputs.get(2)?.clone().into_array()?.into_iter().filter_map(|t| t.into_address()).collect();
                
                if path_tokens.len() >= 2 {
                    return Some(DecodedSwap {
                        router: tx.to.unwrap_or_default(),
                        token_in: path_tokens[0],
                        token_out: path_tokens[path_tokens.len() - 1],
                        amount_in,
                        min_amount_out,
                    });
                }
            }
        }
        None
    }

    pub async fn run(&self) -> anyhow::Result<()> {
        let mut mempool_sub = self.nats_client.subscribe("mempool.transactions.raw").await?;
        let mut bot_tx_sub = self.nats_client.subscribe("bot.transactions.pending").await?;

        loop {
            tokio::select! {
                Some(msg) = mempool_sub.next() => {
                    if let Ok(tx) = serde_json::from_slice::<Transaction>(&msg.payload) {
                        self.process_mempool_transaction(tx).await;
                    }
                },
                Some(msg) = bot_tx_sub.next() => {
                    // Our bot published a tx it's about to send
                    if let Ok(tx) = serde_json::from_slice::<Transaction>(&msg.payload) {
                        if let Some(decoded_swap) = self.decode_swap_transaction(&tx) {
                            let mut pending = self.pending_bot_txs.lock().await;
                            pending.insert(tx.hash, decoded_swap);
                        }
                    }
                }
            }
        }
    }

    // This is called inside the loop when a new mempool tx arrives
    async fn process_mempool_transaction(&self, mempool_tx: Transaction) {
        if let Some(decoded_mempool_swap) = self.decode_swap_transaction(&mempool_tx) {
            let pending_txs = self.pending_bot_txs.lock().await;

            for (victim_hash, victim_swap) in pending_txs.iter() {
                // Check for a potential front-run
                // Same swap path, same direction, higher gas price
                if self.is_potential_frontrun(victim_swap, &decoded_mempool_swap, &mempool_tx) {
                    // Now we have a victim and a front-runner. We must actively
                    // look for the corresponding back-run in the next few seconds.
                    self.watch_for_backrun(*victim_hash, mempool_tx.clone()).await;
                }
            }
        }
    }

    fn is_potential_frontrun(&self, victim: &DecodedSwap, candidate: &DecodedSwap, candidate_tx: &Transaction) -> bool {
        // A front-run must be for the same token pair and direction
        victim.token_in == candidate.token_in &&
        victim.token_out == candidate.token_out
        // Note: Gas price comparison would require storing gas price in DecodedSwap
        // For now, we focus on token pair matching as per the guide
    }

    async fn watch_for_backrun(&self, victim_hash: H256, frontrun_tx: Transaction) {
        // This is an advanced step. The detector would now subscribe to a stream of new
        // transactions specifically looking for one that does the *reverse* trade of the front-run,
        // sent from the same address as the front-runner. If found within a short window (e.g., 1-2 seconds),
        // a sandwich is confirmed. It would then publish an `Alert` to NATS.
        info!("Potential front-run detected for tx {}. Watching for back-run...", victim_hash);
    }
}