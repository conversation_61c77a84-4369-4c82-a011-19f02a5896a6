// Comprehensive test suite for Hurst Exponent and Market Character Analysis
// This test suite validates the findings from the audit report

use std::collections::VecDeque;
use chrono::{DateTime, Utc, Duration};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use rust_decimal::prelude::{ToPrimitive, FromPrimitive};
use rand::Rng;

// Mock structures to test the implementation
#[derive(Debug, <PERSON>lone)]
struct PricePoint {
    price: Decimal,
    timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone)]
struct KalmanState {
    state_estimate: Decimal,
    error_covariance: Decimal,
    process_noise: Decimal,
    measurement_noise: Decimal,
}

#[derive(Debug, <PERSON><PERSON>, PartialEq)]
enum MarketCharacter {
    Trending,
    MeanReverting,
    RandomWalk,
}

struct HurstExponentTester {
    kalman_state: Option<KalmanState>,
}

impl HurstExponentTester {
    fn new() -> Self {
        Self {
            kalman_state: None,
        }
    }

    // Replicated implementation from fractal_analyzer.rs for testing
    fn calculate_hurst_exponent(&self, price_history: &VecDeque<PricePoint>) -> Decimal {
        if price_history.len() < 20 {
            return Decimal::new(5, 1); // Default to 0.5 (random walk)
        }

        // Convert prices to log returns
        let mut log_returns = Vec::new();
        for window in price_history.iter().collect::<Vec<_>>().windows(2) {
            let prev_price = window[0].price;
            let curr_price = window[1].price;

            if prev_price > Decimal::ZERO && curr_price > Decimal::ZERO {
                let price_ratio = curr_price / prev_price;
                if let (Some(ratio_f64), Some(log_return_f64)) = (price_ratio.to_f64(), price_ratio.to_f64().map(|x| x.ln())) {
                    if let Ok(log_return) = Decimal::try_from(log_return_f64) {
                        log_returns.push(log_return);
                    }
                }
            }
        }

        if log_returns.len() < 10 {
            return Decimal::new(5, 1);
        }

        // Calculate R/S statistic for different time scales
        let mut rs_values = Vec::new();
        let mut time_scales = Vec::new();

        for n in [5, 10, 15, 20].iter() {
            if log_returns.len() >= *n {
                let rs = self.calculate_rs_statistic(&log_returns[0..*n]);
                if rs > Decimal::ZERO {
                    if let (Some(rs_f64), Some(n_f64)) = (rs.to_f64(), Some(*n as f64)) {
                        if let (Some(rs_ln), Some(n_ln)) = (Decimal::from_f64(rs_f64.ln()), Decimal::from_f64(n_f64.ln())) {
                            rs_values.push(rs_ln);
                            time_scales.push(n_ln);
                        }
                    }
                }
            }
        }

        if rs_values.len() < 2 {
            return Decimal::new(5, 1);
        }

        // Calculate slope of log(R/S) vs log(n) - this is the Hurst exponent
        let hurst = self.calculate_slope(&time_scales, &rs_values);

        // Clamp to reasonable bounds
        hurst.max(Decimal::new(1, 1)).min(Decimal::new(9, 1)) // 0.1 to 0.9
    }

    fn calculate_rs_statistic(&self, returns: &[Decimal]) -> Decimal {
        if returns.is_empty() {
            return Decimal::ZERO;
        }

        let n = Decimal::from(returns.len());
        let mean = returns.iter().sum::<Decimal>() / n;

        // Calculate cumulative deviations from mean
        let mut cumulative_deviations = Vec::new();
        let mut cumsum = Decimal::ZERO;

        for &ret in returns {
            cumsum += ret - mean;
            cumulative_deviations.push(cumsum);
        }

        // Calculate range
        let max_dev = cumulative_deviations
            .iter()
            .max()
            .copied()
            .unwrap_or(Decimal::ZERO);
        let min_dev = cumulative_deviations
            .iter()
            .min()
            .copied()
            .unwrap_or(Decimal::ZERO);
        let range = max_dev - min_dev;

        // Calculate standard deviation (ISSUE: Uses biased estimator)
        let variance = returns
            .iter()
            .map(|&ret| (ret - mean) * (ret - mean))
            .sum::<Decimal>()
            / n; // AUDIT FINDING: Should be / (n - 1) for unbiased estimator
        let std_dev = if let Some(variance_f64) = variance.to_f64() {
            Decimal::from_f64(variance_f64.sqrt()).unwrap_or(Decimal::ZERO)
        } else {
            Decimal::ZERO
        };

        if std_dev > Decimal::ZERO {
            range / std_dev
        } else {
            Decimal::ZERO
        }
    }

    fn calculate_slope(&self, x_values: &[Decimal], y_values: &[Decimal]) -> Decimal {
        if x_values.len() != y_values.len() || x_values.len() < 2 {
            return Decimal::new(5, 1); // Default to 0.5
        }

        let n = Decimal::from(x_values.len());
        let sum_x = x_values.iter().sum::<Decimal>();
        let sum_y = y_values.iter().sum::<Decimal>();
        let sum_xy = x_values
            .iter()
            .zip(y_values.iter())
            .map(|(&x, &y)| x * y)
            .sum::<Decimal>();
        let sum_x_squared = x_values.iter().map(|&x| x * x).sum::<Decimal>();

        let denominator = n * sum_x_squared - sum_x * sum_x;

        if denominator != Decimal::ZERO {
            (n * sum_xy - sum_x * sum_y) / denominator
        } else {
            Decimal::new(5, 1)
        }
    }

    fn classify_market_character(&self, hurst_exponent: Decimal) -> MarketCharacter {
        let threshold_low = Decimal::new(45, 2); // 0.45
        let threshold_high = Decimal::new(55, 2); // 0.55

        if hurst_exponent < threshold_low {
            MarketCharacter::MeanReverting
        } else if hurst_exponent > threshold_high {
            MarketCharacter::Trending
        } else {
            MarketCharacter::RandomWalk
        }
    }

    fn apply_kalman_filter(&mut self, raw_price: Decimal) -> Result<Decimal, String> {
        // Initialize state if needed
        if self.kalman_state.is_none() {
            self.kalman_state = Some(KalmanState {
                state_estimate: raw_price,
                error_covariance: Decimal::ONE,
                process_noise: dec!(0.01),
                measurement_noise: dec!(0.05),
            });
            return Ok(raw_price);
        }
        
        let state = self.kalman_state.as_mut()
            .ok_or_else(|| "Kalman filter not initialized".to_string())?;
        
        // Prediction step
        let predicted_state = state.state_estimate;
        let predicted_error_covariance = state.error_covariance + state.process_noise;
        
        // Update step
        let kalman_gain = predicted_error_covariance / 
                         (predicted_error_covariance + state.measurement_noise);
        
        let updated_state = predicted_state + kalman_gain * (raw_price - predicted_state);
        let updated_error_covariance = (Decimal::ONE - kalman_gain) * predicted_error_covariance;
        
        // Update state
        state.state_estimate = updated_state;
        state.error_covariance = updated_error_covariance;
        
        Ok(updated_state)
    }
}

// Test data generators
fn generate_trending_data(length: usize, trend: f64) -> VecDeque<PricePoint> {
    let mut data = VecDeque::new();
    let base_time = Utc::now();
    let mut price = 100.0;
    
    for i in 0..length {
        price += trend + (i as f64 * 0.01); // Linear trend with small noise
        data.push_back(PricePoint {
            price: Decimal::from_f64(price).unwrap(),
            timestamp: base_time + Duration::minutes(i as i64),
        });
    }
    data
}

fn generate_mean_reverting_data(length: usize, mean: f64, reversion_strength: f64) -> VecDeque<PricePoint> {
    let mut data = VecDeque::new();
    let base_time = Utc::now();
    let mut price = mean;
    
    for i in 0..length {
        // Mean reversion: price moves back toward mean
        let deviation = price - mean;
        let mut rng = rand::thread_rng();
        price = price - (deviation * reversion_strength) + (rng.gen::<f64>() - 0.5) * 0.1;
        
        data.push_back(PricePoint {
            price: Decimal::from_f64(price).unwrap(),
            timestamp: base_time + Duration::minutes(i as i64),
        });
    }
    data
}

fn generate_random_walk_data(length: usize) -> VecDeque<PricePoint> {
    let mut data = VecDeque::new();
    let base_time = Utc::now();
    let mut price = 100.0;
    
    for i in 0..length {
        let mut rng = rand::thread_rng();
        price += (rng.gen::<f64>() - 0.5) * 0.2; // Pure random walk
        data.push_back(PricePoint {
            price: Decimal::from_f64(price).unwrap(),
            timestamp: base_time + Duration::minutes(i as i64),
        });
    }
    data
}

fn generate_constant_data(length: usize, value: f64) -> VecDeque<PricePoint> {
    let mut data = VecDeque::new();
    let base_time = Utc::now();
    
    for i in 0..length {
        data.push_back(PricePoint {
            price: Decimal::from_f64(value).unwrap(),
            timestamp: base_time + Duration::minutes(i as i64),
        });
    }
    data
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_insufficient_data_handling() {
        let tester = HurstExponentTester::new();
        
        // Test with insufficient data (< 20 points)
        let short_data = generate_trending_data(10, 0.1);
        let hurst = tester.calculate_hurst_exponent(&short_data);
        
        // AUDIT FINDING: Should return 0.5 for insufficient data
        assert_eq!(hurst, dec!(0.5));
        println!("✓ Insufficient data handling: Returns default 0.5");
    }

    #[test]
    fn test_minimum_data_requirements() {
        let tester = HurstExponentTester::new();
        
        // Test with exactly minimum data (20 points)
        let min_data = generate_trending_data(20, 0.1);
        let hurst = tester.calculate_hurst_exponent(&min_data);
        
        // AUDIT FINDING: 20 points is insufficient for reliable R/S analysis
        println!("⚠️  Minimum data test (20 points): Hurst = {:.3}", hurst);
        println!("   ISSUE: 20 points insufficient for reliable R/S analysis");
        
        // Test with recommended minimum (100 points)
        let adequate_data = generate_trending_data(100, 0.1);
        let hurst_adequate = tester.calculate_hurst_exponent(&adequate_data);
        println!("✓ Adequate data test (100 points): Hurst = {:.3}", hurst_adequate);
    }

    #[test]
    fn test_time_scale_limitations() {
        let tester = HurstExponentTester::new();
        
        // AUDIT FINDING: Only uses 4 fixed time scales [5, 10, 15, 20]
        let data = generate_trending_data(100, 0.1);
        let hurst = tester.calculate_hurst_exponent(&data);
        
        println!("⚠️  Time scale limitation test: Hurst = {:.3}", hurst);
        println!("   ISSUE: Only 4 fixed scales used, should use logarithmic spacing");
        println!("   RECOMMENDATION: Use scales from 10 to N/4 where N = data length");
    }

    #[test]
    fn test_constant_price_edge_case() {
        let tester = HurstExponentTester::new();
        
        // Test with constant prices (zero volatility)
        let constant_data = generate_constant_data(50, 100.0);
        let hurst = tester.calculate_hurst_exponent(&constant_data);
        
        println!("⚠️  Constant price test: Hurst = {:.3}", hurst);
        println!("   ISSUE: Zero standard deviation case not handled properly");
        
        // Should handle gracefully without crashing
        assert!(hurst >= dec!(0.1) && hurst <= dec!(0.9));
    }

    #[test]
    fn test_biased_variance_calculation() {
        let tester = HurstExponentTester::new();
        
        // Test R/S calculation with known data
        let returns = vec![dec!(0.01), dec!(-0.005), dec!(0.02), dec!(-0.01), dec!(0.015)];
        let rs = tester.calculate_rs_statistic(&returns);
        
        println!("⚠️  Variance calculation test: R/S = {:.6}", rs);
        println!("   ISSUE: Uses biased variance estimator (n instead of n-1)");
        println!("   IMPACT: Systematic bias in R/S values");
        
        // Calculate what it should be with unbiased estimator
        let n = returns.len() as f64;
        let mean = returns.iter().sum::<Decimal>() / Decimal::from(returns.len());
        let biased_var = returns.iter()
            .map(|&ret| (ret - mean) * (ret - mean))
            .sum::<Decimal>() / Decimal::from(returns.len());
        let unbiased_var = returns.iter()
            .map(|&ret| (ret - mean) * (ret - mean))
            .sum::<Decimal>() / Decimal::from(returns.len() - 1);
        
        println!("   Biased variance: {:.6}", biased_var);
        println!("   Unbiased variance: {:.6}", unbiased_var);
        println!("   Bias factor: {:.3}", unbiased_var / biased_var);
    }

    #[test]
    fn test_market_character_classification() {
        let tester = HurstExponentTester::new();
        
        // Test classification thresholds
        let test_cases = vec![
            (dec!(0.3), MarketCharacter::MeanReverting),
            (dec!(0.44), MarketCharacter::MeanReverting),
            (dec!(0.45), MarketCharacter::RandomWalk),
            (dec!(0.5), MarketCharacter::RandomWalk),
            (dec!(0.55), MarketCharacter::RandomWalk),
            (dec!(0.56), MarketCharacter::Trending),
            (dec!(0.7), MarketCharacter::Trending),
        ];
        
        for (hurst, expected) in test_cases {
            let result = tester.classify_market_character(hurst);
            assert_eq!(result, expected);
            println!("✓ Classification test: H={:.2} -> {:?}", hurst, result);
        }
        
        println!("⚠️  ISSUE: Hardcoded thresholds without statistical justification");
        println!("   RECOMMENDATION: Use adaptive thresholds or confidence intervals");
    }

    #[test]
    fn test_kalman_filter_convergence() {
        let mut tester = HurstExponentTester::new();
        
        // Test Kalman filter with constant input
        let constant_price = dec!(100.0);
        let mut filtered_prices = Vec::new();
        
        for i in 0..20 {
            let filtered = tester.apply_kalman_filter(constant_price).unwrap();
            filtered_prices.push(filtered);
            println!("Step {}: Filtered = {:.6}", i, filtered);
        }
        
        // Should converge to input value
        let final_price = filtered_prices.last().unwrap();
        let convergence_error = (final_price - constant_price).abs();
        
        println!("✓ Kalman convergence test: Error = {:.6}", convergence_error);
        assert!(convergence_error < dec!(0.01), "Filter should converge to input");
        
        println!("⚠️  ISSUE: No convergence monitoring in implementation");
        println!("   RECOMMENDATION: Add convergence detection and reset mechanisms");
    }

    #[test]
    fn test_kalman_filter_noise_parameters() {
        let mut tester = HurstExponentTester::new();
        
        // Test with noisy input
        let base_price = dec!(100.0);
        let noise_amplitude = dec!(1.0);
        
        for i in 0..10 {
            let noisy_price = base_price + (dec!(i % 2) * dec!(2) - dec!(1)) * noise_amplitude;
            let filtered = tester.apply_kalman_filter(noisy_price).unwrap();
            println!("Noisy: {:.2}, Filtered: {:.4}", noisy_price, filtered);
        }
        
        println!("⚠️  ISSUE: Hardcoded noise parameters (1% process, 5% measurement)");
        println!("   RECOMMENDATION: Make parameters configurable or adaptive");
    }

    #[test]
    fn test_statistical_significance() {
        let tester = HurstExponentTester::new();
        
        // Test with known synthetic data
        let trending_data = generate_trending_data(200, 0.05);
        let random_data = generate_random_walk_data(200);
        let mean_reverting_data = generate_mean_reverting_data(200, 100.0, 0.1);
        
        let hurst_trending = tester.calculate_hurst_exponent(&trending_data);
        let hurst_random = tester.calculate_hurst_exponent(&random_data);
        let hurst_mean_rev = tester.calculate_hurst_exponent(&mean_reverting_data);
        
        println!("Hurst exponents:");
        println!("  Trending data: {:.3}", hurst_trending);
        println!("  Random walk: {:.3}", hurst_random);
        println!("  Mean reverting: {:.3}", hurst_mean_rev);
        
        println!("⚠️  ISSUE: No statistical significance testing");
        println!("   RECOMMENDATION: Add confidence intervals and significance tests");
        
        // Basic sanity check (may fail due to implementation issues)
        if hurst_trending > dec!(0.55) {
            println!("✓ Trending data correctly identified");
        } else {
            println!("❌ Trending data not identified (implementation issue)");
        }
    }

    #[test]
    fn test_performance_characteristics() {
        let tester = HurstExponentTester::new();
        
        // Test with different data sizes
        let sizes = vec![50, 100, 200, 500];
        
        for size in sizes {
            let data = generate_trending_data(size, 0.01);
            
            let start = std::time::Instant::now();
            let hurst = tester.calculate_hurst_exponent(&data);
            let duration = start.elapsed();
            
            println!("Size {}: Hurst = {:.3}, Time = {:?}", size, hurst, duration);
        }
        
        println!("⚠️  PERFORMANCE: O(n²) complexity may not scale for high-frequency use");
        println!("   RECOMMENDATION: Optimize for production performance requirements");
    }
}

// Integration test to demonstrate the issues
#[cfg(test)]
mod integration_tests {
    use super::*;

    #[test]
    fn test_complete_analysis_pipeline() {
        let mut tester = HurstExponentTester::new();
        
        println!("=== Complete Analysis Pipeline Test ===");
        
        // Generate test data
        let raw_data = generate_trending_data(100, 0.02);
        
        // Apply Kalman filtering
        let mut filtered_data = VecDeque::new();
        for point in &raw_data {
            match tester.apply_kalman_filter(point.price) {
                Ok(filtered_price) => {
                    filtered_data.push_back(PricePoint {
                        price: filtered_price,
                        timestamp: point.timestamp,
                    });
                }
                Err(e) => {
                    println!("Kalman filter error: {}", e);
                    break;
                }
            }
        }
        
        // Calculate Hurst exponent
        let hurst = tester.calculate_hurst_exponent(&filtered_data);
        let character = tester.classify_market_character(hurst);
        
        println!("Results:");
        println!("  Data points: {}", filtered_data.len());
        println!("  Hurst exponent: {:.3}", hurst);
        println!("  Market character: {:?}", character);
        
        // Demonstrate the issues
        println!("\n=== Issues Demonstrated ===");
        println!("1. Limited time scales: Only [5,10,15,20] used");
        println!("2. Insufficient data validation: Accepts {} points", filtered_data.len());
        println!("3. No statistical confidence: Single point estimate");
        println!("4. Hardcoded thresholds: 0.45/0.55 without justification");
        println!("5. No temporal consistency: Classification can change rapidly");
        
        // This test passes but demonstrates the implementation issues
        assert!(hurst >= dec!(0.1) && hurst <= dec!(0.9));
    }
}
fn main() {
    println!("Running Hurst Exponent Test Suite...");
    let tester = HurstExponentTester::new();
    let trending_data = generate_trending_data(100, 0.05);
    let hurst_trending = tester.calculate_hurst_exponent(&trending_data);
    println!("Trending data Hurst exponent: {}", hurst_trending);
    println!("Hurst Exponent Test Suite completed.");
}
