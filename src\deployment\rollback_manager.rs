// src/deployment/rollback_manager.rs
// Rollback manager for safe deployment rollbacks

use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::time::Duration;
use tokio::time::sleep;
use tracing::{debug, error, info, warn};

use super::{DeploymentConfig, DeploymentPhase, RollbackStrategy, TrafficRouting};
use super::feature_flags::FeatureFlagManager;
use super::health_checks::HealthCheckManager;
use super::monitoring::DeploymentMonitor;

/// Rollback manager for handling deployment rollbacks
#[derive(Debug)]
pub struct RollbackManager {
    config: Arc<RwLock<DeploymentConfig>>,
    feature_flags: Arc<FeatureFlagManager>,
    health_checks: Arc<HealthCheckManager>,
    monitor: Arc<DeploymentMonitor>,
    rollback_history: Arc<RwLock<Vec<RollbackRecord>>>,
}

impl RollbackManager {
    /// Create a new rollback manager
    pub fn new(
        config: Arc<RwLock<DeploymentConfig>>,
        feature_flags: Arc<FeatureFlagManager>,
        health_checks: Arc<HealthCheckManager>,
        monitor: Arc<DeploymentMonitor>,
    ) -> Self {
        Self {
            config,
            feature_flags,
            health_checks,
            monitor,
            rollback_history: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Execute rollback to target phase
    pub async fn rollback_to_phase(&self, target_phase: DeploymentPhase) -> Result<()> {
        let (current_phase, strategy) = {
            let config = self.config.read().await;
            (config.current_phase, config.rollback_strategy)
        };

        info!(
            "Starting rollback from {} to {} using {} strategy",
            current_phase, target_phase, strategy
        );

        // Validate rollback
        self.validate_rollback(current_phase, target_phase)?;

        // Create rollback record
        let rollback_record = RollbackRecord {
            id: uuid::Uuid::new_v4().to_string(),
            from_phase: current_phase,
            to_phase: target_phase,
            strategy,
            started_at: Utc::now(),
            completed_at: None,
            status: RollbackStatus::InProgress,
            reason: "Manual rollback".to_string(),
            metrics_before: self.monitor.collect_deployment_metrics().await?,
            metrics_after: HashMap::new(),
            errors: Vec::new(),
        };

        // Add to history
        {
            let mut history = self.rollback_history.write().await;
            history.push(rollback_record.clone());
        }

        // Execute rollback based on strategy
        let result = match strategy {
            RollbackStrategy::Immediate => {
                self.execute_immediate_rollback(current_phase, target_phase).await
            }
            RollbackStrategy::Gradual => {
                self.execute_gradual_rollback(current_phase, target_phase).await
            }
            RollbackStrategy::BlueGreen => {
                self.execute_blue_green_rollback(current_phase, target_phase).await
            }
        };

        // Update rollback record
        let final_status = match result {
            Ok(()) => {
                info!("Rollback completed successfully");
                RollbackStatus::Completed
            }
            Err(ref e) => {
                error!("Rollback failed: {}", e);
                RollbackStatus::Failed
            }
        };

        self.update_rollback_record(&rollback_record.id, final_status).await?;

        result
    }

    /// Execute immediate rollback
    async fn execute_immediate_rollback(
        &self,
        from_phase: DeploymentPhase,
        to_phase: DeploymentPhase,
    ) -> Result<()> {
        info!("Executing immediate rollback from {} to {}", from_phase, to_phase);

        // Step 1: Immediately switch all traffic to legacy implementation
        self.switch_traffic_immediately(to_phase).await?;

        // Step 2: Disable new implementation features
        self.disable_phase_features(from_phase).await?;

        // Step 3: Update feature flags to target phase
        self.feature_flags.update_phase(to_phase)?;

        // Step 4: Wait for stabilization
        info!("Waiting for system stabilization after immediate rollback");
        sleep(Duration::from_secs(30)).await;

        // Step 5: Verify rollback success
        self.verify_rollback_success(to_phase).await?;

        // Step 6: Update configuration
        self.update_current_phase(to_phase).await?;

        info!("Immediate rollback completed successfully");
        Ok(())
    }

    /// Execute gradual rollback
    async fn execute_gradual_rollback(
        &self,
        from_phase: DeploymentPhase,
        to_phase: DeploymentPhase,
    ) -> Result<()> {
        info!("Executing gradual rollback from {} to {}", from_phase, to_phase);

        let current_percentage = {
            let config = self.config.read().await;
            config.traffic_routing.new_implementation_percentage
        };
        let target_percentage = to_phase.traffic_percentage() as f64;

        // Step 1: Gradually reduce traffic to new implementation
        self.gradual_traffic_reduction(current_percentage, target_percentage).await?;

        // Step 2: Disable features after traffic is reduced
        self.disable_phase_features(from_phase).await?;

        // Step 3: Update feature flags
        self.feature_flags.update_phase(to_phase)?;

        // Step 4: Final verification
        self.verify_rollback_success(to_phase).await?;

        // Step 5: Update configuration
        self.update_current_phase(to_phase).await?;

        info!("Gradual rollback completed successfully");
        Ok(())
    }

    /// Execute blue-green rollback
    async fn execute_blue_green_rollback(
        &self,
        from_phase: DeploymentPhase,
        to_phase: DeploymentPhase,
    ) -> Result<()> {
        info!("Executing blue-green rollback from {} to {}", from_phase, to_phase);

        // Step 1: Prepare green environment (legacy implementation)
        self.prepare_green_environment(to_phase).await?;

        // Step 2: Verify green environment health
        self.verify_green_environment_health().await?;

        // Step 3: Switch load balancer to green environment
        self.switch_to_green_environment(to_phase).await?;

        // Step 4: Shutdown blue environment (new implementation)
        self.shutdown_blue_environment().await?;

        // Step 5: Update configuration
        self.update_current_phase(to_phase).await?;

        info!("Blue-green rollback completed successfully");
        Ok(())
    }

    /// Switch traffic immediately to legacy implementation
    async fn switch_traffic_immediately(&self, target_phase: DeploymentPhase) -> Result<()> {
        let target_percentage = target_phase.traffic_percentage() as f64;
        
        info!("Switching traffic immediately to {}% new implementation", target_percentage);

        {
            let mut config = self.config.write().await;
            config.traffic_routing.new_implementation_percentage = target_percentage;
            config.traffic_routing.legacy_implementation_percentage = 100.0 - target_percentage;
        }

        // Notify traffic routing system of immediate change
        self.notify_traffic_routing_change().await?;

        Ok(())
    }

    /// Gradually reduce traffic to new implementation
    async fn gradual_traffic_reduction(&self, current: f64, target: f64) -> Result<()> {
        info!("Gradually reducing traffic from {}% to {}", current, target);

        let steps = self.calculate_rollback_steps(current, target);

        for step_percentage in steps {
            info!("Reducing traffic to {}%", step_percentage);

            // Update traffic routing
            {
                let mut config = self.config.write().await;
                config.traffic_routing.new_implementation_percentage = step_percentage;
                config.traffic_routing.legacy_implementation_percentage = 100.0 - step_percentage;
            }

            // Notify traffic routing system
            self.notify_traffic_routing_change().await?;

            // Wait for stabilization
            let stabilization_time = self.calculate_rollback_stabilization_time(step_percentage);
            info!("Waiting {}s for stabilization", stabilization_time.as_secs());
            sleep(stabilization_time).await;

            // Check health after each step
            if !self.health_checks.run_all_checks().await? {
                warn!("Health checks failed during gradual rollback at {}%", step_percentage);
                // Continue with rollback even if health checks fail
            }

            // Monitor for issues
            if let Some(issue) = self.monitor.check_deployment_issues().await? {
                warn!("Issue detected during rollback: {}", issue);
                // Continue with rollback
            }
        }

        info!("Traffic reduction completed to {}%", target);
        Ok(())
    }

    /// Calculate rollback steps for gradual traffic reduction
    fn calculate_rollback_steps(&self, current: f64, target: f64) -> Vec<f64> {
        if current <= target {
            return vec![target];
        }

        let mut steps = Vec::new();
        let difference = current - target;
        
        // Use larger steps for rollback (faster than deployment)
        let step_size = if difference <= 10.0 {
            difference / 2.0
        } else if difference <= 30.0 {
            10.0
        } else {
            20.0
        };

        let mut current_step = current - step_size;
        while current_step > target {
            steps.push(current_step);
            current_step -= step_size;
        }
        
        steps.push(target);
        steps
    }

    /// Calculate stabilization time for rollback steps
    fn calculate_rollback_stabilization_time(&self, _percentage: f64) -> Duration {
        // Rollback stabilization is faster than deployment
        Duration::from_secs(15)
    }

    /// Disable features for a specific phase
    async fn disable_phase_features(&self, phase: DeploymentPhase) -> Result<()> {
        info!("Disabling features for phase: {}", phase);

        let flags_to_disable = self.feature_flags.get_flags_for_phase(phase);
        
        for flag in flags_to_disable {
            if flag.enabled {
                if let Err(e) = self.feature_flags.disable_flag(&flag.name) {
                    warn!("Failed to disable flag '{}': {}", flag.name, e);
                    // Continue with other flags
                }
            }
        }

        Ok(())
    }

    /// Prepare green environment for blue-green rollback
    async fn prepare_green_environment(&self, target_phase: DeploymentPhase) -> Result<()> {
        info!("Preparing green environment for phase: {}", target_phase);

        // This would typically involve:
        // 1. Starting a new instance with target phase configuration
        // 2. Warming up the instance
        // 3. Running health checks on the green environment

        // For now, we'll simulate this by updating feature flags
        self.feature_flags.update_phase(target_phase)?;

        info!("Green environment prepared successfully");
        Ok(())
    }

    /// Verify green environment health
    async fn verify_green_environment_health(&self) -> Result<()> {
        info!("Verifying green environment health");

        // Run comprehensive health checks
        if !self.health_checks.run_all_checks().await? {
            return Err(anyhow::anyhow!("Green environment health checks failed"));
        }

        // Check metrics
        let metrics = self.monitor.collect_deployment_metrics().await?;
        if let Some(issue) = self.analyze_green_environment_metrics(&metrics) {
            return Err(anyhow::anyhow!("Green environment metrics issue: {}", issue));
        }

        info!("Green environment health verified");
        Ok(())
    }

    /// Analyze green environment metrics
    fn analyze_green_environment_metrics(&self, metrics: &HashMap<String, f64>) -> Option<String> {
        // Similar to deployment metrics analysis but with stricter thresholds for rollback
        if let Some(&error_rate) = metrics.get("error_rate_percentage") {
            if error_rate > 2.0 {
                return Some(format!("High error rate in green environment: {}%", error_rate));
            }
        }

        if let Some(&response_time) = metrics.get("avg_response_time_ms") {
            if response_time > 500.0 {
                return Some(format!("High response time in green environment: {}ms", response_time));
            }
        }

        None
    }

    /// Switch to green environment
    async fn switch_to_green_environment(&self, target_phase: DeploymentPhase) -> Result<()> {
        info!("Switching to green environment");

        let target_percentage = target_phase.traffic_percentage() as f64;

        {
            let mut config = self.config.write().await;
            config.traffic_routing.new_implementation_percentage = target_percentage;
            config.traffic_routing.legacy_implementation_percentage = 100.0 - target_percentage;
        }

        self.notify_traffic_routing_change().await?;

        info!("Switched to green environment successfully");
        Ok(())
    }

    /// Shutdown blue environment
    async fn shutdown_blue_environment(&self) -> Result<()> {
        info!("Shutting down blue environment");

        // This would typically involve:
        // 1. Gracefully stopping the blue environment
        // 2. Cleaning up resources
        // 3. Updating load balancer configuration

        // For now, we'll simulate this
        sleep(Duration::from_secs(5)).await;

        info!("Blue environment shut down successfully");
        Ok(())
    }

    /// Verify rollback success
    async fn verify_rollback_success(&self, target_phase: DeploymentPhase) -> Result<()> {
        info!("Verifying rollback success for phase: {}", target_phase);

        // Step 1: Run health checks
        if !self.health_checks.run_all_checks().await? {
            return Err(anyhow::anyhow!("Health checks failed after rollback"));
        }

        // Step 2: Verify feature flags are correct
        let current_phase = self.feature_flags.current_phase();
        if current_phase != target_phase {
            return Err(anyhow::anyhow!(
                "Feature flag phase mismatch: expected {}, got {}",
                target_phase, current_phase
            ));
        }

        // Step 3: Verify traffic routing
        let traffic_percentage = {
            let config = self.config.read().await;
            config.traffic_routing.new_implementation_percentage
        };
        let expected_percentage = target_phase.traffic_percentage() as f64;
        
        if (traffic_percentage - expected_percentage).abs() > 1.0 {
            return Err(anyhow::anyhow!(
                "Traffic routing mismatch: expected {}%, got {}%",
                expected_percentage, traffic_percentage
            ));
        }

        // Step 4: Check system metrics
        let metrics = self.monitor.collect_deployment_metrics().await?;
        if let Some(issue) = self.analyze_rollback_metrics(&metrics) {
            return Err(anyhow::anyhow!("Rollback verification failed: {}", issue));
        }

        info!("Rollback verification successful");
        Ok(())
    }

    /// Analyze metrics after rollback
    fn analyze_rollback_metrics(&self, metrics: &HashMap<String, f64>) -> Option<String> {
        // Check if metrics are within acceptable ranges after rollback
        if let Some(&error_rate) = metrics.get("error_rate_percentage") {
            if error_rate > 3.0 {
                return Some(format!("High error rate after rollback: {}%", error_rate));
            }
        }

        if let Some(&response_time) = metrics.get("avg_response_time_ms") {
            if response_time > 800.0 {
                return Some(format!("High response time after rollback: {}ms", response_time));
            }
        }

        None
    }

    /// Update current phase in configuration
    async fn update_current_phase(&self, phase: DeploymentPhase) -> Result<()> {
        {
            let mut config = self.config.write().await;
            config.current_phase = phase;
            config.target_phase = None;
        }

        info!("Updated current phase to: {}", phase);
        Ok(())
    }

    /// Notify traffic routing system of changes
    async fn notify_traffic_routing_change(&self) -> Result<()> {
        // This would typically involve:
        // 1. Updating load balancer configuration
        // 2. Notifying service mesh
        // 3. Updating DNS records if needed

        debug!("Notified traffic routing system of changes");
        Ok(())
    }

    /// Validate rollback request
    fn validate_rollback(&self, current: DeploymentPhase, target: DeploymentPhase) -> Result<()> {
        if current == target {
            return Err(anyhow::anyhow!("Already at target phase"));
        }

        // Check if target is a valid rollback destination
        let mut phase = current;
        while let Some(prev) = phase.previous_phase() {
            if prev == target {
                return Ok(());
            }
            phase = prev;
        }

        Err(anyhow::anyhow!(
            "Cannot rollback from {} to {}",
            current, target
        ))
    }

    /// Update rollback record
    async fn update_rollback_record(&self, id: &str, status: RollbackStatus) -> Result<()> {
        let mut history = self.rollback_history.write().await;
        
        if let Some(record) = history.iter_mut().find(|r| r.id == id) {
            record.status = status;
            record.completed_at = Some(Utc::now());
            
            if status == RollbackStatus::Completed {
                record.metrics_after = self.monitor.collect_deployment_metrics().await?;
            }
        }

        Ok(())
    }

    /// Get rollback history
    pub async fn get_rollback_history(&self) -> Vec<RollbackRecord> {
        self.rollback_history.read().await.clone()
    }

    /// Get recent rollback records
    pub async fn get_recent_rollbacks(&self, limit: usize) -> Vec<RollbackRecord> {
        let history = self.rollback_history.read().await;
        history.iter().rev().take(limit).cloned().collect()
    }

    /// Check if rollback is safe for current phase
    pub fn is_rollback_safe(&self, target_phase: DeploymentPhase) -> bool {
        let current_phase = self.feature_flags.current_phase();
        
        // Check if all enabled flags are rollback-safe
        let enabled_flags = self.feature_flags.get_enabled_flags();
        let flags_to_disable: Vec<_> = enabled_flags.into_iter()
            .filter(|flag| !target_phase.included_phases().contains(&flag.phase))
            .collect();

        flags_to_disable.iter().all(|flag| flag.can_rollback())
    }

    /// Get rollback recommendations
    pub fn get_rollback_recommendations(&self) -> Vec<RollbackRecommendation> {
        let current_phase = self.feature_flags.current_phase();
        let mut recommendations = Vec::new();

        // Check if immediate rollback is recommended based on metrics
        if let Ok(metrics) = futures::executor::block_on(self.monitor.collect_deployment_metrics()) {
            if let Some(&error_rate) = metrics.get("error_rate_percentage") {
                if error_rate > 10.0 {
                    recommendations.push(RollbackRecommendation {
                        target_phase: current_phase.previous_phase().unwrap_or(current_phase),
                        strategy: RollbackStrategy::Immediate,
                        reason: format!("High error rate: {}%", error_rate),
                        urgency: RollbackUrgency::High,
                    });
                }
            }
        }

        recommendations
    }
}

/// Rollback record for tracking rollback history
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RollbackRecord {
    pub id: String,
    pub from_phase: DeploymentPhase,
    pub to_phase: DeploymentPhase,
    pub strategy: RollbackStrategy,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub status: RollbackStatus,
    pub reason: String,
    pub metrics_before: HashMap<String, f64>,
    pub metrics_after: HashMap<String, f64>,
    pub errors: Vec<String>,
}

/// Rollback status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RollbackStatus {
    InProgress,
    Completed,
    Failed,
    Cancelled,
}

impl std::fmt::Display for RollbackStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RollbackStatus::InProgress => write!(f, "in-progress"),
            RollbackStatus::Completed => write!(f, "completed"),
            RollbackStatus::Failed => write!(f, "failed"),
            RollbackStatus::Cancelled => write!(f, "cancelled"),
        }
    }
}

/// Rollback recommendation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RollbackRecommendation {
    pub target_phase: DeploymentPhase,
    pub strategy: RollbackStrategy,
    pub reason: String,
    pub urgency: RollbackUrgency,
}

/// Rollback urgency level
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RollbackUrgency {
    Low,
    Medium,
    High,
    Critical,
}

impl std::fmt::Display for RollbackUrgency {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RollbackUrgency::Low => write!(f, "low"),
            RollbackUrgency::Medium => write!(f, "medium"),
            RollbackUrgency::High => write!(f, "high"),
            RollbackUrgency::Critical => write!(f, "critical"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::deployment::feature_flags::FeatureFlagManager;
    use crate::deployment::health_checks::HealthCheckManager;
    use crate::deployment::monitoring::DeploymentMonitor;

    #[test]
    fn test_rollback_steps_calculation() {
        let config = Arc::new(RwLock::new(DeploymentConfig::default()));
        let feature_flags = Arc::new(FeatureFlagManager::new(DeploymentPhase::CoreScoring));
        let health_checks = Arc::new(HealthCheckManager::new());
        let monitor = Arc::new(DeploymentMonitor::new());
        
        let manager = RollbackManager::new(config, feature_flags, health_checks, monitor);
        
        let steps = manager.calculate_rollback_steps(35.0, 5.0);
        assert!(!steps.is_empty());
        assert_eq!(steps.last(), Some(&5.0));
        
        // Steps should be larger for rollback (faster)
        let step_size = 35.0 - steps[0];
        assert!(step_size >= 10.0);
    }

    #[test]
    fn test_rollback_validation() {
        let config = Arc::new(RwLock::new(DeploymentConfig::default()));
        let feature_flags = Arc::new(FeatureFlagManager::new(DeploymentPhase::CoreScoring));
        let health_checks = Arc::new(HealthCheckManager::new());
        let monitor = Arc::new(DeploymentMonitor::new());
        
        let manager = RollbackManager::new(config, feature_flags, health_checks, monitor);
        
        // Valid rollback
        assert!(manager.validate_rollback(
            DeploymentPhase::CoreScoring,
            DeploymentPhase::Development
        ).is_ok());
        
        // Invalid rollback (same phase)
        assert!(manager.validate_rollback(
            DeploymentPhase::CoreScoring,
            DeploymentPhase::CoreScoring
        ).is_err());
        
        // Invalid rollback (forward)
        assert!(manager.validate_rollback(
            DeploymentPhase::Development,
            DeploymentPhase::CoreScoring
        ).is_err());
    }

    #[test]
    fn test_rollback_safety_check() {
        let config = Arc::new(RwLock::new(DeploymentConfig::default()));
        let feature_flags = Arc::new(FeatureFlagManager::new(DeploymentPhase::CoreScoring));
        let health_checks = Arc::new(HealthCheckManager::new());
        let monitor = Arc::new(DeploymentMonitor::new());
        
        let manager = RollbackManager::new(config, feature_flags, health_checks, monitor);
        
        // Should be safe to rollback to development
        assert!(manager.is_rollback_safe(DeploymentPhase::Development));
    }
}
