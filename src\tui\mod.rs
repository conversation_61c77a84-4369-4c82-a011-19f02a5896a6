use std::io::Stdout;
pub mod app;
mod components;
mod render;

use crate::config::{Settings, Config};
use app::App;
use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode},
    execute,
    terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
};
use ratatui::Terminal;
use std::{error::Error as StdError, io, time::Duration, sync::Arc};
use tracing::{error, info};

pub async fn run(
    settings: Settings,
    nats_client: async_nats::Client,
    execution_config: Arc<crate::config::ExecutionConfig>,
    risk_config: Arc<crate::config::RiskConfig>,
    price_oracle: Arc<crate::data::price_oracle::PriceOracle>,
) -> Result<(), Box<dyn StdError>> {
    info!("Starting Basilisk TUI");

    // Setup terminal
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = ratatui::backend::CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create app state
    let mut app = App::new(Arc::new(Config::from(settings)), nats_client.clone(), price_oracle);

    // Initialize app (connect to NATS, etc.)
    if let Err(e) = app.init().await {
        error!("Failed to initialize TUI application: {}", e);
        // Cleanup terminal before returning error
        disable_raw_mode()?;
        execute!(
            terminal.backend_mut(),
            LeaveAlternateScreen,
            DisableMouseCapture
        )?;
        return Err(format!("TUI initialization failed: {}", e).into());
    }

    info!("TUI initialized successfully");

    // Main event loop
    let mut last_tick = std::time::Instant::now();
    let tick_rate = Duration::from_millis(100);

    while app.running {
        // Draw UI
        if let Err(e) = terminal.draw(|f| app.render(f)) {
            error!("Failed to draw TUI: {}", e);
            break;
        }

        // Handle input events
        let timeout = tick_rate
            .checked_sub(last_tick.elapsed())
            .unwrap_or_else(|| Duration::from_secs(0));

        if event::poll(timeout)? {
            if let Event::Key(key) = event::read()? {
                app.handle_key(key);
            }
        }

        // Update app state periodically
        if last_tick.elapsed() >= tick_rate {
            if let Err(e) = app.update().await {
                error!("Failed to update TUI state: {}", e);
            }
            last_tick = std::time::Instant::now();
        }
    }

    // Cleanup and restore terminal
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    info!("Basilisk TUI shutdown complete");
    Ok(())
}
