# Staging Environment Configuration
# Overrides for staging environment - more aggressive than production but safer than development

# Override log level for more verbose staging logs
log_level = "debug"

[strategy]
# Slightly more aggressive Kelly fraction for staging testing
kelly_fraction_cap = 0.35
# Lower profitability threshold for more opportunities
min_profitability_bps = 30
# Enable additional strategies for testing
enabled_strategies = ["zen_geometer", "basilisk_gaze", "pilot_fish"]

[execution]
# Higher slippage tolerance for staging
max_slippage_bps = 500
# More aggressive gas multiplier
gas_limit_multiplier = 1.5
# Higher gas price limit for staging
max_gas_price_gwei = 150

# Override Base network for staging (use testnet or different RPC)
[chains.8453]
name = "Base Staging"
rpc_url = "https://staging-rpc.base.org"  # Hypothetical staging RPC
max_gas_price = 75000000000  # 75 gwei

# Add testnet chains for staging
[chains.84531]
name = "Base Goerli"
rpc_url = "https://goerli.base.org"
max_gas_price = 10000000000  # 10 gwei
private_key_env_var = "BASE_GOERLI_PRIVATE_KEY"

[chains.84531.contracts]
multicall = "0xcA11bde05977b3631167028862bE2a173976CA11"

[chains.84531.dex]
uniswap_v2_router = "0x4752ba5dbc23f44d87826276bf6fd6b1c372ad24"
