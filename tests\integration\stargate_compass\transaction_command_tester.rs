// MISSION: TUI Transaction Command Testing - Comprehensive Transaction Testing Framework
// WHY: Verify transaction initiation commands, success verification, emergency stop, and error handling
// HOW: Transaction monitoring, success verification, emergency stop testing, and error validation

use anyhow::{Result, Context, anyhow};
use ethers::{
    providers::{Provider, Http, Middleware},
    types::{Address, H256, U256, TransactionReceipt, Transaction},
};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::timeout;
use tracing::{debug, info, warn, error};

use super::{
    anvil_client::AnvilClient,
    core::{
        CommandTestResult, ContractInteractionResult, DataValidationResult, TestDetails,
        TestError, TestResult, TuiTestResult, UiStateValidationResult,
    },
    data_validator::{TuiDataValidator, TransactionStatusData},
    tui_functionality_tester::{TuiCommandResult, TuiFunctionalityTester},
};
use anyhow::{anyhow, Result};
use async_trait::async_trait;
use ethers::types::{Address, H256, U256};
use std::sync::Arc;
use std::time::Duration;

/// Transaction command test result
#[derive(Debug, Clone)]
pub struct TransactionCommandTestResult {
    pub command_name: String,
    pub success: bool,
    pub transaction_initiated: bool,
    pub transaction_hash: Option<H256>,
    pub transaction_status: Option<TransactionStatus>,
    pub execution_time_ms: u64,
    pub error_message: Option<String>,
    pub tui_output: String,
    pub verification_results: Vec<TransactionVerificationResult>,
}

/// Transaction verification result
#[derive(Debug, Clone)]
pub struct TransactionVerificationResult {
    pub verification_type: String,
    pub success: bool,
    pub expected_value: String,
    pub actual_value: String,
    pub error_message: Option<String>,
}

/// Emergency stop test result
#[derive(Debug, Clone)]
pub struct EmergencyStopTestResult {
    pub command_executed: bool,
    pub stop_signal_sent: bool,
    pub contract_state_changed: bool,
    pub all_operations_halted: bool,
    pub recovery_possible: bool,
    pub execution_time_ms: u64,
    pub error_messages: Vec<String>,
    pub verification_details: HashMap<String, String>,
}

/// Error message validation result
#[derive(Debug, Clone)]
pub struct ErrorMessageValidationResult {
    pub error_scenario: String,
    pub error_detected: bool,
    pub error_message_clear: bool,
    pub error_message_actionable: bool,
    pub recovery_instructions_provided: bool,
    pub error_categorized_correctly: bool,
    pub actual_error_message: String,
}

/// Transaction command tester implementation
pub struct TransactionCommandTester {
    tui_tester: TuiFunctionalityTester,
    anvil_client: Arc<AnvilClient>,
    data_validator: Arc<TuiDataValidator>,
    contract_address: Address,
    provider: Arc<Provider<Http>>,
    test_timeout: Duration,
}

impl TransactionCommandTester {
    /// Create new transaction command tester
    pub fn new(
        anvil_client: Arc<AnvilClient>,
        contract_address: Address,
        anvil_url: String,
    ) -> Result<Self> {
        let mut tui_tester = TuiFunctionalityTester::new(
            anvil_url.clone(),
            format!("{:?}", contract_address),
        );
        
        // Set up data validator for TUI tester
        tui_tester.set_data_validator(anvil_client.clone())?;
        
        let data_validator = Arc::new(TuiDataValidator::new(
            anvil_client.clone(),
            contract_address,
        ));
        
        let provider = anvil_client.provider.clone();
        
        Ok(Self {
            tui_tester,
            anvil_client,
            data_validator,
            contract_address,
            provider,
            test_timeout: Duration::from_secs(30),
        })
    }

    /// Set test timeout duration
    pub fn set_timeout(&mut self, timeout: Duration) {
        self.test_timeout = timeout;
    }

    // ============= TRANSACTION INITIATION COMMAND TESTING =============

    /// Test transaction initiation commands from TUI interface
    pub async fn test_transaction_initiation_commands(&mut self) -> Result<Vec<TransactionCommandTestResult>> {
        info!("Starting transaction initiation command testing");
        
        let mut results = Vec::new();
        
        // Test emergency stop transaction command
        results.push(self.test_emergency_stop_transaction().await?);
        
        // Test pause bot transaction command
        results.push(self.test_pause_bot_transaction().await?);
        
        // Test restart bot transaction command
        results.push(self.test_restart_bot_transaction().await?);
        
        // Test execute opportunity transaction command
        results.push(self.test_execute_opportunity_transaction().await?);
        
        info!("Completed transaction initiation command testing. Results: {}/{} successful",
              results.iter().filter(|r| r.success).count(),
              results.len());
        
        Ok(results)
    }

    /// Test emergency stop transaction command
    async fn test_emergency_stop_transaction(&mut self) -> Result<TransactionCommandTestResult> {
        info!("Testing emergency stop transaction command");
        
        let start_time = Instant::now();
        let command_name = "emergency_stop";
        
        // Get initial contract state
        let initial_state = self.data_validator.query_contract_state().await?;
        
        // Execute emergency stop command via TUI
        let tui_result = self.tui_tester.execute_command(command_name).await?;
        
        // Monitor for transaction initiation
        let transaction_hash = self.monitor_for_transaction_initiation(&tui_result.output_captured).await?;
        
        let mut verification_results = Vec::new();
        
        // Verify transaction was initiated
        if let Some(tx_hash) = transaction_hash {
            info!("Emergency stop transaction initiated: {:?}", tx_hash);
            
            // Wait for transaction confirmation
            let tx_status = self.wait_for_transaction_confirmation(tx_hash).await?;
            
            // Verify transaction success
            verification_results.push(self.verify_transaction_success(tx_hash, &tx_status).await?);
            
            // Verify contract state changed (emergency stopped)
            verification_results.push(self.verify_emergency_stop_state_change(&initial_state).await?);
            
            // Verify TUI reflects the change
            verification_results.push(self.verify_tui_reflects_emergency_stop().await?);
            
            Ok(TransactionCommandTestResult {
                command_name: command_name.to_string(),
                success: tui_result.success && tx_status.status == TransactionStatus::Confirmed,
                transaction_initiated: true,
                transaction_hash: Some(tx_hash),
                transaction_status: Some(tx_status.status),
                execution_time_ms: start_time.elapsed().as_millis() as u64,
                error_message: tui_result.error_message,
                tui_output: tui_result.output_captured,
                verification_results,
            })
        } else {
            warn!("Emergency stop command did not initiate a transaction");
            
            Ok(TransactionCommandTestResult {
                command_name: command_name.to_string(),
                success: false,
                transaction_initiated: false,
                transaction_hash: None,
                transaction_status: None,
                execution_time_ms: start_time.elapsed().as_millis() as u64,
                error_message: Some("No transaction initiated".to_string()),
                tui_output: tui_result.output_captured,
                verification_results,
            })
        }
    }

    /// Test pause bot transaction command
    async fn test_pause_bot_transaction(&mut self) -> Result<TransactionCommandTestResult> {
        info!("Testing pause bot transaction command");
        
        let start_time = Instant::now();
        let command_name = "pause_bot";
        
        // Get initial contract state
        let initial_state = self.data_validator.query_contract_state().await?;
        
        // Execute pause bot command via TUI
        let tui_result = self.tui_tester.execute_command(command_name).await?;
        
        // Monitor for transaction initiation
        let transaction_hash = self.monitor_for_transaction_initiation(&tui_result.output_captured).await?;
        
        let mut verification_results = Vec::new();
        
        if let Some(tx_hash) = transaction_hash {
            info!("Pause bot transaction initiated: {:?}", tx_hash);
            
            // Wait for transaction confirmation
            let tx_status = self.wait_for_transaction_confirmation(tx_hash).await?;
            
            // Verify transaction success
            verification_results.push(self.verify_transaction_success(tx_hash, &tx_status).await?);
            
            // Verify contract state changed (paused)
            verification_results.push(self.verify_pause_state_change(&initial_state).await?);
            
            // Verify TUI reflects the change
            verification_results.push(self.verify_tui_reflects_pause_state().await?);
            
            Ok(TransactionCommandTestResult {
                command_name: command_name.to_string(),
                success: tui_result.success && tx_status.status == TransactionStatus::Confirmed,
                transaction_initiated: true,
                transaction_hash: Some(tx_hash),
                transaction_status: Some(tx_status.status),
                execution_time_ms: start_time.elapsed().as_millis() as u64,
                error_message: tui_result.error_message,
                tui_output: tui_result.output_captured,
                verification_results,
            })
        } else {
            Ok(TransactionCommandTestResult {
                command_name: command_name.to_string(),
                success: false,
                transaction_initiated: false,
                transaction_hash: None,
                transaction_status: None,
                execution_time_ms: start_time.elapsed().as_millis() as u64,
                error_message: Some("No transaction initiated".to_string()),
                tui_output: tui_result.output_captured,
                verification_results,
            })
        }
    }    
/// Test restart bot transaction command
    async fn test_restart_bot_transaction(&mut self) -> Result<TransactionCommandTestResult> {
        info!("Testing restart bot transaction command");
        
        let start_time = Instant::now();
        let command_name = "restart_bot";
        
        // Get initial contract state
        let initial_state = self.data_validator.query_contract_state().await?;
        
        // Execute restart bot command via TUI
        let tui_result = self.tui_tester.execute_command(command_name).await?;
        
        // Monitor for transaction initiation
        let transaction_hash = self.monitor_for_transaction_initiation(&tui_result.output_captured).await?;
        
        let mut verification_results = Vec::new();
        
        if let Some(tx_hash) = transaction_hash {
            info!("Restart bot transaction initiated: {:?}", tx_hash);
            
            // Wait for transaction confirmation
            let tx_status = self.wait_for_transaction_confirmation(tx_hash).await?;
            
            // Verify transaction success
            verification_results.push(self.verify_transaction_success(tx_hash, &tx_status).await?);
            
            // Verify contract state changed (unpaused/active)
            verification_results.push(self.verify_restart_state_change(&initial_state).await?);
            
            // Verify TUI reflects the change
            verification_results.push(self.verify_tui_reflects_active_state().await?);
            
            Ok(TransactionCommandTestResult {
                command_name: command_name.to_string(),
                success: tui_result.success && tx_status.status == TransactionStatus::Confirmed,
                transaction_initiated: true,
                transaction_hash: Some(tx_hash),
                transaction_status: Some(tx_status.status),
                execution_time_ms: start_time.elapsed().as_millis() as u64,
                error_message: tui_result.error_message,
                tui_output: tui_result.output_captured,
                verification_results,
            })
        } else {
            Ok(TransactionCommandTestResult {
                command_name: command_name.to_string(),
                success: false,
                transaction_initiated: false,
                transaction_hash: None,
                transaction_status: None,
                execution_time_ms: start_time.elapsed().as_millis() as u64,
                error_message: Some("No transaction initiated".to_string()),
                tui_output: tui_result.output_captured,
                verification_results,
            })
        }
    }

    /// Test execute opportunity transaction command
    async fn test_execute_opportunity_transaction(&mut self) -> Result<TransactionCommandTestResult> {
        info!("Testing execute opportunity transaction command");
        
        let start_time = Instant::now();
        let command_name = "execute_opportunity";
        
        // Execute opportunity command via TUI
        let tui_result = self.tui_tester.execute_command(command_name).await?;
        
        // Monitor for transaction initiation
        let transaction_hash = self.monitor_for_transaction_initiation(&tui_result.output_captured).await?;
        
        let mut verification_results = Vec::new();
        
        if let Some(tx_hash) = transaction_hash {
            info!("Execute opportunity transaction initiated: {:?}", tx_hash);
            
            // Wait for transaction confirmation
            let tx_status = self.wait_for_transaction_confirmation(tx_hash).await?;
            
            // Verify transaction success
            verification_results.push(self.verify_transaction_success(tx_hash, &tx_status).await?);
            
            // Verify opportunity execution results
            verification_results.push(self.verify_opportunity_execution_results(tx_hash).await?);
            
            // Verify TUI shows execution results
            verification_results.push(self.verify_tui_shows_execution_results().await?);
            
            Ok(TransactionCommandTestResult {
                command_name: command_name.to_string(),
                success: tui_result.success && tx_status.status == TransactionStatus::Confirmed,
                transaction_initiated: true,
                transaction_hash: Some(tx_hash),
                transaction_status: Some(tx_status.status),
                execution_time_ms: start_time.elapsed().as_millis() as u64,
                error_message: tui_result.error_message,
                tui_output: tui_result.output_captured,
                verification_results,
            })
        } else {
            Ok(TransactionCommandTestResult {
                command_name: command_name.to_string(),
                success: false,
                transaction_initiated: false,
                transaction_hash: None,
                transaction_status: None,
                execution_time_ms: start_time.elapsed().as_millis() as u64,
                error_message: Some("No transaction initiated".to_string()),
                tui_output: tui_result.output_captured,
                verification_results,
            })
        }
    }

    // ============= TRANSACTION SUCCESS VERIFICATION =============

    /// Monitor TUI output for transaction initiation
    async fn monitor_for_transaction_initiation(&self, tui_output: &str) -> Result<Option<H256>> {
        info!("Monitoring TUI output for transaction initiation");
        
        // Look for transaction hash patterns in TUI output
        let tx_hash_patterns = [
            r"0x[a-fA-F0-9]{64}",  // Full transaction hash
            r"Transaction:\s*(0x[a-fA-F0-9]{64})",  // Transaction: 0x...
            r"Hash:\s*(0x[a-fA-F0-9]{64})",  // Hash: 0x...
            r"Tx:\s*(0x[a-fA-F0-9]{64})",  // Tx: 0x...
        ];
        
        for pattern in &tx_hash_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                if let Some(captures) = regex.captures(tui_output) {
                    if let Some(hash_match) = captures.get(1).or_else(|| captures.get(0)) {
                        let hash_str = hash_match.as_str();
                        if let Ok(tx_hash) = hash_str.parse::<H256>() {
                            info!("Found transaction hash in TUI output: {:?}", tx_hash);
                            return Ok(Some(tx_hash));
                        }
                    }
                }
            }
        }
        
        // Also check recent transactions to the contract
        let recent_tx = self.get_most_recent_contract_transaction().await?;
        if let Some(tx_hash) = recent_tx {
            info!("Found recent contract transaction: {:?}", tx_hash);
            return Ok(Some(tx_hash));
        }
        
        Ok(None)
    }

    /// Get the most recent transaction to the contract
    async fn get_most_recent_contract_transaction(&self) -> Result<Option<H256>> {
        let current_block = self.provider.get_block_number().await?.as_u64();
        
        // Look back through recent blocks
        for block_num in (current_block.saturating_sub(10)..=current_block).rev() {
            if let Ok(Some(block)) = self.provider.get_block_with_txs(block_num).await {
                for tx in block.transactions {
                    if tx.to == Some(self.contract_address) {
                        return Ok(Some(tx.hash));
                    }
                }
            }
        }
        
        Ok(None)
    }

    /// Wait for transaction confirmation
    async fn wait_for_transaction_confirmation(&self, tx_hash: H256) -> Result<TransactionStatusData> {
        info!("Waiting for transaction confirmation: {:?}", tx_hash);
        
        let timeout_duration = Duration::from_secs(30);
        let poll_interval = Duration::from_millis(500);
        
        let result = timeout(timeout_duration, async {
            loop {
                match self.data_validator.query_transaction_status(tx_hash).await {
                    Ok(status) => {
                        match status.status {
                            TransactionStatus::Confirmed | TransactionStatus::Failed => {
                                return Ok(status);
                            }
                            TransactionStatus::Pending => {
                                debug!("Transaction still pending, waiting...");
                                tokio::time::sleep(poll_interval).await;
                            }
                            TransactionStatus::NotFound => {
                                debug!("Transaction not found yet, waiting...");
                                tokio::time::sleep(poll_interval).await;
                            }
                        }
                    }
                    Err(e) => {
                        warn!("Error querying transaction status: {}", e);
                        tokio::time::sleep(poll_interval).await;
                    }
                }
            }
        }).await;
        
        match result {
            Ok(status) => Ok(status),
            Err(_) => Err(anyhow!("Transaction confirmation timeout")),
        }
    }

    /// Verify transaction success
    async fn verify_transaction_success(
        &self,
        tx_hash: H256,
        tx_status: &TransactionStatusData,
    ) -> Result<TransactionVerificationResult> {
        info!("Verifying transaction success for: {:?}", tx_hash);
        
        let success = tx_status.status == TransactionStatus::Confirmed;
        let expected = "Transaction confirmed successfully";
        let actual = format!("Transaction status: {:?}", tx_status.status);
        
        Ok(TransactionVerificationResult {
            verification_type: "transaction_success".to_string(),
            success,
            expected_value: expected.to_string(),
            actual_value: actual,
            error_message: if success { None } else { Some("Transaction failed or not confirmed".to_string()) },
        })
    }    /// Verify emergency stop state change
    async fn verify_emergency_stop_state_change(
        &self,
        initial_state: &super::data_validator::ContractStateData,
    ) -> Result<TransactionVerificationResult> {
        info!("Verifying emergency stop state change");
        
        // Wait a moment for state to update
        tokio::time::sleep(Duration::from_millis(1000)).await;
        
        let current_state = self.data_validator.query_contract_state().await?;
        
        let success = current_state.emergency_stopped && !initial_state.emergency_stopped;
        let expected = "Emergency stop activated (true)";
        let actual = format!("Emergency stopped: {}", current_state.emergency_stopped);
        
        Ok(TransactionVerificationResult {
            verification_type: "emergency_stop_state".to_string(),
            success,
            expected_value: expected.to_string(),
            actual_value: actual,
            error_message: if success { None } else { Some("Emergency stop state not changed".to_string()) },
        })
    }

    /// Verify pause state change
    async fn verify_pause_state_change(
        &self,
        initial_state: &super::data_validator::ContractStateData,
    ) -> Result<TransactionVerificationResult> {
        info!("Verifying pause state change");
        
        // Wait a moment for state to update
        tokio::time::sleep(Duration::from_millis(1000)).await;
        
        let current_state = self.data_validator.query_contract_state().await?;
        
        let success = current_state.is_paused && !initial_state.is_paused;
        let expected = "Contract paused (true)";
        let actual = format!("Contract paused: {}", current_state.is_paused);
        
        Ok(TransactionVerificationResult {
            verification_type: "pause_state".to_string(),
            success,
            expected_value: expected.to_string(),
            actual_value: actual,
            error_message: if success { None } else { Some("Pause state not changed".to_string()) },
        })
    }

    /// Verify restart state change
    async fn verify_restart_state_change(
        &self,
        initial_state: &super::data_validator::ContractStateData,
    ) -> Result<TransactionVerificationResult> {
        info!("Verifying restart state change");
        
        // Wait a moment for state to update
        tokio::time::sleep(Duration::from_millis(1000)).await;
        
        let current_state = self.data_validator.query_contract_state().await?;
        
        let success = !current_state.is_paused && initial_state.is_paused;
        let expected = "Contract unpaused (false)";
        let actual = format!("Contract paused: {}", current_state.is_paused);
        
        Ok(TransactionVerificationResult {
            verification_type: "restart_state".to_string(),
            success,
            expected_value: expected.to_string(),
            actual_value: actual,
            error_message: if success { None } else { Some("Restart state not changed".to_string()) },
        })
    }

    /// Verify opportunity execution results
    async fn verify_opportunity_execution_results(&self, tx_hash: H256) -> Result<TransactionVerificationResult> {
        info!("Verifying opportunity execution results for: {:?}", tx_hash);
        
        // Get transaction receipt to check for events
        let receipt = self.provider.get_transaction_receipt(tx_hash).await?;
        
        let success = if let Some(receipt) = receipt {
            // Check if transaction was successful and had events
            receipt.status == Some(1.into()) && !receipt.logs.is_empty()
        } else {
            false
        };
        
        let expected = "Opportunity executed with events emitted";
        let actual = if success {
            "Transaction successful with events"
        } else {
            "Transaction failed or no events"
        };
        
        Ok(TransactionVerificationResult {
            verification_type: "opportunity_execution".to_string(),
            success,
            expected_value: expected.to_string(),
            actual_value: actual.to_string(),
            error_message: if success { None } else { Some("Opportunity execution verification failed".to_string()) },
        })
    }

    // ============= TUI STATE VERIFICATION =============

    /// Verify TUI reflects emergency stop
    async fn verify_tui_reflects_emergency_stop(&mut self) -> Result<TransactionVerificationResult> {
        info!("Verifying TUI reflects emergency stop state");
        
        // Query contract status via TUI
        let tui_result = self.tui_tester.execute_command("query_contract_status").await?;
        
        let emergency_indicators = ["EMERGENCY", "STOPPED", "HALTED"];
        let success = emergency_indicators.iter().any(|indicator| {
            tui_result.output_captured.to_uppercase().contains(indicator)
        });
        
        let expected = "TUI shows emergency stop indicators";
        let actual = if success {
            "Emergency stop indicators found in TUI"
        } else {
            "No emergency stop indicators in TUI"
        };
        
        Ok(TransactionVerificationResult {
            verification_type: "tui_emergency_stop_display".to_string(),
            success,
            expected_value: expected.to_string(),
            actual_value: actual.to_string(),
            error_message: if success { None } else { Some("TUI does not reflect emergency stop state".to_string()) },
        })
    }

    /// Verify TUI reflects pause state
    async fn verify_tui_reflects_pause_state(&mut self) -> Result<TransactionVerificationResult> {
        info!("Verifying TUI reflects pause state");
        
        // Query contract status via TUI
        let tui_result = self.tui_tester.execute_command("query_contract_status").await?;
        
        let pause_indicators = ["PAUSED", "SUSPENDED", "INACTIVE"];
        let success = pause_indicators.iter().any(|indicator| {
            tui_result.output_captured.to_uppercase().contains(indicator)
        });
        
        let expected = "TUI shows pause indicators";
        let actual = if success {
            "Pause indicators found in TUI"
        } else {
            "No pause indicators in TUI"
        };
        
        Ok(TransactionVerificationResult {
            verification_type: "tui_pause_display".to_string(),
            success,
            expected_value: expected.to_string(),
            actual_value: actual.to_string(),
            error_message: if success { None } else { Some("TUI does not reflect pause state".to_string()) },
        })
    }

    /// Verify TUI reflects active state
    async fn verify_tui_reflects_active_state(&mut self) -> Result<TransactionVerificationResult> {
        info!("Verifying TUI reflects active state");
        
        // Query contract status via TUI
        let tui_result = self.tui_tester.execute_command("query_contract_status").await?;
        
        let active_indicators = ["ACTIVE", "RUNNING", "OPERATIONAL"];
        let success = active_indicators.iter().any(|indicator| {
            tui_result.output_captured.to_uppercase().contains(indicator)
        });
        
        let expected = "TUI shows active indicators";
        let actual = if success {
            "Active indicators found in TUI"
        } else {
            "No active indicators in TUI"
        };
        
        Ok(TransactionVerificationResult {
            verification_type: "tui_active_display".to_string(),
            success,
            expected_value: expected.to_string(),
            actual_value: actual.to_string(),
            error_message: if success { None } else { Some("TUI does not reflect active state".to_string()) },
        })
    }

    /// Verify TUI shows execution results
    async fn verify_tui_shows_execution_results(&mut self) -> Result<TransactionVerificationResult> {
        info!("Verifying TUI shows execution results");
        
        // Query balances to see if they changed
        let tui_result = self.tui_tester.execute_command("query_balances").await?;
        
        let execution_indicators = ["EXECUTED", "PROFIT", "LOSS", "COMPLETED"];
        let success = execution_indicators.iter().any(|indicator| {
            tui_result.output_captured.to_uppercase().contains(indicator)
        });
        
        let expected = "TUI shows execution result indicators";
        let actual = if success {
            "Execution result indicators found in TUI"
        } else {
            "No execution result indicators in TUI"
        };
        
        Ok(TransactionVerificationResult {
            verification_type: "tui_execution_results_display".to_string(),
            success,
            expected_value: expected.to_string(),
            actual_value: actual.to_string(),
            error_message: if success { None } else { Some("TUI does not show execution results".to_string()) },
        })
    }    
    /// Test comprehensive emergency stop functionality
    pub async fn test_emergency_stop_functionality(&mut self) -> Result<EmergencyStopTestResult> {
        info!("Starting comprehensive emergency stop functionality testing");
        
        let start_time = Instant::now();
        let mut error_messages = Vec::new();
        let mut verification_details = HashMap::new();
        
        // Step 1: Execute emergency stop command
        let command_executed = match self.tui_tester.execute_command("emergency_stop").await {
            Ok(result) => {
                verification_details.insert("command_output".to_string(), result.output_captured);
                result.success
            }
            Err(e) => {
                error_messages.push(format!("Emergency stop command failed: {}", e));
                false
            }
        };
        
        // Step 2: Verify stop signal was sent
        let stop_signal_sent = if command_executed {
            self.verify_emergency_stop_signal_sent().await.unwrap_or_else(|e| {
                error_messages.push(format!("Failed to verify stop signal: {}", e));
                false
            })
        } else {
            false
        };
        
        // Step 3: Verify contract state changed
        let contract_state_changed = if stop_signal_sent {
            self.verify_contract_emergency_stopped().await.unwrap_or_else(|e| {
                error_messages.push(format!("Failed to verify contract state: {}", e));
                false
            })
        } else {
            false
        };
        
        // Step 4: Verify all operations halted
        let all_operations_halted = if contract_state_changed {
            self.verify_all_operations_halted().await.unwrap_or_else(|e| {
                error_messages.push(format!("Failed to verify operations halted: {}", e));
                false
            })
        } else {
            false
        };
        
        // Step 5: Test recovery possibility
        let recovery_possible = self.test_emergency_stop_recovery().await.unwrap_or_else(|e| {
            error_messages.push(format!("Failed to test recovery: {}", e));
            false
        });
        
        let execution_time = start_time.elapsed().as_millis() as u64;
        
        Ok(EmergencyStopTestResult {
            command_executed,
            stop_signal_sent,
            contract_state_changed,
            all_operations_halted,
            recovery_possible,
            execution_time_ms: execution_time,
            error_messages,
            verification_details,
        })
    }

    /// Verify emergency stop signal was sent
    async fn verify_emergency_stop_signal_sent(&self) -> Result<bool> {
        // Check recent transactions for emergency stop call
        let recent_tx = self.get_most_recent_contract_transaction().await?;
        
        if let Some(tx_hash) = recent_tx {
            let tx_status = self.data_validator.query_transaction_status(tx_hash).await?;
            Ok(tx_status.status == TransactionStatus::Confirmed)
        } else {
            Ok(false)
        }
    }

    /// Verify contract is in emergency stopped state
    async fn verify_contract_emergency_stopped(&self) -> Result<bool> {
        let contract_state = self.data_validator.query_contract_state().await?;
        Ok(contract_state.emergency_stopped)
    }

    /// Verify all operations are halted
    async fn verify_all_operations_halted(&mut self) -> Result<bool> {
        // Try to execute an opportunity - should fail
        let execute_result = self.tui_tester.execute_command("execute_opportunity").await?;
        
        // Check if execution was blocked
        let blocked_indicators = ["BLOCKED", "HALTED", "EMERGENCY", "STOPPED"];
        let operations_blocked = blocked_indicators.iter().any(|indicator| {
            execute_result.output_captured.to_uppercase().contains(indicator)
        });
        
        Ok(operations_blocked)
    }

    /// Test emergency stop recovery
    async fn test_emergency_stop_recovery(&mut self) -> Result<bool> {
        // Try to restart the bot - should work for recovery
        let restart_result = self.tui_tester.execute_command("restart_bot").await?;
        
        if restart_result.success {
            // Wait for state to update
            tokio::time::sleep(Duration::from_millis(2000)).await;
            
            // Check if contract is no longer emergency stopped
            let contract_state = self.data_validator.query_contract_state().await?;
            Ok(!contract_state.emergency_stopped)
        } else {
            Ok(false)
        }
    }

    // ============= ERROR MESSAGE VALIDATION =============

    /// Test comprehensive error message validation for failed operations
    pub async fn test_error_message_validation(&mut self) -> Result<Vec<ErrorMessageValidationResult>> {
        info!("Starting comprehensive error message validation testing");
        
        let mut results = Vec::new();
        
        // Test insufficient balance error
        results.push(self.test_insufficient_balance_error().await?);
        
        // Test invalid transaction error
        results.push(self.test_invalid_transaction_error().await?);
        
        // Test network connection error
        results.push(self.test_network_connection_error().await?);
        
        // Test contract interaction error
        results.push(self.test_contract_interaction_error().await?);
        
        // Test timeout error
        results.push(self.test_timeout_error().await?);
        
        info!("Completed error message validation testing. Results: {}/{} scenarios validated",
              results.iter().filter(|r| r.error_detected).count(),
              results.len());
        
        Ok(results)
    }

    /// Test insufficient balance error scenario
    async fn test_insufficient_balance_error(&mut self) -> Result<ErrorMessageValidationResult> {
        info!("Testing insufficient balance error scenario");
        
        // This would typically involve setting up a scenario with insufficient balance
        // For now, we'll simulate by trying to execute with minimal balance
        
        let execute_result = self.tui_tester.execute_command("execute_opportunity").await?;
        
        let error_indicators = ["INSUFFICIENT", "BALANCE", "FUNDS"];
        let error_detected = error_indicators.iter().any(|indicator| {
            execute_result.output_captured.to_uppercase().contains(indicator)
        });
        
        let error_message_clear = if error_detected {
            self.validate_error_message_clarity(&execute_result.output_captured, "balance")
        } else {
            false
        };
        
        Ok(ErrorMessageValidationResult {
            error_scenario: "insufficient_balance".to_string(),
            error_detected,
            error_message_clear,
            error_message_actionable: error_message_clear,
            recovery_instructions_provided: error_message_clear,
            error_categorized_correctly: error_detected,
            actual_error_message: execute_result.output_captured,
        })
    }

    /// Test invalid transaction error scenario
    async fn test_invalid_transaction_error(&mut self) -> Result<ErrorMessageValidationResult> {
        info!("Testing invalid transaction error scenario");
        
        // Try to execute with invalid parameters
        let execute_result = self.tui_tester.execute_command("execute_opportunity").await?;
        
        let error_indicators = ["INVALID", "TRANSACTION", "FAILED"];
        let error_detected = error_indicators.iter().any(|indicator| {
            execute_result.output_captured.to_uppercase().contains(indicator)
        });
        
        let error_message_clear = if error_detected {
            self.validate_error_message_clarity(&execute_result.output_captured, "transaction")
        } else {
            false
        };
        
        Ok(ErrorMessageValidationResult {
            error_scenario: "invalid_transaction".to_string(),
            error_detected,
            error_message_clear,
            error_message_actionable: error_message_clear,
            recovery_instructions_provided: error_message_clear,
            error_categorized_correctly: error_detected,
            actual_error_message: execute_result.output_captured,
        })
    }

    /// Test network connection error scenario
    async fn test_network_connection_error(&mut self) -> Result<ErrorMessageValidationResult> {
        info!("Testing network connection error scenario");
        
        // Query contract status to check connection
        let status_result = self.tui_tester.execute_command("query_contract_status").await?;
        
        let error_indicators = ["CONNECTION", "NETWORK", "OFFLINE"];
        let error_detected = error_indicators.iter().any(|indicator| {
            status_result.output_captured.to_uppercase().contains(indicator)
        });
        
        let error_message_clear = if error_detected {
            self.validate_error_message_clarity(&status_result.output_captured, "network")
        } else {
            false
        };
        
        Ok(ErrorMessageValidationResult {
            error_scenario: "network_connection".to_string(),
            error_detected,
            error_message_clear,
            error_message_actionable: error_message_clear,
            recovery_instructions_provided: error_message_clear,
            error_categorized_correctly: error_detected,
            actual_error_message: status_result.output_captured,
        })
    }

    /// Test contract interaction error scenario
    async fn test_contract_interaction_error(&mut self) -> Result<ErrorMessageValidationResult> {
        info!("Testing contract interaction error scenario");
        
        // Try to interact with contract
        let pause_result = self.tui_tester.execute_command("pause_bot").await?;
        
        let error_indicators = ["CONTRACT", "ERROR", "REVERT"];
        let error_detected = error_indicators.iter().any(|indicator| {
            pause_result.output_captured.to_uppercase().contains(indicator)
        });
        
        let error_message_clear = if error_detected {
            self.validate_error_message_clarity(&pause_result.output_captured, "contract")
        } else {
            false
        };
        
        Ok(ErrorMessageValidationResult {
            error_scenario: "contract_interaction".to_string(),
            error_detected,
            error_message_clear,
            error_message_actionable: error_message_clear,
            recovery_instructions_provided: error_message_clear,
            error_categorized_correctly: error_detected,
            actual_error_message: pause_result.output_captured,
        })
    }

    /// Test timeout error scenario
    async fn test_timeout_error(&mut self) -> Result<ErrorMessageValidationResult> {
        info!("Testing timeout error scenario");
        
        // Execute with very short timeout to force timeout
        let execute_result = self.tui_tester.execute_command("execute_opportunity").await?;
        
        let error_indicators = ["TIMEOUT", "EXPIRED", "SLOW"];
        let error_detected = error_indicators.iter().any(|indicator| {
            execute_result.output_captured.to_uppercase().contains(indicator)
        });
        
        let error_message_clear = if error_detected {
            self.validate_error_message_clarity(&execute_result.output_captured, "timeout")
        } else {
            false
        };
        
        Ok(ErrorMessageValidationResult {
            error_scenario: "timeout".to_string(),
            error_detected,
            error_message_clear,
            error_message_actionable: error_message_clear,
            recovery_instructions_provided: error_message_clear,
            error_categorized_correctly: error_detected,
            actual_error_message: execute_result.output_captured,
        })
    }

    /// Validate error message clarity and actionability
    fn validate_error_message_clarity(&self, error_message: &str, error_type: &str) -> bool {
        let clarity_indicators = match error_type {
            "balance" => vec!["insufficient", "balance", "required", "available"],
            "transaction" => vec!["invalid", "failed", "reverted", "gas"],
            "network" => vec!["connection", "network", "offline", "retry"],
            "contract" => vec!["contract", "error", "revert", "function"],
            "timeout" => vec!["timeout", "slow", "retry", "wait"],
            _ => vec!["error", "failed"],
        };
        
        let message_lower = error_message.to_lowercase();
        clarity_indicators.iter().any(|indicator| message_lower.contains(indicator))
    }

    /// Run comprehensive transaction command testing
    pub async fn run_comprehensive_transaction_testing(&mut self) -> Result<TransactionTestSuite> {
        info!("Starting comprehensive transaction command testing suite");
        
        let start_time = Instant::now();
        
        // Test transaction initiation commands
        let transaction_results = self.test_transaction_initiation_commands().await?;
        
        // Test emergency stop functionality
        let emergency_stop_result = self.test_emergency_stop_functionality().await?;
        
        // Test error message validation
        let error_validation_results = self.test_error_message_validation().await?;
        
        let execution_time = start_time.elapsed();
        
        let test_suite = TransactionTestSuite {
            transaction_command_results: transaction_results,
            emergency_stop_result,
            error_validation_results,
            total_execution_time: execution_time,
            overall_success: true, // Will be calculated based on results
        };
        
        info!("Completed comprehensive transaction command testing in {:?}", execution_time);
        
        Ok(test_suite)
    }
}

/// Complete transaction test suite results
#[derive(Debug)]
pub struct TransactionTestSuite {
    pub transaction_command_results: Vec<TransactionCommandTestResult>,
    pub emergency_stop_result: EmergencyStopTestResult,
    pub error_validation_results: Vec<ErrorMessageValidationResult>,
    pub total_execution_time: Duration,
    pub overall_success: bool,
}

impl TransactionTestSuite {
    /// Calculate success statistics
    pub fn calculate_statistics(&self) -> TransactionTestStatistics {
        let total_transaction_tests = self.transaction_command_results.len();
        let successful_transaction_tests = self.transaction_command_results.iter()
            .filter(|r| r.success)
            .count();
        
        let total_error_tests = self.error_validation_results.len();
        let successful_error_tests = self.error_validation_results.iter()
            .filter(|r| r.error_detected)
            .count();
        
        let emergency_stop_success = self.emergency_stop_result.command_executed &&
            self.emergency_stop_result.stop_signal_sent &&
            self.emergency_stop_result.contract_state_changed;
        
        TransactionTestStatistics {
            total_transaction_tests,
            successful_transaction_tests,
            transaction_success_rate: if total_transaction_tests > 0 {
                successful_transaction_tests as f64 / total_transaction_tests as f64
            } else {
                0.0
            },
            total_error_tests,
            successful_error_tests,
            error_detection_rate: if total_error_tests > 0 {
                successful_error_tests as f64 / total_error_tests as f64
            } else {
                0.0
            },
            emergency_stop_success,
            total_execution_time: self.total_execution_time,
        }
    }
}

/// Transaction test statistics
#[derive(Debug)]
pub struct TransactionTestStatistics {
    pub total_transaction_tests: usize,
    pub successful_transaction_tests: usize,
    pub transaction_success_rate: f64,
    pub total_error_tests: usize,
    pub successful_error_tests: usize,
    pub error_detection_rate: f64,
    pub emergency_stop_success: bool,
    pub total_execution_time: Duration,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    #[test]
    fn test_error_message_validation() {
        // Create a mock tester for testing validation logic
        let anvil_url = "http://localhost:8545".to_string();
        let contract_address = Address::from_str("******************************************").unwrap();
        
        // Test error message clarity validation
        let clear_message = "Error: Insufficient balance. Required: 100 USDC, Available: 50 USDC";
        let unclear_message = "Something went wrong";
        
        // We can't create a full tester without AnvilClient, so we'll test the validation logic directly
        let clarity_indicators = vec!["insufficient", "balance", "required", "available"];
        let message_lower = clear_message.to_lowercase();
        let is_clear = clarity_indicators.iter().any(|indicator| message_lower.contains(indicator));
        assert!(is_clear);
        
        let unclear_lower = unclear_message.to_lowercase();
        let is_unclear = clarity_indicators.iter().any(|indicator| unclear_lower.contains(indicator));
        assert!(!is_unclear);
    }

    #[test]
    fn test_transaction_verification_result() {
        let result = TransactionVerificationResult {
            verification_type: "test".to_string(),
            success: true,
            expected_value: "expected".to_string(),
            actual_value: "actual".to_string(),
            error_message: None,
        };
        
        assert_eq!(result.verification_type, "test");
        assert!(result.success);
        assert!(result.error_message.is_none());
    }

    #[test]
    fn test_emergency_stop_test_result() {
        let result = EmergencyStopTestResult {
            command_executed: true,
            stop_signal_sent: true,
            contract_state_changed: true,
            all_operations_halted: true,
            recovery_possible: true,
            execution_time_ms: 1000,
            error_messages: Vec::new(),
            verification_details: HashMap::new(),
        };
        
        assert!(result.command_executed);
        assert!(result.stop_signal_sent);
        assert!(result.contract_state_changed);
        assert!(result.all_operations_halted);
        assert!(result.recovery_possible);
        assert_eq!(result.execution_time_ms, 1000);
    }

    #[test]
    fn test_transaction_test_statistics() {
        let test_suite = TransactionTestSuite {
            transaction_command_results: vec![
                TransactionCommandTestResult {
                    command_name: "test1".to_string(),
                    success: true,
                    transaction_initiated: true,
                    transaction_hash: None,
                    transaction_status: None,
                    execution_time_ms: 1000,
                    error_message: None,
                    tui_output: String::new(),
                    verification_results: Vec::new(),
                },
                TransactionCommandTestResult {
                    command_name: "test2".to_string(),
                    success: false,
                    transaction_initiated: false,
                    transaction_hash: None,
                    transaction_status: None,
                    execution_time_ms: 500,
                    error_message: Some("Failed".to_string()),
                    tui_output: String::new(),
                    verification_results: Vec::new(),
                },
            ],
            emergency_stop_result: EmergencyStopTestResult {
                command_executed: true,
                stop_signal_sent: true,
                contract_state_changed: true,
                all_operations_halted: true,
                recovery_possible: true,
                execution_time_ms: 2000,
                error_messages: Vec::new(),
                verification_details: HashMap::new(),
            },
            error_validation_results: vec![
                ErrorMessageValidationResult {
                    error_scenario: "test_error".to_string(),
                    error_detected: true,
                    error_message_clear: true,
                    error_message_actionable: true,
                    recovery_instructions_provided: true,
                    error_categorized_correctly: true,
                    actual_error_message: "Test error message".to_string(),
                },
            ],
            total_execution_time: Duration::from_secs(5),
            overall_success: true,
        };
        
        let stats = test_suite.calculate_statistics();
        
        assert_eq!(stats.total_transaction_tests, 2);
        assert_eq!(stats.successful_transaction_tests, 1);
        assert_eq!(stats.transaction_success_rate, 0.5);
        assert_eq!(stats.total_error_tests, 1);
        assert_eq!(stats.successful_error_tests, 1);
        assert_eq!(stats.error_detection_rate, 1.0);
        assert!(stats.emergency_stop_success);
    }
}