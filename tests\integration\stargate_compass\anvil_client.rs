// Anvil client utilities for Stargate Compass integration testing
// Provides blockchain interaction and contract verification capabilities

use super::core::*;
use ethers::{
    providers::{Provider, Http, Middleware},
    types::{Address, H256, U256, TransactionRequest, TransactionReceipt, Block, TxHash},
    utils::{Anvil, AnvilInstance},
    signers::{LocalWallet, Signer},
    contract::{Contract, ContractFactory},
    abi::Abi,
};
use std::sync::Arc;
use anyhow::{Result, anyhow};
use async_trait::async_trait;
use std::time::{Duration, Instant};
use tokio::time::timeout;

/// Anvil client for integration testing
pub struct AnvilClient {
    pub anvil: Option<AnvilInstance>,
    pub provider: Arc<Provider<Http>>,
    pub wallet: LocalWallet,
    pub chain_id: u64,
    pub url: String,
    pub contract_address: Option<Address>,
}

impl AnvilClient {
    /// Create new Anvil client with Base fork for realistic testing
    pub async fn new_base_fork(contract_address: Option<Address>) -> Result<Self> {
        let anvil = Anvil::new()
            .fork("https://mainnet.base.org")
            .fork_block_number(20000000u64)
            .chain_id(8453u64)
            .port(8545u16)
            .spawn();
        
        let url = anvil.endpoint();
        let provider = Arc::new(Provider::<Http>::try_from(&url)?);
        let wallet = anvil.keys()[0].clone().into();
        
        Ok(Self {
            anvil: Some(anvil),
            provider,
            wallet,
            chain_id: 8453,
            url,
            contract_address,
        })
    }
    
    /// Create new Anvil client connecting to existing instance
    pub async fn connect_existing(url: String, contract_address: Option<Address>) -> Result<Self> {
        let provider = Arc::new(Provider::<Http>::try_from(&url)?);
        
        // Use a default wallet for testing (in production, this would be configurable)
        let wallet = "0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80"
            .parse::<LocalWallet>()?;
        
        let chain_id = provider.get_chainid().await?.as_u64();
        
        Ok(Self {
            anvil: None,
            provider,
            wallet,
            chain_id,
            url,
            contract_address,
        })
    }
    
    /// Check if Anvil connection is healthy
    pub async fn health_check(&self) -> Result<bool> {
        match timeout(Duration::from_secs(5), self.provider.get_block_number()).await {
            Ok(Ok(_)) => Ok(true),
            Ok(Err(e)) => Err(anyhow!("Provider error: {}", e)),
            Err(_) => Err(anyhow!("Connection timeout")),
        }
    }
    
    /// Get current block number
    pub async fn current_block(&self) -> Result<u64> {
        Ok(self.provider.get_block_number().await?.as_u64())
    }
    
    /// Get current block with full transaction details
    pub async fn current_block_full(&self) -> Result<Option<Block<TxHash>>> {
        let block_number = self.current_block().await?;
        Ok(self.provider.get_block(block_number).await?)
    }
    
    /// Fund an address with ETH for testing
    pub async fn fund_address(&self, address: Address, amount_eth: u64) -> Result<H256> {
        let tx = TransactionRequest::new()
            .to(address)
            .value(U256::from(amount_eth) * U256::exp10(18))
            .gas(21000u64);
        
        let tx_hash = self.provider.send_transaction(tx, None).await?.await?;
        Ok(tx_hash.unwrap_or_default().transaction_hash)
    }
    
    /// Get balance of an address
    pub async fn get_balance(&self, address: Address) -> Result<U256> {
        Ok(self.provider.get_balance(address, None).await?)
    }
    
    /// Mine a specific number of blocks
    pub async fn mine_blocks(&self, count: u64) -> Result<()> {
        for _ in 0..count {
            self.provider.request::<_, ()>("evm_mine", ()).await?;
        }
        Ok(())
    }
    
    /// Set next block timestamp
    pub async fn set_next_block_timestamp(&self, timestamp: u64) -> Result<()> {
        self.provider.request::<_, ()>("evm_setNextBlockTimestamp", [timestamp]).await?;
        Ok(())
    }
    
    /// Take a snapshot of current blockchain state
    pub async fn snapshot(&self) -> Result<U256> {
        let snapshot_id: U256 = self.provider.request("evm_snapshot", ()).await?;
        Ok(snapshot_id)
    }
    
    /// Revert to a previous snapshot
    pub async fn revert_to_snapshot(&self, snapshot_id: U256) -> Result<()> {
        self.provider.request::<_, ()>("evm_revert", [snapshot_id]).await?;
        Ok(())
    }
    
    /// Verify contract deployment at address
    pub async fn verify_contract_deployment(&self, address: Address) -> Result<ContractVerificationResult> {
        let start_time = Instant::now();
        
        // Check if there's code at the address
        let code = self.provider.get_code(address, None).await?;
        let has_code = !code.is_empty();
        
        if !has_code {
            return Ok(ContractVerificationResult {
                deployed: false,
                bytecode_matches: false,
                functions_callable: false,
                verification_time: start_time.elapsed(),
                error_message: Some("No code found at address".to_string()),
            });
        }
        
        // Try to call a basic function to verify contract is functional
        let functions_callable = self.test_contract_basic_functionality(address).await.unwrap_or(false);
        
        Ok(ContractVerificationResult {
            deployed: true,
            bytecode_matches: true, // We assume bytecode matches if code exists
            functions_callable,
            verification_time: start_time.elapsed(),
            error_message: None,
        })
    }
    
    /// Test basic contract functionality
    async fn test_contract_basic_functionality(&self, address: Address) -> Result<bool> {
        // Try to call a view function that should exist on StargateCompassV1
        // This is a simplified test - in production, we'd use the actual ABI
        let call_data = vec![0x00, 0x00, 0x00, 0x00]; // Placeholder call data
        
        let tx = TransactionRequest::new()
            .to(address)
            .data(call_data);
        
        match self.provider.call(&tx.into(), None).await {
            Ok(_) => Ok(true),
            Err(_) => Ok(false), // Contract exists but function call failed
        }
    }
    
    /// Execute a transaction and wait for confirmation
    pub async fn execute_transaction(&self, tx: TransactionRequest) -> Result<TransactionValidationResult> {
        let start_time = Instant::now();
        
        // Send transaction
        let pending_tx = self.provider.send_transaction(tx, None).await?;
        let tx_hash = pending_tx.tx_hash();
        
        // Wait for confirmation
        let receipt = match pending_tx.await? {
            Some(receipt) => receipt,
            None => return Err(anyhow!("Transaction receipt not found")),
        };
        
        // Analyze transaction result
        let success = receipt.status == Some(1.into());
        let reverted = receipt.status == Some(0.into());
        let gas_used = receipt.gas_used.unwrap_or_default();
        
        // Extract events (simplified - in production, use ABI decoding)
        let events_emitted = receipt.logs.iter()
            .map(|log| format!("Event at {}", log.address))
            .collect();
        
        Ok(TransactionValidationResult {
            transaction_hash: tx_hash,
            success,
            reverted,
            gas_used,
            gas_price: receipt.effective_gas_price.unwrap_or_default(),
            return_values: Vec::new(), // Would be populated with ABI decoding
            events_emitted,
            validation_errors: Vec::new(),
            execution_time: start_time.elapsed(),
        })
    }
    
    /// Get transaction receipt with retry logic
    pub async fn get_transaction_receipt_with_retry(&self, tx_hash: H256, max_retries: u32) -> Result<TransactionReceipt> {
        for attempt in 0..max_retries {
            match self.provider.get_transaction_receipt(tx_hash).await? {
                Some(receipt) => return Ok(receipt),
                None => {
                    if attempt < max_retries - 1 {
                        tokio::time::sleep(Duration::from_millis(500)).await;
                    }
                }
            }
        }
        Err(anyhow!("Transaction receipt not found after {} attempts", max_retries))
    }
    
    /// Monitor transaction status until confirmation
    pub async fn monitor_transaction(&self, tx_hash: H256, timeout_secs: u64) -> Result<TransactionStatus> {
        let start_time = Instant::now();
        let timeout_duration = Duration::from_secs(timeout_secs);
        
        while start_time.elapsed() < timeout_duration {
            match self.provider.get_transaction_receipt(tx_hash).await? {
                Some(receipt) => {
                    return Ok(TransactionStatus {
                        confirmed: true,
                        success: receipt.status == Some(1.into()),
                        block_number: receipt.block_number.map(|n| n.as_u64()),
                        gas_used: receipt.gas_used,
                        confirmation_time: start_time.elapsed(),
                    });
                }
                None => {
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }
        }
        
        Ok(TransactionStatus {
            confirmed: false,
            success: false,
            block_number: None,
            gas_used: None,
            confirmation_time: start_time.elapsed(),
        })
    }
    
    /// Get comprehensive blockchain state information
    pub async fn get_blockchain_state(&self) -> Result<BlockchainState> {
        let block_number = self.current_block().await?;
        let block = self.current_block_full().await?;
        let chain_id = self.chain_id;
        
        let gas_price = self.provider.get_gas_price().await?;
        
        // Get some basic network stats
        let peer_count = self.provider.request::<_, U256>("net_peerCount", ()).await
            .unwrap_or_default();
        
        Ok(BlockchainState {
            block_number,
            chain_id,
            gas_price,
            peer_count: peer_count.as_u64(),
            block_timestamp: block.and_then(|b| Some(b.timestamp)).map(|t| t.as_u64()),
            is_mining: true, // Anvil is always mining
            network_healthy: true,
        })
    }
}

// ============= SUPPORTING DATA STRUCTURES =============

/// Contract verification result
#[derive(Debug, Clone)]
pub struct ContractVerificationResult {
    pub deployed: bool,
    pub bytecode_matches: bool,
    pub functions_callable: bool,
    pub verification_time: Duration,
    pub error_message: Option<String>,
}

/// Transaction monitoring status
#[derive(Debug, Clone)]
pub struct TransactionStatus {
    pub confirmed: bool,
    pub success: bool,
    pub block_number: Option<u64>,
    pub gas_used: Option<U256>,
    pub confirmation_time: Duration,
}

/// Comprehensive blockchain state
#[derive(Debug, Clone)]
pub struct BlockchainState {
    pub block_number: u64,
    pub chain_id: u64,
    pub gas_price: U256,
    pub peer_count: u64,
    pub block_timestamp: Option<u64>,
    pub is_mining: bool,
    pub network_healthy: bool,
}

// ============= UTILITY FUNCTIONS =============

/// Check if Anvil is available on the system
pub fn is_anvil_available() -> bool {
    std::process::Command::new("anvil")
        .arg("--version")
        .output()
        .is_ok()
}

/// Get default test token addresses for Base network
pub mod test_tokens {
    use ethers::types::Address;
    use std::str::FromStr;

    /// WETH on Base
    pub fn weth() -> Address {
        Address::from_str("******************************************").unwrap()
    }

    /// USDC on Base
    pub fn usdc() -> Address {
        Address::from_str("******************************************").unwrap()
    }

    /// DAI on Base
    pub fn dai() -> Address {
        Address::from_str("******************************************").unwrap()
    }
}

/// Create a test transaction request
pub fn create_test_transaction(to: Address, value: U256, data: Vec<u8>) -> TransactionRequest {
    TransactionRequest::new()
        .to(to)
        .value(value)
        .data(data)
        .gas(100000u64)
}

/// Wait for transaction confirmation with timeout
pub async fn wait_for_confirmation(
    provider: &Provider<Http>,
    tx_hash: H256,
    timeout_secs: u64,
) -> Result<TransactionReceipt> {
    let timeout_duration = Duration::from_secs(timeout_secs);
    
    match timeout(timeout_duration, async {
        loop {
            if let Some(receipt) = provider.get_transaction_receipt(tx_hash).await? {
                return Ok(receipt);
            }
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
    }).await {
        Ok(result) => result,
        Err(_) => Err(anyhow!("Transaction confirmation timeout")),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_anvil_availability() {
        // This test checks if Anvil is available for testing
        if is_anvil_available() {
            println!("Anvil is available for testing");
        } else {
            println!("Anvil is not available - integration tests may be skipped");
        }
    }

    #[tokio::test]
    async fn test_anvil_client_creation() {
        if !is_anvil_available() {
            println!("Skipping test - Anvil not available");
            return;
        }

        let client = AnvilClient::new_base_fork(None).await;
        assert!(client.is_ok(), "Should be able to create Anvil client");
        
        if let Ok(client) = client {
            let health = client.health_check().await;
            assert!(health.is_ok(), "Health check should pass");
        }
    }

    #[tokio::test]
    async fn test_blockchain_operations() {
        if !is_anvil_available() {
            println!("Skipping test - Anvil not available");
            return;
        }

        let client = AnvilClient::new_base_fork(None).await.unwrap();
        
        // Test basic operations
        let block_number = client.current_block().await;
        assert!(block_number.is_ok(), "Should be able to get block number");
        
        let balance = client.get_balance(client.wallet.address()).await;
        assert!(balance.is_ok(), "Should be able to get balance");
        
        let state = client.get_blockchain_state().await;
        assert!(state.is_ok(), "Should be able to get blockchain state");
    }
}