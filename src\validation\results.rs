// src/validation/results.rs

//! Validation result types and utilities

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::time::Duration;

pub use crate::validation::types::{ValidationStatus, ValidationError, ValidationWarning};

/// Result of a validation operation
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ValidationResult<T> {
    /// Unique identifier for this validation test
    pub test_id: String,
    /// Human-readable name of the test
    pub test_name: String,
    /// Status of the validation
    pub status: ValidationStatus,
    /// Time taken to execute the validation
    pub execution_time: Duration,
    /// Test-specific metrics and data
    pub metrics: T,
    /// Errors that occurred during validation
    pub errors: Vec<ValidationError>,
    /// Warnings generated during validation
    pub warnings: Vec<ValidationWarning>,
    /// Timestamp when the validation was performed
    pub timestamp: DateTime<Utc>,
}

impl<T> ValidationResult<T> {
    /// Create a new validation result
    pub fn new(
        test_id: impl Into<String>,
        test_name: impl Into<String>,
        status: ValidationStatus,
        execution_time: Duration,
        metrics: T,
    ) -> Self {
        Self {
            test_id: test_id.into(),
            test_name: test_name.into(),
            status,
            execution_time,
            metrics,
            errors: Vec::new(),
            warnings: Vec::new(),
            timestamp: Utc::now(),
        }
    }

    /// Create a successful validation result
    pub fn success(
        test_id: impl Into<String>,
        test_name: impl Into<String>,
        execution_time: Duration,
        metrics: T,
    ) -> Self {
        Self::new(test_id, test_name, ValidationStatus::Passed, execution_time, metrics)
    }

    /// Create a failed validation result
    pub fn failure(
        test_id: impl Into<String>,
        test_name: impl Into<String>,
        execution_time: Duration,
        metrics: T,
        error: ValidationError,
    ) -> Self {
        let mut result = Self::new(test_id, test_name, ValidationStatus::Failed, execution_time, metrics);
        result.errors.push(error);
        result
    }

    /// Create a validation result with warnings
    pub fn warning(
        test_id: impl Into<String>,
        test_name: impl Into<String>,
        execution_time: Duration,
        metrics: T,
        warning: ValidationWarning,
    ) -> Self {
        let mut result = Self::new(test_id, test_name, ValidationStatus::Warning, execution_time, metrics);
        result.warnings.push(warning);
        result
    }

    /// Create a skipped validation result
    pub fn skipped(
        test_id: impl Into<String>,
        test_name: impl Into<String>,
        metrics: T,
        reason: impl Into<String>,
    ) -> Self {
        let mut result = Self::new(
            test_id,
            test_name,
            ValidationStatus::Skipped,
            Duration::from_millis(0),
            metrics,
        );
        result.warnings.push(ValidationWarning::new(
            "VALIDATION_SKIPPED",
            reason,
            "validation_framework",
        ));
        result
    }

    /// Add an error to the validation result
    pub fn add_error(&mut self, error: ValidationError) {
        self.errors.push(error);
        if self.status == ValidationStatus::Passed {
            self.status = ValidationStatus::Failed;
        }
    }

    /// Add a warning to the validation result
    pub fn add_warning(&mut self, warning: ValidationWarning) {
        self.warnings.push(warning);
        if self.status == ValidationStatus::Passed {
            self.status = ValidationStatus::Warning;
        }
    }

    /// Check if the validation was successful (passed or warning)
    pub fn is_successful(&self) -> bool {
        matches!(self.status, ValidationStatus::Passed | ValidationStatus::Warning)
    }

    /// Check if the validation failed
    pub fn is_failed(&self) -> bool {
        self.status == ValidationStatus::Failed
    }

    /// Check if the validation was skipped
    pub fn is_skipped(&self) -> bool {
        self.status == ValidationStatus::Skipped
    }

    /// Get a summary string for the validation result
    pub fn summary(&self) -> String {
        let error_count = self.errors.len();
        let warning_count = self.warnings.len();
        
        match self.status {
            ValidationStatus::Passed => {
                format!("✓ {} - PASSED ({}ms)", self.test_name, self.execution_time.as_millis())
            }
            ValidationStatus::Failed => {
                format!(
                    "✗ {} - FAILED ({} errors, {}ms)",
                    self.test_name,
                    error_count,
                    self.execution_time.as_millis()
                )
            }
            ValidationStatus::Warning => {
                format!(
                    "⚠ {} - WARNING ({} warnings, {}ms)",
                    self.test_name,
                    warning_count,
                    self.execution_time.as_millis()
                )
            }
            ValidationStatus::Skipped => {
                format!("⊘ {} - SKIPPED", self.test_name)
            }
            ValidationStatus::InProgress => {
                format!("⟳ {} - IN PROGRESS", self.test_name)
            }
        }
    }

    /// Get detailed information about errors and warnings
    pub fn details(&self) -> String {
        let mut details = Vec::new();

        if !self.errors.is_empty() {
            details.push("Errors:".to_string());
            for (i, error) in self.errors.iter().enumerate() {
                details.push(format!("  {}. [{}] {} ({})", i + 1, error.code, error.message, error.component));
            }
        }

        if !self.warnings.is_empty() {
            details.push("Warnings:".to_string());
            for (i, warning) in self.warnings.iter().enumerate() {
                details.push(format!("  {}. [{}] {} ({})", i + 1, warning.code, warning.message, warning.component));
            }
        }

        details.join("\n")
    }

    /// Convert to a different metrics type
    pub fn map_metrics<U, F>(self, f: F) -> ValidationResult<U>
    where
        F: FnOnce(T) -> U,
    {
        ValidationResult {
            test_id: self.test_id,
            test_name: self.test_name,
            status: self.status,
            execution_time: self.execution_time,
            metrics: f(self.metrics),
            errors: self.errors,
            warnings: self.warnings,
            timestamp: self.timestamp,
        }
    }
}

impl<T: std::fmt::Display> std::fmt::Display for ValidationResult<T> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.summary())?;
        
        if !self.errors.is_empty() || !self.warnings.is_empty() {
            write!(f, "\n{}", self.details())?;
        }
        
        Ok(())
    }
}

/// Collection of validation results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResultSet<T> {
    /// Name of the validation suite
    pub suite_name: String,
    /// Individual validation results
    pub results: Vec<ValidationResult<T>>,
    /// Overall execution time for the suite
    pub total_execution_time: Duration,
    /// Timestamp when the suite was executed
    pub timestamp: DateTime<Utc>,
}

impl<T> ValidationResultSet<T> {
    /// Create a new validation result set
    pub fn new(suite_name: impl Into<String>) -> Self {
        Self {
            suite_name: suite_name.into(),
            results: Vec::new(),
            total_execution_time: Duration::from_millis(0),
            timestamp: Utc::now(),
        }
    }

    /// Add a validation result to the set
    pub fn add_result(&mut self, result: ValidationResult<T>) {
        self.total_execution_time += result.execution_time;
        self.results.push(result);
    }

    /// Get the overall status of the validation suite
    pub fn overall_status(&self) -> ValidationStatus {
        if self.results.is_empty() {
            return ValidationStatus::Skipped;
        }

        let has_failures = self.results.iter().any(|r| r.is_failed());
        let has_warnings = self.results.iter().any(|r| r.status == ValidationStatus::Warning);

        if has_failures {
            ValidationStatus::Failed
        } else if has_warnings {
            ValidationStatus::Warning
        } else {
            ValidationStatus::Passed
        }
    }

    /// Get summary statistics for the validation suite
    pub fn summary_stats(&self) -> ValidationSummaryStats {
        let total = self.results.len();
        let passed = self.results.iter().filter(|r| r.status == ValidationStatus::Passed).count();
        let failed = self.results.iter().filter(|r| r.status == ValidationStatus::Failed).count();
        let warnings = self.results.iter().filter(|r| r.status == ValidationStatus::Warning).count();
        let skipped = self.results.iter().filter(|r| r.status == ValidationStatus::Skipped).count();

        let success_rate = if total > 0 {
            (passed + warnings) as f64 / total as f64
        } else {
            0.0
        };

        ValidationSummaryStats {
            total_tests: total,
            passed_tests: passed,
            failed_tests: failed,
            warning_tests: warnings,
            skipped_tests: skipped,
            success_rate,
            total_execution_time: self.total_execution_time,
        }
    }

    /// Get a formatted summary of the validation suite
    pub fn format_summary(&self) -> String {
        let stats = self.summary_stats();
        let status = self.overall_status();
        
        format!(
            "Validation Suite: {} [{}]\n\
             Total Tests: {} | Passed: {} | Failed: {} | Warnings: {} | Skipped: {}\n\
             Success Rate: {:.1}% | Total Time: {}ms",
            self.suite_name,
            status,
            stats.total_tests,
            stats.passed_tests,
            stats.failed_tests,
            stats.warning_tests,
            stats.skipped_tests,
            stats.success_rate * 100.0,
            stats.total_execution_time.as_millis()
        )
    }
}

/// Summary statistics for a validation result set
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationSummaryStats {
    pub total_tests: usize,
    pub passed_tests: usize,
    pub failed_tests: usize,
    pub warning_tests: usize,
    pub skipped_tests: usize,
    pub success_rate: f64,
    pub total_execution_time: Duration,
}