// MISSION: Comprehensive Failure Diagnostics for Stargate Compass Integration Testing
// WHY: Provide detailed failure analysis, root cause identification, and actionable remediation steps
// HOW: Analyze failure patterns, correlate errors across components, and generate diagnostic reports

use anyhow::{Result, Context};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;
use tracing::{debug, info, warn, error};

use super::{
    TuiTestSuite, TuiCommandResult, TuiStressTestResult,
    TransactionTestSuite, TransactionCommandTestResult, EmergencyStopTestResult,
    EndToEndWorkflowResult, WorkflowSynthesisResult,
};

/// Comprehensive failure diagnostics engine
#[derive(Debug)]
pub struct FailureDiagnosticsEngine {
    pub session_id: String,
    pub analysis_timestamp: DateTime<Utc>,
    pub failure_patterns: HashMap<String, FailurePattern>,
    pub component_health: HashMap<String, ComponentHealthStatus>,
    pub diagnostic_config: DiagnosticConfiguration,
}

/// Configuration for diagnostic analysis
#[derive(Debug, <PERSON>lone)]
pub struct DiagnosticConfiguration {
    pub performance_threshold_ms: u64,
    pub expected_execution_time_ms: u64,
    pub enable_deep_analysis: bool,
    pub correlation_threshold: f64,
}

impl Default for DiagnosticConfiguration {
    fn default() -> Self {
        Self {
            performance_threshold_ms: 5000,
            expected_execution_time_ms: 1000,
            enable_deep_analysis: true,
            correlation_threshold: 0.7,
        }
    }
}

/// Failure pattern identification
#[derive(Debug, Clone)]
pub struct FailurePattern {
    pub pattern_id: String,
    pub pattern_type: FailurePatternType,
    pub frequency: usize,
    pub affected_components: Vec<String>,
    pub error_signatures: Vec<String>,
    pub first_occurrence: DateTime<Utc>,
    pub last_occurrence: DateTime<Utc>,
}

/// Types of failure patterns
#[derive(Debug, Clone, PartialEq)]
pub enum FailurePatternType {
    Timeout,
    NetworkConnectivity,
    ContractInteraction,
    DataValidation,
    ResourceExhaustion,
    ConfigurationError,
    Unknown,
}

/// Component health status
#[derive(Debug, Clone, PartialEq)]
pub enum ComponentHealthStatus {
    Healthy,
    Degraded,
    Critical,
    Unknown,
}

/// Failure categories for classification
#[derive(Debug, Clone, PartialEq)]
pub enum FailureCategory {
    Timeout,
    Network,
    ContractInteraction,
    DataParsing,
    Authorization,
    GasRelated,
    InsufficientFunds,
    Unknown,
}

/// Failure severity levels
#[derive(Debug, Clone, PartialEq, PartialOrd)]
pub enum FailureSeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl FailureDiagnosticsEngine {
    /// Create new failure diagnostics engine
    pub fn new(session_id: String, config: DiagnosticConfiguration) -> Self {
        Self {
            session_id,
            analysis_timestamp: Utc::now(),
            failure_patterns: HashMap::new(),
            component_health: HashMap::new(),
            diagnostic_config: config,
        }
    }

    /// Analyze comprehensive failure patterns across all test components
    pub fn analyze_comprehensive_failures(
        &mut self,
        tui_results: &[TuiTestSuite],
        transaction_results: &[TransactionTestSuite],
        workflow_results: &[EndToEndWorkflowResult],
        synthesis_results: &[WorkflowSynthesisResult],
    ) -> Result<ComprehensiveFailureDiagnostic> {
        info!("Starting comprehensive failure analysis for session: {}", self.session_id);

        // Analyze each component type
        let tui_diagnostics = self.analyze_tui_failures(tui_results)?;
        let transaction_diagnostics = self.analyze_transaction_failures(transaction_results)?;
        let workflow_diagnostics = self.analyze_workflow_failures(workflow_results)?;

        // Generate comprehensive diagnostic
        let comprehensive_diagnostic = ComprehensiveFailureDiagnostic {
            session_id: self.session_id.clone(),
            analysis_timestamp: self.analysis_timestamp,
            tui_diagnostics,
            transaction_diagnostics,
            workflow_diagnostics,
            overall_health_status: self.assess_overall_health(),
            critical_issues: self.identify_critical_issues(),
            remediation_recommendations: self.generate_remediation_recommendations(),
        };

        info!("Comprehensive failure analysis completed");
        Ok(comprehensive_diagnostic)
    }

    /// Analyze TUI-specific failures with detailed diagnostics
    fn analyze_tui_failures(&mut self, tui_results: &[TuiTestSuite]) -> Result<TuiFailureDiagnostic> {
        debug!("Analyzing TUI failures across {} test suites", tui_results.len());

        let mut failed_commands = Vec::new();
        let mut performance_issues = Vec::new();
        let mut error_patterns = HashMap::new();

        for (suite_idx, suite) in tui_results.iter().enumerate() {
            self.analyze_tui_suite(suite, suite_idx, &mut failed_commands, &mut performance_issues, &mut error_patterns)?;
        }

        Ok(TuiFailureDiagnostic {
            total_suites_analyzed: tui_results.len(),
            failed_commands,
            performance_issues,
            error_patterns,
            health_status: self.assess_tui_health(&failed_commands),
        })
    }

    /// Analyze individual TUI test suite
    fn analyze_tui_suite(
        &self,
        suite: &TuiTestSuite,
        suite_idx: usize,
        failed_commands: &mut Vec<CommandFailureDetail>,
        performance_issues: &mut Vec<PerformanceIssue>,
        error_patterns: &mut HashMap<String, usize>,
    ) -> Result<()> {
        let commands = vec![
            ("emergency_stop", &suite.emergency_stop_result),
            ("pause_bot", &suite.pause_bot_result),
            ("restart_bot", &suite.restart_bot_result),
            ("execute_opportunity", &suite.execute_opportunity_result),
            ("query_balances", &suite.query_balances_result),
            ("query_contract_status", &suite.query_contract_status_result),
        ];

        for (command_name, result_opt) in commands {
            if let Some(result) = result_opt {
                if !result.success {
                    let failure_detail = self.create_command_failure_detail(command_name, result, suite_idx);
                    failed_commands.push(failure_detail);

                    // Track error patterns
                    if let Some(error_msg) = &result.error_message {
                        let pattern = self.extract_error_pattern(error_msg);
                        *error_patterns.entry(pattern).or_insert(0) += 1;
                    }
                }

                // Check for performance issues
                if result.execution_time_ms > self.diagnostic_config.performance_threshold_ms {
                    performance_issues.push(PerformanceIssue {
                        command_name: command_name.to_string(),
                        suite_index: suite_idx,
                        execution_time_ms: result.execution_time_ms,
                        threshold_ms: self.diagnostic_config.performance_threshold_ms,
                        severity: self.assess_performance_severity(result.execution_time_ms),
                    });
                }
            }
        }

        Ok(())
    }

    /// Create detailed command failure information
    fn create_command_failure_detail(&self, command_name: &str, result: &TuiCommandResult, suite_idx: usize) -> CommandFailureDetail {
        let error_message = result.error_message.as_deref().unwrap_or("Unknown error");
        let failure_category = self.categorize_failure_type(error_message);
        let severity = self.assess_command_failure_severity(command_name, error_message);

        CommandFailureDetail {
            command_name: command_name.to_string(),
            suite_index: suite_idx,
            error_message: error_message.to_string(),
            failure_category,
            severity,
            execution_time_ms: result.execution_time_ms,
            contract_interaction_attempted: result.contract_interaction_detected,
            remediation_steps: self.generate_command_remediation_steps(command_name, &failure_category),
        }
    }

    /// Categorize failure type based on error message
    fn categorize_failure_type(&self, error_message: &str) -> FailureCategory {
        let error_lower = error_message.to_lowercase();

        if error_lower.contains("timeout") || error_lower.contains("timed out") {
            FailureCategory::Timeout
        } else if error_lower.contains("connection") || error_lower.contains("network") {
            FailureCategory::Network
        } else if error_lower.contains("contract") || error_lower.contains("revert") {
            FailureCategory::ContractInteraction
        } else if error_lower.contains("parse") || error_lower.contains("format") {
            FailureCategory::DataParsing
        } else if error_lower.contains("permission") || error_lower.contains("unauthorized") {
            FailureCategory::Authorization
        } else if error_lower.contains("gas") || error_lower.contains("fee") {
            FailureCategory::GasRelated
        } else if error_lower.contains("balance") || error_lower.contains("insufficient") {
            FailureCategory::InsufficientFunds
        } else {
            FailureCategory::Unknown
        }
    }

    /// Assess command failure severity
    fn assess_command_failure_severity(&self, command_name: &str, error_message: &str) -> FailureSeverity {
        match command_name {
            "emergency_stop" => FailureSeverity::Critical,
            "execute_opportunity" => {
                if error_message.to_lowercase().contains("revert") {
                    FailureSeverity::High
                } else {
                    FailureSeverity::Medium
                }
            }
            "pause_bot" | "restart_bot" => FailureSeverity::Medium,
            "query_balances" | "query_contract_status" => FailureSeverity::Low,
            _ => FailureSeverity::Medium,
        }
    }

    /// Generate command-specific remediation steps
    fn generate_command_remediation_steps(&self, command_name: &str, failure_category: &FailureCategory) -> Vec<String> {
        match (command_name, failure_category) {
            ("emergency_stop", FailureCategory::Timeout) => vec![
                "Increase emergency stop timeout threshold".to_string(),
                "Review emergency stop mechanism implementation".to_string(),
                "Add retry logic for emergency stop operations".to_string(),
            ],
            (_, FailureCategory::Network) => vec![
                "Implement network retry logic with exponential backoff".to_string(),
                "Check network connectivity and firewall settings".to_string(),
                "Add network health monitoring".to_string(),
            ],
            (_, FailureCategory::ContractInteraction) => vec![
                "Add pre-transaction validation checks".to_string(),
                "Review contract ABI and function signatures".to_string(),
                "Implement better error handling for contract calls".to_string(),
            ],
            _ => vec![
                format!("Investigate {} failure in {}", failure_category, command_name),
                "Add additional logging for debugging".to_string(),
                "Review test environment configuration".to_string(),
            ],
        }
    }

    /// Analyze transaction failures
    fn analyze_transaction_failures(&mut self, transaction_results: &[TransactionTestSuite]) -> Result<TransactionFailureDiagnostic> {
        debug!("Analyzing transaction failures across {} test suites", transaction_results.len());

        let mut failed_transactions = Vec::new();
        let mut gas_issues = Vec::new();
        let mut emergency_stop_failures = Vec::new();

        for (suite_idx, suite) in transaction_results.iter().enumerate() {
            // Analyze emergency stop failures
            let emergency_result = &suite.emergency_stop_result;
            if !emergency_result.command_executed || !emergency_result.all_operations_halted {
                let error_message = if emergency_result.error_messages.is_empty() {
                    "Emergency stop failed to execute properly".to_string()
                } else {
                    emergency_result.error_messages.join("; ")
                };
                
                emergency_stop_failures.push(EmergencyStopFailure {
                    suite_index: suite_idx,
                    error_message,
                    severity: FailureSeverity::Critical,
                    remediation_steps: vec![
                        "Review emergency stop contract implementation".to_string(),
                        "Check contract permissions and ownership".to_string(),
                        "Verify gas limits for emergency operations".to_string(),
                    ],
                });
            }

            // Analyze transaction command results
            for (tx_idx, tx_result) in suite.transaction_command_results.iter().enumerate() {
                if !tx_result.success {
                    let error_message = tx_result.error_message.clone().unwrap_or_default();
                    failed_transactions.push(TransactionFailure {
                        suite_index: suite_idx,
                        transaction_index: tx_idx,
                        error_message: error_message.clone(),
                        failure_category: self.categorize_transaction_failure(&error_message),
                        severity: self.assess_transaction_severity(&error_message),
                    });
                }
            }
        }

        Ok(TransactionFailureDiagnostic {
            total_suites_analyzed: transaction_results.len(),
            failed_transactions,
            gas_issues,
            emergency_stop_failures,
            health_status: self.assess_transaction_health(&failed_transactions, &emergency_stop_failures),
        })
    }

    /// Analyze workflow failures
    fn analyze_workflow_failures(&mut self, workflow_results: &[EndToEndWorkflowResult]) -> Result<WorkflowFailureDiagnostic> {
        debug!("Analyzing workflow failures across {} workflows", workflow_results.len());

        let mut failed_workflows = Vec::new();
        let mut component_failures = Vec::new();

        for (workflow_idx, workflow) in workflow_results.iter().enumerate() {
            if !workflow.overall_success {
                let workflow_failure = WorkflowFailure {
                    workflow_index: workflow_idx,
                    failed_components: self.identify_failed_workflow_components(workflow),
                    severity: self.assess_workflow_severity(workflow),
                    remediation_steps: self.generate_workflow_remediation_steps(workflow),
                };
                failed_workflows.push(workflow_failure);
            }
        }

        Ok(WorkflowFailureDiagnostic {
            total_workflows_analyzed: workflow_results.len(),
            failed_workflows,
            component_failures,
            health_status: self.assess_workflow_health(&failed_workflows),
        })
    }

    // Helper methods for analysis
    fn extract_error_pattern(&self, error_message: &str) -> String {
        // Extract common error patterns for grouping
        if error_message.contains("timeout") {
            "timeout_pattern".to_string()
        } else if error_message.contains("connection") {
            "connection_pattern".to_string()
        } else if error_message.contains("revert") {
            "contract_revert_pattern".to_string()
        } else {
            "unknown_pattern".to_string()
        }
    }

    fn assess_performance_severity(&self, execution_time_ms: u64) -> FailureSeverity {
        let threshold = self.diagnostic_config.performance_threshold_ms;
        if execution_time_ms > threshold * 3 {
            FailureSeverity::Critical
        } else if execution_time_ms > threshold * 2 {
            FailureSeverity::High
        } else {
            FailureSeverity::Medium
        }
    }

    fn assess_tui_health(&self, failed_commands: &[CommandFailureDetail]) -> ComponentHealthStatus {
        if failed_commands.is_empty() {
            ComponentHealthStatus::Healthy
        } else if failed_commands.len() < 3 {
            ComponentHealthStatus::Degraded
        } else {
            ComponentHealthStatus::Critical
        }
    }

    fn categorize_transaction_failure(&self, error_message: &str) -> FailureCategory {
        self.categorize_failure_type(error_message)
    }

    fn assess_transaction_severity(&self, error_message: &str) -> FailureSeverity {
        if error_message.to_lowercase().contains("emergency") {
            FailureSeverity::Critical
        } else if error_message.to_lowercase().contains("revert") {
            FailureSeverity::High
        } else {
            FailureSeverity::Medium
        }
    }

    fn assess_transaction_health(&self, failed_transactions: &[TransactionFailure], emergency_failures: &[EmergencyStopFailure]) -> ComponentHealthStatus {
        if !emergency_failures.is_empty() {
            ComponentHealthStatus::Critical
        } else if failed_transactions.len() > 2 {
            ComponentHealthStatus::Degraded
        } else if failed_transactions.is_empty() {
            ComponentHealthStatus::Healthy
        } else {
            ComponentHealthStatus::Degraded
        }
    }

    fn identify_failed_workflow_components(&self, workflow: &EndToEndWorkflowResult) -> Vec<String> {
        let mut failed_components = Vec::new();

        if let Some(opportunity_result) = &workflow.opportunity_simulation {
            if !opportunity_result.success {
                failed_components.push("opportunity_simulation".to_string());
            }
        }

        if let Some(backend_result) = &workflow.backend_execution {
            if !backend_result.success {
                failed_components.push("backend_execution".to_string());
            }
        }

        if let Some(tui_result) = &workflow.tui_validation {
            if !tui_result.success {
                failed_components.push("tui_validation".to_string());
            }
        }

        failed_components
    }

    fn assess_workflow_severity(&self, workflow: &EndToEndWorkflowResult) -> FailureSeverity {
        let failed_components = self.identify_failed_workflow_components(workflow);
        match failed_components.len() {
            0 => FailureSeverity::Low,
            1 => FailureSeverity::Medium,
            2 => FailureSeverity::High,
            _ => FailureSeverity::Critical,
        }
    }

    fn generate_workflow_remediation_steps(&self, workflow: &EndToEndWorkflowResult) -> Vec<String> {
        let mut steps = Vec::new();
        let failed_components = self.identify_failed_workflow_components(workflow);

        for component in failed_components {
            match component.as_str() {
                "opportunity_simulation" => {
                    steps.push("Review opportunity detection logic".to_string());
                    steps.push("Validate market data inputs".to_string());
                }
                "backend_execution" => {
                    steps.push("Check backend service connectivity".to_string());
                    steps.push("Review execution engine configuration".to_string());
                }
                "tui_validation" => {
                    steps.push("Verify TUI display logic".to_string());
                    steps.push("Check data synchronization between components".to_string());
                }
                _ => {
                    steps.push(format!("Investigate {} component failure", component));
                }
            }
        }

        if steps.is_empty() {
            steps.push("Perform general workflow health check".to_string());
        }

        steps
    }

    fn assess_workflow_health(&self, failed_workflows: &[WorkflowFailure]) -> ComponentHealthStatus {
        if failed_workflows.is_empty() {
            ComponentHealthStatus::Healthy
        } else if failed_workflows.len() < 2 {
            ComponentHealthStatus::Degraded
        } else {
            ComponentHealthStatus::Critical
        }
    }

    fn assess_overall_health(&self) -> ComponentHealthStatus {
        // This would be implemented based on component health aggregation
        ComponentHealthStatus::Healthy
    }

    fn identify_critical_issues(&self) -> Vec<CriticalIssue> {
        // This would identify the most critical issues requiring immediate attention
        Vec::new()
    }

    fn generate_remediation_recommendations(&self) -> Vec<RemediationRecommendation> {
        // This would generate prioritized remediation recommendations
        Vec::new()
    }
}
// =
// ============ DATA STRUCTURES ============

/// Comprehensive failure diagnostic result
#[derive(Debug, Serialize, Deserialize)]
pub struct ComprehensiveFailureDiagnostic {
    pub session_id: String,
    pub analysis_timestamp: DateTime<Utc>,
    pub tui_diagnostics: TuiFailureDiagnostic,
    pub transaction_diagnostics: TransactionFailureDiagnostic,
    pub workflow_diagnostics: WorkflowFailureDiagnostic,
    pub overall_health_status: ComponentHealthStatus,
    pub critical_issues: Vec<CriticalIssue>,
    pub remediation_recommendations: Vec<RemediationRecommendation>,
}

/// TUI-specific failure diagnostics
#[derive(Debug, Serialize, Deserialize)]
pub struct TuiFailureDiagnostic {
    pub total_suites_analyzed: usize,
    pub failed_commands: Vec<CommandFailureDetail>,
    pub performance_issues: Vec<PerformanceIssue>,
    pub error_patterns: HashMap<String, usize>,
    pub health_status: ComponentHealthStatus,
}

/// Transaction-specific failure diagnostics
#[derive(Debug, Serialize, Deserialize)]
pub struct TransactionFailureDiagnostic {
    pub total_suites_analyzed: usize,
    pub failed_transactions: Vec<TransactionFailure>,
    pub gas_issues: Vec<GasIssue>,
    pub emergency_stop_failures: Vec<EmergencyStopFailure>,
    pub health_status: ComponentHealthStatus,
}

/// Workflow-specific failure diagnostics
#[derive(Debug, Serialize, Deserialize)]
pub struct WorkflowFailureDiagnostic {
    pub total_workflows_analyzed: usize,
    pub failed_workflows: Vec<WorkflowFailure>,
    pub component_failures: Vec<ComponentFailure>,
    pub health_status: ComponentHealthStatus,
}

/// Detailed command failure information
#[derive(Debug, Serialize, Deserialize)]
pub struct CommandFailureDetail {
    pub command_name: String,
    pub suite_index: usize,
    pub error_message: String,
    pub failure_category: FailureCategory,
    pub severity: FailureSeverity,
    pub execution_time_ms: u64,
    pub contract_interaction_attempted: bool,
    pub remediation_steps: Vec<String>,
}

/// Performance issue details
#[derive(Debug, Serialize, Deserialize)]
pub struct PerformanceIssue {
    pub command_name: String,
    pub suite_index: usize,
    pub execution_time_ms: u64,
    pub threshold_ms: u64,
    pub severity: FailureSeverity,
}

/// Transaction failure details
#[derive(Debug, Serialize, Deserialize)]
pub struct TransactionFailure {
    pub suite_index: usize,
    pub transaction_index: usize,
    pub error_message: String,
    pub failure_category: FailureCategory,
    pub severity: FailureSeverity,
}

/// Gas-related issue details
#[derive(Debug, Serialize, Deserialize)]
pub struct GasIssue {
    pub transaction_index: usize,
    pub issue_type: GasIssueType,
    pub gas_used: Option<u64>,
    pub gas_limit: Option<u64>,
    pub gas_price: Option<u64>,
    pub severity: FailureSeverity,
}

/// Types of gas issues
#[derive(Debug, Serialize, Deserialize)]
pub enum GasIssueType {
    OutOfGas,
    GasPriceTooLow,
    GasLimitTooLow,
    UnexpectedGasUsage,
}

/// Emergency stop failure details
#[derive(Debug, Serialize, Deserialize)]
pub struct EmergencyStopFailure {
    pub suite_index: usize,
    pub error_message: String,
    pub severity: FailureSeverity,
    pub remediation_steps: Vec<String>,
}

/// Workflow failure details
#[derive(Debug, Serialize, Deserialize)]
pub struct WorkflowFailure {
    pub workflow_index: usize,
    pub failed_components: Vec<String>,
    pub severity: FailureSeverity,
    pub remediation_steps: Vec<String>,
}

/// Component failure details
#[derive(Debug, Serialize, Deserialize)]
pub struct ComponentFailure {
    pub component_name: String,
    pub failure_reason: String,
    pub impact_level: ImpactLevel,
    pub remediation_priority: RemediationPriority,
}

/// Impact levels for failures
#[derive(Debug, Serialize, Deserialize, PartialEq, PartialOrd)]
pub enum ImpactLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// Remediation priority levels
#[derive(Debug, Serialize, Deserialize, PartialEq, PartialOrd)]
pub enum RemediationPriority {
    Low,
    Medium,
    High,
    Critical,
}

/// Critical issue identification
#[derive(Debug, Serialize, Deserialize)]
pub struct CriticalIssue {
    pub issue_id: String,
    pub title: String,
    pub description: String,
    pub affected_components: Vec<String>,
    pub severity: FailureSeverity,
    pub immediate_action_required: bool,
    pub estimated_impact: String,
}

/// Remediation recommendation
#[derive(Debug, Serialize, Deserialize)]
pub struct RemediationRecommendation {
    pub recommendation_id: String,
    pub title: String,
    pub description: String,
    pub priority: RemediationPriority,
    pub estimated_effort_hours: f64,
    pub success_probability: f64,
    pub dependencies: Vec<String>,
    pub validation_criteria: Vec<String>,
}

/// Failure diagnostic report formatter
pub struct FailureDiagnosticReporter;

impl FailureDiagnosticReporter {
    /// Generate formatted diagnostic report
    pub fn generate_report(diagnostic: &ComprehensiveFailureDiagnostic) -> Result<String> {
        let mut report = String::new();
        
        // Header
        report.push_str(&format!("🔍 COMPREHENSIVE FAILURE DIAGNOSTIC REPORT\n"));
        report.push_str(&format!("═══════════════════════════════════════════\n"));
        report.push_str(&format!("Session ID: {}\n", diagnostic.session_id));
        report.push_str(&format!("Analysis Time: {}\n", diagnostic.analysis_timestamp.format("%Y-%m-%d %H:%M:%S UTC")));
        report.push_str(&format!("Overall Health: {:?}\n\n", diagnostic.overall_health_status));

        // TUI Diagnostics
        Self::add_tui_diagnostics(&mut report, &diagnostic.tui_diagnostics);
        
        // Transaction Diagnostics
        Self::add_transaction_diagnostics(&mut report, &diagnostic.transaction_diagnostics);
        
        // Workflow Diagnostics
        Self::add_workflow_diagnostics(&mut report, &diagnostic.workflow_diagnostics);
        
        // Critical Issues
        Self::add_critical_issues(&mut report, &diagnostic.critical_issues);
        
        // Remediation Recommendations
        Self::add_remediation_recommendations(&mut report, &diagnostic.remediation_recommendations);

        Ok(report)
    }

    fn add_tui_diagnostics(report: &mut String, diagnostics: &TuiFailureDiagnostic) {
        report.push_str("🖥️  TUI FAILURE DIAGNOSTICS\n");
        report.push_str("──────────────────────────\n");
        report.push_str(&format!("Suites Analyzed: {}\n", diagnostics.total_suites_analyzed));
        report.push_str(&format!("Health Status: {:?}\n", diagnostics.health_status));
        report.push_str(&format!("Failed Commands: {}\n", diagnostics.failed_commands.len()));
        report.push_str(&format!("Performance Issues: {}\n", diagnostics.performance_issues.len()));

        if !diagnostics.failed_commands.is_empty() {
            report.push_str("\n❌ Failed Commands:\n");
            for (i, failure) in diagnostics.failed_commands.iter().enumerate() {
                report.push_str(&format!("  {}. {} (Suite {}) - {:?}\n", 
                    i + 1, failure.command_name, failure.suite_index, failure.severity));
                report.push_str(&format!("     Error: {}\n", failure.error_message));
                report.push_str(&format!("     Category: {:?}\n", failure.failure_category));
                if !failure.remediation_steps.is_empty() {
                    report.push_str("     Remediation:\n");
                    for step in &failure.remediation_steps {
                        report.push_str(&format!("       • {}\n", step));
                    }
                }
                report.push_str("\n");
            }
        }

        if !diagnostics.performance_issues.is_empty() {
            report.push_str("⚠️  Performance Issues:\n");
            for (i, issue) in diagnostics.performance_issues.iter().enumerate() {
                report.push_str(&format!("  {}. {} took {}ms (threshold: {}ms) - {:?}\n",
                    i + 1, issue.command_name, issue.execution_time_ms, issue.threshold_ms, issue.severity));
            }
        }

        if !diagnostics.error_patterns.is_empty() {
            report.push_str("\n📊 Error Patterns:\n");
            for (pattern, count) in &diagnostics.error_patterns {
                report.push_str(&format!("  • {}: {} occurrences\n", pattern, count));
            }
        }

        report.push_str("\n");
    }

    fn add_transaction_diagnostics(report: &mut String, diagnostics: &TransactionFailureDiagnostic) {
        report.push_str("💰 TRANSACTION FAILURE DIAGNOSTICS\n");
        report.push_str("─────────────────────────────────\n");
        report.push_str(&format!("Suites Analyzed: {}\n", diagnostics.total_suites_analyzed));
        report.push_str(&format!("Health Status: {:?}\n", diagnostics.health_status));
        report.push_str(&format!("Failed Transactions: {}\n", diagnostics.failed_transactions.len()));
        report.push_str(&format!("Emergency Stop Failures: {}\n", diagnostics.emergency_stop_failures.len()));

        if !diagnostics.emergency_stop_failures.is_empty() {
            report.push_str("\n🚨 Emergency Stop Failures (CRITICAL):\n");
            for (i, failure) in diagnostics.emergency_stop_failures.iter().enumerate() {
                report.push_str(&format!("  {}. Suite {} - {:?}\n", 
                    i + 1, failure.suite_index, failure.severity));
                report.push_str(&format!("     Error: {}\n", failure.error_message));
                report.push_str("     Remediation:\n");
                for step in &failure.remediation_steps {
                    report.push_str(&format!("       • {}\n", step));
                }
                report.push_str("\n");
            }
        }

        if !diagnostics.failed_transactions.is_empty() {
            report.push_str("❌ Failed Transactions:\n");
            for (i, failure) in diagnostics.failed_transactions.iter().enumerate() {
                report.push_str(&format!("  {}. Suite {}, Transaction {} - {:?}\n",
                    i + 1, failure.suite_index, failure.transaction_index, failure.severity));
                report.push_str(&format!("     Error: {}\n", failure.error_message));
                report.push_str(&format!("     Category: {:?}\n", failure.failure_category));
            }
        }

        report.push_str("\n");
    }

    fn add_workflow_diagnostics(report: &mut String, diagnostics: &WorkflowFailureDiagnostic) {
        report.push_str("🔄 WORKFLOW FAILURE DIAGNOSTICS\n");
        report.push_str("──────────────────────────────\n");
        report.push_str(&format!("Workflows Analyzed: {}\n", diagnostics.total_workflows_analyzed));
        report.push_str(&format!("Health Status: {:?}\n", diagnostics.health_status));
        report.push_str(&format!("Failed Workflows: {}\n", diagnostics.failed_workflows.len()));

        if !diagnostics.failed_workflows.is_empty() {
            report.push_str("\n❌ Failed Workflows:\n");
            for (i, failure) in diagnostics.failed_workflows.iter().enumerate() {
                report.push_str(&format!("  {}. Workflow {} - {:?}\n",
                    i + 1, failure.workflow_index, failure.severity));
                report.push_str(&format!("     Failed Components: {}\n", failure.failed_components.join(", ")));
                report.push_str("     Remediation:\n");
                for step in &failure.remediation_steps {
                    report.push_str(&format!("       • {}\n", step));
                }
                report.push_str("\n");
            }
        }

        report.push_str("\n");
    }

    fn add_critical_issues(report: &mut String, issues: &[CriticalIssue]) {
        if !issues.is_empty() {
            report.push_str("🚨 CRITICAL ISSUES REQUIRING IMMEDIATE ATTENTION\n");
            report.push_str("═══════════════════════════════════════════════\n");
            
            for (i, issue) in issues.iter().enumerate() {
                report.push_str(&format!("{}. {} - {:?}\n", i + 1, issue.title, issue.severity));
                report.push_str(&format!("   Description: {}\n", issue.description));
                report.push_str(&format!("   Affected Components: {}\n", issue.affected_components.join(", ")));
                report.push_str(&format!("   Immediate Action Required: {}\n", issue.immediate_action_required));
                report.push_str(&format!("   Estimated Impact: {}\n", issue.estimated_impact));
                report.push_str("\n");
            }
        }
    }

    fn add_remediation_recommendations(report: &mut String, recommendations: &[RemediationRecommendation]) {
        if !recommendations.is_empty() {
            report.push_str("💡 REMEDIATION RECOMMENDATIONS\n");
            report.push_str("════════════════════════════\n");
            
            for (i, rec) in recommendations.iter().enumerate() {
                report.push_str(&format!("{}. {} - {:?} Priority\n", i + 1, rec.title, rec.priority));
                report.push_str(&format!("   Description: {}\n", rec.description));
                report.push_str(&format!("   Estimated Effort: {:.1} hours\n", rec.estimated_effort_hours));
                report.push_str(&format!("   Success Probability: {:.1}%\n", rec.success_probability * 100.0));
                
                if !rec.dependencies.is_empty() {
                    report.push_str(&format!("   Dependencies: {}\n", rec.dependencies.join(", ")));
                }
                
                if !rec.validation_criteria.is_empty() {
                    report.push_str("   Validation Criteria:\n");
                    for criterion in &rec.validation_criteria {
                        report.push_str(&format!("     • {}\n", criterion));
                    }
                }
                report.push_str("\n");
            }
        }
    }

    /// Generate summary statistics
    pub fn generate_summary_stats(diagnostic: &ComprehensiveFailureDiagnostic) -> DiagnosticSummaryStats {
        let total_failures = diagnostic.tui_diagnostics.failed_commands.len() +
                           diagnostic.transaction_diagnostics.failed_transactions.len() +
                           diagnostic.workflow_diagnostics.failed_workflows.len();

        let critical_failures = diagnostic.tui_diagnostics.failed_commands.iter()
            .filter(|f| f.severity == FailureSeverity::Critical).count() +
            diagnostic.transaction_diagnostics.emergency_stop_failures.len() +
            diagnostic.workflow_diagnostics.failed_workflows.iter()
                .filter(|f| f.severity == FailureSeverity::Critical).count();

        DiagnosticSummaryStats {
            total_components_analyzed: 3, // TUI, Transaction, Workflow
            healthy_components: match diagnostic.overall_health_status {
                ComponentHealthStatus::Healthy => 3,
                ComponentHealthStatus::Degraded => 2,
                ComponentHealthStatus::Critical => 1,
                ComponentHealthStatus::Unknown => 0,
            },
            total_failures,
            critical_failures,
            performance_issues: diagnostic.tui_diagnostics.performance_issues.len(),
            remediation_recommendations: diagnostic.remediation_recommendations.len(),
        }
    }
}

/// Summary statistics for diagnostic results
#[derive(Debug, Serialize, Deserialize)]
pub struct DiagnosticSummaryStats {
    pub total_components_analyzed: usize,
    pub healthy_components: usize,
    pub total_failures: usize,
    pub critical_failures: usize,
    pub performance_issues: usize,
    pub remediation_recommendations: usize,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_failure_diagnostics_engine_creation() {
        let config = DiagnosticConfiguration::default();
        let engine = FailureDiagnosticsEngine::new("test_session".to_string(), config);
        
        assert_eq!(engine.session_id, "test_session");
        assert!(engine.failure_patterns.is_empty());
        assert!(engine.component_health.is_empty());
    }

    #[test]
    fn test_failure_categorization() {
        let config = DiagnosticConfiguration::default();
        let engine = FailureDiagnosticsEngine::new("test".to_string(), config);

        assert_eq!(engine.categorize_failure_type("Connection timeout"), FailureCategory::Timeout);
        assert_eq!(engine.categorize_failure_type("Network error"), FailureCategory::Network);
        assert_eq!(engine.categorize_failure_type("Contract reverted"), FailureCategory::ContractInteraction);
        assert_eq!(engine.categorize_failure_type("Parse error"), FailureCategory::DataParsing);
        assert_eq!(engine.categorize_failure_type("Unauthorized access"), FailureCategory::Authorization);
        assert_eq!(engine.categorize_failure_type("Out of gas"), FailureCategory::GasRelated);
        assert_eq!(engine.categorize_failure_type("Insufficient balance"), FailureCategory::InsufficientFunds);
        assert_eq!(engine.categorize_failure_type("Random error"), FailureCategory::Unknown);
    }

    #[test]
    fn test_severity_assessment() {
        let config = DiagnosticConfiguration::default();
        let engine = FailureDiagnosticsEngine::new("test".to_string(), config);

        assert_eq!(engine.assess_command_failure_severity("emergency_stop", "any error"), FailureSeverity::Critical);
        assert_eq!(engine.assess_command_failure_severity("execute_opportunity", "contract reverted"), FailureSeverity::High);
        assert_eq!(engine.assess_command_failure_severity("execute_opportunity", "timeout"), FailureSeverity::Medium);
        assert_eq!(engine.assess_command_failure_severity("query_balances", "any error"), FailureSeverity::Low);
    }

    #[test]
    fn test_performance_severity_assessment() {
        let config = DiagnosticConfiguration::default();
        let engine = FailureDiagnosticsEngine::new("test".to_string(), config);

        assert_eq!(engine.assess_performance_severity(15000), FailureSeverity::Critical); // 3x threshold
        assert_eq!(engine.assess_performance_severity(10000), FailureSeverity::High);     // 2x threshold
        assert_eq!(engine.assess_performance_severity(7000), FailureSeverity::Medium);    // 1.4x threshold
    }
}