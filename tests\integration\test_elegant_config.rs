// Integration tests for the elegant configuration system
// Tests the complete configuration loading, validation, and migration pipeline

use basilisk_bot::config::{Config, Settings, migration::*};
use std::env;
use std::sync::Arc;
use tempfile::TempDir;
use std::fs;

#[cfg(test)]
mod elegant_config_integration_tests {
    use super::*;

    #[tokio::test]
    async fn test_complete_configuration_pipeline() {
        // Test the complete pipeline: load → validate → convert → use
        setup_test_environment();

        // Step 1: Load configuration with new system
        let config = Config::load().expect("Failed to load configuration");
        
        // Step 2: Validate configuration
        config.validate().expect("Configuration validation failed");
        
        // Step 3: Convert to old format for compatibility
        let settings = Arc::new(config.to_settings());
        
        // Step 4: Verify all critical fields are present
        assert_eq!(settings.app.name, "basilisk_bot");
        assert!(settings.risk.kelly_fraction_cap > rust_decimal_macros::dec!(0.0));
        assert!(settings.execution.max_slippage_bps > 0);
        assert!(!settings.chains.is_empty());
        
        println!("✅ Complete configuration pipeline test passed");
    }

    #[tokio::test]
    async fn test_environment_variable_overrides() {
        setup_test_environment();
        
        // Set environment overrides
        env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "0.15");
        env::set_var("APP_EXECUTION__MAX_SLIPPAGE_BPS", "300");
        env::set_var("APP_LOG_LEVEL", "debug");
        
        let config = Config::load().expect("Failed to load with env overrides");
        
        // Verify overrides took effect
        assert_eq!(config.strategy.kelly_fraction_cap, 0.15);
        assert_eq!(config.execution.max_slippage_bps, 300);
        assert_eq!(config.log_level, "debug");
        
        // Clean up
        env::remove_var("APP_STRATEGY__KELLY_FRACTION_CAP");
        env::remove_var("APP_EXECUTION__MAX_SLIPPAGE_BPS");
        env::remove_var("APP_LOG_LEVEL");
        
        println!("✅ Environment variable override test passed");
    }

    #[tokio::test]
    async fn test_layered_configuration_loading() {
        let temp_dir = TempDir::new().expect("Failed to create temp dir");
        let base_config_path = temp_dir.path().join("base.toml");
        let env_config_path = temp_dir.path().join("test.toml");
        
        // Create base configuration
        fs::write(&base_config_path, r#"
app_name = "basilisk_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["zen_geometer"]

[execution]
max_slippage_bps = 500
gas_limit_multiplier = 1.2
max_gas_price_gwei = 100

[secrets]

[chains.8453]
name = "Base"
rpc_url = "https://mainnet.base.org"
max_gas_price = 50000000000
private_key_env_var = "BASE_PRIVATE_KEY"

[chains.8453.contracts]
multicall = "0xcA11bde05977b3631167028862bE2a173976CA11"

[chains.8453.dex]
uniswap_v2_router = "0x4752ba5dbc23f44d87826276bf6fd6b1c372ad24"
        "#).expect("Failed to write base config");
        
        // Create environment-specific overrides
        fs::write(&env_config_path, r#"
log_level = "debug"

[strategy]
kelly_fraction_cap = 0.20

[execution]
max_slippage_bps = 400
        "#).expect("Failed to write env config");
        
        // Set environment variables
        env::set_var("CONFIG_PATH", base_config_path.to_str().unwrap());
        env::set_var("APP_ENV", "test");
        
        // Load configuration
        let config = Config::load().expect("Failed to load layered config");
        
        // Verify layering worked correctly
        assert_eq!(config.app_name, "basilisk_bot"); // From base
        assert_eq!(config.log_level, "debug"); // Overridden by env file
        assert_eq!(config.strategy.kelly_fraction_cap, 0.20); // Overridden by env file
        assert_eq!(config.execution.max_slippage_bps, 400); // Overridden by env file
        assert_eq!(config.execution.gas_limit_multiplier, 1.2); // From base (not overridden)
        
        // Clean up
        env::remove_var("CONFIG_PATH");
        env::remove_var("APP_ENV");
        
        println!("✅ Layered configuration loading test passed");
    }

    #[tokio::test]
    async fn test_validation_edge_cases() {
        setup_test_environment();
        
        // Test production environment validation
        env::set_var("APP_ENV", "production");
        env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "0.6"); // Too high for production
        
        let config = Config::load().expect("Config should load");
        let validation_result = config.validate();
        
        assert!(validation_result.is_err(), "Should fail validation in production");
        
        // Reset to safe value
        env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "0.25");
        let config = Config::load().expect("Config should load");
        assert!(config.validate().is_ok(), "Should pass validation with safe value");
        
        // Clean up
        env::remove_var("APP_ENV");
        env::remove_var("APP_STRATEGY__KELLY_FRACTION_CAP");
        
        println!("✅ Validation edge cases test passed");
    }

    #[tokio::test]
    async fn test_migration_compatibility() {
        setup_test_environment();
        
        // Load with new system
        let new_config = Arc::new(Config::load().expect("Failed to load new config"));
        
        // Convert to old format
        let old_settings = Arc::new(new_config.to_settings());
        
        // Verify critical fields are preserved
        assert_eq!(old_settings.app.name, new_config.app_name);
        
        // Test that both systems can coexist
        let kelly_new = new_config.strategy.kelly_fraction_cap;
        let kelly_old = helpers::get_kelly_fraction(&old_settings);
        
        // Should be approximately equal (accounting for Decimal conversion)
        assert!((kelly_new - kelly_old).abs() < 0.001, 
                "Kelly fraction should be preserved: new={}, old={}", kelly_new, kelly_old);
        
        println!("✅ Migration compatibility test passed");
    }

    #[tokio::test]
    async fn test_secrets_loading() {
        setup_test_environment();
        
        // Set some secret environment variables
        env::set_var("APP_SECRETS__API_KEYS__BINANCE", "test_api_key");
        env::set_var("APP_SECRETS__PRIVATE_KEYS__BASE", "0x1234567890123456789012345678901234567890");
        
        let config = Config::load().expect("Failed to load config with secrets");
        
        // Verify secrets are loaded
        assert_eq!(config.secrets.api_keys.get("binance"), Some(&"test_api_key".to_string()));
        assert_eq!(config.secrets.private_keys.get("base"), Some(&"0x1234567890123456789012345678901234567890".to_string()));
        
        // Clean up
        env::remove_var("APP_SECRETS__API_KEYS__BINANCE");
        env::remove_var("APP_SECRETS__PRIVATE_KEYS__BASE");
        
        println!("✅ Secrets loading test passed");
    }

    fn setup_test_environment() {
        // Set default test configuration
        env::set_var("CONFIG_PATH", "config/elegant-minimal.toml");
        env::set_var("APP_ENV", "test");
    }
}
