use ethers::types::{Address, H256, U256};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

// ============= OPERATIONAL MODES =============

/// Operational modes for the Zen Geometer system
/// Each mode represents a different level of risk and validation
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, clap::ValueEnum)]
pub enum RunMode {
    /// Educational simulation - connects to live data but never broadcasts transactions
    /// Perfect for learning how the bot analyzes real opportunities
    Simulate,
    
    /// Live simulation with on-chain verification - tests transactions on forked state
    /// Validates profitability without broadcasting to mainnet
    Shadow,
    
    /// Live on-chain event monitoring only - monitors deployed contracts
    /// Tests contract functionality with minimal capital
    Sentinel,
    
    /// Live trading with very low risk limits - first real money mode
    /// Overrides risk parameters with hardcoded safe values
    LowCapital,
    
    /// Full production live trading - uses configured risk parameters
    /// Only use after thorough testing in all other modes
    Live,
}

impl Default for RunMode {
    fn default() -> Self {
        RunMode::Simulate
    }
}

impl std::fmt::Display for RunMode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RunMode::Simulate => write!(f, "simulate"),
            RunMode::Shadow => write!(f, "shadow"),
            RunMode::Sentinel => write!(f, "sentinel"),
            RunMode::LowCapital => write!(f, "low-capital"),
            RunMode::Live => write!(f, "live"),
        }
    }
}

impl std::str::FromStr for RunMode {
    type Err = String;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "simulate" => Ok(RunMode::Simulate),
            "shadow" => Ok(RunMode::Shadow),
            "sentinel" => Ok(RunMode::Sentinel),
            "low-capital" | "lowcapital" => Ok(RunMode::LowCapital),
            "live" => Ok(RunMode::Live),
            _ => Err(format!("Invalid run mode: {}", s)),
        }
    }
}

// Living Codex Educational System Types
pub mod are_analysis;
pub mod control_messages;
pub mod degradation;
pub mod trade_lifecycle;
pub use control_messages::SigintReport;

// ============= SIMULATION MODE: OPPORTUNITY LIFECYCLE REPORTING =============

use chrono::{DateTime, Utc};

/// Comprehensive report tracking an opportunity's journey through the decision pipeline
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LifecycleReport {
    pub opportunity_id: String,
    pub scanner_name: String,
    pub detected_at: DateTime<Utc>,
    pub stages: Vec<ReportStage>,
    pub final_decision: FinalDecision,
    pub execution_summary: Option<ExecutionSummary>,
}

/// Individual stage in the opportunity analysis pipeline
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportStage {
    pub name: String,           // e.g., "Honeypot Check", "Cost Analysis", "Gas Estimation"
    pub outcome: StageOutcome,  // e.g., "Passed", "Rejected", "Warning"
    pub details: String,        // Human-readable summary of what happened
    pub data: HashMap<String, String>, // Key-value data for this stage
    pub timestamp: DateTime<Utc>,
    pub duration_ms: Option<u64>, // How long this stage took
}

/// Outcome of a specific analysis stage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StageOutcome {
    Passed,
    Rejected,
    Warning,
    Skipped,
    Error(String),
}

/// Final decision made about the opportunity
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FinalDecision {
    Approved {
        net_profit_usd: Decimal,
        confidence_score: Decimal,
    },
    Rejected {
        reason: String,
        stage: String,
    },
    Error {
        error: String,
        stage: String,
    },
}

/// Summary of execution details (for approved opportunities)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionSummary {
    pub transaction_hash: Option<String>,
    pub gas_used: Option<U256>,
    pub gas_price_gwei: Option<Decimal>,
    pub total_cost_usd: Option<Decimal>,
    pub actual_profit_usd: Option<Decimal>,
    pub execution_time_ms: Option<u64>,
    pub simulation_mode: bool,
}

impl LifecycleReport {
    /// Create a new lifecycle report for an opportunity
    pub fn new(opportunity_id: String, scanner_name: String) -> Self {
        Self {
            opportunity_id,
            scanner_name,
            detected_at: Utc::now(),
            stages: Vec::new(),
            final_decision: FinalDecision::Rejected {
                reason: "Not yet processed".to_string(),
                stage: "Initial".to_string(),
            },
            execution_summary: None,
        }
    }

    /// Add a new stage to the report
    pub fn add_stage(&mut self, name: String, outcome: StageOutcome, details: String, data: HashMap<String, String>) {
        let stage = ReportStage {
            name,
            outcome,
            details,
            data,
            timestamp: Utc::now(),
            duration_ms: None,
        };
        self.stages.push(stage);
    }

    /// Add a stage with timing information
    pub fn add_timed_stage(&mut self, name: String, outcome: StageOutcome, details: String, data: HashMap<String, String>, duration_ms: u64) {
        let stage = ReportStage {
            name,
            outcome,
            details,
            data,
            timestamp: Utc::now(),
            duration_ms: Some(duration_ms),
        };
        self.stages.push(stage);
    }

    /// Set the final decision for this opportunity
    pub fn set_final_decision(&mut self, decision: FinalDecision) {
        self.final_decision = decision;
    }

    /// Set execution summary (for completed transactions)
    pub fn set_execution_summary(&mut self, summary: ExecutionSummary) {
        self.execution_summary = Some(summary);
    }

    /// Print a beautifully formatted report to the console
    pub fn print_report(&self, detailed: bool) {
        println!("\n{}", "=".repeat(80));
        println!("[OPPORTUNITY LIFECYCLE REPORT: {}]", self.opportunity_id);
        println!("{}", "=".repeat(80));
        println!("Scanner: {} | Detected: {}", self.scanner_name, self.detected_at.format("%H:%M:%S UTC"));
        
        // Print each stage
        for (i, stage) in self.stages.iter().enumerate() {
            let outcome_symbol = match stage.outcome {
                StageOutcome::Passed => "PASS",
                StageOutcome::Rejected => "REJECT",
                StageOutcome::Warning => "WARN",
                StageOutcome::Skipped => "SKIP",
                StageOutcome::Error(_) => "ERROR",
            };
            
            println!("\n[{}] {} ({})", i + 1, stage.name, outcome_symbol);
            println!("    - Outcome: {:?}", stage.outcome);
            println!("    - Details: {}", stage.details);
            
            if detailed && !stage.data.is_empty() {
                println!("    - Data:");
                for (key, value) in &stage.data {
                    println!("        {}: {}", key, value);
                }
            }
            
            if let Some(duration) = stage.duration_ms {
                println!("    - Duration: {}ms", duration);
            }
        }
        
        // Print final decision
        println!("\n[FINAL DECISION]:");
        match &self.final_decision {
            FinalDecision::Approved { net_profit_usd, confidence_score } => {
                println!("    [APPROVED]");
                println!("    - Net Profit: ${:.2}", net_profit_usd);
                println!("    - Confidence: {:.1}%", confidence_score * Decimal::from(100));
            }
            FinalDecision::Rejected { reason, stage } => {
                println!("    [REJECTED]");
                println!("    - Reason: {}", reason);
                println!("    - Failed at: {}", stage);
            }
            FinalDecision::Error { error, stage } => {
                println!("    [ERROR]");
                println!("    - Error: {}", error);
                println!("    - Failed at: {}", stage);
            }
        }
        
        // Print execution summary if available
        if let Some(ref summary) = self.execution_summary {
            println!("\n[EXECUTION SUMMARY]:");
            if summary.simulation_mode {
                println!("    [SIMULATION MODE] - No real transaction broadcast");
            }
            
            if let Some(ref hash) = summary.transaction_hash {
                println!("    - Transaction Hash: {}", hash);
            }
            
            if let Some(cost) = summary.total_cost_usd {
                println!("    - Total Cost: ${:.4}", cost);
            }
            if let Some(profit) = summary.actual_profit_usd {
                println!("    - Actual Profit: ${:.2}", profit);
            }
            if let Some(time) = summary.execution_time_ms {
                println!("    - Execution Time: {}ms", time);
            }
        }
        
        println!("{}", "=".repeat(80));
    }

    /// Get a summary string for logging
    pub fn get_summary(&self) -> String {
        match &self.final_decision {
            FinalDecision::Approved { net_profit_usd, .. } => {
                format!("APPROVED: ${:.2} profit from {}", net_profit_usd, self.scanner_name)
            }
            FinalDecision::Rejected { reason, .. } => {
                format!("REJECTED: {} from {}", reason, self.scanner_name)
            }
            FinalDecision::Error { error, .. } => {
                format!("ERROR: {} from {}", error, self.scanner_name)
            }
        }
    }
}
pub mod cross_chain_analysis;

// Pilot Fish Scanner Types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhaleTradeEvent {
    pub chain_id: u64,
    pub token_in: String,
    pub token_out: String,
    pub amount_in: String,
    pub dex_name: String,
    pub trader: Address,
    pub trade_value_usd: Decimal,
    pub estimated_price_impact_usd: Decimal,
    pub estimated_price_impact_percentage: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LargeTradeData {
    pub token_in: String,
    pub token_out: String,
    pub amount_in: String,
    pub dex_name: String,
    pub trader: Address,
    pub trade_value_usd: Decimal,
    pub price_impact_percentage: Decimal,
}

// ============= Harmonist Engine Types =============

// For the Chronos Sieve's output
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemporalHarmonics {
    pub dominant_cycles_minutes: Vec<(f64, f64)>, // (period_minutes, amplitude)
    pub market_rhythm_stability: f64, // 0.0 to 1.0
}

impl TemporalHarmonics {
    /// Convert temporal analysis into educational narrative for the Living Codex
    pub fn to_narrative(&self) -> String {
        let stability_description = if self.market_rhythm_stability > 0.8 {
            "stable and predictable"
        } else if self.market_rhythm_stability > 0.6 {
            "moderately stable"
        } else if self.market_rhythm_stability > 0.4 {
            "somewhat volatile"
        } else {
            "highly chaotic"
        };
        
        let cycle_description = if !self.dominant_cycles_minutes.is_empty() {
            let strongest_cycle = self.dominant_cycles_minutes
                .iter()
                .max_by(|a, b| a.1.partial_cmp(&b.1).unwrap_or(std::cmp::Ordering::Equal))
                .map(|(period, amplitude)| format!("{:.1}-minute cycle (strength: {:.2})", period, amplitude))
                .unwrap_or_else(|| "no clear cycles".to_string());
            
            format!("This opportunity aligns with the dominant {}", strongest_cycle)
        } else {
            "No clear temporal patterns detected in the current market".to_string()
        };
        
        let timing_assessment = if self.market_rhythm_stability > 0.7 {
            "Timing appears favorable"
        } else if self.market_rhythm_stability > 0.5 {
            "Timing is acceptable but requires caution"
        } else {
            "Timing is challenging due to market chaos"
        };
        
        format!(
            "The market pulse is {} (Rhythm Stability: {:.2}). {}. {}.",
            stability_description,
            self.market_rhythm_stability,
            cycle_description,
            timing_assessment
        )
    }
}

// For the Mandorla Gauge's output
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeometricScore {
    pub convexity_ratio: Decimal, // 0.0 to 1.0
    pub liquidity_centroid_bias: Decimal, // 0.0 to 1.0
    pub harmonic_path_score: Decimal, // 0.0 to 1.0
    // AUDIT-FIX: Add vesica piscis depth calculation to GeometricScore struct
    pub vesica_piscis_depth: Decimal, // 0.0 to 1.0 - normalized arbitrage depth
}

// For the Aetheric Resonance Engine's output
#[derive(Debug, Clone, Serialize, Deserialize)]
/// Detailed breakdown of the Aetheric Resonance Score components.
pub struct AethericResonanceScoreDetail {
    pub chronos_sieve: f64,
    pub mandorla_gauge: f64,
    pub network_seismology: f64,
    pub composite_score: f64,
}

// For the Aetheric Resonance Engine's output
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AethericResonanceScore {
    pub temporal_harmonics_score: Decimal,
    pub geometric_score: Decimal,
    pub network_resonance_score: Decimal,
    pub combined_score: Decimal,
}

// For the Fractal Analyzer's output
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FractalAnalysisReport {
    pub timestamp: u64,
    pub market_regime: MarketRegime,
    pub market_character: MarketCharacter,
    pub hurst_exponent: Decimal,
    pub volatility_metrics: HashMap<String, Decimal>,
    pub temporal_harmonics: Option<TemporalHarmonics>,
    pub market_phase: Option<MarketPhase>,
    pub volatility_1m: Decimal,
    pub volatility_15m: Decimal,
    pub volatility_1h: Decimal,
    pub daily_cycle_strength: Decimal,
}

impl GeometricScore {
    /// Convert geometric analysis into educational narrative for the Living Codex
    pub fn to_narrative(&self) -> String {
        let structure_quality = if self.convexity_ratio > dec!(0.8) {
            "robust and well-formed"
        } else if self.convexity_ratio > dec!(0.6) {
            "solid with minor imperfections"
        } else if self.convexity_ratio > dec!(0.4) {
            "acceptable but fragile"
        } else {
            "weak and potentially unstable"
        };

        let centrality_description = if self.liquidity_centroid_bias < dec!(0.3) {
            "well-centered on core assets (WETH, USDC, etc.)"
        } else if self.liquidity_centroid_bias < dec!(0.5) {
            "reasonably centered with some peripheral tokens"
        } else {
            "dispersed across less liquid tokens"
        };

        let depth_description = if self.vesica_piscis_depth > dec!(0.8) {
            "exceptional arbitrage depth"
        } else if self.vesica_piscis_depth > dec!(0.6) {
            "good arbitrage depth"
        } else if self.vesica_piscis_depth > dec!(0.4) {
            "moderate arbitrage depth"
        } else {
            "limited arbitrage depth"
        };

        let confidence_level = if self.convexity_ratio > dec!(0.8) && self.liquidity_centroid_bias < dec!(0.3) && self.vesica_piscis_depth > dec!(0.6) {
            "high"
        } else if self.convexity_ratio > dec!(0.6) && self.liquidity_centroid_bias < dec!(0.5) && self.vesica_piscis_depth > dec!(0.4) {
            "moderate"
        } else {
            "low"
        };

        format!(
            "The geometric structure is {} (Convexity: {:.2}). The liquidity is {}. Sacred geometry reveals {} (Vesica Depth: {:.2}). Confidence is {}.",
            structure_quality,
            self.convexity_ratio,
            centrality_description,
            depth_description,
            self.vesica_piscis_depth,
            confidence_level
        )
    }
}

// For the Network Seismology output
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkResonanceState {
    pub sp_time_ms: f64, // S-P wave time delta
    pub network_coherence_score: f64, // 0.0 to 1.0
    pub is_shock_event: bool,
    pub sp_time_20th_percentile: f64, // For Patient Hunter timing oracle
    pub sequencer_status: String, // e.g., "Healthy", "Degraded", "Down"
    pub censorship_detected: bool,
}

impl NetworkResonanceState {
    /// Convert network analysis into educational narrative for the Living Codex
    pub fn to_narrative(&self) -> String {
        let vibration_state = if self.network_coherence_score > 0.9 {
            "exceptionally calm"
        } else if self.network_coherence_score > 0.8 {
            "calm and stable"
        } else if self.network_coherence_score > 0.6 {
            "moderately turbulent"
        } else if self.network_coherence_score > 0.4 {
            "highly turbulent"
        } else {
            "chaotic and unpredictable"
        };
        
        let stress_level = if self.is_shock_event {
            "significant ongoing network stress due to a shock event"
        } else if self.network_coherence_score > 0.8 {
            "no ongoing network stress"
        } else if self.network_coherence_score > 0.6 {
            "minor network stress"
        } else {
            "considerable network stress"
        };
        
        let timing_context = if self.sp_time_ms <= self.sp_time_20th_percentile {
            format!(" The current S-P time ({:.1}ms) indicates optimal timing conditions.", self.sp_time_ms)
        } else {
            format!(" The current S-P time ({:.1}ms) suggests waiting for better conditions.", self.sp_time_ms)
        };
        
        format!(
            "Network vibrations are {} (Coherence: {:.2}). There is {}.{}.",
            vibration_state,
            self.network_coherence_score,
            stress_level,
            timing_context
        )
    }
}

// For the raw propagation data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockPropagationSample {
    pub block_number: u64,
    pub samples: Vec<(String, u128)>, // (node_id, timestamp_nanos)
}

/// Enhanced block propagation analysis with statistical insights
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockPropagationReport {
    pub block_number: u64,
    pub block_hash: String,
    pub parent_hash: String,
    pub first_seen_ns: u128,
    pub last_seen_ns: u128,
    pub propagation_spread_ns: u128, // max_time - min_time
    pub geographic_jitter_ns: f64,    // Standard deviation of timestamps
    pub sample_count: usize,
    pub endpoint_timings: Vec<(String, u128)>, // (endpoint, timestamp_nanos)
}

/// Time-to-Inclusion analysis for mempool transactions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeToInclusionSample {
    pub tx_hash: String,
    pub mempool_first_seen_ns: u128,
    pub block_inclusion_ns: u128,
    pub tti_ms: u64, // Time-to-inclusion in milliseconds
    pub block_number: u64,
    pub gas_price_gwei: Option<f64>,
    pub priority_fee_gwei: Option<f64>,
}

/// Aggregated TTI statistics over a time window
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeToInclusionReport {
    pub window_start_block: u64,
    pub window_end_block: u64,
    pub sample_count: usize,
    pub avg_tti_ms: f64,
    pub median_tti_ms: f64,
    pub p95_tti_ms: f64,
    pub p99_tti_ms: f64,
    pub min_tti_ms: u64,
    pub max_tti_ms: u64,
    pub timestamp: u128,
}

/// Block coherence and reorganization detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockCoherenceReport {
    pub current_block: u64,
    pub coherence_score: f64, // 1.0 = perfect, 0.0 = chaos
    pub reorgs_in_window: u32,
    pub window_size: u32,
    pub last_reorg_block: Option<u64>,
    pub chain_stability: ChainStability,
    pub timestamp: u128,
}

/// Network stability classification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChainStability {
    Stable,      // < 1% reorg rate
    Nervous,     // 1-3% reorg rate  
    Unstable,    // 3-5% reorg rate
    Chaotic,     // > 5% reorg rate
}

/// Comprehensive network health metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkSeismologyReport {
    pub block_number: u64,
    pub propagation: BlockPropagationReport,
    pub tti_stats: Option<TimeToInclusionReport>,
    pub coherence: BlockCoherenceReport,
    pub network_congestion_level: CongestionLevel,
    pub recommended_gas_strategy: GasStrategy,
    pub timestamp: u128,
}

/// Network congestion classification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CongestionLevel {
    Low,      // Fast TTI, low jitter
    Moderate, // Normal TTI, moderate jitter
    High,     // Slow TTI, high jitter
    Extreme,  // Very slow TTI, extreme jitter
}

/// Recommended gas strategy based on network conditions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GasStrategy {
    Conservative, // Low priority fee, longer wait
    Standard,     // Normal priority fee
    Aggressive,   // High priority fee for fast inclusion
    Emergency,    // Maximum priority fee for immediate inclusion
}

// ============= Data Ingestion Types =============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CexTrade {
    pub exchange: String,
    pub pair: String,
    pub price: Decimal,
    pub amount: Decimal,
    pub side: TradeSide,
    pub timestamp: u64,
}

impl CexTrade {
    pub fn new(
        exchange: String,
        pair: String,
        price: Decimal,
        amount: Decimal,
        side: TradeSide,
    ) -> Self {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Self {
            exchange,
            pair,
            price,
            amount,
            side,
            timestamp,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TradeSide {
    Buy,
    Sell,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBookLevel {
    pub price: Decimal,
    pub amount: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBookUpdate {
    pub exchange: String,
    pub pair: String,
    pub bids: Vec<OrderBookLevel>,
    pub asks: Vec<OrderBookLevel>,
    pub timestamp: u64,
}

impl OrderBookUpdate {
    pub fn new(
        exchange: String,
        pair: String,
        bids: Vec<OrderBookLevel>,
        asks: Vec<OrderBookLevel>,
    ) -> Self {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Self {
            exchange,
            pair,
            bids,
            asks,
            timestamp,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChainBlock {
    pub chain_id: u64,
    pub block_number: u64,
    pub block_hash: H256,
    pub timestamp: u64,
    pub gas_used: U256,
    pub gas_limit: U256,
    pub base_fee_per_gas: Option<U256>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecodedLog {
    pub chain_id: u64,
    pub block_number: u64,
    pub transaction_hash: H256,
    pub address: Address,
    pub event_name: String,
    pub data: HashMap<String, serde_json::Value>,
}

// Specialized log structs for different event types
// These provide strongly-typed representations of blockchain events,
// making it easier and safer to work with the data compared to generic maps.

// DecodedSwapLog: Represents a decoded Swap event from DEX protocols like Uniswap
// Contains all relevant fields from both V2 and V3 swap events in a unified format
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecodedSwapLog {
    pub chain_id: u64,
    pub block_number: u64,
    pub transaction_hash: H256,
    pub pool_address: Address,
    pub token0: Address,
    pub token1: Address,
    pub sender: Address,
    pub recipient: Option<Address>,
    pub amount0_in: U256,
    pub amount1_in: U256,
    pub amount0_out: U256,
    pub amount1_out: U256,
    pub sqrt_price_x96: Option<U256>,
    pub liquidity: Option<U256>,
    pub tick: Option<i32>,
    pub timestamp: u64,
    pub protocol_type: ProtocolType,
}

// DecodedPoolCreatedLog: Represents a decoded PoolCreated event from DEX factory contracts
// Contains information about newly created liquidity pools
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecodedPoolCreatedLog {
    pub chain_id: u64,
    pub block_number: u64,
    pub transaction_hash: H256,
    pub factory_address: Address,
    pub token0: Address,
    pub token1: Address,
    pub fee: u32,
    pub tick_spacing: i32,
    pub pool_address: Address,
    pub timestamp: u64,
    pub protocol_type: ProtocolType,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub enum ProtocolType {
    #[default]
    UniswapV2,
    UniswapV3,
    SushiSwap,
    PancakeSwap,
    Other(String),
}

impl DecodedSwapLog {
    pub fn from_ethers_log(
        log: &ethers::types::Log,
        chain_id: u64,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // Determine protocol type based on event signature
        let v3_swap_signature = "Swap(address,address,int256,int256,uint160,uint128,int24)";
        let v2_swap_signature = "Swap(address,uint256,uint256,uint256,uint256,address)";

        let v3_topic = ethers::utils::keccak256(v3_swap_signature.as_bytes());
        let v2_topic = ethers::utils::keccak256(v2_swap_signature.as_bytes());

        let protocol_type;
        let sender;
        let recipient;
        let amount0_in;
        let amount1_in;
        let amount0_out;
        let amount1_out;
        let sqrt_price_x96;
        let liquidity;
        let tick;

        if log.topics[0] == H256::from(v3_topic) {
            // Uniswap V3 Swap event
            protocol_type = ProtocolType::UniswapV3;

            // For V3, we need to decode the topics and data
            // This is a simplified implementation - in production, use ethers-rs ABI decoder
            sender = Address::from_slice(&log.topics[1].as_bytes()[12..32]);
            recipient = Some(Address::from_slice(&log.topics[2].as_bytes()[12..32]));

            // Decode data for amount0, amount1, sqrtPriceX96, liquidity, tick
            // UniswapV3 Swap event data: (int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)
            if log.data.len() >= 160 { // 5 * 32 bytes
                // Parse amounts (signed integers, but we'll treat as unsigned for simplicity)
                amount0_in = U256::from_big_endian(&log.data[0..32]);
                amount1_in = U256::from_big_endian(&log.data[32..64]);
                amount0_out = U256::zero(); // V3 uses signed amounts, need proper handling
                amount1_out = U256::zero();
                sqrt_price_x96 = Some(U256::from_big_endian(&log.data[64..96]));
                liquidity = Some(U256::from_big_endian(&log.data[96..128]));
                // Tick is int24, stored in last 32 bytes
                let tick_bytes = &log.data[128..160];
                tick = Some(i32::from_be_bytes([tick_bytes[28], tick_bytes[29], tick_bytes[30], tick_bytes[31]]) as i32);
            } else {
                // Fallback to zero values if data is malformed
                amount0_in = U256::zero();
                amount1_in = U256::zero();
                amount0_out = U256::zero();
                amount1_out = U256::zero();
                sqrt_price_x96 = Some(U256::zero());
                liquidity = Some(U256::zero());
                tick = Some(0);
            }
        } else if log.topics[0] == H256::from(v2_topic) {
            // Uniswap V2 Swap event
            protocol_type = ProtocolType::UniswapV2;

            // For V2, we need to decode the topics and data
            sender = Address::from_slice(&log.topics[1].as_bytes()[12..32]);

            // The 'to' address is in the last topic
            recipient = Some(Address::from_slice(&log.topics[2].as_bytes()[12..32]));

            // Decode data for amount0In, amount1In, amount0Out, amount1Out
            // UniswapV2 Swap event data: (uint256 amount0In, uint256 amount1In, uint256 amount0Out, uint256 amount1Out)
            if log.data.len() >= 128 { // 4 * 32 bytes
                amount0_in = U256::from_big_endian(&log.data[0..32]);
                amount1_in = U256::from_big_endian(&log.data[32..64]);
                amount0_out = U256::from_big_endian(&log.data[64..96]);
                amount1_out = U256::from_big_endian(&log.data[96..128]);
            } else {
                // Fallback to zero values if data is malformed
                amount0_in = U256::zero();
                amount1_in = U256::zero();
                amount0_out = U256::zero();
                amount1_out = U256::zero();
            }
            sqrt_price_x96 = None;
            liquidity = None;
            tick = None;
        } else {
            return Err("Unknown swap event signature".into());
        }

        // Extract token addresses from the pool address in the log
        // For now, we'll try to extract from topics if available, otherwise use registry
        let (token0, token1) = if log.topics.len() >= 3 {
            // Some DEXes include token addresses in topics
            let token0 = Address::from_slice(&log.topics[1].as_bytes()[12..32]);
            let token1 = Address::from_slice(&log.topics[2].as_bytes()[12..32]);
            (token0, token1)
        } else {
            // Fallback: would need to query the pool contract for token0() and token1()
            // For now, use common token addresses as fallback
            let registry = crate::token_registry::TokenRegistry::new();
            let weth = registry.get_address("WETH").unwrap_or(Address::zero());
            let usdc = registry.get_address("USDC").unwrap_or(Address::zero());
            (weth, usdc)
        };

        Ok(Self {
            chain_id,
            block_number: log.block_number.unwrap_or_default().as_u64(),
            transaction_hash: log.transaction_hash.unwrap_or_default(),
            pool_address: log.address,
            token0,
            token1,
            sender,
            recipient,
            amount0_in,
            amount1_in,
            amount0_out,
            amount1_out,
            sqrt_price_x96,
            liquidity,
            tick,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            protocol_type,
        })
    }
}

impl DecodedPoolCreatedLog {
    pub fn from_ethers_log(
        log: &ethers::types::Log,
        chain_id: u64,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // Determine protocol type based on factory address
        // This is a simplified implementation - in production, maintain a registry of factory addresses
        let protocol_type = ProtocolType::UniswapV3; // Default to UniswapV3

        // For UniswapV3 PoolCreated event:
        // event PoolCreated(address token0, address token1, uint24 fee, int24 tickSpacing, address pool)

        // Decode PoolCreated event data
        // event PoolCreated(address token0, address token1, uint24 fee, int24 tickSpacing, address pool)
        let (token0, token1, fee, tick_spacing, pool_address) = if log.topics.len() >= 3 && log.data.len() >= 96 {
            // token0 and token1 are in topics[1] and topics[2]
            let token0 = Address::from_slice(&log.topics[1].as_bytes()[12..32]);
            let token1 = Address::from_slice(&log.topics[2].as_bytes()[12..32]);

            // fee, tickSpacing, and pool are in data
            let fee = u32::from_be_bytes([log.data[28], log.data[29], log.data[30], log.data[31]]);
            let tick_spacing = i32::from_be_bytes([log.data[60], log.data[61], log.data[62], log.data[63]]);
            let pool_address = Address::from_slice(&log.data[76..96]);

            (token0, token1, fee, tick_spacing, pool_address)
        } else {
            // Fallback values if event data is malformed
            (Address::zero(), Address::zero(), 3000, 60, Address::zero())
        };

        Ok(Self {
            chain_id,
            block_number: log.block_number.unwrap_or_default().as_u64(),
            transaction_hash: log.transaction_hash.unwrap_or_default(),
            factory_address: log.address,
            token0,
            token1,
            fee,
            tick_spacing,
            pool_address,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            protocol_type,
        })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapEvent {
    pub chain_id: u64,
    pub block_number: u64,
    pub transaction_hash: H256,
    pub pool_address: Address,
    pub token0: Address,
    pub token1: Address,
    pub amount0_in: U256,
    pub amount1_in: U256,
    pub amount0_out: U256,
    pub amount1_out: U256,
    pub timestamp: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MempoolTransaction {
    pub chain_id: u64,
    pub hash: H256,
    pub from: Address,
    pub to: Option<Address>,
    pub value: U256,
    pub gas_price: Option<U256>,
    pub max_fee_per_gas: Option<U256>,
    pub max_priority_fee_per_gas: Option<U256>,
    pub gas: U256,
    pub input: String,
    pub nonce: U256,
    pub timestamp: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NFTListing {
    pub collection: Address,
    pub token_id: U256,
    pub marketplace: String, // "opensea", "blur", etc.
    pub seller: Address,
    pub price_wei: U256,
    pub price_usd: Decimal,
    pub currency: Address, // ETH, WETH, etc.
    pub listing_time: u64,
    pub expiration_time: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NFTFloorPrice {
    pub collection: Address,
    pub floor_price_wei: U256,
    pub floor_price_usd: Decimal,
    pub currency: Address,
    pub timestamp: u64,
    pub source: String,                    // "opensea", "reservoir", etc.
    pub daily_volume_usd: Option<Decimal>, // Daily trading volume for the collection
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoanHealth {
    pub protocol: String, // "aave", "compound", etc.
    pub user: Address,
    pub collateral_token: Address,
    pub debt_token: Address,
    pub collateral_amount: U256,
    pub debt_amount: U256,
    pub health_factor: Decimal, // 1.0 = exactly at liquidation threshold
    pub liquidation_threshold: Decimal,
    pub liquidation_bonus: Decimal, // Percentage bonus for liquidator
    pub timestamp: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct Pool {
    pub address: Address,
    pub token0: Address,
    pub token1: Address,
    pub liquidity: U256,
    pub protocol: ProtocolType,
    // Additional fields for geometric analysis
    pub reserve_0: Decimal,
    pub reserve_1: Decimal,
    pub token_0: String,
    pub token_1: String,
}

// ============= Geometric Scoring Types =============

/// Represents a single pool in an arbitrage path for geometric analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArbitragePool {
    pub address: Address,
    pub reserve0: Decimal, // Reserve of token 0
    pub reserve1: Decimal, // Reserve of token 1
    pub token0_symbol: String,
    pub token1_symbol: String,
    pub protocol: String, // e.g., "Uniswap V3", "Aerodrome"
}

/// Type alias for a complete arbitrage path
pub type ArbitragePath = Vec<ArbitragePool>;

/// Trait for geometric scoring implementations
#[async_trait::async_trait]
pub trait GeometricScorer: Send + Sync {
    async fn calculate_score(&self, path: &ArbitragePath) -> anyhow::Result<GeometricScore>;
}

// ============= Transaction Classification Types =============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransactionClassification {
    RetailUser,
    WhaleUser,
    ArbitrageBot,
    MevBot,
    LiquidationBot,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Transaction {
    pub hash: H256,
    pub from: Address,
    pub to: Option<Address>,
    pub value: U256,
    pub gas_price: U256,
    pub gas_limit: U256,
    pub data: Vec<u8>,
    pub chain_id: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassifiedTransaction {
    pub transaction: Transaction,
    pub classification: TransactionClassification,
    pub confidence: Decimal,                     // 0.0 to 1.0
    pub predicted_price_impact: Option<Decimal>, // USD
    pub estimated_amount_usd: Option<Decimal>,
    pub target_token_in: Option<Address>,
    pub target_token_out: Option<Address>,
    pub timestamp: u64,
}

// ============= SIGINT Workflow Types =============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DirectiveType {
    SetMarketContext,
    ApplyStrategicBias,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SigintDirective {
    pub directive_type: DirectiveType,
    pub regime: Option<MarketRegime>,
    pub character: Option<MarketCharacter>,
    pub bias_target: Option<String>, // Asset address or scanner name
    pub multiplier_adjustment: Option<Decimal>,
    pub reason: String,
    pub duration_hours: Decimal,
    pub created_at: u64, // Unix timestamp
    pub is_dry_run: bool,
    pub signer: Option<String>,
    pub nonce: Option<u64>,
    pub directive_json: Option<String>,
    pub signature: Option<String>,
}

#[derive(Debug, Clone)]
pub struct ActiveDirective {
    pub directive: SigintDirective,
    pub expires_at: u64, // Unix timestamp when this directive expires
}

// Additional directive types for control system
pub type Directive = SigintDirective; // Type alias for backward compatibility
pub type SignedDirective = SigintDirective; // Type alias for backward compatibility

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DirectiveCommand {
    SetMarketContext(MarketRegime, MarketCharacter),
    ApplyStrategicBias(String, Decimal),
    EmergencyStop,
}

// ============= Strategy Types =============

// Unified Opportunity Types for the Ecological Predator
// Using the typestate pattern for better compile-time safety

/// Marker types for opportunity lifecycle states
pub mod opportunity_states {
    #[derive(Debug, Clone)]
    pub struct Detected;

    #[derive(Debug, Clone)]
    pub struct Scored;

    #[derive(Debug, Clone)]
    pub struct Approved;

    #[derive(Debug, Clone)]
    pub struct Executed;
}

/// Base opportunity data shared across all types
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct OpportunityBase {
    pub id: String,
    pub source_scanner: String,
    pub estimated_gross_profit_usd: Decimal,
    pub associated_volatility: Decimal,
    pub requires_flash_liquidity: bool,
    pub chain_id: u64,
    pub timestamp: u64,
    pub intersection_value_usd: Decimal, // Aetheric Resonance Engine - Mandorla Gauge
    pub aetheric_resonance_score: Option<Decimal>, // Added for compatibility
}

/// Specific opportunity data for DEX arbitrage
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct DexArbitrageData {
    pub path: Vec<Address>,
    pub pools: Vec<Address>,
    pub input_amount: U256,
    pub bottleneck_liquidity_usd: Decimal,
    pub estimated_output_amount: Decimal,
}

/// Specific opportunity data for Pilot Fish backrunning
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PilotFishData {
    pub target_whale_tx: H256,
    pub backrun_path: Vec<Address>,
    pub backrun_pools: Vec<Address>,
    pub capital_requirement_usd: Decimal,
    pub whale_impact_estimate: Decimal,
}

/// Specific opportunity data for NFT arbitrage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NftArbitrageData {
    pub collection: Address,
    pub token_id: U256,
    pub buy_from: Address,
    pub sell_to: Address,
    pub floor_price_eth: Decimal,
    pub listing_price_eth: Decimal,
    pub buy_price_usd: Decimal,
    pub sell_price_usd: Decimal,
    pub source_marketplace: String,
    pub target_marketplace: String,
}

/// Specific opportunity data for liquidations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidationData {
    pub user: Address,
    pub collateral: Address,
    pub debt_token: Address,
    pub collateral_amount: U256,
    pub debt_amount: U256,
    pub liquidation_bonus: Decimal,
    pub collateral_token: Address,
}

/// Specific opportunity data for Zen Geometer strategy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ZenGeometerData {
    pub token_in: Address,
    pub token_out: Address,
    pub amount_in: U256,
    pub expected_amount_out: U256,
    pub path: Vec<Address>,
    pub pools: Vec<Address>,
    pub geometric_score: GeometricScore,
    pub temporal_harmonics: Option<TemporalHarmonics>,
    pub loan_amount: U256,
    pub remote_swap_router: Address,
}

/// Type-safe opportunity enum with specific data for each variant
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Opportunity {
    DexArbitrage {
        base: OpportunityBase,
        data: DexArbitrageData,
    },
    PilotFish {
        base: OpportunityBase,
        data: PilotFishData,
    },
    NftArbitrage {
        base: OpportunityBase,
        data: NftArbitrageData,
    },
    Liquidation {
        base: OpportunityBase,
        data: LiquidationData,
    },
    LargeTrade {
        base: OpportunityBase,
        data: LargeTradeData,
    },
    AmmOpportunity {
        base: OpportunityBase,
        data: AmmOpportunityData,
    },
    ZenGeometer {
        base: OpportunityBase,
        data: ZenGeometerData,
    },
}

/// Stateful opportunity wrapper for lifecycle management
#[derive(Debug, Clone)]
pub struct StatefulOpportunity<State> {
    pub opportunity: Opportunity,
    pub state: std::marker::PhantomData<State>,
    pub score: Option<Decimal>,                  // Available after scoring
    pub risk_assessment: Option<RiskAssessment>, // Available after risk analysis
    pub execution_plan: Option<ExecutionPlan>,   // Available after approval
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    pub risk_score: Decimal,
    pub max_position_size_usd: Decimal,
    pub estimated_slippage: Decimal,
    pub confidence_interval: (Decimal, Decimal),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionPlan {
    pub gas_limit: U256,
    pub gas_price: U256,
    pub deadline: u64,
    pub slippage_tolerance: Decimal,
    pub flash_loan_required: bool,
}

impl Opportunity {
    /// Get the base opportunity data regardless of type
    pub fn base(&self) -> &OpportunityBase {
        match self {
            Opportunity::DexArbitrage { base, .. } => base,
            Opportunity::PilotFish { base, .. } => base,
            Opportunity::NftArbitrage { base, .. } => base,
            Opportunity::Liquidation { base, .. } => base,
            Opportunity::LargeTrade { base, .. } => base,
            Opportunity::AmmOpportunity { base, .. } => base,
            Opportunity::ZenGeometer { base, .. } => base,
        }
    }

    /// Get mutable reference to base opportunity data
    pub fn base_mut(&mut self) -> &mut OpportunityBase {
        match self {
            Opportunity::DexArbitrage { base, .. } => base,
            Opportunity::PilotFish { base, .. } => base,
            Opportunity::NftArbitrage { base, .. } => base,
            Opportunity::Liquidation { base, .. } => base,
            Opportunity::LargeTrade { base, .. } => base,
            Opportunity::AmmOpportunity { base, .. } => base,
            Opportunity::ZenGeometer { base, .. } => base,
        }
    }

    /// Get the opportunity type as a string
    pub fn opportunity_type(&self) -> &'static str {
        match self {
            Opportunity::DexArbitrage { .. } => "DexArbitrage",
            Opportunity::PilotFish { .. } => "PilotFish",
            Opportunity::NftArbitrage { .. } => "NftArbitrage",
            Opportunity::Liquidation { .. } => "Liquidation",
            Opportunity::LargeTrade { .. } => "LargeTrade",
            Opportunity::AmmOpportunity { .. } => "AmmOpportunity",
            Opportunity::ZenGeometer { .. } => "ZenGeometer",
        }
    }

    /// Get the associated pools for the opportunity, if any.
    pub fn get_pools(&self) -> Vec<Pool> {
        match self {
            Opportunity::DexArbitrage { data, .. } => {
                data.pools.iter().map(|addr| Pool {
                    address: *addr,
                    token0: Address::zero(), // Placeholder
                    token1: Address::zero(), // Placeholder
                    liquidity: U256::zero(), // Placeholder
                    protocol: ProtocolType::Other("Unknown".to_string()), // Placeholder
                    reserve_0: Decimal::from(1000), // Placeholder
                    reserve_1: Decimal::from(1000), // Placeholder
                    token_0: "WETH".to_string(), // Placeholder
                    token_1: "USDC".to_string(), // Placeholder
                }).collect()
            },
            Opportunity::PilotFish { data, .. } => {
                data.backrun_pools.iter().map(|addr| Pool {
                    address: *addr,
                    token0: Address::zero(), // Placeholder
                    token1: Address::zero(), // Placeholder
                    liquidity: U256::zero(), // Placeholder
                    protocol: ProtocolType::Other("Unknown".to_string()), // Placeholder
                    reserve_0: Decimal::from(1000), // Placeholder
                    reserve_1: Decimal::from(1000), // Placeholder
                    token_0: "WETH".to_string(), // Placeholder
                    token_1: "USDC".to_string(), // Placeholder
                }).collect()
            },
            Opportunity::ZenGeometer { data, .. } => {
                data.pools.iter().map(|addr| Pool {
                    address: *addr,
                    token0: Address::zero(), // Placeholder
                    token1: Address::zero(), // Placeholder
                    liquidity: U256::zero(), // Placeholder
                    protocol: ProtocolType::Other("Unknown".to_string()), // Placeholder
                    reserve_0: Decimal::from(1000), // Placeholder
                    reserve_1: Decimal::from(1000), // Placeholder
                    token_0: "WETH".to_string(), // Placeholder
                    token_1: "USDC".to_string(), // Placeholder
                }).collect()
            },
            Opportunity::LargeTrade { .. } => Vec::new(), // Large trades don't have specific pools
            Opportunity::AmmOpportunity { data, .. } => {
                // AMM opportunities have a single pool for the token pair
                if let Some(pool_address) = data.pair.pool_address {
                    vec![Pool {
                        address: pool_address,
                        token0: data.pair.token_a_address,
                        token1: data.pair.token_b_address,
                        liquidity: U256::zero(), // Will be populated by actual pool data
                        protocol: ProtocolType::UniswapV3, // Assuming V3 for concentrated liquidity
                        reserve_0: Decimal::from(1000), // Placeholder
                        reserve_1: Decimal::from(1000), // Placeholder
                        token_0: data.pair.token_a_symbol.clone(),
                        token_1: data.pair.token_b_symbol.clone(),
                    }]
                } else {
                    Vec::new()
                }
            },
            _ => Vec::new(), // No pools for other opportunity types
        }
    }
}

impl<State> StatefulOpportunity<State> {
    /// Create a new detected opportunity
    pub fn detected(opportunity: Opportunity) -> StatefulOpportunity<opportunity_states::Detected> {
        StatefulOpportunity {
            opportunity,
            state: std::marker::PhantomData,
            score: None,
            risk_assessment: None,
            execution_plan: None,
        }
    }
}

impl StatefulOpportunity<opportunity_states::Detected> {
    /// Transition to scored state
    pub fn score(self, score: Decimal) -> StatefulOpportunity<opportunity_states::Scored> {
        StatefulOpportunity {
            opportunity: self.opportunity,
            state: std::marker::PhantomData,
            score: Some(score),
            risk_assessment: self.risk_assessment,
            execution_plan: self.execution_plan,
        }
    }
}

impl StatefulOpportunity<opportunity_states::Scored> {
    /// Transition to approved state with risk assessment
    pub fn approve(
        self,
        risk_assessment: RiskAssessment,
        execution_plan: ExecutionPlan,
    ) -> StatefulOpportunity<opportunity_states::Approved> {
        StatefulOpportunity {
            opportunity: self.opportunity,
            state: std::marker::PhantomData,
            score: self.score,
            risk_assessment: Some(risk_assessment),
            execution_plan: Some(execution_plan),
        }
    }
}

impl StatefulOpportunity<opportunity_states::Approved> {
    /// Transition to executed state
    pub fn execute(self) -> StatefulOpportunity<opportunity_states::Executed> {
        StatefulOpportunity {
            opportunity: self.opportunity,
            state: std::marker::PhantomData,
            score: self.score,
            risk_assessment: self.risk_assessment,
            execution_plan: self.execution_plan,
        }
    }
}

// Legacy OpportunityType enum for backward compatibility during transition
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub enum OpportunityType {
    DexArbitrage {
        path: Vec<Address>,
        pools: Vec<Address>,
    },
    PilotFishBackrun {
        target_whale_tx: H256,
        backrun_path: Vec<Address>,
        backrun_pools: Vec<Address>,
        capital_requirement_usd: Decimal,
    },
    NftArbitrage {
        collection: Address,
        token_id: U256,
        buy_from: Address,
        sell_to: Address,
    },
    Liquidation {
        user: Address,
        collateral: Address,
    },
    #[default]
    Arbitrage,
    NewTokenLaunch,
}

// Legacy Opportunity struct for backward compatibility
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyOpportunity {
    pub id: String,
    pub opportunity_type: OpportunityType,
    pub source_scanner: String,
    pub estimated_gross_profit_usd: Decimal,
    pub associated_volatility: Decimal,
    pub requires_flash_liquidity: bool,
    pub chain_id: u64,
    pub timestamp: u64,
    pub intersection_value_usd: Decimal,
}

// Legacy type for backward compatibility during transition
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SimpleOpportunity {
    pub id: String,
    pub opportunity_type: OpportunityType,
    pub source_scanner: String,
    pub estimated_gross_profit_usd: Decimal,
    pub associated_volatility: Decimal,
    pub requires_flash_liquidity: bool,
    pub chain_id: u64,
    pub timestamp: u64,
    pub intersection_value_usd: Decimal,
    pub strategy_type: StrategyType,
    pub estimated_profit_usd: Decimal,
    pub gas_cost_usd: Decimal,
    pub loan_amount: Decimal,
    pub token_in: Address,
    pub token_out: Address,
    pub amount_in: U256,
    pub amount_out: U256,
    pub confidence_score: Decimal,
    pub deadline: u64,
    pub metadata: HashMap<String, String>,
}

// Another legacy type for backward compatibility during transition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArbitrageOpportunity {
    pub id: String,
    pub chain_id: u64,
    pub path: Vec<Address>, // Sequence of token addresses in the arbitrage path
    pub pools: Vec<Address>, // Sequence of pool addresses in the arbitrage path
    pub source_strategy: String, // Strategy that found this opportunity
    pub estimated_profit_usd: Decimal, // Estimated profit in USD
    pub input_amount: U256, // Optimal input amount for the first token in the path
    pub timestamp: u64,     // Timestamp when the opportunity was detected

    // New Intelligence Fields
    pub estimated_profit_heuristic: U256, // Rough estimate from Scavenger
    pub bottleneck_liquidity_usd: Decimal, // USD value of the shallowest pool
    pub confidence_score: Decimal,        // 0.0-1.0 score of opportunity quality
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SandwichOpportunity {
    pub chain_id: u64,
    pub victim_tx_hash: H256,
    pub target_pool: Address,
    pub token_in: Address,
    pub token_out: Address,
    pub frontrun_amount: U256,
    pub estimated_profit_usd: Decimal,
    pub timestamp: u64,
}

// ============= Execution Types =============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionRequest {
    pub chain_id: u64,
    pub strategy_type: StrategyType,
    pub payload: serde_json::Value,
    pub priority: ExecutionPriority,
    pub timestamp: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub enum StrategyType {
    #[default]
    DexDexArbitrage,
    CexDexArbitrage,
    Sandwich,
    Liquidation,
    ZenGeometer,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionPriority {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionResult {
    pub request_id: String,
    pub success: bool,
    pub transaction_hash: Option<H256>,
    pub error: Option<String>,
    pub profit_usd: Option<Decimal>,
    pub gas_used: Option<U256>,
    pub gas_cost_usd: Option<Decimal>,
    pub strategy_id: Option<String>,
    pub timestamp: u64,
}

/// Opportunity metrics for chain analysis (used by Nomadic Hunter)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpportunityMetric {
    pub chain_id: u64,
    pub profit_usd: Decimal,
    pub gas_cost_usd: Decimal,
    pub success: bool,
    pub timestamp: u64,
}

/// Post-trade analysis for risk management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PostTradeAnalysis {
    pub opportunity_id: String,
    pub strategy_type: StrategyType,
    pub profit_usd: Decimal,
    pub gas_cost_usd: Decimal,
    pub slippage_pct: Decimal,
    pub execution_time_ms: u64,
    pub success: bool,
    pub timestamp: u64,
    pub realized_pnl_usd: Decimal,
}

// ============= UI/UX State Types =============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServiceStatus {
    Running,
    Stopped,
    Stopping,
    Warning(String),
    Error(String),
    Initializing,
}

impl ServiceStatus {
    pub fn is_healthy(&self) -> bool {
        matches!(self, ServiceStatus::Running)
    }

    pub fn is_error(&self) -> bool {
        matches!(self, ServiceStatus::Error(_))
    }

    pub fn is_warning(&self) -> bool {
        matches!(self, ServiceStatus::Warning(_))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealth {
    pub rpc_node: ServiceStatus,
    pub nats_bus: ServiceStatus,
    pub database: ServiceStatus,
    pub redis: ServiceStatus,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyMetrics {
    pub name: String,
    pub status: ServiceStatus,
    pub pnl_24h: Decimal,
    pub success_rate: Decimal,
    pub trades_24h: u32,
    pub last_activity: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemMetrics {
    pub total_pnl_24h: Decimal,
    pub total_trades_24h: u32,
    pub active_strategies: u32,
    pub total_strategies: u32,
    pub gas_cost_24h: Decimal,
}

// ============= Control Types =============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigUpdate {
    pub component: String,
    pub key: String,
    pub value: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Command {
    pub action: String,
    pub params: HashMap<String, String>,
}

// ============= Market State Types =============

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Default)]
pub enum MarketRegime {
    #[default]
    CalmOrderly,
    RetailFomoSpike,
    BotGasWar,
    HighVolatilityCorrection,
    Trending,
    Unknown,
}

// ZEN GEOMETER: Market Character based on fractal analysis
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum MarketCharacter {
    Trending,      // H > 0.5 - persistent trends
    MeanReverting, // H < 0.5 - mean reversion
    RandomWalk,    // H ~= 0.5 - random walk
}

// ZEN GEOMETER: Enhanced MarketState with fractal intelligence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketState {
    pub chain_id: u64,
    pub regime: MarketRegime,
    pub character: MarketCharacter,
    pub hurst_exponent: Decimal,
    pub volatility_1m: Decimal,
    pub volatility_5m: Decimal,
    pub volatility_1h: Decimal,
    pub gas_price_gwei: Decimal,
    pub mempool_congestion: Decimal,
    pub timestamp: u64,
}

// Network Resonance State already defined above

// ZEN GEOMETER: Market Regime State for Bayesian classification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketRegimeState {
    pub regime: MarketRegime,
    pub confidence: Decimal,
    pub state_probabilities: Vec<Decimal>,
}

// ZEN GEOMETER: Market Phase for AMM opportunity analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MarketPhase {
    Equilibrium,   // Stable, low volatility - ideal for market making
    Expansion,     // Trending upward - asymmetric liquidity placement
    Contraction,   // Trending downward - conservative positioning
    Fracture,      // High volatility chaos - withdraw liquidity
    Accumulation,  // Building positions - gradual accumulation phase
}

// Token pair representation for AMM analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenPair {
    pub token_a_symbol: String,
    pub token_b_symbol: String,
    pub token_a_address: Address,
    pub token_b_address: Address,
    pub pool_address: Option<Address>,
    pub chain_id: u64,
}

// AMM-specific opportunity data for market making
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AmmOpportunityData {
    pub pair: TokenPair,
    pub market_phase: MarketPhase,
    pub liquidity_range: (Decimal, Decimal), // tick range for concentrated liquidity
    pub position_size_usd: Decimal,
    pub expected_fee_apr: Decimal,
    pub risk_score: Decimal,
}

// ============= NATS Topics =============

pub struct NatsTopics;

impl NatsTopics {
    // Data ingestion topics
    pub const CEX_TRADES: &'static str = "data.cex.trades";
    pub const CEX_ORDERBOOK: &'static str = "data.cex.orderbook";
    pub const CHAIN_BLOCKS: &'static str = "data.chain.blocks";
    pub const CHAIN_EVENTS: &'static str = "data.chain.events";
    pub const CHAIN_SWAPS: &'static str = "data.chain.swaps";
    pub const DATA_MEMPOOL_TXS: &'static str = "data.mempool.txs";
    pub const DATA_GAS_PRICES: &'static str = "data.gas.prices";
    pub const DATA_CHAIN_WHALE_TRADES: &'static str = "data.chain.whale_trades";

    // Opportunity topics
    pub const OPPORTUNITIES_ARB_DEX_DEX: &'static str = "opportunities.arb.dex_dex";
    pub const OPPORTUNITIES_ARB_CEX_DEX: &'static str = "opportunities.arb.cex_dex";
    pub const OPPORTUNITIES_ARB_BASILISK_GAZE: &'static str = "opportunities.arb.basilisk_gaze";
    pub const OPPORTUNITIES_ARB_SCAVENGER: &'static str = "opportunities.arb.scavenger";
    pub const OPPORTUNITIES_ARB_PARASITE: &'static str = "opportunities.arb.parasite";
    pub const OPPORTUNITIES_MEV_TICK_RAIDER: &'static str = "opportunities.mev.tick_raider";
    pub const OPPORTUNITIES_MEV_SANDWICH: &'static str = "opportunities.mev.sandwich";
    pub const OPPORTUNITIES_AMM_MARKET_MAKING: &'static str = "opportunities.amm.market_making";

    // Execution topics
    pub const EXECUTION_REQUEST: &'static str = "execution.request";
    pub const EXECUTION_RESULT: &'static str = "execution.result";

    // Control topics
    pub const CONTROL_CONFIG_UPDATE: &'static str = "control.config.update";
    pub const CONTROL_COMMAND: &'static str = "control.command";
    pub const CONTROL_EMERGENCY_STOP: &'static str = "control.emergency_stop";
    pub const CONTROL_SYSTEM_SET_MODE: &'static str = "control.system.set_mode";

    // State topics
    pub const STATE_SYSTEM_HEALTH: &'static str = "state.system.health";
    pub const STATE_STRATEGY_METRICS: &'static str = "state.strategy.metrics";
    pub const STATE_SYSTEM_METRICS: &'static str = "state.system.metrics";
    pub const STATE_MARKET_REGIME: &'static str = "state.market.regime";
    pub const STATE_MARKET_HARMONICS: &'static str = "state.market.harmonics";
    pub const STATE_NETWORK_RESONANCE: &'static str = "state.network.resonance";
    pub const STATE_BALANCES: &'static str = "state.balances";
    pub const STATE_TREASURY: &'static str = "state.treasury";
    pub const STATE_SYSTEM_STATUS: &'static str = "state.system.status";
    pub const STATE_GRAPH_CENTRALITY: &'static str = "state.graph.centrality";

    // Network Seismology topics
    pub const DATA_NETWORK_PROPAGATION: &'static str = "data.network.propagation";
    pub const STATE_NETWORK_TTI: &'static str = "state.network.tti";
    pub const STATE_NETWORK_COHERENCE: &'static str = "state.network.coherence";
    pub const STATE_NETWORK_SEISMOLOGY: &'static str = "state.network.seismology";
    pub const ALERTS_NETWORK_REORG: &'static str = "alerts.network.reorg";

    // Logging topics
    pub const LOG_EVENTS_GAZE: &'static str = "log.events.gaze";
    pub const LOG_EVENTS_NARRATIVE: &'static str = "log.events.narrative";

    // Market state topics
    pub const MARKET_STATE: &'static str = "market.state";
    
    // Living Codex topics
    pub const LIVING_CODEX_ARE_ANALYSIS: &'static str = "living_codex.are_analysis";
    pub const LIVING_CODEX_TRADE_LIFECYCLE: &'static str = "living_codex.trade_lifecycle";

    // Data processing topics for unified system
    pub const DATA_CHAIN_BLOCKS_PROCESSED: &'static str = "data.chain.blocks.processed";
    pub const DATA_CHAIN_LOGS_PROCESSED_SWAPS: &'static str = "data.chain.logs.processed.swaps";
    pub const DATA_CHAIN_LOGS_PROCESSED_POOLS_CREATED: &'static str =
        "data.chain.logs.processed.pools_created";
    pub const DATA_MEMPOOL_TXS_CLASSIFIED: &'static str = "data.mempool.txs.classified";
    pub const DATA_NFT_LISTINGS_PROCESSED: &'static str = "data.nft.listings.processed";
    pub const DATA_NFT_FLOOR_PRICES: &'static str = "data.nft.floor_prices";
    pub const DATA_METRICS_VOLATILITY: &'static str = "data.metrics.volatility";
    pub const STATE_LENDING_LOAN_HEALTH: &'static str = "state.lending.loan_health";

    // Helper function to get exchange-specific topic
    pub fn cex_trades_for(exchange: &str) -> String {
        format!("{}.{}", Self::CEX_TRADES, exchange.to_lowercase())
    }

    // Helper function to get chain-specific topic
    pub fn chain_blocks_for(chain_id: u64) -> String {
        format!("{}.{}", Self::CHAIN_BLOCKS, chain_id)
    }

    pub fn chain_events_for(chain_id: u64) -> String {
        format!("{}.{}", Self::CHAIN_EVENTS, chain_id)
    }

    pub fn chain_swaps_for(chain_id: u64) -> String {
        format!("{}.{}", Self::CHAIN_SWAPS, chain_id)
    }
    
    // Comprehensive Alerting System topics
    pub const ALERTS_WILDCARD: &'static str = "alerts.>";
    pub const ALERTS_RISK: &'static str = "alerts.risk";
    pub const ALERTS_PERFORMANCE: &'static str = "alerts.performance";
    pub const ALERTS_OPERATIONAL: &'static str = "alerts.operational";
    pub const ALERTS_PROFITABILITY: &'static str = "alerts.profitability";
    
    // Missing constants
    pub const STATE_MARKET_REGIME_HMM: &'static str = "state.market.regime.hmm";
    pub const TOPIC_FRACTAL_ANALYSIS_OUTPUT: &'static str = "topic.fractal.analysis.output";
    pub const DATA_PRICES_ALL: &'static str = "data.prices.all";
    pub const MEV_BOT_TX_PENDING: &'static str = "mev.bot.tx.pending";
    pub const CONTROL_RISK_BREACH: &'static str = "control.risk.breach";
    pub const ALERTS_CRITICAL_CIRCUIT_BREAKER: &'static str = "alerts.critical.circuit_breaker";
    pub const ALERTS_EMERGENCY_OPERATOR: &'static str = "alerts.emergency.operator";
    pub const ZEN_GEOMETER_CONTROL_SET_ACTIVE_CHAIN: &'static str = "zen_geometer.control.set_active_chain";
    
    // Additional missing constants for binary tools
    pub const LOG_OPPORTUNITIES_DEGEN: &'static str = "log.opportunities.degen";
    pub const LOG_ANALYSIS_CROSS_CHAIN: &'static str = "log.analysis.cross_chain";
    pub const DATA_OPPORTUNITIES_DETECTED: &'static str = "data.opportunities.detected";
    pub const EXECUTION_TRADE_COMPLETED: &'static str = "execution.trade.completed";
    pub const NETWORK_SEISMOLOGY_UPDATE: &'static str = "network.seismology.update";
    pub const LOGS_SYSTEM: &'static str = "logs.system";
}

// ============= Comprehensive Alerting System Types =============

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Alert {
    pub level: AlertLevel,      // e.g., INFO, WARNING, SEVERE, CRITICAL
    pub category: AlertCategory,  // e.g., Performance, Risk, Operational
    pub title: String,            // A short, clear headline
    pub message: String,          // A detailed, human-readable description
    pub context: HashMap<String, String>, // Key-value pairs for structured data
    pub timestamp: u64,           // Unix timestamp
    pub source: String,           // Which module generated this alert
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq, Eq)]
pub enum AlertLevel { 
    INFO, 
    WARNING, 
    SEVERE, 
    CRITICAL 
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub enum AlertCategory { 
    Performance, 
    Risk, 
    Operational, 
    Profitability 
}

impl Alert {
    pub fn new(
        level: AlertLevel,
        category: AlertCategory,
        title: String,
        message: String,
        source: String,
    ) -> Self {
        Self {
            level,
            category,
            title,
            message,
            context: HashMap::new(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            source,
        }
    }

    pub fn with_context(mut self, key: String, value: String) -> Self {
        self.context.insert(key, value);
        self
    }

    pub fn with_contexts(mut self, contexts: HashMap<String, String>) -> Self {
        self.context.extend(contexts);
        self
    }

    /// Convert alert to Discord-friendly format
    pub fn to_discord_embed(&self) -> serde_json::Value {
        let color = match self.level {
            AlertLevel::INFO => 0x00ff00,      // Green
            AlertLevel::WARNING => 0xffff00,   // Yellow
            AlertLevel::SEVERE => 0xff8000,    // Orange
            AlertLevel::CRITICAL => 0xff0000,  // Red
        };

        let emoji = match self.level {
            AlertLevel::INFO => "INFO",
            AlertLevel::WARNING => "WARN",
            AlertLevel::SEVERE => "SEVERE",
            AlertLevel::CRITICAL => "CRITICAL",
        };

        serde_json::json!({
            "embeds": [{
                "title": format!("{} {} - {}", emoji, self.level_str(), self.title),
                "description": self.message,
                "color": color,
                "timestamp": chrono::DateTime::from_timestamp(self.timestamp as i64, 0)
                    .unwrap_or_else(|| chrono::Utc::now())
                    .to_rfc3339(),
                "fields": self.context.iter().map(|(k, v)| {
                    serde_json::json!({
                        "name": k,
                        "value": v,
                        "inline": true
                    })
                }).collect::<Vec<_>>(),
                "footer": {
                    "text": format!("Source: {} | Category: {:?}", self.source, self.category)
                }
            }]
        })
    }

    pub fn level_str(&self) -> &'static str {
        match self.level {
            AlertLevel::INFO => "INFO",
            AlertLevel::WARNING => "WARNING",
            AlertLevel::SEVERE => "SEVERE",
            AlertLevel::CRITICAL => "CRITICAL",
        }
    }
}

/// PillarBreakdown: Represents the individual scores from each analytical pillar.
/// Used for detailed analysis and debugging of the Aetheric Resonance Score.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PillarBreakdown {
    pub temporal_score: Decimal,
    pub geometric_score: Decimal,
    pub network_score: Decimal,
}