// src/deployment/monitoring.rs
// Deployment monitoring and metrics collection

use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::Duration;
use tokio::time::sleep;
use tracing::{debug, error, info, warn};

use super::{AlertThresholds, DeploymentPhase, MonitoringConfig};

/// Deployment monitor for collecting metrics and detecting issues
#[derive(Debug)]
pub struct DeploymentMonitor {
    config: Arc<RwLock<MonitoringConfig>>,
    metrics_history: Arc<RwLock<Vec<MetricsSnapshot>>>,
    alerts: Arc<RwLock<Vec<Alert>>>,
    is_monitoring: Arc<RwLock<bool>>,
}

impl DeploymentMonitor {
    /// Create a new deployment monitor
    pub fn new() -> Self {
        Self {
            config: Arc::new(RwLock::new(MonitoringConfig::default())),
            metrics_history: Arc::new(RwLock::new(Vec::new())),
            alerts: Arc::new(RwLock::new(Vec::new())),
            is_monitoring: Arc::new(RwLock::new(false)),
        }
    }

    /// Create deployment monitor with custom configuration
    pub fn with_config(config: MonitoringConfig) -> Self {
        Self {
            config: Arc::new(RwLock::new(config)),
            metrics_history: Arc::new(RwLock::new(Vec::new())),
            alerts: Arc::new(RwLock::new(Vec::new())),
            is_monitoring: Arc::new(RwLock::new(false)),
        }
    }

    /// Start continuous monitoring
    pub async fn start_monitoring(&self) -> Result<()> {
        let config = self.config.read().unwrap().clone();
        
        if !config.enabled {
            debug!("Monitoring is disabled");
            return Ok(());
        }

        {
            let mut is_monitoring = self.is_monitoring.write().unwrap();
            if *is_monitoring {
                return Err(anyhow::anyhow!("Monitoring is already running"));
            }
            *is_monitoring = true;
        }

        info!("Starting deployment monitoring");

        let monitor = Arc::new(self.clone());
        tokio::spawn(async move {
            monitor.monitoring_loop().await;
        });

        Ok(())
    }

    /// Stop continuous monitoring
    pub fn stop_monitoring(&self) {
        let mut is_monitoring = self.is_monitoring.write().unwrap();
        *is_monitoring = false;
        info!("Stopped deployment monitoring");
    }

    /// Main monitoring loop
    async fn monitoring_loop(&self) {
        let interval = {
            let config = self.config.read().unwrap();
            Duration::from_secs(config.metrics_collection_interval_seconds)
        };

        while *self.is_monitoring.read().unwrap() {
            if let Err(e) = self.collect_and_analyze_metrics().await {
                error!("Error in monitoring loop: {}", e);
            }

            sleep(interval).await;
        }

        debug!("Monitoring loop stopped");
    }

    /// Collect and analyze metrics
    async fn collect_and_analyze_metrics(&self) -> Result<()> {
        // Collect current metrics
        let metrics = self.collect_deployment_metrics().await?;
        
        // Create snapshot
        let snapshot = MetricsSnapshot {
            timestamp: Utc::now(),
            metrics: metrics.clone(),
        };

        // Store in history
        {
            let mut history = self.metrics_history.write().unwrap();
            history.push(snapshot);
            
            // Keep only last 1000 snapshots
            if history.len() > 1000 {
                history.remove(0);
            }
        }

        // Analyze metrics for alerts
        self.analyze_metrics_for_alerts(&metrics).await?;

        debug!("Collected and analyzed metrics");
        Ok(())
    }

    /// Collect deployment metrics
    pub async fn collect_deployment_metrics(&self) -> Result<HashMap<String, f64>> {
        let mut metrics = HashMap::new();

        // System metrics
        metrics.insert("cpu_usage_percentage".to_string(), self.get_cpu_usage().await?);
        metrics.insert("memory_usage_percentage".to_string(), self.get_memory_usage().await?);
        metrics.insert("disk_usage_percentage".to_string(), self.get_disk_usage().await?);

        // Application metrics
        metrics.insert("error_rate_percentage".to_string(), self.get_error_rate().await?);
        metrics.insert("avg_response_time_ms".to_string(), self.get_avg_response_time().await?);
        metrics.insert("requests_per_second".to_string(), self.get_requests_per_second().await?);
        metrics.insert("active_connections".to_string(), self.get_active_connections().await?);

        // Deployment-specific metrics
        metrics.insert("feature_flags_enabled".to_string(), self.get_feature_flags_count().await?);
        metrics.insert("traffic_split_percentage".to_string(), self.get_traffic_split().await?);
        metrics.insert("deployment_health_score".to_string(), self.get_deployment_health_score().await?);

        // Business metrics
        metrics.insert("successful_trades_per_minute".to_string(), self.get_successful_trades().await?);
        metrics.insert("failed_trades_per_minute".to_string(), self.get_failed_trades().await?);
        metrics.insert("average_profit_per_trade".to_string(), self.get_average_profit().await?);
        metrics.insert("gas_efficiency_score".to_string(), self.get_gas_efficiency().await?);

        debug!("Collected {} deployment metrics", metrics.len());
        Ok(metrics)
    }

    /// Analyze metrics for alert conditions
    async fn analyze_metrics_for_alerts(&self, metrics: &HashMap<String, f64>) -> Result<()> {
        let config = self.config.read().unwrap();
        let thresholds = &config.alert_thresholds;

        let mut new_alerts = Vec::new();

        // Check error rate
        if let Some(&error_rate) = metrics.get("error_rate_percentage") {
            if error_rate > thresholds.error_rate_percentage {
                new_alerts.push(Alert {
                    id: uuid::Uuid::new_v4().to_string(),
                    alert_type: AlertType::HighErrorRate,
                    severity: AlertSeverity::Critical,
                    message: format!("High error rate: {:.2}%", error_rate),
                    timestamp: Utc::now(),
                    metric_name: "error_rate_percentage".to_string(),
                    metric_value: error_rate,
                    threshold: thresholds.error_rate_percentage,
                    resolved: false,
                });
            }
        }

        // Check response time
        if let Some(&response_time) = metrics.get("avg_response_time_ms") {
            if response_time > thresholds.response_time_ms as f64 {
                new_alerts.push(Alert {
                    id: uuid::Uuid::new_v4().to_string(),
                    alert_type: AlertType::HighResponseTime,
                    severity: AlertSeverity::Warning,
                    message: format!("High response time: {:.0}ms", response_time),
                    timestamp: Utc::now(),
                    metric_name: "avg_response_time_ms".to_string(),
                    metric_value: response_time,
                    threshold: thresholds.response_time_ms as f64,
                    resolved: false,
                });
            }
        }

        // Check memory usage
        if let Some(&memory_usage) = metrics.get("memory_usage_percentage") {
            if memory_usage > thresholds.memory_usage_percentage {
                new_alerts.push(Alert {
                    id: uuid::Uuid::new_v4().to_string(),
                    alert_type: AlertType::HighMemoryUsage,
                    severity: AlertSeverity::Warning,
                    message: format!("High memory usage: {:.1}%", memory_usage),
                    timestamp: Utc::now(),
                    metric_name: "memory_usage_percentage".to_string(),
                    metric_value: memory_usage,
                    threshold: thresholds.memory_usage_percentage,
                    resolved: false,
                });
            }
        }

        // Check CPU usage
        if let Some(&cpu_usage) = metrics.get("cpu_usage_percentage") {
            if cpu_usage > thresholds.cpu_usage_percentage {
                new_alerts.push(Alert {
                    id: uuid::Uuid::new_v4().to_string(),
                    alert_type: AlertType::HighCpuUsage,
                    severity: AlertSeverity::Warning,
                    message: format!("High CPU usage: {:.1}%", cpu_usage),
                    timestamp: Utc::now(),
                    metric_name: "cpu_usage_percentage".to_string(),
                    metric_value: cpu_usage,
                    threshold: thresholds.cpu_usage_percentage,
                    resolved: false,
                });
            }
        }

        // Check disk usage
        if let Some(&disk_usage) = metrics.get("disk_usage_percentage") {
            if disk_usage > thresholds.disk_usage_percentage {
                new_alerts.push(Alert {
                    id: uuid::Uuid::new_v4().to_string(),
                    alert_type: AlertType::HighDiskUsage,
                    severity: AlertSeverity::Critical,
                    message: format!("High disk usage: {:.1}%", disk_usage),
                    timestamp: Utc::now(),
                    metric_name: "disk_usage_percentage".to_string(),
                    metric_value: disk_usage,
                    threshold: thresholds.disk_usage_percentage,
                    resolved: false,
                });
            }
        }

        // Add new alerts
        if !new_alerts.is_empty() {
            let mut alerts = self.alerts.write().unwrap();
            for alert in new_alerts {
                warn!("New alert: {}", alert.message);
                alerts.push(alert);
            }

            // Keep only last 500 alerts
            if alerts.len() > 500 {
                let num_to_drain = alerts.len().saturating_sub(500);
                if num_to_drain > 0 {
                    alerts.drain(0..num_to_drain);
                }
            }
        }

        Ok(())
    }

    /// Check for deployment issues
    pub async fn check_deployment_issues(&self) -> Result<Option<String>> {
        let metrics = self.collect_deployment_metrics().await?;

        // Check for critical issues that would require immediate attention
        if let Some(&error_rate) = metrics.get("error_rate_percentage") {
            if error_rate > 15.0 {
                return Ok(Some(format!("Critical error rate: {:.2}%", error_rate)));
            }
        }

        if let Some(&response_time) = metrics.get("avg_response_time_ms") {
            if response_time > 5000.0 {
                return Ok(Some(format!("Critical response time: {:.0}ms", response_time)));
            }
        }

        if let Some(&memory_usage) = metrics.get("memory_usage_percentage") {
            if memory_usage > 95.0 {
                return Ok(Some(format!("Critical memory usage: {:.1}%", memory_usage)));
            }
        }

        // Check for deployment-specific issues
        if let Some(&health_score) = metrics.get("deployment_health_score") {
            if health_score < 0.5 {
                return Ok(Some(format!("Low deployment health score: {:.2}", health_score)));
            }
        }

        Ok(None)
    }

    /// Get deployment health summary
    pub async fn get_deployment_health_summary(&self) -> Result<DeploymentHealthSummary> {
        let metrics = self.collect_deployment_metrics().await?;
        let alerts = self.get_active_alerts();

        let overall_health = self.calculate_overall_health(&metrics, &alerts);

        Ok(DeploymentHealthSummary {
            overall_health,
            metrics_snapshot: metrics,
            active_alerts_count: alerts.len(),
            critical_alerts_count: alerts.iter()
                .filter(|a| a.severity == AlertSeverity::Critical)
                .count(),
            last_updated: Utc::now(),
        })
    }

    /// Calculate overall health score
    fn calculate_overall_health(&self, metrics: &HashMap<String, f64>, alerts: &[Alert]) -> f64 {
        let mut health_score = 1.0;

        // Reduce score based on metrics
        if let Some(&error_rate) = metrics.get("error_rate_percentage") {
            health_score -= (error_rate / 100.0) * 0.3;
        }

        if let Some(&response_time) = metrics.get("avg_response_time_ms") {
            if response_time > 1000.0 {
                health_score -= ((response_time - 1000.0) / 5000.0) * 0.2;
            }
        }

        if let Some(&memory_usage) = metrics.get("memory_usage_percentage") {
            if memory_usage > 80.0 {
                health_score -= ((memory_usage - 80.0) / 20.0) * 0.1;
            }
        }

        // Reduce score based on alerts
        let critical_alerts = alerts.iter().filter(|a| a.severity == AlertSeverity::Critical).count();
        let warning_alerts = alerts.iter().filter(|a| a.severity == AlertSeverity::Warning).count();

        health_score -= (critical_alerts as f64) * 0.2;
        health_score -= (warning_alerts as f64) * 0.1;

        health_score.max(0.0).min(1.0)
    }

    /// Get active alerts
    pub fn get_active_alerts(&self) -> Vec<Alert> {
        self.alerts.read().unwrap()
            .iter()
            .filter(|alert| !alert.resolved)
            .cloned()
            .collect()
    }

    /// Get all alerts
    pub fn get_all_alerts(&self, limit: Option<usize>) -> Vec<Alert> {
        let alerts = self.alerts.read().unwrap();
        let limit = limit.unwrap_or(alerts.len());
        alerts.iter().rev().take(limit).cloned().collect()
    }

    /// Resolve an alert
    pub fn resolve_alert(&self, alert_id: &str) -> Result<()> {
        let mut alerts = self.alerts.write().unwrap();
        
        if let Some(alert) = alerts.iter_mut().find(|a| a.id == alert_id) {
            alert.resolved = true;
            info!("Resolved alert: {}", alert.message);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Alert not found: {}", alert_id))
        }
    }

    /// Get metrics history
    pub fn get_metrics_history(&self, limit: Option<usize>) -> Vec<MetricsSnapshot> {
        let history = self.metrics_history.read().unwrap();
        let limit = limit.unwrap_or(history.len());
        history.iter().rev().take(limit).cloned().collect()
    }

    /// Update monitoring configuration
    pub fn update_config(&self, config: MonitoringConfig) {
        let mut current_config = self.config.write().unwrap();
        *current_config = config;
        info!("Monitoring configuration updated");
    }

    // Metric collection methods (these would typically interface with actual monitoring systems)

    async fn get_cpu_usage(&self) -> Result<f64> {
        // Simulate CPU usage collection
        Ok(25.3)
    }

    async fn get_memory_usage(&self) -> Result<f64> {
        // Simulate memory usage collection
        Ok(67.8)
    }

    async fn get_disk_usage(&self) -> Result<f64> {
        // Simulate disk usage collection
        Ok(45.2)
    }

    async fn get_error_rate(&self) -> Result<f64> {
        // Simulate error rate collection
        Ok(1.2)
    }

    async fn get_avg_response_time(&self) -> Result<f64> {
        // Simulate response time collection
        Ok(234.5)
    }

    async fn get_requests_per_second(&self) -> Result<f64> {
        // Simulate RPS collection
        Ok(156.7)
    }

    async fn get_active_connections(&self) -> Result<f64> {
        // Simulate active connections collection
        Ok(42.0)
    }

    async fn get_feature_flags_count(&self) -> Result<f64> {
        // Simulate feature flags count
        Ok(8.0)
    }

    async fn get_traffic_split(&self) -> Result<f64> {
        // Simulate traffic split percentage
        Ok(35.0)
    }

    async fn get_deployment_health_score(&self) -> Result<f64> {
        // Simulate deployment health score
        Ok(0.85)
    }

    async fn get_successful_trades(&self) -> Result<f64> {
        // Simulate successful trades per minute
        Ok(12.3)
    }

    async fn get_failed_trades(&self) -> Result<f64> {
        // Simulate failed trades per minute
        Ok(0.8)
    }

    async fn get_average_profit(&self) -> Result<f64> {
        // Simulate average profit per trade in USD
        Ok(15.67)
    }

    async fn get_gas_efficiency(&self) -> Result<f64> {
        // Simulate gas efficiency score
        Ok(0.78)
    }
}

impl Clone for DeploymentMonitor {
    fn clone(&self) -> Self {
        Self {
            config: Arc::clone(&self.config),
            metrics_history: Arc::clone(&self.metrics_history),
            alerts: Arc::clone(&self.alerts),
            is_monitoring: Arc::clone(&self.is_monitoring),
        }
    }
}

/// Metrics snapshot at a point in time
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsSnapshot {
    pub timestamp: DateTime<Utc>,
    pub metrics: HashMap<String, f64>,
}

/// Alert for monitoring issues
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Alert {
    pub id: String,
    pub alert_type: AlertType,
    pub severity: AlertSeverity,
    pub message: String,
    pub timestamp: DateTime<Utc>,
    pub metric_name: String,
    pub metric_value: f64,
    pub threshold: f64,
    pub resolved: bool,
}

/// Alert type
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AlertType {
    HighErrorRate,
    HighResponseTime,
    HighMemoryUsage,
    HighCpuUsage,
    HighDiskUsage,
    LowHealthScore,
    DeploymentFailure,
    FeatureFlagIssue,
}

impl std::fmt::Display for AlertType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AlertType::HighErrorRate => write!(f, "high-error-rate"),
            AlertType::HighResponseTime => write!(f, "high-response-time"),
            AlertType::HighMemoryUsage => write!(f, "high-memory-usage"),
            AlertType::HighCpuUsage => write!(f, "high-cpu-usage"),
            AlertType::HighDiskUsage => write!(f, "high-disk-usage"),
            AlertType::LowHealthScore => write!(f, "low-health-score"),
            AlertType::DeploymentFailure => write!(f, "deployment-failure"),
            AlertType::FeatureFlagIssue => write!(f, "feature-flag-issue"),
        }
    }
}

/// Alert severity
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Warning,
    Critical,
}

impl std::fmt::Display for AlertSeverity {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AlertSeverity::Info => write!(f, "info"),
            AlertSeverity::Warning => write!(f, "warning"),
            AlertSeverity::Critical => write!(f, "critical"),
        }
    }
}

/// Deployment health summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentHealthSummary {
    pub overall_health: f64,
    pub metrics_snapshot: HashMap<String, f64>,
    pub active_alerts_count: usize,
    pub critical_alerts_count: usize,
    pub last_updated: DateTime<Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_deployment_monitor_creation() {
        let monitor = DeploymentMonitor::new();
        
        // Should start with no metrics history
        let history = monitor.get_metrics_history(None);
        assert!(history.is_empty());
        
        // Should start with no alerts
        let alerts = monitor.get_active_alerts();
        assert!(alerts.is_empty());
    }

    #[tokio::test]
    async fn test_collect_deployment_metrics() {
        let monitor = DeploymentMonitor::new();
        
        let metrics = monitor.collect_deployment_metrics().await;
        assert!(metrics.is_ok());
        
        let metrics = metrics.unwrap();
        assert!(!metrics.is_empty());
        
        // Should have basic system metrics
        assert!(metrics.contains_key("cpu_usage_percentage"));
        assert!(metrics.contains_key("memory_usage_percentage"));
        assert!(metrics.contains_key("error_rate_percentage"));
    }

    #[tokio::test]
    async fn test_deployment_health_summary() {
        let monitor = DeploymentMonitor::new();
        
        let summary = monitor.get_deployment_health_summary().await;
        assert!(summary.is_ok());
        
        let summary = summary.unwrap();
        assert!(summary.overall_health >= 0.0 && summary.overall_health <= 1.0);
        assert!(!summary.metrics_snapshot.is_empty());
    }

    #[tokio::test]
    async fn test_check_deployment_issues() {
        let monitor = DeploymentMonitor::new();
        
        let issues = monitor.check_deployment_issues().await;
        assert!(issues.is_ok());
        
        // With simulated metrics, should not have critical issues
        assert!(issues.unwrap().is_none());
    }

    #[test]
    fn test_alert_type_display() {
        assert_eq!(AlertType::HighErrorRate.to_string(), "high-error-rate");
        assert_eq!(AlertType::HighResponseTime.to_string(), "high-response-time");
        assert_eq!(AlertType::DeploymentFailure.to_string(), "deployment-failure");
    }

    #[test]
    fn test_alert_severity_display() {
        assert_eq!(AlertSeverity::Info.to_string(), "info");
        assert_eq!(AlertSeverity::Warning.to_string(), "warning");
        assert_eq!(AlertSeverity::Critical.to_string(), "critical");
    }
}