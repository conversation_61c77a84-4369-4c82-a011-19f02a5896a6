// TreasuryManager: Cross-Chain Profit Sweeping and Capital Management
// Manages the bot's treasury across Base (settlement) and Degen Chain (execution)
// Automatically sweeps profits from Degen Chain back to Base using Stargate

use anyhow::Result;
use async_nats::Client as NatsClient;
use ethers::{
    providers::{Http, Provider, Middleware},
    types::{Address, U256, TransactionRequest},
    contract::Contract,
    abi::Abi,
    signers::{LocalWallet, Signer},
    middleware::SignerMiddleware,
};
use rust_decimal::Decimal;
use std::str::FromStr;
use rust_decimal_macros::dec;
use serde_json::json;
use std::sync::Arc;
use std::collections::HashMap;
use tokio::time::{interval, Duration};
use tracing::{debug, error, info, warn};

use crate::config::Settings;
use crate::execution::broadcaster::Broadcaster;

pub struct TreasuryManager {
    base_provider: Arc<Provider<Http>>,
    degen_provider: Arc<Provider<Http>>,
    signer: Arc<LocalWallet>,
    config: Arc<Settings>,
    min_sweep_threshold: Decimal,
    operating_balance_target: Decimal,
    nats_client: Option<NatsClient>, // DEGEN CHAIN: For status publishing
    last_sweep_amount: Decimal,
    last_sweep_time: Option<std::time::Instant>,
    current_status: TreasuryStatus,
}

#[derive(Debug, Clone)]
pub enum TreasuryStatus {
    Idle,
    Sweeping,
    Error(String),
}

impl TreasuryManager {
    pub fn new(
        base_provider: Arc<Provider<Http>>,
        degen_provider: Arc<Provider<Http>>,
        signer: Arc<LocalWallet>,
        config: Arc<Settings>,
        nats_client: Option<NatsClient>,
    ) -> Self {
        Self {
            base_provider,
            degen_provider,
            signer,
            config,
            min_sweep_threshold: dec!(100.0), // Minimum $100 to trigger sweep
            operating_balance_target: dec!(500.0), // Keep $500 operating balance on Degen
            nats_client,
            last_sweep_amount: Decimal::ZERO,
            last_sweep_time: None,
            current_status: TreasuryStatus::Idle,
        }
    }

    /// Start the treasury management service
    pub async fn start(&self) -> Result<()> {
        info!("Starting TreasuryManager for cross-chain profit sweeping");
        
        let mut sweep_interval = interval(Duration::from_secs(3600)); // Check every hour
        
        loop {
            sweep_interval.tick().await;
            
            if let Err(e) = self.check_and_sweep_profits().await {
                error!("Error during profit sweep: {}", e);
            }
        }
    }

    /// Check balances and sweep profits if threshold is met
    async fn check_and_sweep_profits(&self) -> Result<()> {
        debug!("Checking balances for profit sweep");
        
        // Get token balances on Degen Chain
        let degen_balances = self.get_degen_balances().await?;
        
        for (token_symbol, balance_usd) in degen_balances {
            if balance_usd > self.min_sweep_threshold + self.operating_balance_target {
                let sweep_amount_usd = balance_usd - self.operating_balance_target;
                
                info!(
                    "Sweeping {} {} (${}) from Degen Chain to Base",
                    token_symbol, sweep_amount_usd, sweep_amount_usd
                );
                
                if let Err(e) = self.sweep_token_to_base(&token_symbol, sweep_amount_usd).await {
                    error!("Failed to sweep {}: {}", token_symbol, e);
                }
            }
        }
        
        Ok(())
    }

    /// Get token balances on Degen Chain in USD
    async fn get_degen_balances(&self) -> Result<HashMap<String, Decimal>> {
        let mut balances = HashMap::new();
        let wallet_address = self.signer.address();
        
        // Get Degen Chain token addresses
        if let Some(degen_chain) = self.config.chains.get(&666666666) {
            let tokens = &degen_chain.tokens;
            
            // Check USDC balance
            if let Some(tokens_config) = tokens.as_ref() {
                if let Some(usdc_addr_str) = &tokens_config.usdc {
                    if let Ok(token_address) = usdc_addr_str.parse::<Address>() {
                        if let Ok(balance) = self.get_token_balance(token_address, wallet_address).await {
                            let balance_usd = self.convert_to_usd("USDC", balance).await?;
                            if balance_usd > dec!(1.0) {
                                balances.insert("USDC".to_string(), balance_usd);
                            }
                        }
                    }
                }
            }
            
            // Check WETH balance if configured
            if let Some(tokens_config) = tokens.as_ref() {
                if let Some(weth_addr) = &tokens_config.weth {
                    if let Ok(token_address) = weth_addr.parse::<Address>() {
                        if let Ok(balance) = self.get_token_balance(token_address, wallet_address).await {
                            let balance_usd = self.convert_to_usd("WETH", balance).await?;
                            if balance_usd > dec!(1.0) {
                                balances.insert("WETH".to_string(), balance_usd);
                            }
                        }
                    }
                }
            }
            
            // Check DEGEN balance if configured
            if let Some(tokens_config) = tokens.as_ref() {
                if let Some(degen_addr) = &tokens_config.degen {
                    if let Ok(token_address) = degen_addr.parse::<Address>() {
                        if let Ok(balance) = self.get_token_balance(token_address, wallet_address).await {
                            let balance_usd = self.convert_to_usd("DEGEN", balance).await?;
                            if balance_usd > dec!(1.0) {
                                balances.insert("DEGEN".to_string(), balance_usd);
                            }
                        }
                    }
                }
            }
        }
        
        Ok(balances)
    }

    /// Get token balance for an address
    async fn get_token_balance(&self, token_address: Address, wallet_address: Address) -> Result<U256> {
        // ERC20 balanceOf ABI
        let erc20_abi: Abi = serde_json::from_str(r#"[
            {
                "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            }
        ]"#)?;
        
        let token_contract = Contract::new(token_address, erc20_abi, self.degen_provider.clone());
        let balance: U256 = token_contract
            .method("balanceOf", wallet_address)?
            .call()
            .await?;
        
        Ok(balance)
    }

    /// Convert token amount to USD (simplified)
    async fn convert_to_usd(&self, token_symbol: &str, amount: U256) -> Result<Decimal> {
        // Simplified conversion - in practice, this would use a price oracle
        let amount_decimal = Decimal::from_str(&amount.to_string())?;
        
        let price_usd = match token_symbol {
            "USDC" => dec!(1.0), // USDC is $1
            "DEGEN" => dec!(0.01), // Placeholder price for DEGEN
            "WETH" => dec!(3000.0), // Placeholder price for ETH
            _ => dec!(0.0), // Unknown tokens
        };
        
        let decimals = match token_symbol {
            "USDC" => 6,
            _ => 18,
        };
        
        let normalized_amount = amount_decimal / Decimal::from(10u64.pow(decimals));
        Ok(normalized_amount * price_usd)
    }

    /// Sweep a specific token from Degen Chain to Base using Stargate
    async fn sweep_token_to_base(&self, token_symbol: &str, amount_usd: Decimal) -> Result<()> {
        // Only sweep USDC for now (Stargate supports USDC bridging)
        if token_symbol != "USDC" {
            debug!("Skipping sweep for non-USDC token: {}", token_symbol);
            return Ok(());
        }
        
        let usdc_address = self.config.chains.get(&666666666)
            .and_then(|chain| chain.tokens.as_ref())
            .and_then(|tokens| tokens.usdc.as_ref())
            .ok_or_else(|| anyhow::anyhow!("USDC address not configured for Degen Chain"))?
            .parse::<Address>()?;
        
        let stargate_router = self.config.chains.get(&666666666)
            .and_then(|chain| chain.contracts.stargate_compass_v1.as_ref())
            .ok_or_else(|| anyhow::anyhow!("Stargate router not configured for Degen Chain"))?
            .parse::<Address>()?;
        
        // Convert USD amount to USDC amount (6 decimals)
        let usdc_decimal = amount_usd * dec!(1e6);
        let usdc_u64 = usdc_decimal.to_string().parse::<u64>().unwrap_or(0);
        let usdc_amount = U256::from(usdc_u64);
        
        // Build Stargate swap transaction
        let swap_tx = self.build_stargate_swap_tx(
            stargate_router,
            usdc_address,
            usdc_amount,
        ).await?;
        
        // Submit transaction
        let signer_with_chain = (*self.signer).clone().with_chain_id(666666666u64);
        let _client = Arc::new(SignerMiddleware::new(
            self.degen_provider.clone(),
            signer_with_chain,
        ));
        
        let broadcaster = Broadcaster::new_public(self.degen_provider.clone(), false, false);
        
        match broadcaster.send_transaction(swap_tx).await {
            Ok(Some(tx_hash)) => {
                info!("Profit sweep transaction submitted: {:?}", tx_hash);
            }
            Ok(None) => {
                warn!("Profit sweep transaction submitted but no hash returned");
            }
            Err(e) => {
                error!("Failed to submit profit sweep transaction: {}", e);
                return Err(e.into());
            }
        }
        
        Ok(())
    }

    /// Build a Stargate swap transaction to send USDC from Degen to Base
    async fn build_stargate_swap_tx(
        &self,
        router_address: Address,
        _usdc_address: Address,
        amount: U256,
    ) -> Result<TransactionRequest> {
        // Stargate router ABI for swap function
        let stargate_abi: Abi = serde_json::from_str(r#"[
            {
                "inputs": [
                    {"internalType": "uint16", "name": "_dstChainId", "type": "uint16"},
                    {"internalType": "uint8", "name": "_srcPoolId", "type": "uint8"},
                    {"internalType": "uint8", "name": "_dstPoolId", "type": "uint8"},
                    {"internalType": "address payable", "name": "_refundAddress", "type": "address"},
                    {"internalType": "uint256", "name": "_amountLD", "type": "uint256"},
                    {"internalType": "uint256", "name": "_minAmountLD", "type": "uint256"},
                    {"components": [
                        {"internalType": "uint256", "name": "lzTxGas", "type": "uint256"},
                        {"internalType": "uint256", "name": "lzTxValue", "type": "uint256"},
                        {"internalType": "bytes", "name": "lzTxAirdrop", "type": "bytes"}
                    ], "internalType": "struct IStargateRouter.lzTxObj", "name": "_lzTxParams", "type": "tuple"},
                    {"internalType": "bytes", "name": "_to", "type": "bytes"},
                    {"internalType": "bytes", "name": "_payload", "type": "bytes"}
                ],
                "name": "swap",
                "outputs": [],
                "stateMutability": "payable",
                "type": "function"
            },
            {
                "inputs": [
                    {"internalType": "uint16", "name": "_dstChainId", "type": "uint16"},
                    {"internalType": "uint8", "name": "_functionType", "type": "uint8"},
                    {"internalType": "bytes", "name": "_toAddress", "type": "bytes"},
                    {"internalType": "bytes", "name": "_transferAndCallPayload", "type": "bytes"},
                    {"components": [
                        {"internalType": "uint256", "name": "lzTxGas", "type": "uint256"},
                        {"internalType": "uint256", "name": "lzTxValue", "type": "uint256"},
                        {"internalType": "bytes", "name": "lzTxAirdrop", "type": "bytes"}
                    ], "internalType": "struct IStargateRouter.lzTxObj", "name": "_lzTxParams", "type": "tuple"}
                ],
                "name": "quoteLayerZeroFee",
                "outputs": [
                    {"internalType": "uint256", "name": "nativeFee", "type": "uint256"},
                    {"internalType": "uint256", "name": "zroFee", "type": "uint256"}
                ],
                "stateMutability": "view",
                "type": "function"
            }
        ]"#)?;
        
        let router_contract = Contract::new(router_address, stargate_abi, self.degen_provider.clone());
        let wallet_address = self.signer.address();
        
        // Stargate configuration
        let dst_chain_id = 204u16; // Base chain ID in LayerZero
        let src_pool_id = 13u8; // USDC pool ID on Degen Chain
        let dst_pool_id = 1u8; // USDC pool ID on Base
        let min_amount = amount * 95 / 100; // 5% slippage tolerance
        
        // Quote the LayerZero fee
        let (native_fee, _): (U256, U256) = router_contract
            .method("quoteLayerZeroFee", (
                dst_chain_id,
                1u8, // Function type for swap
                ethers::abi::encode(&[ethers::abi::Token::Address(wallet_address)]),
                Vec::<u8>::new(), // Empty payload
                (U256::zero(), U256::zero(), Vec::<u8>::new()), // lzTxParams
            ))?
            .call()
            .await?;
        
        // Build the swap call
        let swap_call = router_contract.method::<_, ()>("swap", (
            dst_chain_id,
            src_pool_id,
            dst_pool_id,
            wallet_address,
            amount,
            min_amount,
            (U256::zero(), U256::zero(), Vec::<u8>::new()), // lzTxParams
            ethers::abi::encode(&[ethers::abi::Token::Address(wallet_address)]), // recipient
            Vec::<u8>::new(), // Empty payload
        ))?;
        
        let mut tx = swap_call.tx;
        
        // Set the value for LayerZero fee - use a simple approach
        let tx_request = TransactionRequest {
            to: tx.to().cloned(),
            value: Some(native_fee),
            data: tx.data().cloned(),
            gas: tx.gas().cloned(),
            gas_price: tx.gas_price(),
            ..Default::default()
        };
        
        Ok(tx_request.into())
    }

    /// Get treasury status across all chains
    pub async fn get_treasury_status(&self) -> Result<HashMap<String, Decimal>> {
        let mut status = HashMap::new();
        
        // Base balances
        let base_balances = self.get_base_balances().await?;
        for (token, balance) in base_balances {
            status.insert(format!("base_{}", token), balance);
        }
        
        // Degen balances
        let degen_balances = self.get_degen_balances().await?;
        for (token, balance) in degen_balances {
            status.insert(format!("degen_{}", token), balance);
        }
        
        Ok(status)
    }

    /// Get token balances on Base Chain
    async fn get_base_balances(&self) -> Result<HashMap<String, Decimal>> {
        let mut balances = HashMap::new();
        let wallet_address = self.signer.address();
        
        // Get Base Chain token addresses
        if let Some(base_chain) = self.config.chains.get(&8453) {
            let tokens = &base_chain.tokens;
            
            // Use Base provider for Base chain balances
            let erc20_abi: Abi = serde_json::from_str(r#"[
                {
                    "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
                    "name": "balanceOf",
                    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                    "stateMutability": "view",
                    "type": "function"
                }
            ]"#)?;
            
            // Check USDC balance
            if let Some(tokens_config) = tokens.as_ref() {
                if let Some(usdc_addr_str) = &tokens_config.usdc {
                    let token_address = usdc_addr_str.parse::<Address>()?;
                    let token_contract = Contract::new(token_address, erc20_abi.clone(), self.base_provider.clone());
                    if let Ok(balance) = token_contract.method::<_, U256>("balanceOf", wallet_address)?.call().await {
                        let balance_usd = self.convert_to_usd("USDC", balance).await?;
                        if balance_usd > dec!(1.0) {
                            balances.insert("USDC".to_string(), balance_usd);
                        }
                    }
                }
            }
            
            // Check WETH balance if configured
            if let Some(tokens_config) = tokens.as_ref() {
                if let Some(weth_addr) = &tokens_config.weth {
                    let token_address = weth_addr.parse::<Address>()?;
                    let token_contract = Contract::new(token_address, erc20_abi.clone(), self.base_provider.clone());
                    if let Ok(balance) = token_contract.method::<_, U256>("balanceOf", wallet_address)?.call().await {
                        let balance_usd = self.convert_to_usd("WETH", balance).await?;
                        if balance_usd > dec!(1.0) {
                            balances.insert("WETH".to_string(), balance_usd);
                        }
                    }
                }
            }
        }
        
        Ok(balances)
    }

    /// DEGEN CHAIN: Publish treasury status to NATS for Mission Control
    async fn publish_status(&self) -> Result<()> {
        if let Some(ref nats_client) = self.nats_client {
            let status_str = match &self.current_status {
                TreasuryStatus::Idle => "idle",
                TreasuryStatus::Sweeping => "sweeping",
                TreasuryStatus::Error(_) => "error",
            };

            let error_message = match &self.current_status {
                TreasuryStatus::Error(msg) => Some(msg.clone()),
                _ => None,
            };

            let status_update = json!({
                "status": status_str,
                "error_message": error_message,
                "last_sweep_amount": self.last_sweep_amount,
                "last_sweep_timestamp": self.last_sweep_time.map(|t| t.elapsed().as_secs()),
                "sweep_threshold": self.min_sweep_threshold,
                "operating_balance_target": self.operating_balance_target,
                "timestamp": chrono::Utc::now().timestamp()
            });

            if let Err(e) = nats_client
                .publish(crate::shared_types::NatsTopics::STATE_TREASURY, serde_json::to_vec(&status_update)?.into())
                .await
            {
                warn!("Failed to publish treasury status: {}", e);
            } else {
                debug!("Published treasury status: {}", status_str);
            }
        }
        Ok(())
    }

    /// DEGEN CHAIN: Update status and publish to NATS
    async fn set_status(&mut self, status: TreasuryStatus) -> Result<()> {
        self.current_status = status;
        self.publish_status().await
    }

    /// DEGEN CHAIN: Record successful sweep
    async fn record_sweep(&mut self, amount: Decimal) -> Result<()> {
        self.last_sweep_amount = amount;
        self.last_sweep_time = Some(std::time::Instant::now());
        self.set_status(TreasuryStatus::Idle).await
    }

    /// Get current status for external access
    pub fn get_status(&self) -> &TreasuryStatus {
        &self.current_status
    }

    /// Get last sweep information
    pub fn get_last_sweep(&self) -> (Decimal, Option<std::time::Instant>) {
        (self.last_sweep_amount, self.last_sweep_time)
    }
}
