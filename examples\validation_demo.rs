// Example demonstrating the enhanced validation system
// Run with: cargo run --example validation_demo

use basilisk_bot::config::{Config, StrategyConfig, ExecutionConfig, Secrets};
use std::collections::HashMap;
use std::env;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 Enhanced Configuration Validation Demo");
    println!("==========================================\n");

    // Set up environment
    env::set_var("APP_ENV", "development");

    println!("1. Testing valid configuration...");
    test_valid_config();

    println!("\n2. Testing invalid Kelly fraction...");
    test_invalid_kelly_fraction();

    println!("\n3. Testing invalid slippage...");
    test_invalid_slippage();

    println!("\n4. Testing invalid chain configuration...");
    test_invalid_chain_config();

    println!("\n5. Testing production environment rules...");
    test_production_rules();

    println!("\n🎯 Validation Demo Complete!");
    println!("The enhanced validation system catches:");
    println!("  ✅ Mathematical constraints (Kelly fraction ≤ 1.0)");
    println!("  ✅ Business rules (slippage limits, gas prices)");
    println!("  ✅ Cross-field consistency (profitability vs costs)");
    println!("  ✅ Environment-specific rules (production safety)");
    println!("  ✅ Address format validation");
    println!("  ✅ Chain ID validation");

    Ok(())
}

fn test_valid_config() {
    let config = create_valid_config();
    match config.validate() {
        Ok(())
        => println!("  ✅ Valid configuration passed validation"),
        Err(e)
        => println!("  ❌ Unexpected validation failure: {}", e),
    }
}

fn test_invalid_kelly_fraction() {
    let mut config = create_valid_config();
    config.strategy.kelly_fraction_cap = 1.5; // Invalid: > 1.0

    match config.validate() {
        Ok(())
        => println!("  ❌ Should have failed validation"),
        Err(e)
        => println!("  ✅ Correctly rejected invalid Kelly fraction: {}", e),
    }
}

fn test_invalid_slippage() {
    let mut config = create_valid_config();
    config.execution.max_slippage_bps = 2000; // Invalid: > 1000

    match config.validate() {
        Ok(())
        => println!("  ❌ Should have failed validation"),
        Err(e)
        => println!("  ✅ Correctly rejected high slippage: {}", e),
    }
}

fn test_invalid_chain_config() {
    let mut config = create_valid_config();
    config.chains.clear(); // Invalid: no chains

    match config.validate() {
        Ok(())
        => println!("  ❌ Should have failed validation"),
        Err(e)
        => println!("  ✅ Correctly rejected empty chains: {}", e),
    }
}

fn test_production_rules() {
    env::set_var("APP_ENV", "production");
    
    let mut config = create_valid_config();
    config.strategy.kelly_fraction_cap = 0.6; // Too aggressive for production

    match config.validate() {
        Ok(())
        => println!("  ❌ Should have failed production validation"),
        Err(e)
        => println!("  ✅ Correctly enforced production rules: {}", e),
    }

    // Reset environment
    env::set_var("APP_ENV", "development");
}

fn create_valid_config() -> Config {
    use basilisk_bot::config::{Config, StrategyConfig, ExecutionConfig, Secrets, ChainConfig, ContractAddresses, DexConfig, NatsConfig, ScoringConfig, RpcEndpoint, TokenConfig};

    let mut chains = HashMap::new();
    chains.insert(8453, ChainConfig {
        name: "Base".to_string(),
        enabled: Some(true),
        rpc_url: "https://mainnet.base.org".to_string(),
        rpc_endpoints: Some(vec![RpcEndpoint { url: "https://mainnet.base.org".to_string(), priority: 1 }]),
        max_gas_price: 50000000000,
        private_key_env_var: "BASE_PRIVATE_KEY".to_string(),
        contracts: ContractAddresses {
            stargate_compass_v1: Some("0x1234567890123456789012345678901234567890".to_string()),
            multicall: Some("0xcA11bde05977b3631167028862bE2a173976CA11".to_string()),
        },
        dex: DexConfig {
            degen_swap_router: Some("0xabcdefabcdefabcdefabcdefabcdefabcdefabcd".to_string()),
            uniswap_v2_router: Some("0x4752ba5dbc23f44d87826276bf6fd6b1c372ad24".to_string()),
            uniswap_universal_router: None,
        },
        tokens: Some(TokenConfig::default()),
    });

    Config {
        app_name: "basilisk_bot".to_string(),
        log_level: "info".to_string(),
        chains,
        strategy: StrategyConfig {
            kelly_fraction_cap: 0.25,
            min_profitability_bps: 50,
            enabled_strategies: vec!["zen_geometer".to_string()],
        },
        execution: ExecutionConfig {
            max_slippage_bps: 500,
            gas_limit_multiplier: 1.2,
            max_gas_price_gwei: 100,
            default_slippage_tolerance: Some("0.5".to_string()),
            max_priority_fee: Some(2),
            gas_multiplier_network_shock: Some(1.5),
            gas_multiplier_bot_gas_war: Some(2.0),
            gas_multiplier_retail_fomo: Some(1.3),
            gas_multiplier_high_volatility: Some(1.4),
        },
        secrets: Secrets {
            api_keys: HashMap::new(),
            private_keys: HashMap::new(),
        },
        nats: NatsConfig {
            url: "nats://localhost:4222".to_string(),
            subjects: vec![],
            queue_group: None,
        },
        scoring: ScoringConfig::default(),
        aetheric_resonance: basilisk_bot::config::AethericResonanceEngineConfig::default(),
    }
}