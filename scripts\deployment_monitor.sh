#!/bin/bash
# Deployment monitoring script for Aetheric Resonance Engine fixes
# This script provides real-time monitoring of deployment health and metrics

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/config/deployment.toml"
METRICS_FILE="$PROJECT_ROOT/logs/deployment_metrics.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

# Function to get current deployment phase
get_current_phase() {
    if [[ -f "$CONFIG_FILE" ]]; then
        if command -v toml &> /dev/null; then
            toml get "$CONFIG_FILE" "deployment.current_phase" 2>/dev/null | tr -d '"' || echo "development"
        else
            grep "current_phase" "$CONFIG_FILE" | cut -d'"' -f2 || echo "development"
        fi
    else
        echo "development"
    fi
}

# Function to get traffic routing
get_traffic_routing() {
    if [[ -f "$CONFIG_FILE" ]]; then
        if command -v toml &> /dev/null; then
            local new_percentage=$(toml get "$CONFIG_FILE" "traffic_routing.new_implementation_percentage" 2>/dev/null | tr -d '"' || echo "0")
            local legacy_percentage=$(toml get "$CONFIG_FILE" "traffic_routing.legacy_implementation_percentage" 2>/dev/null | tr -d '"' || echo "100")
            echo "${new_percentage}% new, ${legacy_percentage}% legacy"
        else
            local new_percentage=$(grep "new_implementation_percentage" "$CONFIG_FILE" | cut -d'=' -f2 | tr -d ' ' || echo "0")
            local legacy_percentage=$(grep "legacy_implementation_percentage" "$CONFIG_FILE" | cut -d'=' -f2 | tr -d ' ' || echo "100")
            echo "${new_percentage}% new, ${legacy_percentage}% legacy"
        fi
    else
        echo "0% new, 100% legacy"
    fi
}

# Function to collect system metrics
collect_system_metrics() {
    local metrics="{}"
    
    # CPU usage
    if command -v top &> /dev/null; then
        local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | cut -d'u' -f1 || echo "0")
        metrics=$(echo "$metrics" | jq --arg cpu "$cpu_usage" '. + {cpu_usage_percentage: ($cpu | tonumber)}')
    fi
    
    # Memory usage
    if command -v free &> /dev/null; then
        local memory_info=$(free | grep Mem)
        local total_mem=$(echo "$memory_info" | awk '{print $2}')
        local used_mem=$(echo "$memory_info" | awk '{print $3}')
        local memory_percentage=$(echo "scale=1; $used_mem * 100 / $total_mem" | bc 2>/dev/null || echo "0")
        metrics=$(echo "$metrics" | jq --arg mem "$memory_percentage" '. + {memory_usage_percentage: ($mem | tonumber)}')
    fi
    
    # Disk usage
    if command -v df &> /dev/null; then
        local disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1 || echo "0")
        metrics=$(echo "$metrics" | jq --arg disk "$disk_usage" '. + {disk_usage_percentage: ($disk | tonumber)}')
    fi
    
    # Load average
    if [[ -f /proc/loadavg ]]; then
        local load_avg=$(cat /proc/loadavg | awk '{print $1}' || echo "0")
        metrics=$(echo "$metrics" | jq --arg load "$load_avg" '. + {load_average_1min: ($load | tonumber)}')
    fi
    
    echo "$metrics"
}

# Function to collect application metrics
collect_application_metrics() {
    local metrics="{}"
    
    # Check if service is running
    if pgrep -f "basilisk" > /dev/null; then
        metrics=$(echo "$metrics" | jq '. + {service_running: true}')
        
        # Try to get metrics from service endpoint
        if curl -f -s "http://localhost:8080/metrics" > /dev/null 2>&1; then
            metrics=$(echo "$metrics" | jq '. + {metrics_endpoint_available: true}')
            
            # Parse Prometheus metrics if available
            local prometheus_metrics=$(curl -s "http://localhost:8080/metrics" 2>/dev/null || echo "")
            if [[ -n "$prometheus_metrics" ]]; then
                # Extract some basic metrics (this would be more sophisticated in practice)
                local requests_total=$(echo "$prometheus_metrics" | grep "http_requests_total" | tail -1 | awk '{print $2}' || echo "0")
                local response_time=$(echo "$prometheus_metrics" | grep "http_request_duration" | tail -1 | awk '{print $2}' || echo "0")
                
                metrics=$(echo "$metrics" | jq --arg req "$requests_total" --arg resp "$response_time" '. + {requests_total: ($req | tonumber), avg_response_time_ms: ($resp | tonumber)}')
            fi
        else
            metrics=$(echo "$metrics" | jq '. + {metrics_endpoint_available: false}')
        fi
        
        # Check health endpoint
        if curl -f -s "http://localhost:8080/health" > /dev/null 2>&1; then
            metrics=$(echo "$metrics" | jq '. + {health_check_passed: true}')
        else
            metrics=$(echo "$metrics" | jq '. + {health_check_passed: false}')
        fi
    else
        metrics=$(echo "$metrics" | jq '. + {service_running: false, metrics_endpoint_available: false, health_check_passed: false}')
    fi
    
    echo "$metrics"
}

# Function to collect deployment-specific metrics
collect_deployment_metrics() {
    local metrics="{}"
    local current_phase=$(get_current_phase)
    
    # Add deployment phase
    metrics=$(echo "$metrics" | jq --arg phase "$current_phase" '. + {current_deployment_phase: $phase}')
    
    # Add traffic routing info
    local traffic_info=$(get_traffic_routing)
    local new_percentage=$(echo "$traffic_info" | cut -d'%' -f1)
    metrics=$(echo "$metrics" | jq --arg traffic "$new_percentage" '. + {traffic_to_new_implementation: ($traffic | tonumber)}')
    
    # Count enabled feature flags
    local enabled_flags=0
    if [[ -f "$CONFIG_FILE" ]]; then
        enabled_flags=$(grep "= true" "$CONFIG_FILE" | grep -c "feature_flags" || echo "0")
    fi
    metrics=$(echo "$metrics" | jq --arg flags "$enabled_flags" '. + {enabled_feature_flags: ($flags | tonumber)}')
    
    # Add timestamp
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    metrics=$(echo "$metrics" | jq --arg ts "$timestamp" '. + {timestamp: $ts}')
    
    echo "$metrics"
}

# Function to collect all metrics
collect_all_metrics() {
    local system_metrics=$(collect_system_metrics)
    local app_metrics=$(collect_application_metrics)
    local deployment_metrics=$(collect_deployment_metrics)
    
    # Merge all metrics
    local all_metrics=$(echo "$system_metrics" | jq --argjson app "$app_metrics" --argjson deploy "$deployment_metrics" '. + $app + $deploy')
    
    echo "$all_metrics"
}

# Function to save metrics to file
save_metrics() {
    local metrics=$1
    echo "$metrics" >> "$METRICS_FILE"
    
    # Keep only last 1000 entries
    if [[ -f "$METRICS_FILE" ]]; then
        tail -1000 "$METRICS_FILE" > "${METRICS_FILE}.tmp" && mv "${METRICS_FILE}.tmp" "$METRICS_FILE"
    fi
}

# Function to display metrics dashboard
display_dashboard() {
    clear
    
    echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${CYAN}║                    Aetheric Resonance Engine Deployment Monitor              ║${NC}"
    echo -e "${BOLD}${CYAN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    local metrics=$(collect_all_metrics)
    
    # Deployment Status
    echo -e "${BOLD}${BLUE}📊 Deployment Status${NC}"
    echo "────────────────────────────────────────────────────────────────────────────────"
    
    local current_phase=$(echo "$metrics" | jq -r '.current_deployment_phase // "unknown"')
    local traffic_percentage=$(echo "$metrics" | jq -r '.traffic_to_new_implementation // 0')
    local enabled_flags=$(echo "$metrics" | jq -r '.enabled_feature_flags // 0')
    
    echo -e "Current Phase:        ${GREEN}$current_phase${NC}"
    echo -e "Traffic Routing:      ${YELLOW}${traffic_percentage}%${NC} to new implementation"
    echo -e "Feature Flags:        ${CYAN}$enabled_flags${NC} enabled"
    echo
    
    # System Health
    echo -e "${BOLD}${BLUE}🖥️  System Health${NC}"
    echo "────────────────────────────────────────────────────────────────────────────────"
    
    local cpu_usage=$(echo "$metrics" | jq -r '.cpu_usage_percentage // 0')
    local memory_usage=$(echo "$metrics" | jq -r '.memory_usage_percentage // 0')
    local disk_usage=$(echo "$metrics" | jq -r '.disk_usage_percentage // 0')
    local load_avg=$(echo "$metrics" | jq -r '.load_average_1min // 0')
    
    # Color code based on thresholds
    local cpu_color=$GREEN
    if (( $(echo "$cpu_usage > 80" | bc -l) )); then cpu_color=$RED; elif (( $(echo "$cpu_usage > 60" | bc -l) )); then cpu_color=$YELLOW; fi
    
    local mem_color=$GREEN
    if (( $(echo "$memory_usage > 80" | bc -l) )); then mem_color=$RED; elif (( $(echo "$memory_usage > 60" | bc -l) )); then mem_color=$YELLOW; fi
    
    local disk_color=$GREEN
    if (( $(echo "$disk_usage > 85" | bc -l) )); then disk_color=$RED; elif (( $(echo "$disk_usage > 70" | bc -l) )); then disk_color=$YELLOW; fi
    
    echo -e "CPU Usage:            ${cpu_color}${cpu_usage}%${NC}"
    echo -e "Memory Usage:         ${mem_color}${memory_usage}%${NC}"
    echo -e "Disk Usage:           ${disk_color}${disk_usage}%${NC}"
    echo -e "Load Average (1m):    ${CYAN}${load_avg}${NC}"
    echo
    
    # Application Status
    echo -e "${BOLD}${BLUE}🚀 Application Status${NC}"
    echo "────────────────────────────────────────────────────────────────────────────────"
    
    local service_running=$(echo "$metrics" | jq -r '.service_running // false')
    local health_check=$(echo "$metrics" | jq -r '.health_check_passed // false')
    local metrics_available=$(echo "$metrics" | jq -r '.metrics_endpoint_available // false')
    
    if [[ "$service_running" == "true" ]]; then
        echo -e "Service Status:       ${GREEN}Running${NC}"
    else
        echo -e "Service Status:       ${RED}Not Running${NC}"
    fi
    
    if [[ "$health_check" == "true" ]]; then
        echo -e "Health Check:         ${GREEN}Passed${NC}"
    else
        echo -e "Health Check:         ${RED}Failed${NC}"
    fi
    
    if [[ "$metrics_available" == "true" ]]; then
        echo -e "Metrics Endpoint:     ${GREEN}Available${NC}"
        
        local requests_total=$(echo "$metrics" | jq -r '.requests_total // 0')
        local response_time=$(echo "$metrics" | jq -r '.avg_response_time_ms // 0')
        
        echo -e "Total Requests:       ${CYAN}$requests_total${NC}"
        echo -e "Avg Response Time:    ${CYAN}${response_time}ms${NC}"
    else
        echo -e "Metrics Endpoint:     ${RED}Unavailable${NC}"
    fi
    echo
    
    # Recent Activity
    echo -e "${BOLD}${BLUE}📈 Recent Activity${NC}"
    echo "────────────────────────────────────────────────────────────────────────────────"
    
    if [[ -f "$METRICS_FILE" ]]; then
        echo "Recent metrics entries:"
        tail -3 "$METRICS_FILE" | while read -r line; do
            local timestamp=$(echo "$line" | jq -r '.timestamp // "unknown"')
            local phase=$(echo "$line" | jq -r '.current_deployment_phase // "unknown"')
            local traffic=$(echo "$line" | jq -r '.traffic_to_new_implementation // 0')
            echo -e "  ${CYAN}$timestamp${NC} - Phase: $phase, Traffic: ${traffic}%"
        done
    else
        echo "No recent activity recorded"
    fi
    echo
    
    # Controls
    echo -e "${BOLD}${BLUE}🎛️  Controls${NC}"
    echo "────────────────────────────────────────────────────────────────────────────────"
    echo "Press 'q' to quit, 'r' to refresh, 'h' for help"
    echo
    
    local timestamp=$(date)
    echo -e "${CYAN}Last updated: $timestamp${NC}"
}

# Function to display help
display_help() {
    clear
    echo -e "${BOLD}${CYAN}Deployment Monitor Help${NC}"
    echo "════════════════════════════════════════════════════════════════════════════════"
    echo
    echo "This monitor displays real-time information about the Aetheric Resonance Engine"
    echo "deployment status, system health, and application metrics."
    echo
    echo -e "${BOLD}Key Sections:${NC}"
    echo "• Deployment Status - Current phase, traffic routing, feature flags"
    echo "• System Health - CPU, memory, disk usage, load average"
    echo "• Application Status - Service status, health checks, metrics"
    echo "• Recent Activity - Historical metrics entries"
    echo
    echo -e "${BOLD}Color Coding:${NC}"
    echo -e "• ${GREEN}Green${NC} - Normal/healthy status"
    echo -e "• ${YELLOW}Yellow${NC} - Warning/elevated status"
    echo -e "• ${RED}Red${NC} - Critical/failed status"
    echo -e "• ${CYAN}Cyan${NC} - Information/neutral status"
    echo
    echo -e "${BOLD}Controls:${NC}"
    echo "• 'q' - Quit the monitor"
    echo "• 'r' - Refresh display immediately"
    echo "• 'h' - Show this help screen"
    echo "• 'd' - Show detailed metrics"
    echo "• 's' - Save current metrics snapshot"
    echo
    echo "Press any key to return to the main dashboard..."
    read -n 1 -s
}

# Function to display detailed metrics
display_detailed_metrics() {
    clear
    echo -e "${BOLD}${CYAN}Detailed Metrics${NC}"
    echo "════════════════════════════════════════════════════════════════════════════════"
    echo
    
    local metrics=$(collect_all_metrics)
    echo "$metrics" | jq '.'
    echo
    echo "Press any key to return to the main dashboard..."
    read -n 1 -s
}

# Function to save metrics snapshot
save_metrics_snapshot() {
    local metrics=$(collect_all_metrics)
    local snapshot_file="$PROJECT_ROOT/logs/metrics_snapshot_$(date +%Y%m%d_%H%M%S).json"
    
    echo "$metrics" > "$snapshot_file"
    
    clear
    echo -e "${BOLD}${GREEN}Metrics Snapshot Saved${NC}"
    echo "════════════════════════════════════════════════════════════════════════════════"
    echo
    echo "Snapshot saved to: $snapshot_file"
    echo
    echo "Press any key to return to the main dashboard..."
    read -n 1 -s
}

# Function to run interactive dashboard
run_interactive_dashboard() {
    local refresh_interval=5
    
    # Set up terminal
    stty -echo
    stty cbreak
    
    # Cleanup function
    cleanup() {
        stty echo
        stty -cbreak
        clear
        echo "Deployment monitor stopped."
        exit 0
    }
    
    # Set up signal handlers
    trap cleanup SIGINT SIGTERM
    
    while true; do
        display_dashboard
        
        # Check for user input with timeout
        if read -t $refresh_interval -n 1 key 2>/dev/null; then
            case $key in
                'q'|'Q')
                    cleanup
                    ;;
                'r'|'R')
                    continue
                    ;;
                'h'|'H')
                    display_help
                    ;;
                'd'|'D')
                    display_detailed_metrics
                    ;;
                's'|'S')
                    save_metrics_snapshot
                    ;;
            esac
        fi
        
        # Save metrics
        local metrics=$(collect_all_metrics)
        save_metrics "$metrics"
    done
}

# Function to run one-time metrics collection
run_once() {
    local metrics=$(collect_all_metrics)
    echo "$metrics" | jq '.'
    save_metrics "$metrics"
}

# Function to run continuous monitoring (non-interactive)
run_continuous() {
    local interval=${1:-30}
    
    echo "Starting continuous monitoring (interval: ${interval}s)"
    echo "Press Ctrl+C to stop"
    
    while true; do
        local metrics=$(collect_all_metrics)
        local timestamp=$(date)
        
        echo "[$timestamp] Collected metrics"
        save_metrics "$metrics"
        
        sleep "$interval"
    done
}

# Main function
main() {
    local command=${1:-"dashboard"}
    
    # Check dependencies
    if ! command -v jq &> /dev/null; then
        echo "Error: jq is required but not installed"
        echo "Please install jq: sudo apt-get install jq"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        echo "Error: bc is required but not installed"
        echo "Please install bc: sudo apt-get install bc"
        exit 1
    fi
    
    case $command in
        "dashboard"|"interactive")
            run_interactive_dashboard
            ;;
        "once"|"snapshot")
            run_once
            ;;
        "continuous")
            local interval=${2:-30}
            run_continuous "$interval"
            ;;
        "help"|"--help"|"-h")
            echo "Usage: $0 {dashboard|once|continuous [interval]|help}"
            echo
            echo "Commands:"
            echo "  dashboard           - Run interactive dashboard (default)"
            echo "  once               - Collect metrics once and display"
            echo "  continuous [interval] - Run continuous monitoring (default interval: 30s)"
            echo "  help               - Show this help message"
            echo
            echo "Examples:"
            echo "  $0                 # Run interactive dashboard"
            echo "  $0 once            # Collect metrics once"
            echo "  $0 continuous 60   # Monitor continuously every 60 seconds"
            ;;
        *)
            echo "Unknown command: $command"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"