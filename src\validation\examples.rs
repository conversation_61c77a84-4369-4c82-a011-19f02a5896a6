// src/validation/examples.rs

//! Example usage of the validation framework

use crate::validation::{ValidationFramework, ValidationResult, ValidationError};
use crate::validation::types::{ValidationConfig, StorageConfig};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::time::Duration;

/// Example metrics for mathematical validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MathValidationMetrics {
    pub calculation_name: String,
    pub expected_value: Decimal,
    pub actual_value: Decimal,
    pub tolerance: Decimal,
    pub within_tolerance: bool,
}

impl std::fmt::Display for MathValidationMetrics {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}: expected={}, actual={}, tolerance={}, within_tolerance={}",
            self.calculation_name,
            self.expected_value,
            self.actual_value,
            self.tolerance,
            self.within_tolerance
        )
    }
}

/// Example metrics for performance validation
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PerformanceValidationMetrics {
    pub operation_name: String,
    pub execution_time_ms: u64,
    pub memory_usage_mb: f64,
    pub throughput_ops_per_sec: f64,
    pub meets_requirements: bool,
}

impl std::fmt::Display for PerformanceValidationMetrics {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}: time={}ms, memory={:.1}MB, throughput={:.1}ops/s, meets_requirements={}",
            self.operation_name,
            self.execution_time_ms,
            self.memory_usage_mb,
            self.throughput_ops_per_sec,
            self.meets_requirements
        )
    }
}

/// Example usage of the validation framework
pub async fn example_validation_usage() -> Result<(), Box<dyn std::error::Error>> {
    // Create validation framework with custom configuration
    let config = ValidationConfig {
        max_execution_time: Duration::from_secs(30),
        continue_on_failure: true,
        mathematical_tolerance: Decimal::new(1, 4), // 0.0001
        storage_config: StorageConfig {
            persist_to_disk: true,
            storage_directory: "validation_results".to_string(),
            max_in_memory_results: 100,
            compress_results: false,
        },
        ..Default::default()
    };

    let framework = ValidationFramework::new(config)?;

    // Example 1: Mathematical validation
    println!("Running mathematical validation example...");
    let math_result = framework.execute_validation(
        "hurst_exponent_calculation",
        || async {
            // Simulate Hurst exponent calculation validation
            let expected = Decimal::new(618, 3); // 0.618 (Golden Ratio related)
            let actual = Decimal::new(6179, 4);   // 0.6179 (slightly off)
            let tolerance = Decimal::new(1, 3);   // 0.001

            let within_tolerance = (expected - actual).abs() <= tolerance;

            let metrics = MathValidationMetrics {
                calculation_name: "Hurst Exponent".to_string(),
                expected_value: expected,
                actual_value: actual,
                tolerance,
                within_tolerance,
            };

            if within_tolerance {
                Ok(metrics)
            } else {
                Err(crate::error::BasiliskError::execution_error(
                    format!("Mathematical validation failed: expected {}, got {}, tolerance {}", 
                           expected, actual, tolerance)
                ))
            }
        }
    ).await?;

    println!("Math validation result: {}", math_result.summary());

    // Example 2: Performance validation
    println!("Running performance validation example...");
    let perf_result = framework.execute_validation(
        "opportunity_detection_performance",
        || async {
            // Simulate performance measurement
            let start = std::time::Instant::now();
            
            // Simulate some work
            tokio::time::sleep(Duration::from_millis(50)).await;
            
            let execution_time_ms = start.elapsed().as_millis() as u64;
            let memory_usage_mb = 45.2; // Simulated memory usage
            let throughput_ops_per_sec = 1000.0 / execution_time_ms as f64;
            
            // Check if performance meets requirements
            let meets_requirements = execution_time_ms < 100 && memory_usage_mb < 100.0;

            let metrics = PerformanceValidationMetrics {
                operation_name: "Opportunity Detection".to_string(),
                execution_time_ms,
                memory_usage_mb,
                throughput_ops_per_sec,
                meets_requirements,
            };

            Ok(metrics)
        }
    ).await?;

    println!("Performance validation result: {}", perf_result.summary());

    // Example 3: Validation suite
    println!("Running validation suite example...");
    
    // Create individual validation functions
    async fn kelly_validation() -> Result<MathValidationMetrics, crate::error::BasiliskError> {
        let metrics = MathValidationMetrics {
            calculation_name: "Kelly Criterion".to_string(),
            expected_value: Decimal::new(25, 2), // 0.25
            actual_value: Decimal::new(249, 3),  // 0.249
            tolerance: Decimal::new(1, 3),       // 0.001
            within_tolerance: true,
        };
        Ok(metrics)
    }
    
    async fn golden_ratio_validation() -> Result<MathValidationMetrics, crate::error::BasiliskError> {
        let metrics = MathValidationMetrics {
            calculation_name: "Golden Ratio Bidding".to_string(),
            expected_value: Decimal::new(382, 3), // 0.382
            actual_value: Decimal::new(381, 3),   // 0.381
            tolerance: Decimal::new(5, 3),        // 0.005
            within_tolerance: true,
        };
        Ok(metrics)
    }
    
    async fn vesica_validation() -> Result<MathValidationMetrics, crate::error::BasiliskError> {
        let metrics = MathValidationMetrics {
            calculation_name: "Vesica Piscis Depth".to_string(),
            expected_value: Decimal::new(866, 3), // 0.866 (sqrt(3)/2)
            actual_value: Decimal::new(865, 3),   // 0.865
            tolerance: Decimal::new(2, 3),        // 0.002
            within_tolerance: true,
        };
        Ok(metrics)
    }

    // Run validations individually since we can't easily create a Vec of different closure types
    let kelly_result = framework.execute_validation("kelly_criterion_validation", || kelly_validation()).await?;
    let golden_ratio_result = framework.execute_validation("golden_ratio_bidding_validation", || golden_ratio_validation()).await?;
    let vesica_result = framework.execute_validation("vesica_piscis_validation", || vesica_validation()).await?;

    println!("Individual validation results:");
    println!("Kelly: {}", kelly_result.summary());
    println!("Golden Ratio: {}", golden_ratio_result.summary());
    println!("Vesica: {}", vesica_result.summary());

    // Display framework status
    println!("\nFramework Status:");
    println!("{}", framework.get_framework_status());

    Ok(())
}

/// Example of handling validation errors
pub async fn example_error_handling() -> Result<(), Box<dyn std::error::Error>> {
    let framework = ValidationFramework::new(ValidationConfig::default())?;

    // Example of a validation that will fail
    let result = framework.execute_validation(
        "failing_validation_example",
        || async {
            // Simulate a mathematical inconsistency
            let expected = Decimal::new(100, 0);
            let actual = Decimal::new(95, 0);
            let tolerance = Decimal::new(1, 0);

            if (expected - actual).abs() > tolerance {
                return Err(crate::error::BasiliskError::execution_error(
                    "Mathematical validation failed with significant deviation"
                ));
            }

            Ok(MathValidationMetrics {
                calculation_name: "Test Calculation".to_string(),
                expected_value: expected,
                actual_value: actual,
                tolerance,
                within_tolerance: false,
            })
        }
    ).await?;

    println!("Failed validation result:");
    println!("{}", result.summary());
    println!("Details:\n{}", result.details());

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_example_validation_usage() {
        // This test ensures the example code compiles and runs
        let result = example_validation_usage().await;
        // We don't assert success here since this is just an example
        println!("Example validation usage result: {:?}", result);
    }

    #[tokio::test]
    async fn test_example_error_handling() {
        let result = example_error_handling().await;
        println!("Example error handling result: {:?}", result);
    }
}