// src/deployment/mod.rs
// Deployment and rollback strategy implementation for Aetheric Resonance Engine fixes

pub mod feature_flags;
pub mod phased_deployment;
pub mod rollback_manager;
pub mod health_checks;
pub mod monitoring;
pub mod cli;

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, warn, error};

/// Deployment phase definitions for gradual rollout
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DeploymentPhase {
    Development,
    CoreScoring,
    MathematicalComponents,
    ComponentIntegration,
    DataQuality,
    ConfigurationMonitoring,
    FullProduction,
}

impl DeploymentPhase {
    /// Get the traffic percentage for this phase
    pub fn traffic_percentage(&self) -> u8 {
        match self {
            DeploymentPhase::Development => 0,
            DeploymentPhase::CoreScoring => 5,
            DeploymentPhase::MathematicalComponents => 15,
            DeploymentPhase::ComponentIntegration => 35,
            DeploymentPhase::DataQuality => 60,
            DeploymentPhase::ConfigurationMonitoring => 85,
            DeploymentPhase::FullProduction => 100,
        }
    }

    /// Get the next phase in the deployment sequence
    pub fn next_phase(&self) -> Option<DeploymentPhase> {
        match self {
            DeploymentPhase::Development => Some(DeploymentPhase::CoreScoring),
            DeploymentPhase::CoreScoring => Some(DeploymentPhase::MathematicalComponents),
            DeploymentPhase::MathematicalComponents => Some(DeploymentPhase::ComponentIntegration),
            DeploymentPhase::ComponentIntegration => Some(DeploymentPhase::DataQuality),
            DeploymentPhase::DataQuality => Some(DeploymentPhase::ConfigurationMonitoring),
            DeploymentPhase::ConfigurationMonitoring => Some(DeploymentPhase::FullProduction),
            DeploymentPhase::FullProduction => None,
        }
    }

    /// Get the previous phase for rollback
    pub fn previous_phase(&self) -> Option<DeploymentPhase> {
        match self {
            DeploymentPhase::Development => None,
            DeploymentPhase::CoreScoring => Some(DeploymentPhase::Development),
            DeploymentPhase::MathematicalComponents => Some(DeploymentPhase::CoreScoring),
            DeploymentPhase::ComponentIntegration => Some(DeploymentPhase::MathematicalComponents),
            DeploymentPhase::DataQuality => Some(DeploymentPhase::ComponentIntegration),
            DeploymentPhase::ConfigurationMonitoring => Some(DeploymentPhase::DataQuality),
            DeploymentPhase::FullProduction => Some(DeploymentPhase::ConfigurationMonitoring),
        }
    }

    /// Get all phases up to and including this phase
    pub fn included_phases(&self) -> Vec<DeploymentPhase> {
        let mut phases = vec![DeploymentPhase::Development];
        
        if *self == DeploymentPhase::Development {
            return phases;
        }
        
        phases.push(DeploymentPhase::CoreScoring);
        if *self == DeploymentPhase::CoreScoring {
            return phases;
        }
        
        phases.push(DeploymentPhase::MathematicalComponents);
        if *self == DeploymentPhase::MathematicalComponents {
            return phases;
        }
        
        phases.push(DeploymentPhase::ComponentIntegration);
        if *self == DeploymentPhase::ComponentIntegration {
            return phases;
        }
        
        phases.push(DeploymentPhase::DataQuality);
        if *self == DeploymentPhase::DataQuality {
            return phases;
        }
        
        phases.push(DeploymentPhase::ConfigurationMonitoring);
        if *self == DeploymentPhase::ConfigurationMonitoring {
            return phases;
        }
        
        phases.push(DeploymentPhase::FullProduction);
        phases
    }
}

impl std::fmt::Display for DeploymentPhase {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DeploymentPhase::Development => write!(f, "development"),
            DeploymentPhase::CoreScoring => write!(f, "core-scoring"),
            DeploymentPhase::MathematicalComponents => write!(f, "mathematical-components"),
            DeploymentPhase::ComponentIntegration => write!(f, "component-integration"),
            DeploymentPhase::DataQuality => write!(f, "data-quality"),
            DeploymentPhase::ConfigurationMonitoring => write!(f, "configuration-monitoring"),
            DeploymentPhase::FullProduction => write!(f, "full-production"),
        }
    }
}

impl std::str::FromStr for DeploymentPhase {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self> {
        match s {
            "development" => Ok(DeploymentPhase::Development),
            "core-scoring" => Ok(DeploymentPhase::CoreScoring),
            "mathematical-components" => Ok(DeploymentPhase::MathematicalComponents),
            "component-integration" => Ok(DeploymentPhase::ComponentIntegration),
            "data-quality" => Ok(DeploymentPhase::DataQuality),
            "configuration-monitoring" => Ok(DeploymentPhase::ConfigurationMonitoring),
            "full-production" => Ok(DeploymentPhase::FullProduction),
            _ => Err(anyhow::anyhow!("Invalid deployment phase: {}", s)),
        }
    }
}

/// Rollback strategy options
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, clap::ValueEnum)]
pub enum RollbackStrategy {
    Immediate,
    Gradual,
    BlueGreen,
}

impl std::fmt::Display for RollbackStrategy {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RollbackStrategy::Immediate => write!(f, "immediate"),
            RollbackStrategy::Gradual => write!(f, "gradual"),
            RollbackStrategy::BlueGreen => write!(f, "blue-green"),
        }
    }
}

impl std::str::FromStr for RollbackStrategy {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self> {
        match s {
            "immediate" => Ok(RollbackStrategy::Immediate),
            "gradual" => Ok(RollbackStrategy::Gradual),
            "blue-green" => Ok(RollbackStrategy::BlueGreen),
            _ => Err(anyhow::anyhow!("Invalid rollback strategy: {}", s)),
        }
    }
}

/// Deployment configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentConfig {
    pub current_phase: DeploymentPhase,
    pub target_phase: Option<DeploymentPhase>,
    pub rollback_strategy: RollbackStrategy,
    pub traffic_routing: TrafficRouting,
    pub feature_flags: HashMap<String, bool>,
    pub health_check_config: HealthCheckConfig,
    pub monitoring_config: MonitoringConfig,
}

impl Default for DeploymentConfig {
    fn default() -> Self {
        Self {
            current_phase: DeploymentPhase::Development,
            target_phase: None,
            rollback_strategy: RollbackStrategy::Gradual,
            traffic_routing: TrafficRouting::default(),
            feature_flags: HashMap::new(),
            health_check_config: HealthCheckConfig::default(),
            monitoring_config: MonitoringConfig::default(),
        }
    }
}

/// Traffic routing configuration for gradual rollout
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrafficRouting {
    pub new_implementation_percentage: f64,
    pub legacy_implementation_percentage: f64,
    pub canary_users: Vec<String>,
    pub excluded_users: Vec<String>,
}

impl Default for TrafficRouting {
    fn default() -> Self {
        Self {
            new_implementation_percentage: 0.0,
            legacy_implementation_percentage: 1.0,
            canary_users: Vec::new(),
            excluded_users: Vec::new(),
        }
    }
}

/// Health check configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    pub enabled: bool,
    pub interval_seconds: u64,
    pub timeout_seconds: u64,
    pub failure_threshold: u32,
    pub success_threshold: u32,
    pub endpoints: Vec<String>,
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            interval_seconds: 30,
            timeout_seconds: 10,
            failure_threshold: 3,
            success_threshold: 2,
            endpoints: vec![
                "/health".to_string(),
                "/metrics".to_string(),
                "/ready".to_string(),
            ],
        }
    }
}

/// Monitoring configuration for deployment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    pub enabled: bool,
    pub metrics_collection_interval_seconds: u64,
    pub alert_thresholds: AlertThresholds,
    pub dashboard_config: DashboardConfig,
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            metrics_collection_interval_seconds: 15,
            alert_thresholds: AlertThresholds::default(),
            dashboard_config: DashboardConfig::default(),
        }
    }
}

/// Alert thresholds for deployment monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThresholds {
    pub error_rate_percentage: f64,
    pub response_time_ms: u64,
    pub memory_usage_percentage: f64,
    pub cpu_usage_percentage: f64,
    pub disk_usage_percentage: f64,
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            error_rate_percentage: 5.0,
            response_time_ms: 1000,
            memory_usage_percentage: 80.0,
            cpu_usage_percentage: 80.0,
            disk_usage_percentage: 85.0,
        }
    }
}

/// Dashboard configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardConfig {
    pub enabled: bool,
    pub refresh_interval_seconds: u64,
    pub panels: Vec<String>,
}

impl Default for DashboardConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            refresh_interval_seconds: 30,
            panels: vec![
                "deployment_status".to_string(),
                "traffic_routing".to_string(),
                "error_rates".to_string(),
                "response_times".to_string(),
                "resource_usage".to_string(),
            ],
        }
    }
}

/// Deployment validation checkpoint
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationCheckpoint {
    pub phase: DeploymentPhase,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub status: CheckpointStatus,
    pub metrics: HashMap<String, f64>,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

/// Checkpoint status
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum CheckpointStatus {
    Pending,
    InProgress,
    Passed,
    Failed,
    Skipped,
}

impl std::fmt::Display for CheckpointStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CheckpointStatus::Pending => write!(f, "pending"),
            CheckpointStatus::InProgress => write!(f, "in-progress"),
            CheckpointStatus::Passed => write!(f, "passed"),
            CheckpointStatus::Failed => write!(f, "failed"),
            CheckpointStatus::Skipped => write!(f, "skipped"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_deployment_phase_progression() {
        let phase = DeploymentPhase::Development;
        assert_eq!(phase.next_phase(), Some(DeploymentPhase::CoreScoring));
        assert_eq!(phase.previous_phase(), None);
        assert_eq!(phase.traffic_percentage(), 0);

        let phase = DeploymentPhase::FullProduction;
        assert_eq!(phase.next_phase(), None);
        assert_eq!(phase.previous_phase(), Some(DeploymentPhase::ConfigurationMonitoring));
        assert_eq!(phase.traffic_percentage(), 100);
    }

    #[test]
    fn test_deployment_phase_included_phases() {
        let phase = DeploymentPhase::ComponentIntegration;
        let included = phase.included_phases();
        
        assert_eq!(included.len(), 4);
        assert!(included.contains(&DeploymentPhase::Development));
        assert!(included.contains(&DeploymentPhase::CoreScoring));
        assert!(included.contains(&DeploymentPhase::MathematicalComponents));
        assert!(included.contains(&DeploymentPhase::ComponentIntegration));
        assert!(!included.contains(&DeploymentPhase::DataQuality));
    }

    #[test]
    fn test_deployment_phase_string_conversion() {
        let phase = DeploymentPhase::MathematicalComponents;
        assert_eq!(phase.to_string(), "mathematical-components");
        
        let parsed: DeploymentPhase = "mathematical-components".parse().unwrap();
        assert_eq!(parsed, DeploymentPhase::MathematicalComponents);
    }

    #[test]
    fn test_traffic_routing_default() {
        let routing = TrafficRouting::default();
        assert_eq!(routing.new_implementation_percentage, 0.0);
        assert_eq!(routing.legacy_implementation_percentage, 1.0);
        assert!(routing.canary_users.is_empty());
        assert!(routing.excluded_users.is_empty());
    }

    #[test]
    fn test_deployment_config_default() {
        let config = DeploymentConfig::default();
        assert_eq!(config.current_phase, DeploymentPhase::Development);
        assert_eq!(config.target_phase, None);
        assert_eq!(config.rollback_strategy, RollbackStrategy::Gradual);
        assert!(config.feature_flags.is_empty());
    }
}