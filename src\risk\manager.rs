use crate::config::Config;
use crate::shared_types::{MarketRegime, NatsTopics, PostTradeAnalysis};
// use crate::alerting::<PERSON><PERSON><PERSON><PERSON><PERSON>; // TODO: Fix alerting module
use std::sync::{Arc, Mutex};
use async_nats::Client as NatsClient;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use tracing::{info, warn, error};
use tokio_stream::StreamExt;

#[derive(Debug, Clone, Default)]
pub struct RiskManagerStatus {
    pub is_trading_halted: bool,
    pub current_pnl: Decimal,
    pub daily_pnl: Decimal,
    pub total_pnl: Decimal,
    pub max_daily_loss: Decimal,
    pub max_position_size: Decimal,
    pub active_positions: u32,
}

pub struct RiskManager {
    nats_client: NatsClient,
    // alert_helper: AlertHelper, // TODO: Fix alerting module
    // Store the baseline configuration
    baseline_settings: crate::config::StrategyConfig,
    // Dynamic (current) settings
    pub max_position_size_usd: Arc<Mutex<Decimal>>,
    run_mode: crate::shared_types::RunMode,
    pub max_daily_loss: Arc<Mutex<Decimal>>,
    // Phase 5: Enhanced dynamic risk parameters
    pub kelly_fraction: Arc<Mutex<Decimal>>,

    // Circuit Breaker State
    daily_pnl_usd: Arc<Mutex<Decimal>>,
    consecutive_failures: Arc<Mutex<u32>>,
    is_halted: Arc<Mutex<bool>>,
}

impl RiskManager {
    pub async fn new(
        nats_client: NatsClient, 
        config: Arc<Config>,
        _price_oracle: Arc<crate::data::price_oracle::PriceOracle>
    ) -> crate::error::Result<Self> {
        let initial_settings = &config.strategy;
        let max_position_size_usd = Arc::new(Mutex::new(dec!(1000.0))); // TODO: Get from config
        let max_daily_loss = Arc::new(Mutex::new(dec!(500.0))); // TODO: Get from config
        let kelly_fraction = Arc::new(Mutex::new(Decimal::from(initial_settings.kelly_fraction_cap as u64) / dec!(100)));

        // let alert_helper = AlertHelper::new(nats_client.clone()); // TODO: Fix alerting module

        Ok(Self {
            nats_client,
            // alert_helper, // TODO: Fix alerting module
            baseline_settings: initial_settings.clone(),
            max_position_size_usd,
            run_mode: crate::shared_types::RunMode::Simulate, // Default mode
            max_daily_loss,
            kelly_fraction,
            daily_pnl_usd: Arc::new(Mutex::new(Decimal::ZERO)),
            consecutive_failures: Arc::new(Mutex::new(0)),
            is_halted: Arc::new(Mutex::new(false)),
        })
    }

    pub async fn run(&self) -> crate::error::Result<()> {
        info!("RiskManager started, subscribing to market regime and trade analysis topics.");
        let mut regime_subscriber = self.nats_client.subscribe(NatsTopics::STATE_MARKET_REGIME).await
            .map_err(|e| crate::error::BasiliskError::DataIngestion { message: format!("Failed to subscribe to market regime: {}", e) })?;
        let mut trade_analysis_subscriber = self.nats_client.subscribe("intelligence.post_trade_analysis").await
            .map_err(|e| crate::error::BasiliskError::DataIngestion { message: format!("Failed to subscribe to trade analysis: {}", e) })?;

        loop {
            tokio::select! {
                Some(msg) = regime_subscriber.next() => {
                    if let Ok(regime) = serde_json::from_slice::<MarketRegime>(&msg.payload) {
                        self.adjust_risk_limits(&regime).await;
                    } else {
                        warn!("RiskManager: Failed to deserialize MarketRegime from NATS.");
                    }
                }
                Some(msg) = trade_analysis_subscriber.next() => {
                    if let Ok(analysis) = serde_json::from_slice::<PostTradeAnalysis>(&msg.payload) {
                        self.update_pnl_and_check_breakers(analysis).await;
                    } else {
                        error!("RiskManager: Failed to deserialize PostTradeAnalysis from NATS.");
                    }
                }
                else => {
                    warn!("All channels closed, RiskManager shutting down.");
                    break;
                }
            }
        }
        Ok(())
    }

    async fn update_pnl_and_check_breakers(&self, analysis: PostTradeAnalysis) {
        let mut daily_pnl = self.daily_pnl_usd.lock().unwrap();
        let mut consecutive_failures = self.consecutive_failures.lock().unwrap();

        *daily_pnl += analysis.realized_pnl_usd;

        if analysis.realized_pnl_usd.is_sign_negative() {
            *consecutive_failures += 1;
        } else {
            *consecutive_failures = 0;
        }

        let max_loss = *self.max_daily_loss.lock().unwrap();
        if *daily_pnl < max_loss {
            // Send circuit breaker alert
            // TODO: Fix alerting module - circuit_breaker_alert
            // if let Err(e) = self.alert_helper.circuit_breaker_alert(pnl, max_loss, source).await {
            if false { // Temporary disable
                error!("Failed to send circuit breaker alert: disabled");
            }
            
            self.trip_circuit_breaker(format!("Daily loss limit exceeded: ${} < ${}", daily_pnl, max_loss)).await;
        }

        if *consecutive_failures >= 5 { // TODO: Get from config
            self.trip_circuit_breaker(format!("Max consecutive failures exceeded: {}", consecutive_failures)).await;
        }
    }

    pub async fn is_trading_halted(&self) -> bool {
        *self.is_halted.lock().unwrap()
    }

    async fn trip_circuit_breaker(&self, reason: String) {
        let mut is_halted = self.is_halted.lock().unwrap();
        if !*is_halted {
            *is_halted = true;
            error!("CIRCUIT BREAKER TRIPPED: {}. Halting all new trades.", reason);
            // Here you would also publish a system-wide alert
            self.nats_client.publish(NatsTopics::CONTROL_RISK_BREACH, reason.into()).await.ok();
        }
    }

    pub async fn is_trade_acceptable(&self, trade_amount_usd: Decimal) -> bool {
        if *self.is_halted.lock().unwrap() {
            warn!("Trade rejected: Circuit breaker is active.");
            return false;
        }

        let max_pos_size = *self.max_position_size_usd.lock().unwrap();
        if trade_amount_usd > max_pos_size {
            warn!("Trade rejected: Exceeds max position size ${} > ${}", trade_amount_usd, max_pos_size);
            return false;
        }

        true
    }

    /// Phase 5: Calculate optimal trade size using Kelly criterion
    pub async fn calculate_trade_size(
        &self,
        win_probability: Decimal,
        win_loss_ratio: Decimal,
        portfolio_value_usd: Decimal,
    ) -> crate::error::Result<Decimal> {
        let current_kelly_fraction = *self.kelly_fraction.lock().unwrap();
        let max_position_usd = *self.max_position_size_usd.lock().unwrap();

        // Kelly formula: f = (bp - q) / b
        // where f = fraction to bet, b = odds received (win_loss_ratio), p = win probability, q = loss probability
        let loss_probability = dec!(1.0) - win_probability;
        let kelly_fraction_optimal = (win_loss_ratio * win_probability - loss_probability) / win_loss_ratio;

        // Apply configured Kelly fraction multiplier (e.g., quarter-Kelly for safety)
        let kelly_fraction_adjusted = kelly_fraction_optimal * current_kelly_fraction;

        // Calculate position size
        let calculated_size = portfolio_value_usd * kelly_fraction_adjusted;

        // Ensure the final size does not exceed the dynamic maximum
        let final_size = calculated_size.min(max_position_usd);

        info!(
            "RISK: Kelly calculation - Win prob: {:.2}, Win/Loss ratio: {:.2}, Kelly optimal: {:.3}, Adjusted: {:.3}, Size: ${:.2}",
            win_probability, win_loss_ratio, kelly_fraction_optimal, kelly_fraction_adjusted, final_size
        );

        Ok(final_size.max(dec!(0.0))) // Ensure non-negative
    }

    async fn adjust_risk_limits(&self, regime: &MarketRegime) {
        let mut max_pos_size = self.max_position_size_usd.lock().unwrap();
        let mut max_daily_loss = self.max_daily_loss.lock().unwrap();
        let mut kelly_fraction = self.kelly_fraction.lock().unwrap();

        let (new_pos_size, new_loss_limit, new_kelly) = match regime {
            MarketRegime::HighVolatilityCorrection | MarketRegime::BotGasWar => {
                info!("High-risk regime detected. Reducing risk limits.");
                (
                    dec!(1000.0) * dec!(0.25), // 25% of normal
                    dec!(500.0) * dec!(0.5),             // 50% of normal loss limit
                    Decimal::from(self.baseline_settings.kelly_fraction_cap as u64) / dec!(100) * dec!(0.5)          // Half of normal Kelly
                )
            },
            MarketRegime::CalmOrderly => {
                info!("Calm regime detected. Restoring default risk limits.");
                (
                    dec!(1000.0),
                    dec!(500.0),
                    Decimal::from(self.baseline_settings.kelly_fraction_cap as u64) / dec!(100)
                )
            },
            MarketRegime::RetailFomoSpike => {
                info!("Retail FOMO regime detected: Slightly increasing risk limits.");
                (
                    dec!(1000.0) * dec!(1.2), // 120% of normal
                    dec!(500.0) * dec!(1.5),             // 150% of normal loss limit
                    Decimal::from(self.baseline_settings.kelly_fraction_cap as u64) / dec!(100) * dec!(1.1)          // 110% of normal Kelly
                )
            },
            _ => return, // No change for other unhandled regimes
        };
        
        *max_pos_size = new_pos_size;
        *max_daily_loss = new_loss_limit;
        *kelly_fraction = new_kelly;
    }
}