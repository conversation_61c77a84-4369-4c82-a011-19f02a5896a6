// src/deployment/phased_deployment.rs
// Phased deployment manager with validation checkpoints

use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::Duration;
use tokio::time::sleep;
use tracing::{debug, error, info, warn};

use super::{
    CheckpointStatus, DeploymentConfig, DeploymentPhase, TrafficRouting, ValidationCheckpoint,
};
use super::feature_flags::FeatureFlagManager;
use super::health_checks::HealthCheckManager;
use super::monitoring::DeploymentMonitor;

/// Phased deployment manager for gradual rollout
#[derive(Debug)]
pub struct PhasedDeploymentManager {
    config: Arc<RwLock<DeploymentConfig>>,
    feature_flags: Arc<FeatureFlagManager>,
    health_checks: Arc<HealthCheckManager>,
    monitor: Arc<DeploymentMonitor>,
    checkpoints: Arc<RwLock<Vec<ValidationCheckpoint>>>,
}

impl PhasedDeploymentManager {
    /// Create a new phased deployment manager
    pub fn new(
        config: DeploymentConfig,
        feature_flags: Arc<FeatureFlagManager>,
        health_checks: Arc<HealthCheckManager>,
        monitor: Arc<DeploymentMonitor>,
    ) -> Self {
        Self {
            config: Arc::new(RwLock::new(config)),
            feature_flags,
            health_checks,
            monitor,
            checkpoints: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Start phased deployment to target phase
    pub async fn deploy_to_phase(&self, target_phase: DeploymentPhase) -> Result<()> {
        let current_phase = {
            let config = self.config.read().unwrap();
            config.current_phase
        };

        info!(
            "Starting phased deployment from {} to {}",
            current_phase, target_phase
        );

        // Validate deployment path
        self.validate_deployment_path(current_phase, target_phase)?;

        // Update target phase in configuration
        {
            let mut config = self.config.write().unwrap();
            config.target_phase = Some(target_phase);
        }

        // Execute deployment phases
        let mut current = current_phase;
        while current != target_phase {
            let next_phase = current.next_phase()
                .ok_or_else(|| anyhow::anyhow!("No next phase available from {}", current))?;

            info!("Deploying phase: {}", next_phase);
            
            // Create validation checkpoint
            let checkpoint = self.create_checkpoint(next_phase).await;
            self.add_checkpoint(checkpoint).await;

            // Execute phase deployment
            match self.deploy_phase(next_phase).await {
                Ok(()) => {
                    info!("Successfully deployed phase: {}", next_phase);
                    current = next_phase;
                    
                    // Update checkpoint status
                    self.update_checkpoint_status(next_phase, CheckpointStatus::Passed).await;
                }
                Err(e) => {
                    error!("Failed to deploy phase {}: {}", next_phase, e);
                    
                    // Update checkpoint status
                    self.update_checkpoint_status(next_phase, CheckpointStatus::Failed).await;
                    
                    // Attempt rollback
                    warn!("Attempting rollback due to deployment failure");
                    self.rollback_to_phase(current).await?;
                    
                    return Err(anyhow::anyhow!(
                        "Deployment failed at phase {}: {}",
                        next_phase, e
                    ));
                }
            }

            if next_phase == target_phase {
                break;
            }
        }

        // Update current phase in configuration
        {
            let mut config = self.config.write().unwrap();
            config.current_phase = target_phase;
            config.target_phase = None;
        }

        info!("Phased deployment completed successfully to phase: {}", target_phase);
        Ok(())
    }

    /// Deploy a specific phase
    async fn deploy_phase(&self, phase: DeploymentPhase) -> Result<()> {
        info!("Deploying phase: {}", phase);

        // Step 1: Update feature flags for the phase
        self.feature_flags.update_phase(phase)?;

        // Step 2: Gradually increase traffic to new implementation
        self.gradual_traffic_increase(phase).await?;

        // Step 3: Run validation checkpoints
        self.run_validation_checkpoints(phase).await?;

        // Step 4: Monitor deployment health
        self.monitor_deployment_health(phase).await?;

        info!("Phase {} deployed successfully", phase);
        Ok(())
    }

    /// Gradually increase traffic to new implementation
    async fn gradual_traffic_increase(&self, phase: DeploymentPhase) -> Result<()> {
        let target_percentage = phase.traffic_percentage() as f64;
        let current_percentage = {
            let config = self.config.read().unwrap();
            config.traffic_routing.new_implementation_percentage
        };

        info!(
            "Gradually increasing traffic from {}% to {}%",
            current_percentage, target_percentage
        );

        // Calculate traffic increase steps
        let steps = self.calculate_traffic_steps(current_percentage, target_percentage);

        for step_percentage in steps {
            info!("Increasing traffic to {}%", step_percentage);

            // Update traffic routing
            self.update_traffic_routing(step_percentage).await?;

            // Wait for stabilization
            let stabilization_time = self.calculate_stabilization_time(step_percentage);
            info!("Waiting {}s for stabilization", stabilization_time.as_secs());
            sleep(stabilization_time).await;

            // Check health after each step
            if !self.health_checks.run_all_checks().await? {
                return Err(anyhow::anyhow!(
                    "Health checks failed during traffic increase at {}%",
                    step_percentage
                ));
            }

            // Check for deployment issues
            if let Some(issue) = self.monitor.check_deployment_issues().await? {
                return Err(anyhow::anyhow!(
                    "Deployment issue detected during traffic increase: {}",
                    issue
                ));
            }
        }

        info!("Traffic increase completed to {}%", target_percentage);
        Ok(())
    }

    /// Calculate traffic increase steps
    fn calculate_traffic_steps(&self, current: f64, target: f64) -> Vec<f64> {
        if current >= target {
            return vec![target];
        }

        let mut steps = Vec::new();
        let difference = target - current;
        
        // Use smaller steps for larger increases
        let step_size = if difference <= 10.0 {
            difference / 2.0
        } else if difference <= 30.0 {
            5.0
        } else {
            10.0
        };

        let mut current_step = current + step_size;
        while current_step < target {
            steps.push(current_step);
            current_step += step_size;
        }
        
        steps.push(target);
        steps
    }

    /// Calculate stabilization time based on traffic percentage
    fn calculate_stabilization_time(&self, percentage: f64) -> Duration {
        // Higher traffic changes need more stabilization time
        let base_time = Duration::from_secs(30);
        let additional_time = Duration::from_secs((percentage / 10.0) as u64 * 10);
        base_time + additional_time
    }

    /// Update traffic routing configuration
    async fn update_traffic_routing(&self, new_percentage: f64) -> Result<()> {
        let mut config = self.config.write().unwrap();
        config.traffic_routing.new_implementation_percentage = new_percentage;
        config.traffic_routing.legacy_implementation_percentage = 100.0 - new_percentage;

        debug!(
            "Updated traffic routing: {}% new, {}% legacy",
            new_percentage,
            100.0 - new_percentage
        );

        Ok(())
    }

    /// Run validation checkpoints for a phase
    async fn run_validation_checkpoints(&self, phase: DeploymentPhase) -> Result<()> {
        info!("Running validation checkpoints for phase: {}", phase);

        let checkpoints = self.get_phase_checkpoints(phase);
        
        for checkpoint_name in checkpoints {
            info!("Running checkpoint: {}", checkpoint_name);
            
            match self.run_checkpoint(&checkpoint_name, phase).await {
                Ok(()) => {
                    info!("Checkpoint passed: {}", checkpoint_name);
                }
                Err(e) => {
                    error!("Checkpoint failed: {}: {}", checkpoint_name, e);
                    return Err(anyhow::anyhow!(
                        "Validation checkpoint '{}' failed: {}",
                        checkpoint_name, e
                    ));
                }
            }
        }

        info!("All validation checkpoints passed for phase: {}", phase);
        Ok(())
    }

    /// Get validation checkpoints for a specific phase
    fn get_phase_checkpoints(&self, phase: DeploymentPhase) -> Vec<String> {
        match phase {
            DeploymentPhase::Development => vec![
                "basic_functionality".to_string(),
                "configuration_loading".to_string(),
            ],
            DeploymentPhase::CoreScoring => vec![
                "scoring_engine_functionality".to_string(),
                "weight_application_validation".to_string(),
                "neutral_score_fallback_validation".to_string(),
                "geometric_score_completeness".to_string(),
            ],
            DeploymentPhase::MathematicalComponents => vec![
                "hurst_exponent_calculation".to_string(),
                "market_rhythm_stability".to_string(),
                "vesica_piscis_negative_handling".to_string(),
                "temporal_harmonics_integration".to_string(),
                "liquidity_centroid_calculation".to_string(),
            ],
            DeploymentPhase::ComponentIntegration => vec![
                "network_state_integration".to_string(),
                "asset_centrality_initialization".to_string(),
                "token_registry_integration".to_string(),
                "vesica_piscis_geometric_integration".to_string(),
            ],
            DeploymentPhase::DataQuality => vec![
                "network_coherence_calculation".to_string(),
                "censorship_detection".to_string(),
                "sequencer_health_monitoring".to_string(),
                "data_validation_comprehensive".to_string(),
            ],
            DeploymentPhase::ConfigurationMonitoring => vec![
                "configuration_validation_enhanced".to_string(),
                "graceful_degradation_patterns".to_string(),
                "performance_monitoring".to_string(),
                "error_propagation_enhanced".to_string(),
            ],
            DeploymentPhase::FullProduction => vec![
                "full_system_integration".to_string(),
                "performance_under_load".to_string(),
                "error_handling_comprehensive".to_string(),
                "monitoring_alerting_complete".to_string(),
            ],
        }
    }

    /// Run a specific validation checkpoint
    async fn run_checkpoint(&self, checkpoint_name: &str, phase: DeploymentPhase) -> Result<()> {
        match checkpoint_name {
            "basic_functionality" => self.validate_basic_functionality().await,
            "configuration_loading" => self.validate_configuration_loading().await,
            "scoring_engine_functionality" => self.validate_scoring_engine().await,
            "weight_application_validation" => self.validate_weight_application().await,
            "neutral_score_fallback_validation" => self.validate_neutral_score_fallbacks().await,
            "geometric_score_completeness" => self.validate_geometric_score_completeness().await,
            "hurst_exponent_calculation" => self.validate_hurst_exponent().await,
            "market_rhythm_stability" => self.validate_market_rhythm_stability().await,
            "vesica_piscis_negative_handling" => self.validate_vesica_piscis_negative().await,
            "temporal_harmonics_integration" => self.validate_temporal_harmonics().await,
            "liquidity_centroid_calculation" => self.validate_liquidity_centroid().await,
            "network_state_integration" => self.validate_network_state_integration().await,
            "asset_centrality_initialization" => self.validate_asset_centrality().await,
            "token_registry_integration" => self.validate_token_registry().await,
            "vesica_piscis_geometric_integration" => self.validate_vesica_piscis_integration().await,
            "network_coherence_calculation" => self.validate_network_coherence().await,
            "censorship_detection" => self.validate_censorship_detection().await,
            "sequencer_health_monitoring" => self.validate_sequencer_health().await,
            "data_validation_comprehensive" => self.validate_data_quality().await,
            "configuration_validation_enhanced" => self.validate_enhanced_configuration().await,
            "graceful_degradation_patterns" => self.validate_graceful_degradation().await,
            "performance_monitoring" => self.validate_performance_monitoring().await,
            "error_propagation_enhanced" => self.validate_error_propagation().await,
            "full_system_integration" => self.validate_full_system().await,
            "performance_under_load" => self.validate_performance_under_load().await,
            "error_handling_comprehensive" => self.validate_comprehensive_error_handling().await,
            "monitoring_alerting_complete" => self.validate_monitoring_alerting().await,
            _ => Err(anyhow::anyhow!("Unknown checkpoint: {}", checkpoint_name)),
        }
    }

    /// Monitor deployment health during phase rollout
    async fn monitor_deployment_health(&self, phase: DeploymentPhase) -> Result<()> {
        info!("Monitoring deployment health for phase: {}", phase);

        let monitoring_duration = Duration::from_secs(300); // 5 minutes
        let check_interval = Duration::from_secs(30);
        let start_time = Utc::now();

        while Utc::now().signed_duration_since(start_time).to_std()? < monitoring_duration {
            // Run health checks
            if !self.health_checks.run_all_checks().await? {
                return Err(anyhow::anyhow!(
                    "Health checks failed during deployment monitoring"
                ));
            }

            // Check deployment metrics
            let metrics = self.monitor.collect_deployment_metrics().await?;
            if let Some(issue) = self.analyze_deployment_metrics(&metrics) {
                return Err(anyhow::anyhow!(
                    "Deployment metrics indicate issue: {}",
                    issue
                ));
            }

            sleep(check_interval).await;
        }

        info!("Deployment health monitoring completed successfully");
        Ok(())
    }

    /// Analyze deployment metrics for issues
    fn analyze_deployment_metrics(&self, metrics: &HashMap<String, f64>) -> Option<String> {
        // Check error rate
        if let Some(&error_rate) = metrics.get("error_rate_percentage") {
            if error_rate > 5.0 {
                return Some(format!("High error rate: {}%", error_rate));
            }
        }

        // Check response time
        if let Some(&response_time) = metrics.get("avg_response_time_ms") {
            if response_time > 1000.0 {
                return Some(format!("High response time: {}ms", response_time));
            }
        }

        // Check memory usage
        if let Some(&memory_usage) = metrics.get("memory_usage_percentage") {
            if memory_usage > 85.0 {
                return Some(format!("High memory usage: {}%", memory_usage));
            }
        }

        // Check CPU usage
        if let Some(&cpu_usage) = metrics.get("cpu_usage_percentage") {
            if cpu_usage > 80.0 {
                return Some(format!("High CPU usage: {}%", cpu_usage));
            }
        }

        None
    }

    /// Rollback to a specific phase
    pub async fn rollback_to_phase(&self, target_phase: DeploymentPhase) -> Result<()> {
        let current_phase = {
            let config = self.config.read().unwrap();
            config.current_phase
        };

        info!("Rolling back from {} to {}", current_phase, target_phase);

        // Validate rollback path
        self.validate_rollback_path(current_phase, target_phase)?;

        // Execute rollback
        self.execute_rollback(current_phase, target_phase).await?;

        // Update configuration
        {
            let mut config = self.config.write().unwrap();
            config.current_phase = target_phase;
            config.target_phase = None;
        }

        info!("Rollback completed successfully to phase: {}", target_phase);
        Ok(())
    }

    /// Execute rollback between phases
    async fn execute_rollback(&self, from_phase: DeploymentPhase, to_phase: DeploymentPhase) -> Result<()> {
        // Step 1: Immediately reduce traffic to new implementation
        let target_percentage = to_phase.traffic_percentage() as f64;
        self.update_traffic_routing(target_percentage).await?;

        // Step 2: Update feature flags
        self.feature_flags.update_phase(to_phase)?;

        // Step 3: Wait for stabilization
        sleep(Duration::from_secs(60)).await;

        // Step 4: Verify rollback success
        if !self.health_checks.run_all_checks().await? {
            return Err(anyhow::anyhow!("Health checks failed after rollback"));
        }

        info!("Rollback executed successfully from {} to {}", from_phase, to_phase);
        Ok(())
    }

    /// Validate deployment path
    fn validate_deployment_path(&self, current: DeploymentPhase, target: DeploymentPhase) -> Result<()> {
        if current == target {
            return Err(anyhow::anyhow!("Current phase is already the target phase"));
        }

        // Check if target is reachable from current
        let mut phase = current;
        while let Some(next) = phase.next_phase() {
            if next == target {
                return Ok(());
            }
            phase = next;
        }

        Err(anyhow::anyhow!(
            "Target phase {} is not reachable from current phase {}",
            target, current
        ))
    }

    /// Validate rollback path
    fn validate_rollback_path(&self, current: DeploymentPhase, target: DeploymentPhase) -> Result<()> {
        if current == target {
            return Err(anyhow::anyhow!("Current phase is already the target phase"));
        }

        // Check if target is reachable by going backwards
        let mut phase = current;
        while let Some(prev) = phase.previous_phase() {
            if prev == target {
                return Ok(());
            }
            phase = prev;
        }

        Err(anyhow::anyhow!(
            "Target phase {} is not reachable by rollback from current phase {}",
            target, current
        ))
    }

    /// Create a validation checkpoint
    async fn create_checkpoint(&self, phase: DeploymentPhase) -> ValidationCheckpoint {
        ValidationCheckpoint {
            phase,
            timestamp: Utc::now(),
            status: CheckpointStatus::Pending,
            metrics: HashMap::new(),
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }

    /// Add a checkpoint to the list
    async fn add_checkpoint(&self, checkpoint: ValidationCheckpoint) {
        let mut checkpoints = self.checkpoints.write().unwrap();
        checkpoints.push(checkpoint);
    }

    /// Update checkpoint status
    async fn update_checkpoint_status(&self, phase: DeploymentPhase, status: CheckpointStatus) {
        let mut checkpoints = self.checkpoints.write().unwrap();
        if let Some(checkpoint) = checkpoints.iter_mut().rev().find(|c| c.phase == phase) {
            checkpoint.status = status;
        }
    }

    /// Get all checkpoints
    pub fn get_checkpoints(&self) -> Vec<ValidationCheckpoint> {
        self.checkpoints.read().unwrap().clone()
    }

    /// Get current deployment status
    pub fn get_deployment_status(&self) -> DeploymentStatus {
        let config = self.config.read().unwrap();
        let checkpoints = self.checkpoints.read().unwrap();
        
        DeploymentStatus {
            current_phase: config.current_phase,
            target_phase: config.target_phase,
            traffic_routing: config.traffic_routing.clone(),
            recent_checkpoints: checkpoints.iter().rev().take(5).cloned().collect(),
            feature_flags_enabled: self.feature_flags.get_enabled_flags().len(),
            health_status: "healthy".to_string(), // This would be determined by health checks
        }
    }

    // Validation checkpoint implementations
    async fn validate_basic_functionality(&self) -> Result<()> {
        debug!("Validating basic functionality");
        // Implementation would test basic system operations
        Ok(())
    }

    async fn validate_configuration_loading(&self) -> Result<()> {
        debug!("Validating configuration loading");
        // Implementation would test configuration loading and validation
        Ok(())
    }

    async fn validate_scoring_engine(&self) -> Result<()> {
        debug!("Validating scoring engine functionality");
        // Implementation would test scoring engine with sample data
        Ok(())
    }

    async fn validate_weight_application(&self) -> Result<()> {
        debug!("Validating weight application");
        // Implementation would verify weights are properly applied
        Ok(())
    }

    async fn validate_neutral_score_fallbacks(&self) -> Result<()> {
        debug!("Validating neutral score fallbacks");
        // Implementation would test fallback behavior
        Ok(())
    }

    async fn validate_geometric_score_completeness(&self) -> Result<()> {
        debug!("Validating geometric score completeness");
        // Implementation would verify all geometric components are used
        Ok(())
    }

    async fn validate_hurst_exponent(&self) -> Result<()> {
        debug!("Validating Hurst exponent calculation");
        // Implementation would test Hurst exponent with known data
        Ok(())
    }

    async fn validate_market_rhythm_stability(&self) -> Result<()> {
        debug!("Validating market rhythm stability");
        // Implementation would test stability calculation
        Ok(())
    }

    async fn validate_vesica_piscis_negative(&self) -> Result<()> {
        debug!("Validating vesica piscis negative handling");
        // Implementation would test negative price deviation handling
        Ok(())
    }

    async fn validate_temporal_harmonics(&self) -> Result<()> {
        debug!("Validating temporal harmonics integration");
        // Implementation would test temporal analysis integration
        Ok(())
    }

    async fn validate_liquidity_centroid(&self) -> Result<()> {
        debug!("Validating liquidity centroid calculation");
        // Implementation would test centroid calculation
        Ok(())
    }

    async fn validate_network_state_integration(&self) -> Result<()> {
        debug!("Validating network state integration");
        // Implementation would test network state usage
        Ok(())
    }

    async fn validate_asset_centrality(&self) -> Result<()> {
        debug!("Validating asset centrality initialization");
        // Implementation would test centrality scores
        Ok(())
    }

    async fn validate_token_registry(&self) -> Result<()> {
        debug!("Validating token registry integration");
        // Implementation would test token address resolution
        Ok(())
    }

    async fn validate_vesica_piscis_integration(&self) -> Result<()> {
        debug!("Validating vesica piscis geometric integration");
        // Implementation would test integration with geometric scoring
        Ok(())
    }

    async fn validate_network_coherence(&self) -> Result<()> {
        debug!("Validating network coherence calculation");
        // Implementation would test coherence calculation
        Ok(())
    }

    async fn validate_censorship_detection(&self) -> Result<()> {
        debug!("Validating censorship detection");
        // Implementation would test censorship detection logic
        Ok(())
    }

    async fn validate_sequencer_health(&self) -> Result<()> {
        debug!("Validating sequencer health monitoring");
        // Implementation would test health monitoring
        Ok(())
    }

    async fn validate_data_quality(&self) -> Result<()> {
        debug!("Validating comprehensive data quality");
        // Implementation would test data validation
        Ok(())
    }

    async fn validate_enhanced_configuration(&self) -> Result<()> {
        debug!("Validating enhanced configuration validation");
        // Implementation would test configuration validation
        Ok(())
    }

    async fn validate_graceful_degradation(&self) -> Result<()> {
        debug!("Validating graceful degradation patterns");
        // Implementation would test degradation behavior
        Ok(())
    }

    async fn validate_performance_monitoring(&self) -> Result<()> {
        debug!("Validating performance monitoring");
        // Implementation would test monitoring systems
        Ok(())
    }

    async fn validate_error_propagation(&self) -> Result<()> {
        debug!("Validating enhanced error propagation");
        // Implementation would test error handling
        Ok(())
    }

    async fn validate_full_system(&self) -> Result<()> {
        debug!("Validating full system integration");
        // Implementation would test complete system
        Ok(())
    }

    async fn validate_performance_under_load(&self) -> Result<()> {
        debug!("Validating performance under load");
        // Implementation would test system under load
        Ok(())
    }

    async fn validate_comprehensive_error_handling(&self) -> Result<()> {
        debug!("Validating comprehensive error handling");
        // Implementation would test error handling
        Ok(())
    }

    async fn validate_monitoring_alerting(&self) -> Result<()> {
        debug!("Validating monitoring and alerting");
        // Implementation would test monitoring and alerting
        Ok(())
    }
}

/// Deployment status information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentStatus {
    pub current_phase: DeploymentPhase,
    pub target_phase: Option<DeploymentPhase>,
    pub traffic_routing: TrafficRouting,
    pub recent_checkpoints: Vec<ValidationCheckpoint>,
    pub feature_flags_enabled: usize,
    pub health_status: String,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::deployment::health_checks::HealthCheckManager;
    use crate::deployment::monitoring::DeploymentMonitor;

    #[tokio::test]
    async fn test_traffic_steps_calculation() {
        let config = DeploymentConfig::default();
        let feature_flags = Arc::new(FeatureFlagManager::new(DeploymentPhase::Development));
        let health_checks = Arc::new(HealthCheckManager::new());
        let monitor = Arc::new(DeploymentMonitor::new());
        
        let manager = PhasedDeploymentManager::new(config, feature_flags, health_checks, monitor);
        
        let steps = manager.calculate_traffic_steps(0.0, 15.0);
        assert!(!steps.is_empty());
        assert_eq!(steps.last(), Some(&15.0));
    }

    #[test]
    fn test_deployment_path_validation() {
        let config = DeploymentConfig::default();
        let feature_flags = Arc::new(FeatureFlagManager::new(DeploymentPhase::Development));
        let health_checks = Arc::new(HealthCheckManager::new());
        let monitor = Arc::new(DeploymentMonitor::new());
        
        let manager = PhasedDeploymentManager::new(config, feature_flags, health_checks, monitor);
        
        // Valid path
        assert!(manager.validate_deployment_path(
            DeploymentPhase::Development,
            DeploymentPhase::CoreScoring
        ).is_ok());
        
        // Invalid path (same phase)
        assert!(manager.validate_deployment_path(
            DeploymentPhase::Development,
            DeploymentPhase::Development
        ).is_err());
    }

    #[test]
    fn test_rollback_path_validation() {
        let config = DeploymentConfig::default();
        let feature_flags = Arc::new(FeatureFlagManager::new(DeploymentPhase::CoreScoring));
        let health_checks = Arc::new(HealthCheckManager::new());
        let monitor = Arc::new(DeploymentMonitor::new());
        
        let manager = PhasedDeploymentManager::new(config, feature_flags, health_checks, monitor);
        
        // Valid rollback path
        assert!(manager.validate_rollback_path(
            DeploymentPhase::CoreScoring,
            DeploymentPhase::Development
        ).is_ok());
        
        // Invalid rollback path (forward)
        assert!(manager.validate_rollback_path(
            DeploymentPhase::Development,
            DeploymentPhase::CoreScoring
        ).is_err());
    }
}