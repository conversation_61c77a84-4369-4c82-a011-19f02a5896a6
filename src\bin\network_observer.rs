use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};
use futures_util::{StreamExt, SinkExt};
use url::Url;
use serde_json::json;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use std::time::{SystemTime, UNIX_EPOCH};
use anyhow::Result;

use basilisk_bot::shared_types::{
    BlockPropagationSample, BlockPropagationReport, BlockCoherenceReport, 
    ChainStability, CongestionLevel, GasStrategy, NetworkSeismologyReport, NatsTopics
};
use async_nats::Client;
use basilisk_bot::config::{Config, Settings};

#[tokio::main]
async fn main() -> Result<()> {
    println!("Network Observer - Enhanced Seismology Analysis");
    
    // Load RPC endpoints from config
    let new_config = Config::load()?;
    let config = Settings::from(new_config);
    
    // Determine WebSocket endpoints based on configuration
    let rpc_endpoints = get_websocket_endpoints(&config)?;
    
    println!("Network Observer starting with {} endpoints", rpc_endpoints.len());
    for endpoint in &rpc_endpoints {
        println!("  - {}", endpoint);
    }

    let nats_client = async_nats::connect("nats://localhost:4222").await?;
    let nats_client_arc = Arc::new(nats_client);

    let block_samples: Arc<Mutex<HashMap<u64, BlockPropagationSample>>> = Arc::new(Mutex::new(HashMap::new()));
    
    // Block coherence tracking: block_number -> (block_hash, parent_hash)
    let block_coherence: Arc<Mutex<HashMap<u64, (String, String)>>> = Arc::new(Mutex::new(HashMap::new()));
    
    // Reorg detection window (last 100 blocks)
    let reorg_window: Arc<Mutex<Vec<u64>>> = Arc::new(Mutex::new(Vec::new()));

    for endpoint in rpc_endpoints {
        let nats_client_clone = nats_client_arc.clone();
        let block_samples_clone = block_samples.clone();
        let block_coherence_clone = block_coherence.clone();
        let reorg_window_clone = reorg_window.clone();
        let endpoint_clone = endpoint.clone();
        tokio::spawn(async move {
            loop {
                let result = connect_and_listen(
                    &endpoint_clone, 
                    nats_client_clone.clone(), 
                    block_samples_clone.clone(),
                    block_coherence_clone.clone(),
                    reorg_window_clone.clone()
                ).await;
                match result {
                    Ok(_) => {
                        println!("Connection to {} ended normally", endpoint_clone);
                        break;
                    },
                    Err(e) => {
                        eprintln!("Error connecting to {}: {}", endpoint_clone, e);
                        eprintln!("Retrying connection to {} in 5 seconds...", endpoint_clone);
                        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    }
                }
            }
        });
    }

    // Keep the main thread alive
    tokio::signal::ctrl_c().await?;
    println!("Shutting down network observer.");

    Ok(())
}

async fn connect_and_listen(
    endpoint: &str,
    nats_client: Arc<Client>,
    block_samples: Arc<Mutex<HashMap<u64, BlockPropagationSample>>>,
    block_coherence: Arc<Mutex<HashMap<u64, (String, String)>>>,
    reorg_window: Arc<Mutex<Vec<u64>>>,
) -> Result<()> {
    println!("Attempting to connect to {}", endpoint);
    
    let url = Url::parse(endpoint)?;
    let (ws_stream, response) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    println!("Connected to {} (status: {})", endpoint, response.status());

    // Subscribe to newHeads
    let subscribe_message = json!({
        "jsonrpc": "2.0",
        "id": 1,
        "method": "eth_subscribe",
        "params": ["newHeads"]
    }).to_string();

    write.send(Message::Text(subscribe_message)).await?;

    while let Some(message) = read.next().await {
        match message? {
            Message::Text(text) => {
                let json_msg: serde_json::Value = serde_json::from_str(&text)?;
                if let Some(params) = json_msg.get("params") {
                    if let Some(result) = params.get("result") {
                        if let Some(block_number_hex) = result.get("number") {
                            let block_number_str = block_number_hex.as_str()
                                .ok_or_else(|| anyhow::anyhow!("Block number is not a valid string"))?;
                            let block_number = u64::from_str_radix(&block_number_str[2..], 16)?;
                            let timestamp_nanos = SystemTime::now()
                                .duration_since(UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_nanos() as u128;

                            // Extract block hash and parent hash for coherence analysis
                            let block_hash = result.get("hash")
                                .and_then(|h| h.as_str())
                                .unwrap_or("unknown")
                                .to_string();
                            let parent_hash = result.get("parentHash")
                                .and_then(|h| h.as_str())
                                .unwrap_or("unknown")
                                .to_string();

                            // Check for reorgs and update coherence tracking
                            check_for_reorg(
                                block_number,
                                &block_hash,
                                &parent_hash,
                                block_coherence.clone(),
                                reorg_window.clone(),
                                nats_client.clone()
                            ).await;

                            let mut samples = block_samples.lock().await;
                            let entry = samples.entry(block_number).or_insert_with(|| BlockPropagationSample {
                                block_number,
                                samples: Vec::new(),
                            });
                            entry.samples.push((endpoint.to_string(), timestamp_nanos));

                            // Spawn enhanced aggregation task if this is the first sample for the block
                            if entry.samples.len() == 1 {
                                spawn_enhanced_aggregation_task(
                                    block_number,
                                    block_hash,
                                    parent_hash,
                                    nats_client.clone(),
                                    block_samples.clone(),
                                    block_coherence.clone(),
                                    reorg_window.clone(),
                                );
                            }
                        }
                    }
                }
            },
            Message::Ping(p) => { write.send(Message::Pong(p)).await?; },
            _ => {},
        }
    }

    Ok(())
}

async fn check_for_reorg(
    block_number: u64,
    block_hash: &str,
    parent_hash: &str,
    block_coherence: Arc<Mutex<HashMap<u64, (String, String)>>>,
    reorg_window: Arc<Mutex<Vec<u64>>>,
    nats_client: Arc<Client>,
) -> bool {
    let mut coherence = block_coherence.lock().await;
    let mut reorg_detected = false;

    // Check if parent hash matches the previous block's hash
    if block_number > 0 {
        if let Some((prev_block_hash, _)) = coherence.get(&(block_number - 1)) {
            if prev_block_hash != parent_hash {
                reorg_detected = true;
                eprintln!("[REORG] CRITICAL: Reorg detected at block {}: parent {} != expected {}", 
                    block_number, parent_hash, prev_block_hash);

                // Publish critical reorg alert
                let alert = serde_json::json!({
                    "block_number": block_number,
                    "expected_parent": prev_block_hash,
                    "actual_parent": parent_hash,
                    "timestamp": SystemTime::now().duration_since(UNIX_EPOCH)
                        .unwrap_or_default().as_secs()
                });

                if let Err(e) = nats_client.publish(
                    NatsTopics::ALERTS_NETWORK_REORG, 
                    serde_json::to_vec(&alert)
                        .unwrap_or_else(|e| {
                            eprintln!("Failed to serialize reorg alert: {}", e);
                            Vec::new()
                        }).into()
                ).await {
                    eprintln!("Failed to publish reorg alert: {}", e);
                }

                // Add to reorg window
                let mut window = reorg_window.lock().await;
                window.push(block_number);
                // Keep only last 100 reorgs
                if window.len() > 100 {
                    window.remove(0);
                }
            }
        }
    }

    // Store this block's info for future coherence checks
    coherence.insert(block_number, (block_hash.to_string(), parent_hash.to_string()));
    
    // Clean up old entries (keep last 1000 blocks)
    if coherence.len() > 1000 {
        let min_block = block_number.saturating_sub(1000);
        coherence.retain(|&k, _| k >= min_block);
    }

    reorg_detected
}

fn spawn_enhanced_aggregation_task(
    block_number: u64,
    block_hash: String,
    parent_hash: String,
    nats_client: Arc<Client>,
    block_samples: Arc<Mutex<HashMap<u64, BlockPropagationSample>>>,
    block_coherence: Arc<Mutex<HashMap<u64, (String, String)>>>,
    reorg_window: Arc<Mutex<Vec<u64>>>,
) {
    tokio::spawn(async move {
        const AGGREGATION_TIMEOUT_MS: u64 = 1000; // 1 second
        const MIN_SAMPLES: usize = 3; // Reduced for faster analysis

        let start_time = SystemTime::now();

        loop {
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await; // Check every 100ms

            let mut samples_guard = block_samples.lock().await;
            if let Some(sample_entry) = samples_guard.get(&block_number) {
                let elapsed = start_time.elapsed().unwrap_or_default();

                if elapsed.as_millis() as u64 >= AGGREGATION_TIMEOUT_MS || sample_entry.samples.len() >= MIN_SAMPLES {
                    if let Some(sample_to_publish) = samples_guard.remove(&block_number) {
                        // Create enhanced propagation report with statistical analysis
                        let report = create_enhanced_propagation_report(
                            block_number,
                            block_hash.clone(),
                            parent_hash.clone(),
                            sample_to_publish.clone(),
                            block_coherence.clone(),
                            reorg_window.clone()
                        ).await;

                        // Publish both the original sample and enhanced report
                        let original_payload = serde_json::to_vec(&sample_to_publish)
                            .unwrap_or_else(|e| {
                                eprintln!("Failed to serialize block sample: {}", e);
                                Vec::new()
                            });
                        if let Err(e) = nats_client.publish(NatsTopics::DATA_NETWORK_PROPAGATION, original_payload.into()).await {
                            eprintln!("Error publishing block propagation sample: {}", e);
                        }

                        let enhanced_payload = serde_json::to_vec(&report)
                            .unwrap_or_else(|e| {
                                eprintln!("Failed to serialize seismology report: {}", e);
                                Vec::new()
                            });
                        if let Err(e) = nats_client.publish(NatsTopics::STATE_NETWORK_SEISMOLOGY, enhanced_payload.into()).await {
                            eprintln!("Error publishing network seismology report: {}", e);
                        }

                        println!("Published seismology report for block {} (jitter: {:.2}ms, coherence: {:.3})", 
                            block_number, report.propagation.geographic_jitter_ns / 1_000_000.0, report.coherence.coherence_score);

                        break; // Task complete
                    }
                }
            } else {
                // Block entry was removed, likely already published by another task or error
                break;
            }
        }
    });
}

async fn create_enhanced_propagation_report(
    block_number: u64,
    block_hash: String,
    parent_hash: String,
    sample: BlockPropagationSample,
    block_coherence: Arc<Mutex<HashMap<u64, (String, String)>>>,
    reorg_window: Arc<Mutex<Vec<u64>>>,
) -> NetworkSeismologyReport {
    let timestamps: Vec<u128> = sample.samples.iter().map(|(_, ts)| *ts).collect();
    
    // Calculate propagation statistics
    let first_seen = *timestamps.iter().min().unwrap_or(&0);
    let last_seen = *timestamps.iter().max().unwrap_or(&0);
    let propagation_spread = last_seen - first_seen;
    
    // Calculate geographic jitter (standard deviation)
    let mean = timestamps.iter().sum::<u128>() as f64 / timestamps.len() as f64;
    let variance = timestamps.iter()
        .map(|&ts| {
            let diff = ts as f64 - mean;
            diff * diff
        })
        .sum::<f64>() / timestamps.len() as f64;
    let geographic_jitter = variance.sqrt();
    
    // Create propagation report
    let propagation_report = BlockPropagationReport {
        block_number,
        block_hash: block_hash.clone(),
        parent_hash: parent_hash.clone(),
        first_seen_ns: first_seen,
        last_seen_ns: last_seen,
        propagation_spread_ns: propagation_spread,
        geographic_jitter_ns: geographic_jitter,
        sample_count: sample.samples.len(),
        endpoint_timings: sample.samples,
    };
    
    // Calculate coherence score
    let reorg_window_guard = reorg_window.lock().await;
    let recent_reorgs = reorg_window_guard.iter()
        .filter(|&&reorg_block| reorg_block >= block_number.saturating_sub(100))
        .count() as u32;
    
    let coherence_score = 1.0 - (recent_reorgs as f64 / 100.0);
    let chain_stability = match recent_reorgs {
        0 => ChainStability::Stable,
        1..=3 => ChainStability::Nervous,
        4..=5 => ChainStability::Unstable,
        _ => ChainStability::Chaotic,
    };
    
    let coherence_report = BlockCoherenceReport {
        current_block: block_number,
        coherence_score,
        reorgs_in_window: recent_reorgs,
        window_size: 100,
        last_reorg_block: reorg_window_guard.last().copied(),
        chain_stability,
        timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos() as u128,
    };
    
    // Determine congestion level and gas strategy
    let jitter_ms = geographic_jitter / 1_000_000.0;
    let (congestion_level, gas_strategy) = match jitter_ms {
        x if x < 50.0 => (CongestionLevel::Low, GasStrategy::Conservative),
        x if x < 150.0 => (CongestionLevel::Moderate, GasStrategy::Standard),
        x if x < 300.0 => (CongestionLevel::High, GasStrategy::Aggressive),
        _ => (CongestionLevel::Extreme, GasStrategy::Emergency),
    };
    
    NetworkSeismologyReport {
        block_number,
        propagation: propagation_report,
        tti_stats: None, // TODO: Integrate with mempool observer
        coherence: coherence_report,
        network_congestion_level: congestion_level,
        recommended_gas_strategy: gas_strategy,
        timestamp: SystemTime::now().duration_since(UNIX_EPOCH)
            .unwrap_or_default().as_nanos() as u128,
    }
}

fn get_websocket_endpoints(config: &basilisk_bot::config::Settings) -> Result<Vec<String>> {
    let mut endpoints = Vec::new();
    
    // Get the first enabled chain or default to Base
    let active_chain_id = config.get_enabled_chains().first().copied().unwrap_or(8453);
    
    // Determine endpoints based on active chain ID
    match active_chain_id {
        8453 => {
            // Base Mainnet - use multiple reliable endpoints
            endpoints.extend_from_slice(&[
                "wss://base.publicnode.com".to_string(),
                "wss://rpc.ankr.com/base/ws".to_string(),
                "wss://base.drpc.org".to_string(),
            ]);
        },
        84532 => {
            // Base Sepolia Testnet
            endpoints.extend_from_slice(&[
                "wss://sepolia.base.org".to_string(),
                "wss://base-sepolia-rpc.publicnode.com".to_string(),
            ]);
        },
        1337 | 31337 => {
            // Local development (Anvil/Hardhat)
            endpoints.push("ws://127.0.0.1:8545".to_string());
        },
        _ => {
            // Fallback to first chain's RPC URL or error
            if let Some(chain_config) = config.chains.get(&active_chain_id) {
                let ws_url = chain_config.rpc_url.replace("https://", "wss://").replace("http://", "ws://");
                endpoints.push(ws_url);
            } else {
                return Err(anyhow::anyhow!("No WebSocket endpoints configured for chain ID {}", active_chain_id));
            }
        }
    }
    
    Ok(endpoints)
}