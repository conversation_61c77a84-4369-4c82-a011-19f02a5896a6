// src/validation/contract_integration_validator_cli.rs

//! CLI interface for contract integration validation

use crate::validation::{
    ContractIntegrationValidator, ContractIntegrationConfig, ContractAddresses,
    ValidationFramework, ValidationConfig
};
use clap::{Args, Subcommand};
use ethers::types::Address;
use std::time::Duration;
use tracing::{error, info};

/// CLI arguments for contract integration validation
#[derive(Debug, Args)]
pub struct ContractIntegrationArgs {
    #[command(subcommand)]
    pub command: ContractIntegrationCommand,
}

/// Contract integration validation commands
#[derive(Debug, Subcommand)]
pub enum ContractIntegrationCommand {
    /// Validate StargateCompassV1 contract integration
    StargateCompass {
        /// Custom compass contract address
        #[arg(long)]
        compass_address: Option<Address>,
        /// RPC URL to fork from
        #[arg(long, default_value = "https://mainnet.base.org")]
        fork_rpc_url: String,
        /// Chain ID
        #[arg(long, default_value = "8453")]
        chain_id: u64,
    },
    /// Validate Aave V3 flash loan integration
    AaveFlashLoan {
        /// Custom Aave pool address
        #[arg(long)]
        aave_pool_address: Option<Address>,
        /// RPC URL to fork from
        #[arg(long, default_value = "https://mainnet.base.org")]
        fork_rpc_url: String,
        /// Chain ID
        #[arg(long, default_value = "8453")]
        chain_id: u64,
    },
    /// Validate DEX contract integrations
    DexIntegrations {
        /// Custom Uniswap V3 router address
        #[arg(long)]
        uniswap_router: Option<Address>,
        /// Custom Aerodrome router address
        #[arg(long)]
        aerodrome_router: Option<Address>,
        /// Custom SushiSwap router address
        #[arg(long)]
        sushiswap_router: Option<Address>,
        /// RPC URL to fork from
        #[arg(long, default_value = "https://mainnet.base.org")]
        fork_rpc_url: String,
        /// Chain ID
        #[arg(long, default_value = "8453")]
        chain_id: u64,
    },
    /// Validate contract addresses and ABI consistency
    ContractAddresses {
        /// RPC URL to fork from
        #[arg(long, default_value = "https://mainnet.base.org")]
        fork_rpc_url: String,
        /// Chain ID
        #[arg(long, default_value = "8453")]
        chain_id: u64,
    },
    /// Validate transaction simulation accuracy
    TransactionSimulation {
        /// RPC URL to fork from
        #[arg(long, default_value = "https://mainnet.base.org")]
        fork_rpc_url: String,
        /// Chain ID
        #[arg(long, default_value = "8453")]
        chain_id: u64,
    },
    /// Run comprehensive contract integration validation
    All {
        /// RPC URL to fork from
        #[arg(long, default_value = "https://mainnet.base.org")]
        fork_rpc_url: String,
        /// Chain ID
        #[arg(long, default_value = "8453")]
        chain_id: u64,
        /// Anvil port to use
        #[arg(long, default_value = "8545")]
        anvil_port: u16,
    },
}

/// Handle contract integration validation commands
pub async fn handle_contract_integration_command(args: ContractIntegrationArgs) -> crate::error::Result<()> {
    match args.command {
        ContractIntegrationCommand::StargateCompass { 
            compass_address, 
            fork_rpc_url, 
            chain_id 
        } => {
            info!("Running StargateCompass contract integration validation");
            
            let mut config = ContractIntegrationConfig {
                fork_rpc_url,
                chain_id,
                ..Default::default()
            };
            
            if let Some(address) = compass_address {
                config.contract_addresses.stargate_compass = address;
            }
            
            let mut validator = ContractIntegrationValidator::new(config);
            validator.start_anvil().await?;
            
            let result = validator.validate_stargate_compass_integration().await?;
            
        println!("StargateCompass Integration Validation Result:");
        println!("Status: {}", result.status);
        println!("Execution Time: {}ms", result.execution_time.as_millis());
            
            let metrics = &result.metrics;
        println!("Metrics:");
        println!("  - Contracts Validated: {}", metrics.total_contracts_validated);
        println!("  - Average Latency: {:.2}ms", metrics.average_interaction_latency_ms);
            for (address, success_rate) in &metrics.contract_call_success_rate {
            println!("  - {:?} Success Rate: {:.1}%", address, success_rate * 100.0);
            }
            
            if !result.errors.is_empty() {
            println!("Errors:");
                for error in &result.errors {
                println!("  - {}: {}", error.code, error.message);
                }
            }
            
            if !result.warnings.is_empty() {
            println!("Warnings:");
                for warning in &result.warnings {
                println!("  - {}: {}", warning.code, warning.message);
                }
            }
            
            validator.stop_anvil().await?;
        }
        
        ContractIntegrationCommand::AaveFlashLoan { 
            aave_pool_address, 
            fork_rpc_url, 
            chain_id 
        } => {
            info!("Running Aave V3 flash loan integration validation");
            
            let mut config = ContractIntegrationConfig {
                fork_rpc_url,
                chain_id,
                ..Default::default()
            };
            
            if let Some(address) = aave_pool_address {
                config.contract_addresses.aave_pool = address;
            }
            
            let mut validator = ContractIntegrationValidator::new(config);
            validator.start_anvil().await?;
            
            let result = validator.validate_aave_integration().await?;
            
        println!("Aave V3 Flash Loan Integration Validation Result:");
        println!("Status: {}", result.status);
        println!("Execution Time: {}ms", result.execution_time.as_millis());
            
            let metrics = &result.metrics;
        println!("Metrics:");
        println!("  - Contracts Validated: {}", metrics.total_contracts_validated);
        println!("  - Average Latency: {:.2}ms", metrics.average_interaction_latency_ms);
            for (address, success_rate) in &metrics.contract_call_success_rate {
            println!("  - {:?} Success Rate: {:.1}%", address, success_rate * 100.0);
            }
            
            if !result.errors.is_empty() {
            println!("Errors:");
                for error in &result.errors {
                println!("  - {}: {}", error.code, error.message);
                }
            }
            
            if !result.warnings.is_empty() {
            println!("Warnings:");
                for warning in &result.warnings {
                println!("  - {}: {}", warning.code, warning.message);
                }
            }
            
            validator.stop_anvil().await?;
        }
        
        ContractIntegrationCommand::DexIntegrations { 
            uniswap_router,
            aerodrome_router,
            sushiswap_router,
            fork_rpc_url, 
            chain_id 
        } => {
            info!("Running DEX contract integrations validation");
            
            let mut config = ContractIntegrationConfig {
                fork_rpc_url,
                chain_id,
                ..Default::default()
            };
            
            if let Some(address) = uniswap_router {
                config.contract_addresses.uniswap_v3_router = address;
            }
            if let Some(address) = aerodrome_router {
                config.contract_addresses.aerodrome_router = address;
            }
            if let Some(address) = sushiswap_router {
                config.contract_addresses.sushiswap_router = address;
            }
            
            let mut validator = ContractIntegrationValidator::new(config);
            validator.start_anvil().await?;
            
            let result = validator.validate_dex_integrations().await?;
            
        println!("DEX Contract Integrations Validation Result:");
        println!("Status: {}", result.status);
        println!("Execution Time: {}ms", result.execution_time.as_millis());
            
            let metrics = &result.metrics;
        println!("Metrics:");
        println!("  - Contracts Validated: {}", metrics.total_contracts_validated);
        println!("  - Average Latency: {:.2}ms", metrics.average_interaction_latency_ms);
            for (address, success_rate) in &metrics.contract_call_success_rate {
            println!("  - {:?} Success Rate: {:.1}%", address, success_rate * 100.0);
            }
            
            if !result.errors.is_empty() {
            println!("Errors:");
                for error in &result.errors {
                println!("  - {}: {}", error.code, error.message);
                }
            }
            
            if !result.warnings.is_empty() {
            println!("Warnings:");
                for warning in &result.warnings {
                println!("  - {}: {}", warning.code, warning.message);
                }
            }
            
            validator.stop_anvil().await?;
        }
        
        ContractIntegrationCommand::ContractAddresses { 
            fork_rpc_url, 
            chain_id 
        } => {
            info!("Running contract addresses validation");
            
            let config = ContractIntegrationConfig {
                fork_rpc_url,
                chain_id,
                ..Default::default()
            };
            
            let mut validator = ContractIntegrationValidator::new(config);
            validator.start_anvil().await?;
            
            let result = validator.validate_contract_addresses().await?;
            
        println!("Contract Addresses Validation Result:");
        println!("Status: {}", result.status);
        println!("Execution Time: {}ms", result.execution_time.as_millis());
            
            let metrics = &result.metrics;
        println!("Metrics:");
        println!("  - Contracts Validated: {}", metrics.total_contracts_validated);
        println!("  - State Consistency: {:.1}%", metrics.contract_state_consistency * 100.0);
        println!("  - Average Latency: {:.2}ms", metrics.average_interaction_latency_ms);
            
        println!("  - ABI Consistency Results:");
            for (address, consistent) in &metrics.abi_consistency_results {
            println!("    - {:?}: {}", address, if *consistent { "✓" } else { "✗" });
            }
            
            if !result.errors.is_empty() {
            println!("Errors:");
                for error in &result.errors {
                println!("  - {}: {}", error.code, error.message);
                }
            }
            
            if !result.warnings.is_empty() {
            println!("Warnings:");
                for warning in &result.warnings {
                println!("  - {}: {}", warning.code, warning.message);
                }
            }
            
            validator.stop_anvil().await?;
        }
        
        ContractIntegrationCommand::TransactionSimulation { 
            fork_rpc_url, 
            chain_id 
        } => {
            info!("Running transaction simulation validation");
            
            let config = ContractIntegrationConfig {
                fork_rpc_url,
                chain_id,
                ..Default::default()
            };
            
            let mut validator = ContractIntegrationValidator::new(config);
            validator.start_anvil().await?;
            
            let result = validator.validate_transaction_simulation().await?;
            
        println!("Transaction Simulation Validation Result:");
        println!("Status: {}", result.status);
        println!("Execution Time: {}ms", result.execution_time.as_millis());
            
            let metrics = &result.metrics;
        println!("Metrics:");
        println!("  - Transactions Simulated: {}", metrics.total_transactions_simulated);
        println!("  - Simulation Accuracy: {:.1}%", metrics.transaction_simulation_accuracy * 100.0);
        println!("  - Average Latency: {:.2}ms", metrics.average_interaction_latency_ms);
            
            if !result.errors.is_empty() {
            println!("Errors:");
                for error in &result.errors {
                println!("  - {}: {}", error.code, error.message);
                }
            }
            
            if !result.warnings.is_empty() {
            println!("Warnings:");
                for warning in &result.warnings {
                println!("  - {}: {}", warning.code, warning.message);
                }
            }
            
            validator.stop_anvil().await?;
        }
        
        ContractIntegrationCommand::All { 
            fork_rpc_url, 
            chain_id,
            anvil_port
        } => {
            info!("Running comprehensive contract integration validation");
            
            let config = ContractIntegrationConfig {
                fork_rpc_url,
                chain_id,
                anvil_port,
                ..Default::default()
            };
            
            let mut validator = ContractIntegrationValidator::new(config);
            validator.start_anvil().await?;
            
            let result = validator.validate_all_integrations().await?;
            
        println!("Comprehensive Contract Integration Validation Result:");
        println!("Status: {}", result.status);
        println!("Execution Time: {}ms", result.execution_time.as_millis());
            
            let metrics = &result.metrics;
        println!("Metrics:");
        println!("  - Total Contracts Validated: {}", metrics.total_contracts_validated);
        println!("  - Total Transactions Simulated: {}", metrics.total_transactions_simulated);
        println!("  - Gas Estimation Accuracy: {:.1}%", metrics.gas_estimation_accuracy * 100.0);
        println!("  - Transaction Simulation Accuracy: {:.1}%", metrics.transaction_simulation_accuracy * 100.0);
        println!("  - Contract State Consistency: {:.1}%", metrics.contract_state_consistency * 100.0);
        println!("  - Error Handling Effectiveness: {:.1}%", metrics.error_handling_effectiveness * 100.0);
        println!("  - Average Interaction Latency: {:.2}ms", metrics.average_interaction_latency_ms);
            
        println!("  - Contract Success Rates:");
            for (address, success_rate) in &metrics.contract_call_success_rate {
            println!("    - {:?}: {:.1}%", address, success_rate * 100.0);
            }
            
        println!("  - ABI Consistency Results:");
            for (address, consistent) in &metrics.abi_consistency_results {
            println!("    - {:?}: {}", address, if *consistent { "✓" } else { "✗" });
            }
            
            if !result.errors.is_empty() {
            println!("Errors:");
                for error in &result.errors {
                println!("  - {}: {}", error.code, error.message);
                }
            }
            
            if !result.warnings.is_empty() {
            println!("Warnings:");
                for warning in &result.warnings {
                println!("  - {}: {}", warning.code, warning.message);
                }
            }
            
            validator.stop_anvil().await?;
        }
    }
    Ok(())
}

/// Run contract integration validation using the validation framework
pub async fn run_contract_integration_validation_suite() -> crate::error::Result<()> {
    info!("Running contract integration validation suite");
    
    let validation_config = ValidationConfig::default();
    let framework = ValidationFramework::new(validation_config)?;
    
    let integration_config = ContractIntegrationConfig::default();
    let mut validator = ContractIntegrationValidator::new(integration_config);
    
    // Start Anvil
    validator.start_anvil().await?;
    
    // Run comprehensive validation through the framework
    let result = framework.execute_validation(
        "contract_integration_comprehensive",
        || async {
            validator.validate_all_integrations().await
        }
    ).await?;
    
    // Stop Anvil
    validator.stop_anvil().await?;
    
    println!("Contract Integration Validation Suite Result:");
    println!("Status: {}", result.status);
    println!("Execution Time: {}ms", result.execution_time.as_millis());
    
    if let Some(validation_result) = result.metrics {
        let metrics = validation_result.metrics;
        println!("Comprehensive Metrics:");
        println!("  - Total Contracts Validated: {}", metrics.total_contracts_validated);
        println!("  - Total Transactions Simulated: {}", metrics.total_transactions_simulated);
        println!("  - Overall Effectiveness: {:.1}%", metrics.error_handling_effectiveness * 100.0);
    }
    
    if !result.errors.is_empty() {
        println!("Framework Errors:");
        for error in &result.errors {
            println!("  - {}: {}", error.code, error.message);
        }
    }
    
    Ok(())
}
