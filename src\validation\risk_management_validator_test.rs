// src/validation/risk_management_validator_test.rs

//! Comprehensive tests for Risk Management Validator
//! 
//! This module contains integration tests to verify the risk management
//! validation functionality works correctly.

#[cfg(test)]
mod tests {
    use super::*;
    use crate::validation::{
        ValidationConfig, ValidationStatus,
        risk_management_validator::{
            RiskManagementValidator, RiskTestDataGenerator, RiskTestScenario,
            ExpectedRiskBehavior, KellyFractionTest
        }
    };
    use crate::shared_types::MarketRegime;
    use rust_decimal_macros::dec;
    use tokio;

    /// Test basic validator creation and configuration
    #[tokio::test]
    async fn test_validator_creation() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        
        // Validator should be created successfully
        assert!(true); // Placeholder - in real test we'd verify internal state
    }

    /// Test Kelly Criterion position sizing validation
    #[tokio::test]
    async fn test_kelly_criterion_validation() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        let test_data_generator = RiskTestDataGenerator::new(42);
        
        // Generate test scenarios
        let scenarios = test_data_generator.generate_risk_test_scenarios();
        
        // Run Kelly Criterion validation
        let result = validator.validate_kelly_criterion_position_sizing(&scenarios).await;
        
        // Verify result structure
        assert!(result.is_ok());
        let validation_result = result.unwrap();
        
        // Check basic result properties
        assert_eq!(validation_result.test_name, "Kelly Criterion Position Sizing Validation");
        assert!(validation_result.metrics.is_some());
        
        // Check metrics
        let metrics = validation_result.metrics.unwrap();
        assert!(metrics.calculations_tested > 0);
        assert!(metrics.average_accuracy >= 0.0);
        assert!(metrics.average_accuracy <= 100.0);
        assert!(!metrics.kelly_fraction_capping_results.is_empty());
    }

    /// Test daily loss limit validation
    #[tokio::test]
    async fn test_daily_loss_limit_validation() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        let test_data_generator = RiskTestDataGenerator::new(42);
        
        // Generate test scenarios
        let scenarios = test_data_generator.generate_risk_test_scenarios();
        
        // Run daily loss limit validation
        let result = validator.validate_daily_loss_limits(&scenarios).await;
        
        // Verify result structure
        assert!(result.is_ok());
        let validation_result = result.unwrap();
        
        // Check basic result properties
        assert_eq!(validation_result.test_name, "Daily Loss Limit Enforcement Validation");
        assert!(validation_result.metrics.is_some());
        
        // Check metrics
        let metrics = validation_result.metrics.unwrap();
        assert!(metrics.scenarios_tested > 0);
        assert!(metrics.circuit_breaker_activation_rate >= 0.0);
        assert!(metrics.circuit_breaker_activation_rate <= 100.0);
    }

    /// Test comprehensive validation report generation
    #[tokio::test]
    async fn test_comprehensive_validation() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        let test_data_generator = RiskTestDataGenerator::new(42);
        
        // Generate test scenarios
        let scenarios = test_data_generator.generate_risk_test_scenarios();
        
        // Run comprehensive validation
        let result = validator.generate_comprehensive_validation_report(&scenarios).await;
        
        // Verify result structure
        assert!(result.is_ok());
        let validation_result = result.unwrap();
        
        // Check basic result properties
        assert_eq!(validation_result.test_name, "Comprehensive Risk Management Validation");
        assert!(validation_result.metrics.is_some());
        
        // Check comprehensive metrics
        let metrics = validation_result.metrics.unwrap();
        assert!(metrics.validation_summary.total_tests_executed > 0);
        assert!(metrics.validation_summary.overall_success_rate >= 0.0);
        assert!(metrics.validation_summary.overall_success_rate <= 100.0);
        
        // Verify all component metrics are present
        assert!(metrics.kelly_criterion_metrics.calculations_tested >= 0);
        assert!(metrics.daily_loss_limit_metrics.scenarios_tested >= 0);
        assert!(metrics.volatility_adjustment_metrics.volatility_scenarios_tested >= 0);
        assert!(metrics.consecutive_failure_metrics.failure_scenarios_tested >= 0);
        assert!(metrics.emergency_shutdown_metrics.emergency_scenarios_tested >= 0);
        assert!(metrics.circuit_breaker_metrics.scenarios_tested >= 0);
    }

    /// Test scenario generation
    #[tokio::test]
    async fn test_scenario_generation() {
        let test_data_generator = RiskTestDataGenerator::new(42);
        let scenarios = test_data_generator.generate_risk_test_scenarios();
        
        // Should generate multiple scenarios
        assert!(!scenarios.is_empty());
        assert!(scenarios.len() >= 5); // Should have at least 5 different scenarios
        
        // Verify scenario structure
        for scenario in &scenarios {
            assert!(!scenario.name.is_empty());
            assert!(scenario.portfolio_value > dec!(0.0));
            assert!(scenario.max_position_size > dec!(0.0));
            assert!(scenario.volatility_level >= dec!(0.0));
            
            // Verify expected behavior is defined
            let behavior = &scenario.expected_behavior;
            assert!(behavior.expected_position_multiplier >= dec!(0.0));
            assert!(behavior.expected_kelly_adjustment >= dec!(0.0));
        }
        
        // Should have scenarios with different market regimes
        let regimes: std::collections::HashSet<_> = scenarios.iter()
            .map(|s| s.market_regime.clone())
            .collect();
        assert!(regimes.len() > 1); // Should have multiple different regimes
        
        // Should have scenarios with different risk levels
        let has_normal_scenario = scenarios.iter()
            .any(|s| s.consecutive_failures == 0 && s.daily_pnl > dec!(0.0));
        let has_high_risk_scenario = scenarios.iter()
            .any(|s| s.consecutive_failures > 3 || s.daily_pnl < s.max_daily_loss);
        
        assert!(has_normal_scenario);
        assert!(has_high_risk_scenario);
    }

    /// Test Kelly Criterion calculation logic
    #[tokio::test]
    async fn test_kelly_calculation_logic() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        
        // Create a test scenario
        let scenario = RiskTestScenario {
            name: "test_kelly_calculation".to_string(),
            market_regime: MarketRegime::CalmOrderly,
            portfolio_value: dec!(10000.0),
            daily_pnl: dec!(100.0),
            max_daily_loss: dec!(-500.0),
            max_position_size: dec!(1000.0),
            consecutive_failures: 0,
            volatility_level: dec!(0.15),
            expected_behavior: ExpectedRiskBehavior {
                should_halt_trading: false,
                expected_position_multiplier: dec!(1.0),
                should_trigger_circuit_breaker: false,
                expected_kelly_adjustment: dec!(1.0),
                should_trigger_emergency_shutdown: false,
            },
        };
        
        // Test Kelly calculation (this would be a private method, so we test through public interface)
        let scenarios = vec![scenario];
        let result = validator.validate_kelly_criterion_position_sizing(&scenarios).await;
        
        assert!(result.is_ok());
        let validation_result = result.unwrap();
        let metrics = validation_result.metrics.unwrap();
        
        // Should have calculated at least one Kelly fraction
        assert!(!metrics.kelly_fraction_capping_results.is_empty());
        
        let kelly_test = &metrics.kelly_fraction_capping_results[0];
        assert_eq!(kelly_test.scenario_name, "test_kelly_calculation");
        assert!(kelly_test.win_probability > dec!(0.0));
        assert!(kelly_test.win_probability <= dec!(1.0));
        assert!(kelly_test.win_loss_ratio > dec!(0.0));
        assert!(kelly_test.portfolio_value == dec!(10000.0));
    }

    /// Test loss limit enforcement logic
    #[tokio::test]
    async fn test_loss_limit_enforcement() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        
        // Create scenarios with different loss levels
        let normal_scenario = RiskTestScenario {
            name: "normal_losses".to_string(),
            market_regime: MarketRegime::CalmOrderly,
            portfolio_value: dec!(10000.0),
            daily_pnl: dec!(-200.0), // Within limits
            max_daily_loss: dec!(-500.0),
            max_position_size: dec!(1000.0),
            consecutive_failures: 1,
            volatility_level: dec!(0.15),
            expected_behavior: ExpectedRiskBehavior {
                should_halt_trading: false,
                expected_position_multiplier: dec!(1.0),
                should_trigger_circuit_breaker: false,
                expected_kelly_adjustment: dec!(1.0),
                should_trigger_emergency_shutdown: false,
            },
        };
        
        let breach_scenario = RiskTestScenario {
            name: "loss_limit_breach".to_string(),
            market_regime: MarketRegime::CalmOrderly,
            portfolio_value: dec!(10000.0),
            daily_pnl: dec!(-600.0), // Exceeds limits
            max_daily_loss: dec!(-500.0),
            max_position_size: dec!(1000.0),
            consecutive_failures: 3,
            volatility_level: dec!(0.25),
            expected_behavior: ExpectedRiskBehavior {
                should_halt_trading: true,
                expected_position_multiplier: dec!(0.0),
                should_trigger_circuit_breaker: true,
                expected_kelly_adjustment: dec!(0.0),
                should_trigger_emergency_shutdown: false,
            },
        };
        
        let scenarios = vec![normal_scenario, breach_scenario];
        let result = validator.validate_daily_loss_limits(&scenarios).await;
        
        assert!(result.is_ok());
        let validation_result = result.unwrap();
        let metrics = validation_result.metrics.unwrap();
        
        // Should have tested both scenarios
        assert_eq!(metrics.scenarios_tested, 2);
        
        // Should have some enforcement rate
        assert!(metrics.circuit_breaker_activation_rate >= 0.0);
        assert!(metrics.circuit_breaker_activation_rate <= 100.0);
    }

    /// Test validation status determination
    #[tokio::test]
    async fn test_validation_status_logic() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        let test_data_generator = RiskTestDataGenerator::new(42);
        
        // Generate scenarios
        let scenarios = test_data_generator.generate_risk_test_scenarios();
        
        // Run validation and check status logic
        let kelly_result = validator.validate_kelly_criterion_position_sizing(&scenarios).await.unwrap();
        let loss_result = validator.validate_daily_loss_limits(&scenarios).await.unwrap();
        let comprehensive_result = validator.generate_comprehensive_validation_report(&scenarios).await.unwrap();
        
        // Status should be one of the valid enum values
        assert!(matches!(kelly_result.status, 
                        ValidationStatus::Passed | 
                        ValidationStatus::Warning | 
                        ValidationStatus::Failed));
        
        assert!(matches!(loss_result.status, 
                        ValidationStatus::Passed | 
                        ValidationStatus::Warning | 
                        ValidationStatus::Failed));
        
        assert!(matches!(comprehensive_result.status, 
                        ValidationStatus::Passed | 
                        ValidationStatus::Warning | 
                        ValidationStatus::Failed));
        
        // Comprehensive result should aggregate individual results
        if let Some(metrics) = comprehensive_result.metrics {
            let summary = metrics.validation_summary;
            
            // Should have reasonable success rate
            assert!(summary.overall_success_rate >= 0.0);
            assert!(summary.overall_success_rate <= 100.0);
            
            // Should have executed multiple tests
            assert!(summary.total_tests_executed >= 6); // At least 6 components
            
            // Performance metrics should be reasonable
            assert!(summary.performance_summary.average_risk_decision_time_ms >= 0.0);
            assert!(summary.performance_summary.max_response_time_ms >= summary.performance_summary.min_response_time_ms);
        }
    }

    /// Test error handling
    #[tokio::test]
    async fn test_error_handling() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        
        // Test with empty scenarios
        let empty_scenarios: Vec<RiskTestScenario> = vec![];
        let result = validator.validate_kelly_criterion_position_sizing(&empty_scenarios).await;
        
        // Should handle empty scenarios gracefully
        assert!(result.is_ok());
        let validation_result = result.unwrap();
        
        if let Some(metrics) = validation_result.metrics {
            assert_eq!(metrics.calculations_tested, 0);
            assert_eq!(metrics.calculations_passed, 0);
        }
    }

    /// Test performance characteristics
    #[tokio::test]
    async fn test_performance_characteristics() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        let test_data_generator = RiskTestDataGenerator::new(42);
        
        // Generate scenarios
        let scenarios = test_data_generator.generate_risk_test_scenarios();
        
        // Measure execution time
        let start = std::time::Instant::now();
        let result = validator.validate_kelly_criterion_position_sizing(&scenarios).await;
        let duration = start.elapsed();
        
        // Should complete reasonably quickly (less than 1 second for basic validation)
        assert!(duration.as_secs() < 1);
        
        // Result should be successful
        assert!(result.is_ok());
        let validation_result = result.unwrap();
        
        // Should have performance metrics
        if let Some(metrics) = validation_result.metrics {
            assert!(!metrics.calculation_latency_ms.is_empty());
            
            // Latency should be reasonable (less than 100ms per calculation)
            for latency in &metrics.calculation_latency_ms {
                assert!(*latency < 100);
            }
        }
    }

    /// Test regime-specific behavior
    #[tokio::test]
    async fn test_regime_specific_behavior() {
        let config = ValidationConfig::default();
        let validator = RiskManagementValidator::new(config);
        
        // Create scenarios for different market regimes
        let calm_scenario = RiskTestScenario {
            name: "calm_regime".to_string(),
            market_regime: MarketRegime::CalmOrderly,
            portfolio_value: dec!(10000.0),
            daily_pnl: dec!(100.0),
            max_daily_loss: dec!(-500.0),
            max_position_size: dec!(1000.0),
            consecutive_failures: 0,
            volatility_level: dec!(0.15),
            expected_behavior: ExpectedRiskBehavior {
                should_halt_trading: false,
                expected_position_multiplier: dec!(1.0),
                should_trigger_circuit_breaker: false,
                expected_kelly_adjustment: dec!(1.0),
                should_trigger_emergency_shutdown: false,
            },
        };
        
        let volatile_scenario = RiskTestScenario {
            name: "volatile_regime".to_string(),
            market_regime: MarketRegime::HighVolatilityCorrection,
            portfolio_value: dec!(10000.0),
            daily_pnl: dec!(-100.0),
            max_daily_loss: dec!(-500.0),
            max_position_size: dec!(1000.0),
            consecutive_failures: 1,
            volatility_level: dec!(0.45),
            expected_behavior: ExpectedRiskBehavior {
                should_halt_trading: false,
                expected_position_multiplier: dec!(0.5), // Reduced for high volatility
                should_trigger_circuit_breaker: false,
                expected_kelly_adjustment: dec!(0.5), // Reduced for high volatility
                should_trigger_emergency_shutdown: false,
            },
        };
        
        let scenarios = vec![calm_scenario, volatile_scenario];
        let result = validator.validate_kelly_criterion_position_sizing(&scenarios).await;
        
        assert!(result.is_ok());
        let validation_result = result.unwrap();
        let metrics = validation_result.metrics.unwrap();
        
        // Should have tested both scenarios
        assert_eq!(metrics.calculations_tested, 2);
        
        // Should have Kelly fraction results for both
        assert_eq!(metrics.kelly_fraction_capping_results.len(), 2);
        
        // Find results for each scenario
        let calm_result = metrics.kelly_fraction_capping_results.iter()
            .find(|r| r.scenario_name == "calm_regime");
        let volatile_result = metrics.kelly_fraction_capping_results.iter()
            .find(|r| r.scenario_name == "volatile_regime");
        
        assert!(calm_result.is_some());
        assert!(volatile_result.is_some());
        
        // In a high volatility regime, the Kelly fraction should typically be lower
        // (This is a simplified test - in reality the relationship depends on the specific parameters)
        let calm_kelly = calm_result.unwrap();
        let volatile_kelly = volatile_result.unwrap();
        
        // Both should have valid Kelly fractions
        assert!(calm_kelly.actual_kelly_fraction >= dec!(0.0));
        assert!(volatile_kelly.actual_kelly_fraction >= dec!(0.0));
    }
}