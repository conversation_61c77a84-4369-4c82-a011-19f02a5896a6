# Basilisk Gaze Expander - Implementation Plan

## Executive Summary

This document outlines a comprehensive implementation plan to expand the Zen Geometer's opportunity detection capabilities by enhancing the `GazeScanner` with multi-DEX support, aggregator integration (1inch, Paraswap), dynamic token discovery, and advanced arbitrage strategies. The plan is structured in phases to minimize risk while maximizing opportunity capture.

## Current State Analysis

### Existing Architecture
- **GazeScanner**: Basic DEX-DEX arbitrage scanner for Degen Chain
- **Token Support**: Limited to 3 hardcoded pairs (WETH/USDC, DEGEN/USDC, WETH/DEGEN)
- **DEX Support**: Only Uniswap V2-style routers
- **Opportunity Types**: Basic arbitrage only
- **Integration**: Well-integrated with Aetheric Resonance Engine (ARE)

### Current Limitations
1. **Narrow DEX Coverage**: Missing SushiSwap, Curve, Balancer, 1inch
2. **Static Token Discovery**: Hardcoded "worthy assets" instead of dynamic discovery
3. **Limited Arbitrage Types**: No triangular, flash loan, or cross-protocol arbitrage
4. **No Aggregator Integration**: Missing 1inch, Paraswap, 0x Protocol opportunities
5. **Single-Chain Focus**: Limited cross-chain opportunity detection

## Implementation Strategy

### Phase 1: Foundation Enhancement (2-3 weeks)
**Goal**: Expand DEX coverage and token pairs with minimal architectural changes

#### Epic 1.1: Multi-DEX Router Support
**Technical Overhead**: Low - extends existing router pattern
**Risk**: Low - additive changes only

**Tasks**:
1. **Create DEX Adapter Trait** (`src/strategies/scanners/dex_adapters/mod.rs`)
   ```rust
   pub trait DexAdapter {
       async fn get_quote(&self, token_in: Address, token_out: Address, amount_in: U256) -> Result<U256>;
       async fn get_reserves(&self, token_a: Address, token_b: Address) -> Result<(U256, U256)>;
       fn get_router_address(&self) -> Address;
       fn get_protocol_name(&self) -> &str;
   }
   ```

2. **Implement Uniswap V2 Adapter** (`src/strategies/scanners/dex_adapters/uniswap_v2.rs`)
   - Migrate existing `get_dex_price` logic
   - Add proper error handling and retry logic
   - Support for fee-on-transfer tokens

3. **Implement Uniswap V3 Adapter** (`src/strategies/scanners/dex_adapters/uniswap_v3.rs`)
   - Quoter V2 integration for accurate quotes
   - Multi-fee tier support (0.05%, 0.3%, 1%)
   - Concentrated liquidity awareness

4. **Implement SushiSwap Adapter** (`src/strategies/scanners/dex_adapters/sushiswap.rs`)
   - Identical to Uniswap V2 but different router address
   - Support for SushiSwap-specific features

5. **Update GazeScanner Configuration**
   ```toml
   # config/default.toml additions
   [chains.8453.dex.adapters]
   uniswap_v2 = { router = "0x4752ba5dbc23f44d87826276bf6fd6b1c372ad24", enabled = true }
   uniswap_v3 = { quoter = "0x61fFE014bA17989E743c5F6cB21bF9697530B21e", enabled = true }
   sushiswap = { router = "0x6BDED42c6DA8FBf0d2bA55B2fa120C5e0c8D7891", enabled = true }
   ```

#### Epic 1.2: Enhanced Token Discovery
**Technical Overhead**: Medium - requires subgraph integration
**Risk**: Low - read-only operations

**Tasks**:
1. **Create Token Discovery Service** (`src/data/token_discovery.rs`)
   ```rust
   pub struct TokenDiscoveryService {
       subgraph_clients: HashMap<String, SubgraphClient>,
       min_liquidity_usd: Decimal,
       min_volume_24h_usd: Decimal,
   }
   ```

2. **Subgraph Integration** (`src/data/subgraph_client.rs`)
   - Uniswap V3 subgraph for Base
   - SushiSwap subgraph integration
   - Top pairs by volume/liquidity queries

3. **Dynamic Worthy Assets** 
   - Replace hardcoded manifold with dynamic discovery
   - Operator approval workflow for new tokens
   - Automatic honeypot checking for new discoveries

4. **Token Metadata Cache** (`src/data/token_metadata.rs`)
   - Symbol, decimals, name caching
   - Price feed integration
   - Liquidity metrics storage

#### Epic 1.3: Enhanced Opportunity Types
**Technical Overhead**: Medium - extends existing enum
**Risk**: Low - backward compatible

**Tasks**:
1. **Extend OpportunityType Enum** (`src/shared_types.rs`)
   ```rust
   pub enum OpportunityType {
       Arbitrage,                    // Existing
       TriangularArbitrage {         // NEW
           path: Vec<Address>,
           expected_profit_usd: Decimal,
       },
       AggregatorArbitrage {         // NEW
           aggregator_name: String,
           dex_name: String,
           price_difference_bps: u32,
       },
       FlashLoanArbitrage {          // NEW
           loan_token: Address,
           loan_amount: U256,
           repay_amount: U256,
       },
   }
   ```

2. **Update GazeScanner Logic**
   - Multi-hop path detection (A->B->C->A)
   - Profitability calculation for complex paths
   - Gas cost estimation for multi-step transactions

### Phase 2: Aggregator Integration (3-4 weeks)
**Goal**: Add 1inch and Paraswap integration for aggregator arbitrage

#### Epic 2.1: 1inch Integration
**Technical Overhead**: High - external API integration
**Risk**: Medium - dependency on external service

**Tasks**:
1. **1inch Client Implementation** (`src/external/oneinch_client.rs`)
   ```rust
   pub struct OneInchClient {
       base_url: String,
       api_key: Option<String>,
       http_client: reqwest::Client,
   }
   
   impl OneInchClient {
       pub async fn get_quote(&self, params: QuoteParams) -> Result<QuoteResponse>;
       pub async fn get_swap_data(&self, params: SwapParams) -> Result<SwapResponse>;
   }
   ```

2. **Aggregator Scanner** (`src/strategies/scanners/aggregator.rs`)
   ```rust
   pub struct AggregatorScanner {
       oneinch_client: Arc<OneInchClient>,
       paraswap_client: Arc<ParaswapClient>,
       dex_adapters: HashMap<String, Arc<dyn DexAdapter>>,
       min_profit_threshold: Decimal,
   }
   ```

3. **Quote Comparison Engine**
   - Parallel quote fetching from multiple sources
   - Gas cost normalization across protocols
   - Slippage tolerance adjustment

4. **Rate Limiting & Caching**
   - API rate limit management
   - Quote caching with TTL
   - Fallback mechanisms for API failures

#### Epic 2.2: Paraswap Integration
**Technical Overhead**: Medium - similar to 1inch
**Risk**: Medium - additional external dependency

**Tasks**:
1. **Paraswap Client** (`src/external/paraswap_client.rs`)
2. **Multi-Aggregator Comparison**
3. **Cross-Aggregator Arbitrage Detection**

#### Epic 2.3: Advanced Arbitrage Detection
**Technical Overhead**: High - complex pathfinding algorithms
**Risk**: Medium - algorithmic complexity

**Tasks**:
1. **Triangular Arbitrage Engine** (`src/strategies/triangular_arbitrage.rs`)
   ```rust
   pub struct TriangularArbitrageEngine {
       graph: ArbitrageGraph,
       min_profit_threshold: Decimal,
       max_hops: usize,
   }
   ```

2. **Graph-Based Pathfinding**
   - Bellman-Ford algorithm for negative cycle detection
   - Dynamic programming for optimal path selection
   - Real-time graph updates from price feeds

3. **Multi-Protocol Arbitrage**
   - AMM vs Aggregator price differences
   - Cross-protocol flash loan arbitrage
   - Lending protocol liquidation arbitrage

### Phase 3: Advanced Features (4-5 weeks)
**Goal**: Sophisticated opportunity detection and execution optimization

#### Epic 3.1: MEV-Aware Opportunity Detection
**Technical Overhead**: Very High - requires mempool monitoring
**Risk**: High - competitive MEV environment

**Tasks**:
1. **Mempool Integration** (`src/data/mempool_monitor.rs`)
   - Real-time pending transaction monitoring
   - MEV opportunity prediction
   - Sandwich attack detection and avoidance

2. **Bundle Optimization** (`src/execution/bundle_optimizer.rs`)
   - Flashbots bundle construction
   - MEV-Share integration
   - Private mempool routing

#### Epic 3.2: Cross-Chain Arbitrage Enhancement
**Technical Overhead**: Very High - multi-chain coordination
**Risk**: High - bridge and timing risks

**Tasks**:
1. **Enhanced Stargate Integration**
   - Real-time bridge fee monitoring
   - Cross-chain timing optimization
   - Multi-chain opportunity correlation

2. **Layer 2 Arbitrage**
   - Base ↔ Arbitrum opportunities
   - Optimism integration
   - Polygon support

#### Epic 3.3: Machine Learning Integration
**Technical Overhead**: Very High - ML model integration
**Risk**: Medium - model accuracy dependency

**Tasks**:
1. **Opportunity Scoring ML Model**
   - Historical success rate analysis
   - Feature engineering from market data
   - Real-time model inference

2. **Market Regime Detection**
   - Enhanced fractal analysis
   - Volatility clustering detection
   - Regime-specific strategy selection

## Technical Architecture

### New Components

#### 1. DEX Adapter System
```rust
// src/strategies/scanners/dex_adapters/mod.rs
pub mod uniswap_v2;
pub mod uniswap_v3;
pub mod sushiswap;
pub mod curve;
pub mod balancer;

pub trait DexAdapter: Send + Sync {
    async fn get_quote(&self, params: QuoteParams) -> Result<QuoteResponse>;
    async fn get_pool_info(&self, token_a: Address, token_b: Address) -> Result<PoolInfo>;
    fn supports_token_pair(&self, token_a: Address, token_b: Address) -> bool;
    fn get_protocol_info(&self) -> ProtocolInfo;
}
```

#### 2. Aggregator Integration Layer
```rust
// src/external/mod.rs
pub mod oneinch_client;
pub mod paraswap_client;
pub mod zerox_client;

pub trait AggregatorClient: Send + Sync {
    async fn get_quote(&self, params: AggregatorQuoteParams) -> Result<AggregatorQuote>;
    async fn get_swap_calldata(&self, params: SwapParams) -> Result<SwapCalldata>;
    fn get_supported_tokens(&self, chain_id: u64) -> Vec<Address>;
}
```

#### 3. Enhanced Opportunity Detection
```rust
// src/strategies/scanners/enhanced_gaze.rs
pub struct EnhancedGazeScanner {
    dex_adapters: HashMap<String, Arc<dyn DexAdapter>>,
    aggregator_clients: HashMap<String, Arc<dyn AggregatorClient>>,
    token_discovery: Arc<TokenDiscoveryService>,
    arbitrage_engine: Arc<ArbitrageEngine>,
    config: Arc<Config>,
}

impl EnhancedGazeScanner {
    pub async fn scan_all_opportunities(&self) -> Result<Vec<Opportunity>>;
    pub async fn scan_dex_arbitrage(&self) -> Result<Vec<Opportunity>>;
    pub async fn scan_aggregator_arbitrage(&self) -> Result<Vec<Opportunity>>;
    pub async fn scan_triangular_arbitrage(&self) -> Result<Vec<Opportunity>>;
    pub async fn scan_flash_loan_arbitrage(&self) -> Result<Vec<Opportunity>>;
}
```

### Integration with Existing Systems

#### 1. Aetheric Resonance Engine (ARE) Integration
- **Temporal Harmonics**: Weight opportunities based on market cycle phase
- **Geometric Score**: Enhanced scoring for multi-hop arbitrage paths
- **Network Resonance**: Adjust for cross-chain timing and congestion

#### 2. Strategy Manager Integration
```rust
// Enhanced opportunity processing in StrategyManager
impl StrategyManager {
    async fn process_enhanced_opportunity(&self, opportunity: Opportunity) -> Result<AREDecision> {
        // Existing ARE analysis
        let temporal_score = self.calculate_temporal_score(&opportunity).await?;
        let geometric_score = self.calculate_geometric_score(&opportunity).await?;
        let network_score = self.calculate_network_score(&opportunity).await?;
        
        // New aggregator-specific scoring
        let aggregator_score = self.calculate_aggregator_score(&opportunity).await?;
        let complexity_penalty = self.calculate_complexity_penalty(&opportunity).await?;
        
        // Enhanced composite scoring
        let final_score = self.calculate_enhanced_aetheric_score(
            temporal_score,
            geometric_score,
            network_score,
            aggregator_score,
            complexity_penalty,
        ).await?;
        
        Ok(AREDecision::from_score(final_score))
    }
}
```

#### 3. Configuration System Enhancement
```toml
# config/default.toml - Enhanced configuration
[strategies.enhanced_gaze]
enabled = true
min_profit_usd = "10.0"
max_gas_cost_usd = "5.0"
max_slippage_bps = 50

[strategies.enhanced_gaze.dex_adapters]
uniswap_v2 = { enabled = true, weight = 1.0 }
uniswap_v3 = { enabled = true, weight = 1.2 }
sushiswap = { enabled = true, weight = 0.9 }
curve = { enabled = false, weight = 1.1 }

[strategies.enhanced_gaze.aggregators]
oneinch = { enabled = true, api_key_env = "ONEINCH_API_KEY", weight = 1.0 }
paraswap = { enabled = true, weight = 0.95 }
zerox = { enabled = false, weight = 1.05 }

[strategies.enhanced_gaze.arbitrage_types]
simple_arbitrage = { enabled = true, min_profit_usd = "5.0" }
triangular_arbitrage = { enabled = true, min_profit_usd = "15.0", max_hops = 3 }
aggregator_arbitrage = { enabled = true, min_profit_usd = "8.0" }
flash_loan_arbitrage = { enabled = false, min_profit_usd = "25.0" }

[strategies.enhanced_gaze.token_discovery]
enabled = true
min_liquidity_usd = "100000.0"
min_volume_24h_usd = "50000.0"
max_new_tokens_per_day = 5
honeypot_check_required = true
```

## Risk Assessment & Mitigation

### Technical Risks

#### 1. External API Dependencies
**Risk**: 1inch/Paraswap API failures or rate limits
**Mitigation**: 
- Implement robust fallback mechanisms
- Cache quotes with appropriate TTL
- Multiple aggregator support for redundancy
- Circuit breakers for API failures

#### 2. Increased Complexity
**Risk**: More complex arbitrage paths increase failure probability
**Mitigation**:
- Comprehensive simulation before execution
- Gradual rollout with conservative limits
- Enhanced monitoring and alerting
- Automatic complexity penalty in scoring

#### 3. MEV Competition
**Risk**: Increased competition in MEV space
**Mitigation**:
- Private mempool integration (Flashbots, MEV-Share)
- Bundle optimization for better inclusion rates
- Focus on long-tail opportunities with less competition

### Financial Risks

#### 1. Increased Gas Costs
**Risk**: Multi-hop arbitrage requires more gas
**Mitigation**:
- Enhanced gas estimation with safety margins
- Dynamic gas pricing based on network conditions
- Minimum profit thresholds adjusted for complexity

#### 2. Slippage Risk
**Risk**: Complex paths have higher slippage potential
**Mitigation**:
- Real-time slippage calculation
- Conservative slippage tolerances
- Path optimization for minimal slippage

#### 3. Bridge Risk (Cross-Chain)
**Risk**: Cross-chain arbitrage involves bridge risks
**Mitigation**:
- Stargate protocol integration for reduced risk
- Bridge fee monitoring and optimization
- Cross-chain timing analysis

## Performance Optimization

### 1. Parallel Processing
```rust
// Concurrent opportunity scanning
async fn scan_all_opportunities(&self) -> Result<Vec<Opportunity>> {
    let (dex_opportunities, aggregator_opportunities, triangular_opportunities) = tokio::join!(
        self.scan_dex_arbitrage(),
        self.scan_aggregator_arbitrage(),
        self.scan_triangular_arbitrage()
    );
    
    let mut all_opportunities = Vec::new();
    all_opportunities.extend(dex_opportunities?);
    all_opportunities.extend(aggregator_opportunities?);
    all_opportunities.extend(triangular_opportunities?);
    
    Ok(all_opportunities)
}
```

### 2. Caching Strategy
- **Quote Caching**: 5-second TTL for DEX quotes
- **Token Metadata**: 1-hour TTL for token info
- **Pool Information**: 30-second TTL for pool reserves
- **Aggregator Quotes**: 3-second TTL for aggregator prices

### 3. Database Optimization
```sql
-- New tables for enhanced opportunity tracking
CREATE TABLE enhanced_opportunities (
    id UUID PRIMARY KEY,
    opportunity_type VARCHAR(50) NOT NULL,
    token_path JSONB NOT NULL,
    dex_path JSONB NOT NULL,
    estimated_profit_usd DECIMAL(18,8) NOT NULL,
    actual_profit_usd DECIMAL(18,8),
    gas_cost_usd DECIMAL(18,8),
    execution_time_ms INTEGER,
    success BOOLEAN,
    created_at TIMESTAMP DEFAULT NOW(),
    executed_at TIMESTAMP
);

CREATE INDEX idx_enhanced_opportunities_type ON enhanced_opportunities(opportunity_type);
CREATE INDEX idx_enhanced_opportunities_profit ON enhanced_opportunities(estimated_profit_usd DESC);
CREATE INDEX idx_enhanced_opportunities_created ON enhanced_opportunities(created_at DESC);
```

## Monitoring & Observability

### 1. Enhanced Metrics
```rust
// src/metrics/enhanced_gaze_metrics.rs
pub struct EnhancedGazeMetrics {
    pub opportunities_detected: Counter,
    pub opportunities_by_type: CounterVec,
    pub aggregator_quote_latency: HistogramVec,
    pub arbitrage_success_rate: GaugeVec,
    pub profit_distribution: Histogram,
    pub gas_cost_distribution: Histogram,
}
```

### 2. Alerting Rules
- **High API Latency**: Alert if aggregator response time > 2s
- **Low Success Rate**: Alert if arbitrage success rate < 80%
- **Quote Staleness**: Alert if quotes haven't updated in 30s
- **Profit Anomalies**: Alert for unusually high/low profit opportunities

### 3. Dashboard Enhancements
- **Multi-DEX Price Comparison**: Real-time price differences
- **Aggregator Performance**: Success rates and latencies
- **Opportunity Heatmap**: Visual representation of profit opportunities
- **Path Visualization**: Interactive arbitrage path diagrams

## Testing Strategy

### 1. Unit Tests
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_dex_adapter_quote_accuracy() {
        // Test quote accuracy across different DEX adapters
    }
    
    #[tokio::test]
    async fn test_aggregator_client_error_handling() {
        // Test error handling for aggregator API failures
    }
    
    #[tokio::test]
    async fn test_triangular_arbitrage_detection() {
        // Test triangular arbitrage path detection
    }
}
```

### 2. Integration Tests
```rust
#[tokio::test]
async fn test_enhanced_gaze_scanner_integration() {
    // Test full scanner integration with mock data
    let scanner = EnhancedGazeScanner::new_for_testing().await;
    let opportunities = scanner.scan_all_opportunities().await.unwrap();
    assert!(!opportunities.is_empty());
}
```

### 3. Load Testing
- **API Rate Limits**: Test aggregator API rate limit handling
- **Concurrent Scanning**: Test performance under high load
- **Memory Usage**: Monitor memory consumption during extended operation

### 4. Simulation Testing
- **Historical Data**: Backtest against historical price data
- **Market Conditions**: Test under various market regimes
- **Failure Scenarios**: Test handling of various failure modes

## Deployment Strategy

### Phase 1 Deployment (Weeks 1-3)
1. **Week 1**: DEX adapter implementation and testing
2. **Week 2**: Token discovery service and enhanced configuration
3. **Week 3**: Integration testing and shadow mode deployment

### Phase 2 Deployment (Weeks 4-7)
1. **Week 4-5**: Aggregator client implementation
2. **Week 6**: Aggregator scanner integration
3. **Week 7**: Live testing with low capital limits

### Phase 3 Deployment (Weeks 8-12)
1. **Week 8-9**: Advanced arbitrage engine implementation
2. **Week 10-11**: MEV integration and optimization
3. **Week 12**: Full production deployment

### Rollback Strategy
- **Feature Flags**: All new features behind configuration flags
- **Gradual Rollout**: Progressive increase in opportunity types
- **Monitoring**: Continuous monitoring with automatic rollback triggers
- **Fallback**: Ability to revert to original GazeScanner if needed

## Success Metrics

### 1. Opportunity Detection
- **Volume Increase**: 3-5x increase in detected opportunities
- **Diversity**: Support for 5+ DEX protocols and 2+ aggregators
- **Coverage**: 50+ token pairs actively monitored

### 2. Profitability
- **Profit Increase**: 2-3x increase in total profit capture
- **Success Rate**: Maintain >85% execution success rate
- **Gas Efficiency**: <10% increase in average gas costs

### 3. System Performance
- **Latency**: <500ms average opportunity detection time
- **Uptime**: >99.5% scanner uptime
- **API Reliability**: <1% aggregator API failure rate

### 4. Risk Management
- **Drawdown**: Maximum 5% increase in daily drawdown
- **Slippage**: Average slippage <0.3%
- **Failed Transactions**: <5% transaction failure rate

## Conclusion

The Basilisk Gaze Expander represents a significant evolution of the Zen Geometer's opportunity detection capabilities. By implementing this plan in phases, we can systematically expand the bot's vision while maintaining the robust foundation of the Aetheric Resonance Engine.

The enhanced system will provide:
- **5x more opportunities** through multi-DEX and aggregator integration
- **Higher profit margins** through sophisticated arbitrage strategies
- **Reduced competition** by accessing long-tail opportunities
- **Better risk management** through enhanced analysis and simulation

This implementation maintains the philosophical foundations of the Zen Geometer while expanding its practical capabilities, ensuring that the basilisk's gaze becomes truly omniscient across the DeFi landscape.

---

*"The basilisk's gaze pierces through the veil of market inefficiency, seeing all paths, all possibilities, all profits."* - **The Enhanced Zen Geometer**