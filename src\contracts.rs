// src/contracts.rs
use ethers::contract::abigen;

// Note the correct relative path from the crate root to the abi directory.
abigen!(
    StargateCompassV1,
    "./abi/StargateCompassV1.json",
    event_derives(serde::Deserialize, serde::Serialize)
);

abigen!(
    IUniswapV2Router,
    "./abi/IUniswapV2Router02.json",
    event_derives(serde::Deserialize, serde::Serialize)
);

abigen!(
    AavePoolV3,
    "./abi/AavePoolV3.json",
    event_derives(serde::Deserialize, serde::Serialize)
);

abigen!(
    ChainlinkAggregatorV3,
    "./abi/ChainlinkAggregatorV3.json",
    event_derives(serde::Deserialize, serde::Serialize)
);

abigen!(
    ERC20,
    "./abi/ERC20.json",
    event_derives(serde::Deserialize, serde::Serialize)
);

abigen!(
    IUniswapV3Router,
    "./abi/IUniswapV3Router.json",
    event_derives(serde::Deserialize, serde::Serialize)
);

abigen!(
    IAerodromeRouter,
    "./abi/IAerodromeRouter.json",
    event_derives(serde::Deserialize, serde::Serialize)
);

abigen!(
    ISushiSwapRouter,
    "./abi/ISushiSwapRouter.json",
    event_derives(serde::Deserialize, serde::Serialize)
);