# Enhanced Error Propagation and Alerting System

## Overview

The Enhanced Error Propagation and Alerting System provides comprehensive error handling, recovery mechanisms, metrics collection, and multi-channel alerting for the Basilisk trading bot. This system addresses the requirements from task 5.3 by implementing proper error types, propagation chains, structured logging, alerting mechanisms, and error recovery logic.

## Architecture

### Core Components

1. **Enhanced Error Types** (`src/error/enhanced.rs`)
   - `EnhancedError`: Wraps `BasiliskError` with additional context and metadata
   - `ErrorContext`: Tracks error propagation chain and contextual information
   - `RetryManager`: Handles exponential backoff and retry logic
   - `AlertManager`: Multi-channel alerting system

2. **Recovery System** (`src/error/recovery.rs`)
   - `CircuitBreaker`: Prevents cascading failures
   - `RecoveryManager`: Coordinates recovery strategies
   - `FallbackStrategy`: Defines different recovery approaches

3. **Metrics and Monitoring** (`src/error/metrics.rs`)
   - `ErrorMetricsCollector`: Tracks error patterns and trends
   - `ErrorImpactAnalyzer`: Analyzes trading impact of errors
   - `HealthReport`: Generates system health assessments

4. **Integration Layer** (`src/error/integration.rs`)
   - `IntegratedErrorSystem`: Unified interface for all error handling
   - `SystemHealthMonitor`: Tracks overall system health
   - Convenience macros for common error handling patterns

## Key Features

### 1. Error Propagation Chains

Every error maintains a complete propagation chain showing how it moved through the system:

```rust
use crate::error::{ErrorContext, EnhancedError};

let mut context = ErrorContext::new("PriceOracle", "get_price")
    .with_opportunity("opp_12345")
    .with_chain(8453);

// Error propagates through multiple components
context.propagate("DataProvider", "fetch_price", &original_error);
context.propagate("PriceOracle", "validate_price", &validation_error);

let enhanced_error = EnhancedError::new(final_error, context);
```

### 2. Structured Logging

All errors are logged with rich context and structured data:

```rust
use crate::log_error;
use crate::logging::{TradingContext, ErrorCode};

let context = TradingContext::new("StrategyManager", "evaluate_opportunity")
    .with_opportunity("opp_12345")
    .with_chain(8453)
    .with_path(vec!["WETH".to_string(), "USDC".to_string()])
    .with_profit(dec!(10.50));

log_error!(context, ErrorCode::EInsufficientLiquidity,
    "Insufficient liquidity for trade execution");
```

### 3. Multi-Channel Alerting

Alerts can be sent to multiple channels with different severity thresholds:

```rust
use crate::error::{AlertChannelConfig, AlertChannelType, AlertSeverity};

let alert_channels = vec![
    AlertChannelConfig {
        channel_type: AlertChannelType::Slack,
        enabled: true,
        min_severity: AlertSeverity::Error,
        rate_limit_seconds: 300,
        config: {
            let mut config = HashMap::new();
            config.insert("webhook_url".to_string(), "https://hooks.slack.com/...".to_string());
            config
        },
    },
    AlertChannelConfig {
        channel_type: AlertChannelType::PagerDuty,
        enabled: true,
        min_severity: AlertSeverity::Critical,
        rate_limit_seconds: 60,
        config: pagerduty_config,
    },
];
```

### 4. Circuit Breakers

Prevent cascading failures by automatically opening circuits when error thresholds are exceeded:

```rust
use crate::error::{CircuitBreaker, CircuitBreakerConfig};

let config = CircuitBreakerConfig {
    failure_threshold: 5,
    success_threshold: 3,
    timeout_duration: Duration::from_secs(60),
    half_open_max_calls: 3,
};

let circuit_breaker = CircuitBreaker::new(config);

let result = circuit_breaker.call(|| {
    // Your operation here
    risky_operation()
}).await;
```

### 5. Automatic Recovery

The system attempts automatic recovery using configurable strategies:

```rust
use crate::error::{RecoveryManager, FallbackStrategy};

let recovery_manager = RecoveryManager::new();

let result = recovery_manager.execute_with_recovery(
    "price_oracle",
    || fetch_price_from_primary_source(),
    &mut error_context,
).await;

// If primary fails, system automatically tries:
// 1. Backup service
// 2. Cached data
// 3. Default values
// 4. Graceful degradation
```

### 6. Error Metrics and Analysis

Comprehensive metrics collection and analysis:

```rust
use crate::error::{ErrorMetricsCollector, ErrorImpactAnalyzer};

let metrics_collector = ErrorMetricsCollector::new(config);
let impact_analyzer = ErrorImpactAnalyzer::new(metrics_collector.clone());

// Get current metrics
let metrics = metrics_collector.get_metrics().await;
println!("Error rate: {}/min", metrics.error_rate_per_minute);
println!("Health score: {}", metrics_collector.get_health_score().await);

// Analyze trading impact
let trading_impact = impact_analyzer.analyze_trading_impact().await;
println!("Execution impact: {}%", trading_impact.execution_impact_score);
```

## Usage Examples

### Basic Error Handling

```rust
use crate::error::{IntegratedErrorSystem, IntegratedErrorConfig, ErrorContext};

// Initialize the system
let config = IntegratedErrorConfig::default();
let error_system = Arc::new(IntegratedErrorSystem::new(config));

// Handle errors with full integration
let context = ErrorContext::new("MyComponent", "my_function");
let result = error_system.execute_with_full_handling(
    "my_service",
    || potentially_failing_operation(),
    context,
).await;
```

### Trading-Specific Error Handling

```rust
use crate::error::integration::IntegratedErrorSystem;
use crate::logging::TradingContext;
use crate::handle_trading_error;

let trading_context = TradingContext::new("TradeExecutor", "execute_trade")
    .with_opportunity("opp_12345")
    .with_chain(8453)
    .with_path(vec!["WETH".to_string(), "USDC".to_string()])
    .with_profit(dec!(15.75));

let result = handle_trading_error!(
    error_system,
    "trade_execution",
    &trading_context,
    || execute_arbitrage_trade(&opportunity)
);
```

### Health Monitoring

```rust
// Start background health monitoring
error_system.start_health_monitoring().await;

// Get current system health
let health_status = error_system.get_system_health().await;
println!("Overall health: {}%", health_status.overall_health_score);
println!("Active alerts: {}", health_status.system_alerts.len());

// Get error statistics
let stats = error_system.get_error_statistics().await;
println!("Total errors: {}", stats.total_errors_handled);
println!("Recovery success rate: {}%",
    (stats.successful_recoveries as f64 / stats.recovery_attempts as f64) * 100.0);
```

## Configuration

### Error System Configuration

```rust
use crate::error::{IntegratedErrorConfig, AlertChannelConfig, AlertChannelType, AlertSeverity};
use crate::error::metrics::MetricsConfig;

let config = IntegratedErrorConfig {
    metrics_config: MetricsConfig {
        max_events: 10000,
        analysis_interval: Duration::from_secs(60),
        burst_threshold: 10,
        burst_window: Duration::from_secs(60),
        pattern_min_frequency: 5,
    },
    alert_channels: vec![
        AlertChannelConfig {
            channel_type: AlertChannelType::Slack,
            enabled: true,
            min_severity: AlertSeverity::Error,
            rate_limit_seconds: 300,
            config: slack_config,
        },
    ],
    enable_recovery: true,
    enable_circuit_breakers: true,
    enable_metrics_collection: true,
    health_check_interval_seconds: 300,
};
```

### Circuit Breaker Configuration

```rust
use crate::error::{CircuitBreakerConfig};

// For RPC providers
let rpc_config = CircuitBreakerConfig {
    failure_threshold: 3,
    success_threshold: 2,
    timeout_duration: Duration::from_secs(30),
    half_open_max_calls: 2,
};

// For price oracles
let oracle_config = CircuitBreakerConfig {
    failure_threshold: 5,
    success_threshold: 3,
    timeout_duration: Duration::from_secs(60),
    half_open_max_calls: 3,
};
```

## Error Types and Codes

### Network Errors

- `ERpcTimeout`: RPC request timeout
- `ERpcConnectionFailed`: RPC connection failure
- `ERpcRateLimited`: RPC rate limiting
- `ENetworkLatencyHigh`: High network latency

### Data Provider Errors

- `EDataSourceUnavailable`: Data source unavailable
- `EDataStale`: Stale data detected
- `EPriceOracleFailure`: Price oracle failure
- `EDataValidationFailed`: Data validation failure

### Execution Errors

- `EInsufficientLiquidity`: Insufficient liquidity
- `EHighSlippage`: High slippage detected
- `EGasEstimationFailed`: Gas estimation failure
- `ETransactionReverted`: Transaction reverted
- `ETransactionTimeout`: Transaction timeout

### Critical Errors

- `ECriticalConfigError`: Critical configuration error
- `ESystemOverloaded`: System overloaded
- `EUnrecoverableError`: Unrecoverable error
- `ESecurityViolation`: Security violation

## Monitoring and Alerting

### Health Metrics

The system tracks various health metrics:

- **Error Rate**: Errors per minute/hour
- **Error Distribution**: By component, severity, and error code
- **Recovery Success Rate**: Percentage of successful recoveries
- **Circuit Breaker Status**: Open/closed state of all circuit breakers
- **System Health Score**: Overall health score (0-100)

### Alert Channels

Supported alert channels:

1. **Slack**: Rich formatted messages with error details
2. **Discord**: Embedded messages with color coding
3. **Email**: Detailed email notifications
4. **PagerDuty**: Critical incident management
5. **Webhook**: Custom webhook integrations
6. **Log**: Structured log entries

### Rate Limiting

All alert channels support rate limiting to prevent spam:

- **Slack/Discord**: 5-minute default rate limit
- **Email**: 10-minute default rate limit
- **PagerDuty**: 1-minute default rate limit
- **Webhook**: 30-second default rate limit
- **Log**: No rate limit

## Best Practices

### 1. Error Context Creation

Always create rich error contexts:

```rust
let context = ErrorContext::new("ComponentName", "function_name")
    .with_opportunity("opp_12345")
    .with_chain(8453)
    .with_data("additional_info", value)
    .with_max_retries(3);
```

### 2. Proper Error Propagation

Propagate errors through the call chain:

```rust
fn inner_function() -> Result<T, BasiliskError> {
    // ... operation that might fail
}

fn outer_function() -> Result<T, EnhancedError> {
    let mut context = ErrorContext::new("OuterComponent", "outer_function");

    match inner_function() {
        Ok(result) => Ok(result),
        Err(error) => {
            context.propagate("OuterComponent", "outer_function", &error);
            Err(EnhancedError::new(error, context))
        }
    }
}
```

### 3. Service-Specific Circuit Breakers

Use different circuit breaker configurations for different services:

```rust
// Fast-failing for critical path operations
let execution_config = CircuitBreakerConfig {
    failure_threshold: 2,
    timeout_duration: Duration::from_secs(10),
    ..Default::default()
};

// More tolerant for data fetching
let data_config = CircuitBreakerConfig {
    failure_threshold: 5,
    timeout_duration: Duration::from_secs(60),
    ..Default::default()
};
```

### 4. Structured Logging

Use structured logging with appropriate log levels:

```rust
// Critical errors
log_error!(context, ErrorCode::ECriticalConfigError,
    "Critical configuration error detected");

// Warnings for degraded performance
log_warning!(context, ErrorCode::EHighSlippage,
    slippage_percent = actual_slippage,
    "High slippage detected");

// Info for normal operations
log_info!(context, "Operation completed successfully");
```

## Testing

The system includes comprehensive tests for all components:

```bash
# Run all error handling tests
cargo test error::

# Run specific component tests
cargo test error::enhanced::tests
cargo test error::recovery::tests
cargo test error::metrics::tests
cargo test error::integration::tests
```

## Performance Considerations

### Memory Usage

- Error events are stored in bounded queues (default: 10,000 events)
- Metrics are aggregated to prevent memory growth
- Circuit breaker state is minimal and efficient

### CPU Usage

- Background health monitoring runs every 5 minutes by default
- Metrics analysis is performed incrementally
- Alert rate limiting prevents excessive processing

### Network Usage

- Alerts are batched and rate-limited
- Webhook payloads are optimized for size
- Circuit breakers prevent unnecessary network calls

## Migration Guide

### From Basic Error Handling

1. Replace direct `BasiliskError` usage with `IntegratedErrorSystem`
2. Add error contexts to all error-prone operations
3. Configure alert channels for your environment
4. Enable circuit breakers for external services

### Configuration Changes

Update your configuration to include error handling settings:

```toml
[error_handling]
enable_recovery = true
enable_circuit_breakers = true
enable_metrics_collection = true
health_check_interval_seconds = 300

[[error_handling.alert_channels]]
type = "slack"
enabled = true
min_severity = "error"
rate_limit_seconds = 300
webhook_url = "https://hooks.slack.com/services/..."
```

## Troubleshooting

### Common Issues

1. **High Memory Usage**: Reduce `max_events` in `MetricsConfig`
2. **Alert Spam**: Increase `rate_limit_seconds` for alert channels
3. **Circuit Breaker Always Open**: Adjust `failure_threshold` and `timeout_duration`
4. **Missing Error Context**: Ensure all operations use `ErrorContext`

### Debug Information

Enable debug logging to see detailed error handling information:

```rust
RUST_LOG=basilisk::error=debug cargo run
```

### Health Check Endpoints

The system provides health check information:

```rust
let health_status = error_system.get_system_health().await;
let statistics = error_system.get_error_statistics().await;
```

## Future Enhancements

1. **Machine Learning**: Predictive error detection
2. **Advanced Recovery**: Context-aware recovery strategies
3. **Distributed Tracing**: Cross-service error tracking
4. **Custom Metrics**: User-defined error metrics
5. **Integration APIs**: REST/GraphQL APIs for monitoring tools

## Conclusion

The Enhanced Error Propagation and Alerting System provides comprehensive error handling capabilities that improve system reliability, observability, and maintainability. By implementing proper error types, propagation chains, structured logging, multi-channel alerting, and automatic recovery mechanisms, the system ensures robust operation in production environments.
