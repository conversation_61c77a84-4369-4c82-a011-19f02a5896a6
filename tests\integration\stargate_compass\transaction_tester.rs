
pub struct TransactionTestSuite {
    pub statistics: Option<TransactionTestStatistics>,
    pub emergency_stop_result: Option<TransactionCommandTestResult>,
    pub error_message_validations: Vec<String>,
}

pub struct TransactionTestStatistics {
    pub total_tests: usize,
    pub successful_tests: usize,
    pub failed_tests: usize,
    pub total_execution_time_ms: u64,
}

pub struct TransactionCommandTestResult {
    pub command_name: String,
    pub success: bool,
    pub gas_used: Option<u64>,
    pub error_message: Option<String>,
}
