// MISSION: Enhanced Error Handling Examples
// WHY: Demonstrate proper usage of the enhanced error handling system
// HOW: Provide concrete examples of error handling, recovery, and monitoring integration

use std::sync::Arc;
use tokio::time::{sleep, Duration};
use tracing::{info, warn, error};

use crate::error::{
    BasiliskError, NetworkError, DataProviderError, ExecutionError,
    IntegratedErrorSystem, IntegratedErrorConfig, ErrorContext,
    AlertChannelConfig, AlertChannelType, AlertSeverity
};
use crate::logging::{TradingContext, ErrorCode};

/// Example: Enhanced RPC client with full error handling
pub struct EnhancedRpcClient {
    error_system: Arc<IntegratedErrorSystem>,
    endpoint_url: String,
    client: reqwest::Client,
}

impl EnhancedRpcClient {
    pub fn new(endpoint_url: String, error_system: Arc<IntegratedErrorSystem>) -> Self {
        Self {
            error_system,
            endpoint_url,
            client: reqwest::Client::new(),
        }
    }

    /// Example: Make RPC call with comprehensive error handling
    pub async fn make_rpc_call(&self, method: &str, params: serde_json::Value) -> crate::error::Result<serde_json::Value> {
        let context = ErrorContext::new("EnhancedRpcClient", "make_rpc_call")
            .with_data("method", method)
            .with_data("endpoint", &self.endpoint_url);

        self.error_system.execute_with_full_handling(
            "rpc_provider",
            || self.execute_rpc_request(method, &params),
            context,
        ).await
    }

    fn execute_rpc_request(&self, method: &str, params: &serde_json::Value) -> Result<serde_json::Value, NetworkError> {
        // Simulate RPC call logic
        if method == "eth_getBalance" {
            // Simulate timeout
            if self.endpoint_url.contains("slow") {
                return Err(NetworkError::RpcTimeout {
                    endpoint: self.endpoint_url.clone(),
                    timeout_ms: 5000,
                });
            }
            
            // Simulate rate limiting
            if self.endpoint_url.contains("limited") {
                return Err(NetworkError::RpcRateLimited {
                    endpoint: self.endpoint_url.clone(),
                    retry_after_ms: 1000,
                });
            }
            
            // Success case
            Ok(serde_json::json!({
                "jsonrpc": "2.0",
                "id": 1,
                "result": "0x1b1ae4d6e2ef500000"
            }))
        } else {
            Err(NetworkError::RpcInvalidResponse {
                endpoint: self.endpoint_url.clone(),
                details: format!("Unsupported method: {}", method),
            })
        }
    }
}

/// Example: Enhanced price oracle with fallback strategies
pub struct EnhancedPriceOracle {
    error_system: Arc<IntegratedErrorSystem>,
    primary_source: String,
    backup_sources: Vec<String>,
}

impl EnhancedPriceOracle {
    pub fn new(
        primary_source: String,
        backup_sources: Vec<String>,
        error_system: Arc<IntegratedErrorSystem>
    ) -> Self {
        Self {
            error_system,
            primary_source,
            backup_sources,
        }
    }

    /// Example: Get price with automatic fallback to backup sources
    pub async fn get_price(&self, token_symbol: &str) -> crate::error::Result<rust_decimal::Decimal> {
        let trading_context = TradingContext::new("EnhancedPriceOracle", "get_price")
            .with_data("token_symbol", token_symbol);

        let context = self.error_system.create_trading_context(
            "EnhancedPriceOracle",
            "get_price",
            &trading_context
        );

        // Try primary source first
        match self.error_system.execute_with_full_handling(
            "price_oracle_primary",
            || self.fetch_price_from_source(&self.primary_source, token_symbol),
            context.clone(),
        ).await {
            Ok(price) => {
                info!(
                    token_symbol = token_symbol,
                    price = %price,
                    source = %self.primary_source,
                    "Price fetched from primary source"
                );
                Ok(price)
            }
            Err(primary_error) => {
                warn!(
                    token_symbol = token_symbol,
                    source = %self.primary_source,
                    error = %primary_error,
                    "Primary price source failed, trying backup sources"
                );

                // Try backup sources
                for backup_source in &self.backup_sources {
                    match self.error_system.execute_with_full_handling(
                        "price_oracle_backup",
                        || self.fetch_price_from_source(backup_source, token_symbol),
                        context.clone(),
                    ).await {
                        Ok(price) => {
                            info!(
                                token_symbol = token_symbol,
                                price = %price,
                                source = %backup_source,
                                "Price fetched from backup source"
                            );
                            return Ok(price);
                        }
                        Err(backup_error) => {
                            warn!(
                                token_symbol = token_symbol,
                                source = %backup_source,
                                error = %backup_error,
                                "Backup price source also failed"
                            );
                        }
                    }
                }

                // All sources failed
                Err(primary_error)
            }
        }
    }

    fn fetch_price_from_source(&self, source: &str, token_symbol: &str) -> Result<rust_decimal::Decimal, DataProviderError> {
        use rust_decimal_macros::dec;

        // Simulate different failure modes
        match source {
            "primary" => {
                if token_symbol == "UNKNOWN" {
                    Err(DataProviderError::ValidationFailed {
                        data_source: source.to_string(),
                        details: format!("Unknown token: {}", token_symbol),
                    })
                } else if token_symbol == "STALE" {
                    Err(DataProviderError::StaleData {
                        data_source: source.to_string(),
                        age_ms: 300000, // 5 minutes
                        threshold_ms: 60000, // 1 minute
                    })
                } else {
                    Ok(dec!(1500.50)) // Mock price
                }
            }
            "backup1" => {
                if token_symbol == "UNAVAILABLE" {
                    Err(DataProviderError::SourceUnavailable {
                        data_source: source.to_string(),
                        reason: "Service maintenance".to_string(),
                    })
                } else {
                    Ok(dec!(1501.25)) // Slightly different mock price
                }
            }
            "backup2" => Ok(dec!(1499.75)), // Another mock price
            _ => Err(DataProviderError::SourceUnavailable {
                data_source: source.to_string(),
                reason: "Unknown source".to_string(),
            }),
        }
    }
}

/// Example: Enhanced trade executor with comprehensive error handling
pub struct EnhancedTradeExecutor {
    error_system: Arc<IntegratedErrorSystem>,
    wallet_address: String,
}

impl EnhancedTradeExecutor {
    pub fn new(wallet_address: String, error_system: Arc<IntegratedErrorSystem>) -> Self {
        Self {
            error_system,
            wallet_address,
        }
    }

    /// Example: Execute trade with full error handling and recovery
    pub async fn execute_trade(&self, 
        opportunity_id: &str,
        token_path: Vec<String>,
        amount_in: rust_decimal::Decimal
    ) -> crate::error::Result<String> {
        let trading_context = TradingContext::new("EnhancedTradeExecutor", "execute_trade")
            .with_opportunity(opportunity_id)
            .with_path(token_path.clone())
            .with_data("amount_in", amount_in);

        let context = self.error_system.create_trading_context(
            "EnhancedTradeExecutor",
            "execute_trade",
            &trading_context
        );

        info!(
            opportunity_id = opportunity_id,
            token_path = ?token_path,
            amount_in = %amount_in,
            "Starting trade execution"
        );

        self.error_system.execute_with_full_handling(
            "trade_executor",
            || self.perform_trade_execution(opportunity_id, &token_path, amount_in),
            context,
        ).await
    }

    fn perform_trade_execution(&self, 
        opportunity_id: &str,
        token_path: &[String],
        amount_in: rust_decimal::Decimal
    ) -> Result<String, ExecutionError> {
        use rust_decimal_macros::dec;

        // Simulate various execution scenarios
        if opportunity_id.contains("insufficient_liquidity") {
            return Err(ExecutionError::InsufficientLiquidity {
                token_path: token_path.to_vec(),
                available_usd: dec!(1000.0),
                required_usd: amount_in,
            });
        }

        if opportunity_id.contains("high_slippage") {
            return Err(ExecutionError::HighSlippage {
                token_path: token_path.to_vec(),
                actual_percent: dec!(5.5),
                threshold_percent: dec!(2.0),
            });
        }

        if opportunity_id.contains("gas_estimation_failed") {
            return Err(ExecutionError::GasEstimationFailed {
                operation: "swap".to_string(),
                reason: "Network congestion".to_string(),
            });
        }

        if opportunity_id.contains("transaction_reverted") {
            return Err(ExecutionError::TransactionReverted {
                tx_hash: "0x1234567890abcdef".to_string(),
                reason: "Slippage tolerance exceeded".to_string(),
            });
        }

        // Success case
        Ok("0xabcdef1234567890".to_string())
    }
}

/// Example: System health monitoring and alerting setup
pub async fn setup_enhanced_error_system() -> Arc<IntegratedErrorSystem> {
    // Configure alert channels
    let alert_channels = vec![
        AlertChannelConfig {
            channel_type: AlertChannelType::Log,
            enabled: true,
            min_severity: AlertSeverity::Warning,
            rate_limit_seconds: 0,
            config: std::collections::HashMap::new(),
        },
        AlertChannelConfig {
            channel_type: AlertChannelType::Slack,
            enabled: true,
            min_severity: AlertSeverity::Error,
            rate_limit_seconds: 300, // 5 minutes
            config: {
                let mut config = std::collections::HashMap::new();
                config.insert("webhook_url".to_string(), "https://hooks.slack.com/services/...".to_string());
                config
            },
        },
    ];

    // Create integrated error system
    let config = IntegratedErrorConfig {
        alert_channels,
        enable_recovery: true,
        enable_circuit_breakers: true,
        enable_metrics_collection: true,
        health_check_interval_seconds: 60, // 1 minute for demo
        ..Default::default()
    };

    let error_system = Arc::new(IntegratedErrorSystem::new(config));

    // Start health monitoring
    error_system.start_health_monitoring().await;

    info!("Enhanced error handling system initialized with full monitoring");

    error_system
}

/// Example: Comprehensive error handling demonstration
pub async fn demonstrate_error_handling() -> crate::error::Result<()> {
    let error_system = setup_enhanced_error_system().await;

    // Example 1: RPC client with retry and fallback
    let rpc_client = EnhancedRpcClient::new(
        "https://slow-rpc.example.com".to_string(),
        error_system.clone()
    );

    match rpc_client.make_rpc_call("eth_getBalance", serde_json::json!(["0x123", "latest"])).await {
        Ok(result) => info!("RPC call succeeded: {:?}", result),
        Err(e) => warn!("RPC call failed: {}", e),
    }

    // Example 2: Price oracle with multiple fallbacks
    let price_oracle = EnhancedPriceOracle::new(
        "primary".to_string(),
        vec!["backup1".to_string(), "backup2".to_string()],
        error_system.clone()
    );

    match price_oracle.get_price("ETH").await {
        Ok(price) => info!("Price fetched: {}", price),
        Err(e) => warn!("Price fetch failed: {}", e),
    }

    // Example 3: Trade execution with comprehensive error handling
    let trade_executor = EnhancedTradeExecutor::new(
        "0xwallet123".to_string(),
        error_system.clone()
    );

    let test_cases = vec![
        ("opportunity_success", vec!["WETH".to_string(), "USDC".to_string()]),
        ("opportunity_insufficient_liquidity", vec!["RARE".to_string(), "USDC".to_string()]),
        ("opportunity_high_slippage", vec!["VOLATILE".to_string(), "USDC".to_string()]),
        ("opportunity_gas_estimation_failed", vec!["WETH".to_string(), "DAI".to_string()]),
    ];

    for (opportunity_id, token_path) in test_cases {
        match trade_executor.execute_trade(
            opportunity_id,
            token_path,
            rust_decimal_macros::dec!(1000.0)
        ).await {
            Ok(tx_hash) => info!("Trade executed successfully: {}", tx_hash),
            Err(e) => warn!("Trade execution failed: {}", e),
        }

        // Small delay between tests
        sleep(Duration::from_millis(100)).await;
    }

    // Example 4: System health monitoring
    let health_status = error_system.get_system_health().await;
    info!(
        health_score = health_status.overall_health_score,
        total_errors = health_status.error_metrics.total_errors,
        active_alerts = health_status.system_alerts.len(),
        "System health status"
    );

    // Example 5: Error statistics
    let statistics = error_system.get_error_statistics().await;
    info!(
        total_errors = statistics.total_errors_handled,
        recovery_attempts = statistics.recovery_attempts,
        successful_recoveries = statistics.successful_recoveries,
        "Error handling statistics"
    );

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_enhanced_rpc_client() {
        let error_system = setup_enhanced_error_system().await;
        let client = EnhancedRpcClient::new(
            "https://test-rpc.example.com".to_string(),
            error_system
        );

        let result = client.make_rpc_call("eth_getBalance", serde_json::json!(["0x123", "latest"])).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_enhanced_price_oracle_fallback() {
        let error_system = setup_enhanced_error_system().await;
        let oracle = EnhancedPriceOracle::new(
            "primary".to_string(),
            vec!["backup1".to_string(), "backup2".to_string()],
            error_system
        );

        // Test successful primary fetch
        let result = oracle.get_price("ETH").await;
        assert!(result.is_ok());

        // Test fallback when primary fails
        let result = oracle.get_price("STALE").await;
        assert!(result.is_ok()); // Should succeed with backup
    }

    #[tokio::test]
    async fn test_enhanced_trade_executor_error_handling() {
        let error_system = setup_enhanced_error_system().await;
        let executor = EnhancedTradeExecutor::new(
            "0xtest".to_string(),
            error_system
        );

        // Test successful execution
        let result = executor.execute_trade(
            "opportunity_success",
            vec!["WETH".to_string(), "USDC".to_string()],
            rust_decimal_macros::dec!(100.0)
        ).await;
        assert!(result.is_ok());

        // Test error handling
        let result = executor.execute_trade(
            "opportunity_insufficient_liquidity",
            vec!["RARE".to_string(), "USDC".to_string()],
            rust_decimal_macros::dec!(10000.0)
        ).await;
        assert!(result.is_err());
    }
}