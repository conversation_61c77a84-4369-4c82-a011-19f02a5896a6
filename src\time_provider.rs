use async_trait::async_trait;
use std::time::Duration;
use tokio::time::Instant;
use std::sync::{Arc, Mutex};

/// Trait for abstracting time operations.
#[async_trait]
pub trait TimeProvider: Send + Sync + 'static {
    /// Returns the current Instant.
    fn now(&self) -> Instant;

    /// Asynchronously sleeps for the given duration.
    async fn sleep(&self, duration: Duration);
}

/// System-level time provider using `tokio::time::Instant` and `tokio::time::sleep`.
pub struct SystemTimeProvider;

#[async_trait]
impl TimeProvider for SystemTimeProvider {
    fn now(&self) -> Instant {
        Instant::now()
    }

    async fn sleep(&self, duration: Duration) {
        tokio::time::sleep(duration).await;
    }
}

/// Mock time provider for testing, allowing manual control of time.
pub struct MockTimeProvider {
    current_time: Arc<Mutex<Instant>>,
}

impl MockTimeProvider {
    pub fn new() -> Self {
        Self {
            current_time: Arc::new(Mutex::new(Instant::now())),
        }
    }

    /// Advances the mock time by the given duration.
    pub fn advance_time(&self, duration: Duration) {
        let mut time = self.current_time.lock().unwrap();
        *time += duration;
    }
}

#[async_trait]
impl TimeProvider for MockTimeProvider {
    fn now(&self) -> Instant {
        *self.current_time.lock().unwrap()
    }

    async fn sleep(&self, duration: Duration) {
        // In a mock, sleeping doesn't actually block, but we can advance time
        // if the test needs to simulate passage of time without blocking.
        // For the circuit breaker, we'll explicitly call advance_time in tests.
        let mut time = self.current_time.lock().unwrap();
        *time += duration;
    }
}
