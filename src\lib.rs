// src/lib.rs

pub mod time_provider;

pub mod prelude;

pub mod alerting;
pub mod cli_handlers;
pub mod cli;
pub mod config;
pub mod constants;
pub mod contracts;
pub mod control;
pub mod data;
pub mod deployment;
pub mod error;
pub mod execution;
pub mod inspector;
pub mod logging;
pub mod math;
pub mod metrics;
pub mod mev;
pub mod network;
pub mod operational_modes;
pub mod reporting;
pub mod risk;
pub mod shared_types;
pub mod strategies;
pub mod token_registry;
pub mod tui;
pub mod validation;

use anyhow::Result;
use std::sync::Arc;
use tracing::{info, warn};
use crate::config::Config;
use crate::deployment::DeploymentConfig;
use crate::deployment::{
    DeploymentPhase,
    feature_flags::FeatureFlagManager,
    phased_deployment::PhasedDeploymentManager,
    rollback_manager::RollbackManager,
    health_checks::HealthCheckManager,
    monitoring::DeploymentMonitor,
};


/// Main application context with deployment capabilities
pub struct BasiliskApp {
    pub config: Arc<Config>,
    pub deployment_manager: Option<Arc<PhasedDeploymentManager>>,
    pub rollback_manager: Option<Arc<RollbackManager>>,
    pub health_checks: Arc<HealthCheckManager>,
    pub monitor: Arc<DeploymentMonitor>,
    pub feature_flags: Arc<FeatureFlagManager>,
}

impl BasiliskApp {
    /// Create a new Basilisk application instance
    pub fn new(config: Config) -> Result<Self> {
        let config = Arc::new(config);
        
        // Initialize deployment components
        let deployment_config = DeploymentConfig::default();
        let current_phase = deployment_config.current_phase;
        
        let feature_flags = Arc::new(FeatureFlagManager::new(current_phase));
        let health_checks = Arc::new(HealthCheckManager::new());
        let monitor = Arc::new(DeploymentMonitor::new());
        
        // Create deployment manager if in deployment mode
        let deployment_manager = if std::env::var("DEPLOYMENT_MODE").is_ok() {
            let manager = Arc::new(PhasedDeploymentManager::new(
                deployment_config.clone(),
                Arc::clone(&feature_flags),
                Arc::clone(&health_checks),
                Arc::clone(&monitor),
            ));
            Some(manager)
        } else {
            None
        };
        
        // Create rollback manager
        let rollback_manager = if deployment_manager.is_some() {
            let config_arc = Arc::new(tokio::sync::RwLock::new(deployment_config));
            let manager = Arc::new(RollbackManager::new(
                config_arc,
                Arc::clone(&feature_flags),
                Arc::clone(&health_checks),
                Arc::clone(&monitor),
            ));
            Some(manager)
        } else {
            None
        };
        
        info!("Basilisk application initialized with deployment capabilities");
        
        Ok(Self {
            config,
            deployment_manager,
            rollback_manager,
            health_checks,
            monitor,
            feature_flags,
        })
    }
    
    /// Initialize the application with deployment monitoring
    pub async fn initialize(&self) -> Result<()> {
        info!("Initializing Basilisk application");
        
        // Start health monitoring
        if let Err(e) = self.health_checks.run_all_checks().await {
            warn!("Initial health checks failed: {}", e);
        }
        
        // Start deployment monitoring if enabled
        if let Err(e) = self.monitor.start_monitoring().await {
            warn!("Failed to start deployment monitoring: {}", e);
        }
        
        info!("Basilisk application initialization completed");
        Ok(())
    }
    
    /// Check if a feature is enabled
    pub fn is_feature_enabled(&self, feature_name: &str) -> bool {
        self.feature_flags.is_enabled(feature_name)
    }
    
    /// Get current deployment phase
    pub fn current_deployment_phase(&self) -> DeploymentPhase {
        self.feature_flags.current_phase()
    }
    
    /// Deploy to a specific phase (if deployment manager is available)
    pub async fn deploy_to_phase(&self, target_phase: DeploymentPhase) -> Result<()> {
        if let Some(deployment_manager) = &self.deployment_manager {
            deployment_manager.deploy_to_phase(target_phase).await
        } else {
            Err(anyhow::anyhow!("Deployment manager not available"))
        }
    }
    
    /// Rollback to a specific phase (if rollback manager is available)
    pub async fn rollback_to_phase(&self, target_phase: DeploymentPhase) -> Result<()> {
        if let Some(rollback_manager) = &self.rollback_manager {
            rollback_manager.rollback_to_phase(target_phase).await
        } else {
            Err(anyhow::anyhow!("Rollback manager not available"))
        }
    }
    
    /// Get deployment health summary
    pub async fn get_deployment_health(&self) -> Result<deployment::monitoring::DeploymentHealthSummary> {
        self.monitor.get_deployment_health_summary().await
    }
    
    /// Shutdown the application gracefully
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down Basilisk application");
        
        // Stop monitoring
        self.monitor.stop_monitoring();
        
        info!("Basilisk application shutdown completed");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::*;
    use std::collections::HashMap;
    
    fn create_test_config() -> Config {
        Config {
            app_name: "test".to_string(),
            log_level: "info".to_string(),
            chains: HashMap::new(),
            strategy: StrategyConfig::default(),
            execution: ExecutionConfig::default(),
            secrets: Secrets::default(),
            scoring: ScoringConfig::default(),
            nats: NatsConfig::default(),
            aetheric_resonance: AethericResonanceEngineConfig::default(),
        }
    }
    
    #[tokio::test]
    async fn test_basilisk_app_creation() {
        let config = create_test_config();
        let app = BasiliskApp::new(config);
        
        assert!(app.is_ok());
        
        let app = app.unwrap();
        assert_eq!(app.current_deployment_phase(), DeploymentPhase::Development);
    }
    
    #[tokio::test]
    async fn test_feature_flag_integration() {
        let config = create_test_config();
        let app = BasiliskApp::new(config).unwrap();
        
        // Initially, no features should be enabled
        assert!(!app.is_feature_enabled("scoring_engine_weight_fix"));
        
        // Enable a feature flag
        app.feature_flags.enable_flag("scoring_engine_weight_fix").unwrap();
        assert!(app.is_feature_enabled("scoring_engine_weight_fix"));
    }
    
    #[tokio::test]
    async fn test_health_checks_integration() {
        let config = create_test_config();
        let app = BasiliskApp::new(config).unwrap();
        
        let health_summary = app.get_deployment_health().await;
        assert!(health_summary.is_ok());
        
        let summary = health_summary.unwrap();
        assert!(summary.overall_health >= 0.0 && summary.overall_health <= 1.0);
    }
}