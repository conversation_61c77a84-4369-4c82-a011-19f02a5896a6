// tests/audit_fixes/comprehensive_test_suite.rs

//! AUDIT-FIX: Comprehensive test suite for all fixed components - Task 6
//! This module provides systematic testing of all audit fixes to prevent regression

use basilisk_bot::math::vesica;
use basilisk_bot::math::geometry::RealGeometricScorer;
use basilisk_bot::strategies::scoring::ScoringEngine;
use basilisk_bot::data::fractal_analyzer::FractalAnalyzer;
use basilisk_bot::network::censorship_detector::CensorshipDetector;
use basilisk_bot::network::sequencer_monitor::{SequencerMonitor, SequencerHealth};
use basilisk_bot::shared_types::{GeometricScore, TemporalHarmonics, NetworkResonanceState};
use basilisk_bot::config::{ScoringConfig, ConfigValidationError};
use basilisk_bot::shared_types::graceful_degradation::{ComponentWithFallback, DegradationManager, ComponentHealth};
use basilisk_bot::metrics::performance_monitor::{PerformanceMonitor, PerformanceConfig};

use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use ethers::types::H256;
use std::collections::HashMap;
use tokio::time::Duration;

// Import all the new testing modules
pub mod validation_framework;
pub mod test_data_providers;
pub mod component_validators;
pub mod edge_case_tests;
pub mod performance_benchmarks;
pub mod regression_tests;
pub mod test_runner;

// Re-export the main test runner for easy access
pub use test_runner::{run_audit_fix_validation, AuditFixTestRunner, TestRunnerConfig};

/// AUDIT-FIX: Comprehensive test suite covering all major audit fixes
#[cfg(test)]
mod comprehensive_audit_tests {
    use super::*;

    /// Test all Vesica Piscis mathematical fixes
    #[test]
    fn test_vesica_piscis_comprehensive() {
        // Test positive price deviation
        let positive_result = vesica::calculate_amount_to_equalize(
            dec!(1000.0), // pool_a_reserves
            dec!(2000.0), // pool_b_reserves  
            dec!(0.1),    // 10% price deviation
        );
        assert!(positive_result > dec!(0.0), "Positive deviation should yield positive result");
        
        // Test negative price deviation (critical fix)
        let negative_result = vesica::calculate_amount_to_equalize(
            dec!(2000.0), // pool_a_reserves
            dec!(1000.0), // pool_b_reserves
            dec!(-0.1),   // -10% price deviation
        );
        assert!(negative_result > dec!(0.0), "Negative deviation should yield positive result (absolute value)");
        
        // Test zero deviation
        let zero_result = vesica::calculate_amount_to_equalize(
            dec!(1000.0),
            dec!(1000.0),
            dec!(0.0),
        );
        assert_eq!(zero_result, dec!(0.0), "Zero deviation should yield zero result");
        
        // Test mathematical symmetry
        let forward = vesica::calculate_amount_to_equalize(dec!(1000.0), dec!(1500.0), dec!(0.2));
        let reverse = vesica::calculate_amount_to_equalize(dec!(1500.0), dec!(1000.0), dec!(-0.2));
        assert!((forward - reverse).abs() < dec!(0.01), "Mathematical symmetry should hold");
    }

    /// Test geometric scoring with all components
    #[tokio::test]
    async fn test_geometric_scoring_comprehensive() {
        use basilisk_bot::data::oracle::MockPriceOracle;
        
        let mut mock_oracle = MockPriceOracle::new();
        mock_oracle.set_price(ethers::types::Address::zero(), dec!(2000.0));
        
        let scorer = RealGeometricScorer::new(mock_oracle);
        
        // Create test arbitrage path
        let path = create_test_arbitrage_path();
        let score = scorer.calculate_score(&path).await.unwrap();
        
        // Verify all four components are present (including vesica_piscis_depth)
        assert!(score.convexity_ratio >= dec!(0.0) && score.convexity_ratio <= dec!(1.0));
        assert!(score.liquidity_centroid_bias >= dec!(0.0) && score.liquidity_centroid_bias <= dec!(1.0));
        assert!(score.harmonic_path_score >= dec!(0.0) && score.harmonic_path_score <= dec!(1.0));
        assert!(score.vesica_piscis_depth >= dec!(0.0) && score.vesica_piscis_depth <= dec!(1.0));
        
        // Test educational narrative includes vesica piscis
        let narrative = score.to_educational_narrative();
        assert!(narrative.contains("Sacred geometry"), "Narrative should mention sacred geometry");
        assert!(narrative.contains("Vesica"), "Narrative should mention Vesica");
    }

    /// Test scoring engine with proper three-pillar synthesis
    #[tokio::test]
    async fn test_scoring_engine_three_pillar_synthesis() {
        let config = ScoringConfig {
            temporal_harmonics_weight: dec!(0.4),
            geometric_score_weight: dec!(0.3),
            network_resonance_weight: dec!(0.3),
            quality_ratio_floor: dec!(0.3),
            risk_aversion_k: dec!(0.5),
            regime_multiplier_retail_fomo: dec!(1.2),
            regime_multiplier_high_vol: dec!(0.8),
            regime_multiplier_calm: dec!(1.0),
            regime_multiplier_gas_war_penalty: dec!(0.5),
        };
        
        // Validate configuration
        assert!(config.validate().is_ok(), "Test configuration should be valid");
        
        let scoring_engine = create_test_scoring_engine(config);
        
        // Test with all pillar data present
        let temporal_harmonics = Some(TemporalHarmonics {
            dominant_cycles_minutes: vec![60, 240, 1440],
            market_rhythm_stability: 0.8,
            wavelet_features: vec![0.1, 0.2, 0.3],
        });
        
        let network_state = Some(NetworkResonanceState {
            sp_time_ms: 150.0,
            network_coherence_score: 0.9,
            is_shock_event: false,
            sp_time_20th_percentile: 100.0,
            sequencer_status: "Healthy".to_string(),
            censorship_detected: false,
        });
        
        let centrality_scores = std::sync::Arc::new(create_test_centrality_scores());
        let opportunity = create_test_opportunity();
        let market_regime = basilisk_bot::shared_types::MarketRegime::CalmOrderly;
        
        let score = scoring_engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &temporal_harmonics,
            &network_state,
            &centrality_scores,
        ).await;
        
        assert!(score > dec!(0.0), "Score should be positive with good inputs");
        
        // Test with missing pillar data (should use neutral fallbacks)
        let score_missing = scoring_engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &None, // Missing temporal harmonics
            &None, // Missing network state
            &centrality_scores,
        ).await;
        
        assert!(score_missing > dec!(0.0), "Score should still be positive with neutral fallbacks");
    }

    /// Test FFT implementation fixes
    #[tokio::test]
    async fn test_fft_implementation_fixes() {
        let analyzer = FractalAnalyzer::new();
        
        // Test with various data sizes to ensure buffer size fixes work
        let test_sizes = vec![10, 50, 100, 500, 1000];
        
        for size in test_sizes {
            let test_data: Vec<f64> = (0..size).map(|i| (i as f64 * 0.1).sin()).collect();
            
            let result = analyzer.calculate_temporal_harmonics(&test_data).await;
            assert!(result.is_ok(), "FFT should work with {} data points", size);
            
            let harmonics = result.unwrap();
            assert!(harmonics.market_rhythm_stability >= 0.0 && harmonics.market_rhythm_stability <= 1.0,
                   "Market rhythm stability should be in [0,1] range");
        }
    }

    /// Test censorship detection implementation
    #[test]
    fn test_censorship_detection_comprehensive() {
        let mut detector = CensorshipDetector::new();
        
        // Test normal inclusion pattern (no censorship)
        for i in 0..25 {
            let tx_hash = H256::random();
            detector.record_mempool_transaction(tx_hash, Some(20.0), Some(2.0));
            detector.record_block_inclusion(tx_hash, 1000 + i);
        }
        
        assert!(!detector.detect_censorship(), "Normal inclusion should not detect censorship");
        
        // Test poor inclusion pattern (potential censorship)
        let mut detector2 = CensorshipDetector::new();
        for i in 0..30 {
            let tx_hash = H256::random();
            detector2.record_mempool_transaction(tx_hash, Some(20.0), Some(2.0));
            
            // Only include 60% of transactions
            if i % 5 < 3 {
                detector2.record_block_inclusion(tx_hash, 2000 + i);
            }
        }
        
        assert!(detector2.detect_censorship(), "Poor inclusion rate should detect censorship");
        
        // Test inclusion statistics
        let stats = detector.get_inclusion_stats();
        assert!(stats.inclusion_rate > 0.8, "Good inclusion rate should be > 80%");
    }

    /// Test configuration validation comprehensive
    #[test]
    fn test_configuration_validation_comprehensive() {
        // Test valid configuration
        let valid_config = ScoringConfig {
            temporal_harmonics_weight: dec!(0.33),
            geometric_score_weight: dec!(0.33),
            network_resonance_weight: dec!(0.34),
            quality_ratio_floor: dec!(0.3),
            risk_aversion_k: dec!(0.5),
            regime_multiplier_retail_fomo: dec!(1.2),
            regime_multiplier_high_vol: dec!(0.8),
            regime_multiplier_calm: dec!(1.0),
            regime_multiplier_gas_war_penalty: dec!(0.5),
        };
        assert!(valid_config.validate().is_ok());
        
        // Test invalid weight sum
        let invalid_weights = ScoringConfig {
            temporal_harmonics_weight: dec!(0.5),
            geometric_score_weight: dec!(0.5),
            network_resonance_weight: dec!(0.5), // Sum = 1.5
            ..valid_config.clone()
        };
        assert!(invalid_weights.validate().is_err());
        
        // Test invalid risk aversion
        let invalid_risk = ScoringConfig {
            risk_aversion_k: dec!(3.0), // > 2.0 limit
            ..valid_config.clone()
        };
        assert!(invalid_risk.validate().is_err());
        
        // Test invalid regime multipliers
        let invalid_regime = ScoringConfig {
            regime_multiplier_retail_fomo: dec!(10.0), // > 5.0 limit
            ..valid_config.clone()
        };
        assert!(invalid_regime.validate().is_err());
    }

    /// Test graceful degradation patterns
    #[tokio::test]
    async fn test_graceful_degradation_comprehensive() {
        use basilisk_bot::shared_types::graceful_degradation::DegradationError;
        
        // Create test component that can fail
        struct TestComponent {
            fail_primary: bool,
            fail_fallback: bool,
        }
        
        #[async_trait::async_trait]
        impl ComponentWithFallback<String> for TestComponent {
            type Error = DegradationError;
            
            async fn primary_operation(&self) -> Result<String, Self::Error> {
                if self.fail_primary {
                    Err(DegradationError::PrimaryFailed("Test failure".to_string()))
                } else {
                    Ok("Primary success".to_string())
                }
            }
            
            async fn fallback_operation(&self) -> Result<String, Self::Error> {
                if self.fail_fallback {
                    Err(DegradationError::FallbackFailed("Fallback failure".to_string()))
                } else {
                    Ok("Fallback success".to_string())
                }
            }
            
            async fn health_check(&self) -> ComponentHealth {
                if self.fail_primary && self.fail_fallback {
                    ComponentHealth::Down("Both operations failing".to_string())
                } else if self.fail_primary {
                    ComponentHealth::Degraded("Primary failing".to_string())
                } else {
                    ComponentHealth::Healthy
                }
            }
            
            fn component_name(&self) -> &'static str {
                "TestComponent"
            }
        }
        
        // Test primary success
        let component = TestComponent { fail_primary: false, fail_fallback: false };
        let result = component.execute_with_fallback().await.unwrap();
        assert_eq!(result, "Primary success");
        
        // Test fallback success
        let component = TestComponent { fail_primary: true, fail_fallback: false };
        let result = component.execute_with_fallback().await.unwrap();
        assert_eq!(result, "Fallback success");
        
        // Test both fail
        let component = TestComponent { fail_primary: true, fail_fallback: true };
        let result = component.execute_with_fallback().await;
        assert!(result.is_err());
    }

    /// Test performance monitoring comprehensive
    #[tokio::test]
    async fn test_performance_monitoring_comprehensive() {
        let config = PerformanceConfig {
            max_samples: 100,
            warning_threshold_ms: 100,
            critical_threshold_ms: 500,
            enable_detailed_logging: true,
        };
        
        let monitor = PerformanceMonitor::new(config);
        
        // Test operation timing
        let timer = monitor.start_operation("test_operation");
        tokio::time::sleep(Duration::from_millis(50)).await;
        timer.complete(true).await;
        
        // Test metrics collection
        let metrics = monitor.get_operation_metrics("test_operation").await.unwrap();
        assert_eq!(metrics.total_executions, 1);
        assert_eq!(metrics.successful_executions, 1);
        assert!(metrics.avg_time_ms >= 45.0); // Allow some variance
        
        // Test performance summary
        monitor.record_operation("fast_op", Duration::from_millis(10), true).await;
        monitor.record_operation("slow_op", Duration::from_millis(200), true).await;
        
        let summary = monitor.get_performance_summary().await;
        assert_eq!(summary.total_operations, 3);
        assert!(summary.overall_success_rate > 0.9);
        
        // Test slow operation detection
        let slow_ops = monitor.get_slow_operations().await;
        assert!(slow_ops.len() >= 1, "Should detect slow operations");
    }

    // Helper functions for test setup
    fn create_test_arbitrage_path() -> basilisk_bot::shared_types::ArbitragePath {
        use basilisk_bot::shared_types::{ArbitragePath, ArbitragePool};
        use ethers::types::Address;
        
        ArbitragePath {
            pools: vec![
                ArbitragePool {
                    address: Address::zero(),
                    token_a: Address::zero(),
                    token_b: Address::zero(),
                    reserve_a: dec!(1000.0),
                    reserve_b: dec!(2000.0),
                    fee_bps: 30,
                },
                ArbitragePool {
                    address: Address::zero(),
                    token_a: Address::zero(),
                    token_b: Address::zero(),
                    reserve_a: dec!(1500.0),
                    reserve_b: dec!(1800.0),
                    fee_bps: 30,
                },
            ],
        }
    }
    
    fn create_test_scoring_engine(config: ScoringConfig) -> ScoringEngine {
        use basilisk_bot::data::oracle::MockPriceOracle;
        use basilisk_bot::math::geometry::MockGeometricScorer;
        
        let mock_oracle = MockPriceOracle::new();
        let mock_scorer = MockGeometricScorer::new();
        
        ScoringEngine::new(config, mock_scorer)
    }
    
    fn create_test_centrality_scores() -> HashMap<String, Decimal> {
        let mut scores = HashMap::new();
        scores.insert("WETH".to_string(), dec!(0.95));
        scores.insert("USDC".to_string(), dec!(0.90));
        scores.insert("USDT".to_string(), dec!(0.85));
        scores
    }
    
    fn create_test_opportunity() -> basilisk_bot::shared_types::Opportunity {
        use basilisk_bot::shared_types::{Opportunity, OpportunityBase};
        
        Opportunity::Swap {
            base: OpportunityBase {
                id: "test_opportunity".to_string(),
                estimated_gross_profit_usd: dec!(100.0),
                intersection_value_usd: dec!(50.0),
                associated_volatility: dec!(0.1),
                source_scanner: "test_scanner".to_string(),
                created_at: chrono::Utc::now(),
            },
            path: create_test_arbitrage_path(),
        }
    }
}

/// AUDIT-FIX: Unit tests for execution components
#[cfg(test)]
mod execution_component_tests {
    use super::*;
    use basilisk_bot::execution::{
        ExecutionManager, NonceManager, GasEstimator, CircuitBreaker,
        WalletManager, TransactionBuilder, PreExecutionValidator
    };
    use basilisk_bot::shared_types::{Opportunity, OpportunityBase, GasUrgency};
    use ethers::types::{Address, U256, TransactionRequest};
    use rust_decimal_macros::dec;

    /// Test NonceManager comprehensive functionality
    #[tokio::test]
    async fn test_nonce_manager_comprehensive() {
        let nonce_manager = NonceManager::new(Address::zero(), 1).await.unwrap();

        // Test initial nonce retrieval
        let initial_nonce = nonce_manager.get_next_nonce().await.unwrap();
        assert!(initial_nonce >= 0, "Initial nonce should be non-negative");

        // Test nonce increment
        let second_nonce = nonce_manager.get_next_nonce().await.unwrap();
        assert_eq!(second_nonce, initial_nonce + 1, "Nonce should increment");

        // Test pending transaction registration
        let tx_hash = ethers::types::H256::random();
        nonce_manager.register_pending_transaction(
            initial_nonce,
            tx_hash,
            U256::from(20_000_000_000u64), // 20 gwei
            None,
            None,
            TransactionRequest::default(),
        );

        // Test nonce confirmation
        nonce_manager.confirm_transaction(tx_hash).await.unwrap();

        // Test nonce replacement for stuck transactions
        let replacement_hash = ethers::types::H256::random();
        nonce_manager.replace_transaction(
            initial_nonce,
            replacement_hash,
            U256::from(25_000_000_000u64), // 25 gwei
        ).await.unwrap();
    }

    /// Test GasEstimator with various scenarios
    #[tokio::test]
    async fn test_gas_estimator_comprehensive() {
        let gas_estimator = GasEstimator::new(
            ethers::providers::Provider::try_from("https://mainnet.base.org").unwrap(),
            1, // chain_id
        );

        // Test different gas urgency levels
        let urgencies = vec![
            GasUrgency::Low,
            GasUrgency::Standard,
            GasUrgency::High,
            GasUrgency::Emergency,
        ];

        for urgency in urgencies {
            let gas_params = gas_estimator.estimate_gas_params(urgency).await;
            assert!(gas_params.is_ok(), "Gas estimation should succeed for {:?}", urgency);

            let params = gas_params.unwrap();
            assert!(params.gas_price > U256::zero(), "Gas price should be positive");
            assert!(params.gas_limit > U256::zero(), "Gas limit should be positive");
        }

        // Test gas price bounds
        let emergency_params = gas_estimator.estimate_gas_params(GasUrgency::Emergency).await.unwrap();
        let low_params = gas_estimator.estimate_gas_params(GasUrgency::Low).await.unwrap();

        assert!(emergency_params.gas_price >= low_params.gas_price,
               "Emergency gas should be >= low gas");
    }

    /// Test CircuitBreaker functionality
    #[tokio::test]
    async fn test_circuit_breaker_comprehensive() {
        let circuit_breaker = CircuitBreaker::new(
            5, // max_failures
            Duration::from_secs(60), // reset_timeout
        );

        // Test initial state
        assert!(!circuit_breaker.is_open().await, "Circuit breaker should start closed");

        // Test failure accumulation
        for i in 0..4 {
            circuit_breaker.record_failure().await;
            assert!(!circuit_breaker.is_open().await, "Circuit breaker should remain closed at {} failures", i + 1);
        }

        // Test circuit opening
        circuit_breaker.record_failure().await;
        assert!(circuit_breaker.is_open().await, "Circuit breaker should open after max failures");

        // Test success recording
        circuit_breaker.record_success().await;
        // Circuit should still be open due to recent failures

        // Test manual reset
        circuit_breaker.reset().await;
        assert!(!circuit_breaker.is_open().await, "Circuit breaker should close after reset");
    }

    /// Test WalletManager functionality
    #[test]
    fn test_wallet_manager_comprehensive() {
        // Test wallet creation with valid private key
        let private_key = "0x1234567890123456789012345678901234567890123456789012345678901234";
        let wallet_manager = WalletManager::new(private_key, 8453); // Base chain
        assert!(wallet_manager.is_ok(), "Wallet manager should initialize with valid key");

        let wallet = wallet_manager.unwrap();

        // Test address derivation
        let address = wallet.get_address();
        assert_ne!(address, Address::zero(), "Address should not be zero");

        // Test chain ID
        assert_eq!(wallet.get_chain_id(), 8453, "Chain ID should match");

        // Test invalid private key
        let invalid_wallet = WalletManager::new("invalid_key", 8453);
        assert!(invalid_wallet.is_err(), "Should fail with invalid private key");
    }
}

/// AUDIT-FIX: Unit tests for risk management components
#[cfg(test)]
mod risk_management_tests {
    use super::*;
    use basilisk_bot::risk::manager::RiskManager;
    use basilisk_bot::config::RiskConfig;
    use basilisk_bot::shared_types::RunMode;
    use rust_decimal_macros::dec;

    /// Test RiskManager comprehensive functionality
    #[tokio::test]
    async fn test_risk_manager_comprehensive() {
        let risk_config = RiskConfig {
            max_position_size_usd: dec!(1000.0),
            max_daily_loss: dec!(500.0),
            max_consecutive_failures: 5,
            kelly_fraction_cap: dec!(0.25),
            volatility_lookback_periods: 20,
            regime_risk_multipliers: std::collections::HashMap::new(),
        };

        let nats_client = basilisk_bot::nats::NatsClient::new("nats://localhost:4222").await.unwrap();
        let risk_manager = RiskManager::new(
            nats_client,
            risk_config,
            RunMode::Live,
        ).await.unwrap();

        // Test initial state
        assert!(!risk_manager.is_trading_halted().await, "Trading should not be halted initially");

        // Test trade acceptance
        assert!(risk_manager.is_trade_acceptable(dec!(100.0)).await, "Small trade should be acceptable");
        assert!(!risk_manager.is_trade_acceptable(dec!(2000.0)).await, "Large trade should be rejected");

        // Test PnL tracking
        risk_manager.update_pnl(dec!(-100.0), "test_trade").await;
        risk_manager.update_pnl(dec!(-200.0), "test_trade_2").await;

        // Test consecutive failure tracking
        for i in 0..4 {
            risk_manager.record_trade_failure().await;
            assert!(!risk_manager.is_trading_halted().await, "Should not halt at {} failures", i + 1);
        }

        // Test circuit breaker activation
        risk_manager.record_trade_failure().await;
        assert!(risk_manager.is_trading_halted().await, "Should halt after max failures");

        // Test circuit breaker reset
        risk_manager.reset_circuit_breaker().await;
        assert!(!risk_manager.is_trading_halted().await, "Should resume after reset");
    }

    /// Test Kelly Criterion calculations
    #[test]
    fn test_kelly_criterion_comprehensive() {
        use basilisk_bot::risk::kelly::KellyCriterion;

        let kelly = KellyCriterion::new(dec!(0.25)); // 25% cap

        // Test with profitable scenario
        let win_rate = dec!(0.6); // 60% win rate
        let avg_win = dec!(150.0);
        let avg_loss = dec!(100.0);

        let fraction = kelly.calculate_optimal_fraction(win_rate, avg_win, avg_loss);
        assert!(fraction > dec!(0.0), "Kelly fraction should be positive for profitable scenario");
        assert!(fraction <= dec!(0.25), "Kelly fraction should respect cap");

        // Test with unprofitable scenario
        let bad_win_rate = dec!(0.3); // 30% win rate
        let fraction_bad = kelly.calculate_optimal_fraction(bad_win_rate, avg_win, avg_loss);
        assert!(fraction_bad <= dec!(0.0), "Kelly fraction should be zero or negative for unprofitable scenario");

        // Test edge cases
        let zero_fraction = kelly.calculate_optimal_fraction(dec!(0.0), avg_win, avg_loss);
        assert_eq!(zero_fraction, dec!(0.0), "Zero win rate should give zero fraction");

        let perfect_fraction = kelly.calculate_optimal_fraction(dec!(1.0), avg_win, dec!(0.0));
        assert_eq!(perfect_fraction, dec!(0.25), "Perfect scenario should give capped fraction");
    }
}

/// AUDIT-FIX: Integration tests for component interactions
#[cfg(test)]
mod integration_tests {
    use super::*;

    /// Test end-to-end scoring pipeline
    #[tokio::test]
    async fn test_end_to_end_scoring_pipeline() {
        // This test verifies that all components work together correctly
        // from opportunity detection through final scoring
        
        let config = ScoringConfig::default();
        let scoring_engine = create_test_scoring_engine(config);
        
        // Create realistic test data
        let opportunity = create_test_opportunity();
        let market_regime = basilisk_bot::shared_types::MarketRegime::CalmOrderly;
        
        // Test with complete data
        let temporal_harmonics = Some(TemporalHarmonics {
            dominant_cycles_minutes: vec![60, 240],
            market_rhythm_stability: 0.75,
            wavelet_features: vec![0.1, 0.2],
        });
        
        let network_state = Some(NetworkResonanceState {
            sp_time_ms: 120.0,
            network_coherence_score: 0.85,
            is_shock_event: false,
            sp_time_20th_percentile: 90.0,
            sequencer_status: "Healthy".to_string(),
            censorship_detected: false,
        });
        
        let centrality_scores = std::sync::Arc::new(create_test_centrality_scores());
        
        let final_score = scoring_engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &temporal_harmonics,
            &network_state,
            &centrality_scores,
        ).await;
        
        // Verify the score is reasonable
        assert!(final_score > dec!(0.0), "Final score should be positive");
        assert!(final_score < dec!(1000.0), "Final score should be reasonable");
        
        // Test with degraded data (missing pillars)
        let degraded_score = scoring_engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &None, // Missing temporal data
            &network_state,
            &centrality_scores,
        ).await;
        
        assert!(degraded_score > dec!(0.0), "Should handle missing pillar data gracefully");
    }
    
    /// Test system behavior under stress conditions
    #[tokio::test]
    async fn test_system_stress_conditions() {
        let monitor = PerformanceMonitor::new(PerformanceConfig::default());
        let degradation_manager = DegradationManager::new(
            basilisk_bot::shared_types::graceful_degradation::DegradationConfig::default()
        );
        
        // Simulate high load
        for i in 0..100 {
            let timer = monitor.start_operation("stress_test");
            
            // Simulate varying operation times
            let delay = if i % 10 == 0 { 200 } else { 50 }; // Some slow operations
            tokio::time::sleep(Duration::from_millis(delay)).await;
            
            timer.complete(i % 20 != 0).await; // 95% success rate
        }
        
        // Verify system handled stress well
        let summary = monitor.get_performance_summary().await;
        assert!(summary.total_operations == 100);
        assert!(summary.overall_success_rate > 0.9);
        
        // Test degradation manager under stress
        degradation_manager.register_component("stress_component".to_string(), ComponentHealth::Healthy).await;
        degradation_manager.update_component_health("stress_component".to_string(), ComponentHealth::Degraded("High load".to_string())).await;
        
        let system_health = degradation_manager.get_system_health().await;
        assert!(matches!(system_health, basilisk_bot::shared_types::graceful_degradation::SystemHealth::Degraded(_)));
    }
    
    // Helper functions (same as above)
    fn create_test_scoring_engine(config: ScoringConfig) -> ScoringEngine {
        use basilisk_bot::data::oracle::MockPriceOracle;
        use basilisk_bot::math::geometry::MockGeometricScorer;
        
        let mock_oracle = MockPriceOracle::new();
        let mock_scorer = MockGeometricScorer::new();
        
        ScoringEngine::new(config, mock_scorer)
    }
    
    fn create_test_centrality_scores() -> HashMap<String, Decimal> {
        let mut scores = HashMap::new();
        scores.insert("WETH".to_string(), dec!(0.95));
        scores.insert("USDC".to_string(), dec!(0.90));
        scores.insert("USDT".to_string(), dec!(0.85));
        scores
    }
    
    fn create_test_opportunity() -> basilisk_bot::shared_types::Opportunity {
        use basilisk_bot::shared_types::{Opportunity, OpportunityBase, ArbitragePath, ArbitragePool};
        use ethers::types::Address;
        
        Opportunity::Swap {
            base: OpportunityBase {
                id: "integration_test_opportunity".to_string(),
                estimated_gross_profit_usd: dec!(150.0),
                intersection_value_usd: dec!(75.0),
                associated_volatility: dec!(0.15),
                source_scanner: "integration_test_scanner".to_string(),
                created_at: chrono::Utc::now(),
            },
            path: ArbitragePath {
                pools: vec![
                    ArbitragePool {
                        address: Address::zero(),
                        token_a: Address::zero(),
                        token_b: Address::zero(),
                        reserve_a: dec!(2000.0),
                        reserve_b: dec!(4000.0),
                        fee_bps: 30,
                    },
                ],
            },
        }
    }
}