// src/validation/opportunity_validator/test_integration.rs

//! Integration test for opportunity validation functionality

#[cfg(test)]
mod tests {
    use super::super::*;
    use crate::validation::{TestDataProvider, MarketConditions, NetworkCongestionLevel, LiquidityDistribution};
    use crate::shared_types::MarketRegime;
    use rust_decimal_macros::dec;
    use std::sync::Arc;
    use std::time::Duration;

    #[tokio::test]
    async fn test_opportunity_validator_creation() {
        let test_data_provider = Arc::new(TestDataProvider::new().unwrap());
        let validator = OpportunityValidator::new(test_data_provider);
        assert!(validator.is_ok());
    }

    #[tokio::test]
    async fn test_opportunity_detection_validation() {
        let test_data_provider = Arc::new(TestDataProvider::new().unwrap());
        let validator = OpportunityValidator::new(test_data_provider).unwrap();
        
        let market_conditions = MarketConditions {
            regime: MarketRegime::CalmOrderly,
            volatility: dec!(0.1),
            gas_price_gwei: dec!(20.0),
            network_congestion: NetworkCongestionLevel::Low,
            temporal_harmonics: None,
            network_resonance: None,
            liquidity_distribution: LiquidityDistribution::Concentrated,
        };
        
        let result = validator.validate_opportunity_detection(&market_conditions).await;
        assert!(result.is_ok());
        
        let metrics = result.unwrap();
        assert!(metrics.quality_metrics.precision >= 0.0);
        assert!(metrics.quality_metrics.recall >= 0.0);
        assert!(metrics.quality_metrics.f1_score >= 0.0);
    }

    #[tokio::test]
    async fn test_scanner_performance_validation() {
        let test_data_provider = Arc::new(TestDataProvider::new().unwrap());
        let validator = OpportunityValidator::new(test_data_provider).unwrap();
        
        let result = validator.validate_scanner_performance(
            "SwapScanner", 
            Duration::from_secs(1)
        ).await;
        
        assert!(result.is_ok());
        
        let metrics = result.unwrap();
        assert_eq!(metrics.scanner_name, "SwapScanner");
        assert!(metrics.processing_rate >= 0.0);
        assert!(metrics.average_latency_ms >= 0.0);
    }

    #[tokio::test]
    async fn test_swap_scanner_validator() {
        let validator = SwapScannerValidator::new().unwrap();
        assert_eq!(validator.scanner_name(), "SwapScanner");
        
        // Test performance validation
        let result = validator.validate_performance(Duration::from_millis(100)).await;
        assert!(result.is_ok());
        
        let metrics = result.unwrap();
        assert_eq!(metrics.scanner_name, "SwapScanner");
        assert!(metrics.opportunities_processed > 0);
    }

    #[tokio::test]
    async fn test_mempool_scanner_validator() {
        let validator = MempoolScannerValidator::new().unwrap();
        assert_eq!(validator.scanner_name(), "MempoolScanner");
        
        // Test performance validation
        let result = validator.validate_performance(Duration::from_millis(100)).await;
        assert!(result.is_ok());
        
        let metrics = result.unwrap();
        assert_eq!(metrics.scanner_name, "MempoolScanner");
        assert!(metrics.opportunities_processed > 0);
    }

    #[tokio::test]
    async fn test_gaze_scanner_validator() {
        let validator = GazeScannerValidator::new().unwrap();
        assert_eq!(validator.scanner_name(), "GazeScanner");
        
        // Test performance validation
        let result = validator.validate_performance(Duration::from_millis(100)).await;
        assert!(result.is_ok());
        
        let metrics = result.unwrap();
        assert_eq!(metrics.scanner_name, "GazeScanner");
        assert!(metrics.opportunities_processed > 0);
    }
}