// src/validation/store.rs

//! Storage and persistence for validation results

use crate::validation::ValidationFrameworkResult;
use crate::validation::results::{ValidationResult, ValidationResultSet};
use crate::validation::types::{StorageConfig, ValidationMetrics};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use tokio::fs;
use tracing::{debug, error, info, warn};

/// In-memory and persistent storage for validation results
#[derive(Debug)]
pub struct ResultsStore {
    /// Configuration for storage behavior
    config: StorageConfig,
    /// In-memory storage of recent results
    memory_store: Arc<RwLock<MemoryStore>>,
    /// Metrics tracking
    metrics: Arc<RwLock<ValidationMetrics>>,
}

/// In-memory storage structure
#[derive(Debug, Default)]
struct MemoryStore {
    /// Individual validation results by test ID
    results: HashMap<String, ValidationResultEntry>,
    /// Validation result sets by suite name
    result_sets: HashMap<String, ValidationResultSetEntry>,
    /// Insertion order for LRU eviction
    insertion_order: Vec<String>,
}

/// Entry in the memory store with metadata
#[derive(Debug, Clone)]
struct ValidationResultEntry {
    result: serde_json::Value,
    timestamp: DateTime<Utc>,
    size_bytes: usize,
}

/// Entry for validation result sets
#[derive(Debug, Clone)]
struct ValidationResultSetEntry {
    result_set: serde_json::Value,
    timestamp: DateTime<Utc>,
    size_bytes: usize,
}

impl ResultsStore {
    /// Create a new results store with the given configuration
    pub fn new(config: StorageConfig) -> ValidationFrameworkResult<Self> {
        let store = Self {
            config,
            memory_store: Arc::new(RwLock::new(MemoryStore::default())),
            metrics: Arc::new(RwLock::new(ValidationMetrics::default())),
        };

        // Create storage directory if persistence is enabled
        if store.config.persist_to_disk {
            if let Err(e) = std::fs::create_dir_all(&store.config.storage_directory) {
                warn!("Failed to create storage directory: {}", e);
            }
        }

        info!("ResultsStore initialized with config: {:?}", store.config);
        Ok(store)
    }

    /// Store a validation result
    pub async fn store_result<T: Serialize + for<'de> Deserialize<'de>>(
        &self,
        result: &ValidationResult<T>,
    ) -> ValidationFrameworkResult<()> {
        // Update metrics
        {
            let mut metrics = self.metrics.write().unwrap();
            metrics.update_with_result(result);
        }

        // Serialize the result
        let serialized = serde_json::to_value(result)
            .map_err(|e| crate::error::BasiliskError::Serialization(e))?;
        
        let size_bytes = serialized.to_string().len();

        // Store in memory
        {
            let mut memory = self.memory_store.write().unwrap();
            
            let entry = ValidationResultEntry {
                result: serialized.clone(),
                timestamp: result.timestamp,
                size_bytes,
            };

            memory.results.insert(result.test_id.clone(), entry);
            memory.insertion_order.push(result.test_id.clone());

            // Enforce memory limits
            self.enforce_memory_limits(&mut memory);
        }

        // Persist to disk if enabled
        if self.config.persist_to_disk {
            self.persist_result_to_disk(result, &serialized).await?;
        }

        debug!("Stored validation result: {} ({})", result.test_id, result.status);
        Ok(())
    }

    /// Store a validation result set
    pub async fn store_result_set<T: Serialize + for<'de> Deserialize<'de>>(
        &self,
        result_set: &ValidationResultSet<T>,
    ) -> ValidationFrameworkResult<()> {
        // Serialize the result set
        let serialized = serde_json::to_value(result_set)
            .map_err(|e| crate::error::BasiliskError::Serialization(e))?;
        
        let size_bytes = serialized.to_string().len();

        // Store in memory
        {
            let mut memory = self.memory_store.write().unwrap();
            
            let entry = ValidationResultSetEntry {
                result_set: serialized.clone(),
                timestamp: result_set.timestamp,
                size_bytes,
            };

            memory.result_sets.insert(result_set.suite_name.clone(), entry);
        }

        // Persist to disk if enabled
        if self.config.persist_to_disk {
            self.persist_result_set_to_disk(result_set, &serialized).await?;
        }

        debug!("Stored validation result set: {}", result_set.suite_name);
        Ok(())
    }

    /// Retrieve a validation result by test ID
    pub async fn get_result<T: for<'de> Deserialize<'de>>(
        &self,
        test_id: &str,
    ) -> ValidationFrameworkResult<Option<ValidationResult<T>>> {
        // Try memory first
        {
            let memory = self.memory_store.read().unwrap();
            if let Some(entry) = memory.results.get(test_id) {
                let result: ValidationResult<T> = serde_json::from_value(entry.result.clone())
                    .map_err(|e| crate::error::BasiliskError::Serialization(e))?;
                return Ok(Some(result));
            }
        }

        // Try disk if persistence is enabled
        if self.config.persist_to_disk {
            return self.load_result_from_disk(test_id).await;
        }

        Ok(None)
    }

    /// Retrieve a validation result set by suite name
    pub async fn get_result_set<T: for<'de> Deserialize<'de>>(
        &self,
        suite_name: &str,
    ) -> ValidationFrameworkResult<Option<ValidationResultSet<T>>> {
        // Try memory first
        {
            let memory = self.memory_store.read().unwrap();
            if let Some(entry) = memory.result_sets.get(suite_name) {
                let result_set: ValidationResultSet<T> = serde_json::from_value(entry.result_set.clone())
                    .map_err(|e| crate::error::BasiliskError::Serialization(e))?;
                return Ok(Some(result_set));
            }
        }

        // Try disk if persistence is enabled
        if self.config.persist_to_disk {
            return self.load_result_set_from_disk(suite_name).await;
        }

        Ok(None)
    }

    /// Get all test IDs currently stored
    pub fn get_all_test_ids(&self) -> Vec<String> {
        let memory = self.memory_store.read().unwrap();
        memory.results.keys().cloned().collect()
    }

    /// Get all suite names currently stored
    pub fn get_all_suite_names(&self) -> Vec<String> {
        let memory = self.memory_store.read().unwrap();
        memory.result_sets.keys().cloned().collect()
    }

    /// Get current validation metrics
    pub fn get_metrics(&self) -> ValidationMetrics {
        let metrics = self.metrics.read().unwrap();
        metrics.clone()
    }

    /// Clear all stored results
    pub async fn clear_all(&self) -> ValidationFrameworkResult<()> {
        // Clear memory
        {
            let mut memory = self.memory_store.write().unwrap();
            memory.results.clear();
            memory.result_sets.clear();
            memory.insertion_order.clear();
        }

        // Clear disk storage if enabled
        if self.config.persist_to_disk {
            let storage_path = Path::new(&self.config.storage_directory);
            if storage_path.exists() {
                fs::remove_dir_all(storage_path).await
                    .map_err(|e| crate::error::BasiliskError::Io(e))?;
                fs::create_dir_all(storage_path).await
                    .map_err(|e| crate::error::BasiliskError::Io(e))?;
            }
        }

        info!("Cleared all validation results");
        Ok(())
    }

    /// Get storage statistics
    pub fn get_storage_stats(&self) -> StorageStats {
        let memory = self.memory_store.read().unwrap();
        let metrics = self.metrics.read().unwrap();

        let memory_usage_bytes: usize = memory.results.values()
            .map(|entry| entry.size_bytes)
            .sum::<usize>() + memory.result_sets.values()
            .map(|entry| entry.size_bytes)
            .sum::<usize>();

        StorageStats {
            total_results: memory.results.len(),
            total_result_sets: memory.result_sets.len(),
            memory_usage_bytes,
            memory_usage_mb: memory_usage_bytes as f64 / 1024.0 / 1024.0,
            disk_persistence_enabled: self.config.persist_to_disk,
            storage_directory: self.config.storage_directory.clone(),
            validation_metrics: metrics.clone(),
        }
    }

    /// Enforce memory limits by evicting oldest entries
    fn enforce_memory_limits(&self, memory: &mut MemoryStore) {
        while memory.results.len() > self.config.max_in_memory_results {
            if let Some(oldest_id) = memory.insertion_order.first().cloned() {
                memory.results.remove(&oldest_id);
                memory.insertion_order.remove(0);
                debug!("Evicted validation result from memory: {}", oldest_id);
            } else {
                break;
            }
        }
    }

    /// Persist a validation result to disk
    async fn persist_result_to_disk<T: Serialize>(
        &self,
        result: &ValidationResult<T>,
        serialized: &serde_json::Value,
    ) -> ValidationFrameworkResult<()> {
        let file_path = self.get_result_file_path(&result.test_id);
        
        let content = if self.config.compress_results {
            // In a real implementation, you would use compression here
            serde_json::to_string_pretty(serialized)
                .map_err(|e| crate::error::BasiliskError::Serialization(e))?
        } else {
            serde_json::to_string_pretty(serialized)
                .map_err(|e| crate::error::BasiliskError::Serialization(e))?
        };

        fs::write(&file_path, content).await
            .map_err(|e| crate::error::BasiliskError::Io(e))?;

        debug!("Persisted validation result to disk: {}", file_path.display());
        Ok(())
    }

    /// Persist a validation result set to disk
    async fn persist_result_set_to_disk<T: Serialize>(
        &self,
        result_set: &ValidationResultSet<T>,
        serialized: &serde_json::Value,
    ) -> ValidationFrameworkResult<()> {
        let file_path = self.get_result_set_file_path(&result_set.suite_name);
        
        let content = if self.config.compress_results {
            // In a real implementation, you would use compression here
            serde_json::to_string_pretty(serialized)
                .map_err(|e| crate::error::BasiliskError::Serialization(e))?
        } else {
            serde_json::to_string_pretty(serialized)
                .map_err(|e| crate::error::BasiliskError::Serialization(e))?
        };

        fs::write(&file_path, content).await
            .map_err(|e| crate::error::BasiliskError::Io(e))?;

        debug!("Persisted validation result set to disk: {}", file_path.display());
        Ok(())
    }

    /// Load a validation result from disk
    async fn load_result_from_disk<T: for<'de> Deserialize<'de>>(
        &self,
        test_id: &str,
    ) -> ValidationFrameworkResult<Option<ValidationResult<T>>> {
        let file_path = self.get_result_file_path(test_id);
        
        if !file_path.exists() {
            return Ok(None);
        }

        let content = fs::read_to_string(&file_path).await
            .map_err(|e| crate::error::BasiliskError::Io(e))?;

        let result: ValidationResult<T> = serde_json::from_str(&content)
            .map_err(|e| crate::error::BasiliskError::Serialization(e))?;

        debug!("Loaded validation result from disk: {}", file_path.display());
        Ok(Some(result))
    }

    /// Load a validation result set from disk
    async fn load_result_set_from_disk<T: for<'de> Deserialize<'de>>(
        &self,
        suite_name: &str,
    ) -> ValidationFrameworkResult<Option<ValidationResultSet<T>>> {
        let file_path = self.get_result_set_file_path(suite_name);
        
        if !file_path.exists() {
            return Ok(None);
        }

        let content = fs::read_to_string(&file_path).await
            .map_err(|e| crate::error::BasiliskError::Io(e))?;

        let result_set: ValidationResultSet<T> = serde_json::from_str(&content)
            .map_err(|e| crate::error::BasiliskError::Serialization(e))?;

        debug!("Loaded validation result set from disk: {}", file_path.display());
        Ok(Some(result_set))
    }

    /// Get file path for a validation result
    fn get_result_file_path(&self, test_id: &str) -> PathBuf {
        Path::new(&self.config.storage_directory)
            .join("results")
            .join(format!("{}.json", test_id))
    }

    /// Get file path for a validation result set
    fn get_result_set_file_path(&self, suite_name: &str) -> PathBuf {
        Path::new(&self.config.storage_directory)
            .join("result_sets")
            .join(format!("{}.json", suite_name))
    }
}

/// Statistics about the results store
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageStats {
    pub total_results: usize,
    pub total_result_sets: usize,
    pub memory_usage_bytes: usize,
    pub memory_usage_mb: f64,
    pub disk_persistence_enabled: bool,
    pub storage_directory: String,
    pub validation_metrics: ValidationMetrics,
}

impl std::fmt::Display for StorageStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "Storage Stats:\n\
             - Results: {} | Result Sets: {}\n\
             - Memory Usage: {:.2} MB ({} bytes)\n\
             - Disk Persistence: {} ({})\n\
             - Success Rate: {:.1}% | Total Validations: {}",
            self.total_results,
            self.total_result_sets,
            self.memory_usage_mb,
            self.memory_usage_bytes,
            if self.disk_persistence_enabled { "Enabled" } else { "Disabled" },
            self.storage_directory,
            self.validation_metrics.success_rate * 100.0,
            self.validation_metrics.total_validations
        )
    }
}