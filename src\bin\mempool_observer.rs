use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};
use futures_util::{StreamExt, SinkExt};
use url::Url;
use serde_json::json;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use std::time::{SystemTime, UNIX_EPOCH};
use anyhow::Result;

use basilisk_bot::shared_types::{TimeToInclusionSample, NatsTopics};
use async_nats::Client;

/// Mempool Observer - Time-to-Inclusion Analysis
/// Monitors mempool transactions and correlates with block inclusion for TTI metrics
#[tokio::main]
async fn main() -> Result<()> {
    println!("🔍 Mempool Observer - Time-to-Inclusion Analysis");
    
    // Load configuration
    let config = basilisk_bot::config::Config::load()?;
    
    // Get WebSocket endpoints for mempool monitoring
    let mempool_endpoints = get_mempool_endpoints(&config)?;
    
    println!("Starting TTI analysis with {} endpoints", mempool_endpoints.len());
    for endpoint in &mempool_endpoints {
        println!("  - {}", endpoint);
    }

    let nats_client = async_nats::connect("nats://localhost:4222").await?;
    let nats_client_arc = Arc::new(nats_client);

    // Mempool transaction tracking: tx_hash -> first_seen_timestamp
    let mempool_tracker: Arc<Mutex<HashMap<String, u128>>> = Arc::new(Mutex::new(HashMap::new()));

    // Subscribe to block data to correlate inclusions
    let block_subscriber = nats_client_arc.subscribe(NatsTopics::DATA_NETWORK_PROPAGATION).await?;
    let block_nats = nats_client_arc.clone();
    let block_mempool_tracker = mempool_tracker.clone();
    
    tokio::spawn(async move {
        process_block_inclusions(block_subscriber, block_nats, block_mempool_tracker).await;
    });

    // Start mempool listeners for each endpoint
    for endpoint in mempool_endpoints {
        let nats_client_clone = nats_client_arc.clone();
        let mempool_tracker_clone = mempool_tracker.clone();
        let endpoint_clone = endpoint.clone();
        
        tokio::spawn(async move {
            loop {
                match connect_and_listen_mempool(&endpoint_clone, nats_client_clone.clone(), mempool_tracker_clone.clone()).await {
                    Ok(_) => {
                        println!("Mempool connection to {} ended normally", endpoint_clone);
                        break;
                    },
                    Err(e) => {
                        eprintln!("Error connecting to mempool {}: {}", endpoint_clone, e);
                        eprintln!("Retrying mempool connection to {} in 10 seconds...", endpoint_clone);
                        tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
                    }
                }
            }
        });
    }

    // Keep the main thread alive
    tokio::signal::ctrl_c().await?;
    println!("Shutting down mempool observer.");

    Ok(())
}

async fn connect_and_listen_mempool(
    endpoint: &str,
    nats_client: Arc<Client>,
    mempool_tracker: Arc<Mutex<HashMap<String, u128>>>,
) -> Result<()> {
    println!("Attempting to connect to mempool endpoint: {}", endpoint);
    
    let url = Url::parse(endpoint)?;
    let (ws_stream, response) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    println!("Connected to mempool {} (status: {})", endpoint, response.status());

    // Subscribe to pending transactions
    let subscribe_message = json!({
        "jsonrpc": "2.0",
        "id": 1,
        "method": "eth_subscribe",
        "params": ["newPendingTransactions"]
    }).to_string();

    write.send(Message::Text(subscribe_message)).await?;

    while let Some(message) = read.next().await {
        match message? {
            Message::Text(text) => {
                if let Ok(json_msg) = serde_json::from_str::<serde_json::Value>(&text) {
                    if let Some(params) = json_msg.get("params") {
                        if let Some(result) = params.get("result") {
                            if let Some(tx_hash) = result.as_str() {
                                let timestamp_nanos = SystemTime::now()
                                    .duration_since(UNIX_EPOCH)
                                    .unwrap_or_default()
                                    .as_nanos() as u128;

                                // Track first time we see this transaction
                                let mut tracker = mempool_tracker.lock().await;
                                tracker.entry(tx_hash.to_string()).or_insert(timestamp_nanos);
                                
                                // Clean up old entries (older than 10 minutes)
                                let cutoff = timestamp_nanos - (10 * 60 * 1_000_000_000); // 10 minutes in nanos
                                tracker.retain(|_, &mut first_seen| first_seen > cutoff);
                            }
                        }
                    }
                }
            },
            Message::Ping(p) => { write.send(Message::Pong(p)).await?; },
            _ => {},
        }
    }

    Ok(())
}

async fn process_block_inclusions(
    mut block_subscriber: async_nats::Subscriber,
    nats_client: Arc<Client>,
    mempool_tracker: Arc<Mutex<HashMap<String, u128>>>,
) {
    println!("🔗 Processing block inclusions for TTI analysis");
    
    while let Some(message) = block_subscriber.next().await {
        if let Ok(block_sample) = serde_json::from_slice::<basilisk_bot::shared_types::BlockPropagationSample>(&message.payload) {
            // For each block, we need to get the full block data to see included transactions
            // This is a simplified version - in production, you'd fetch full block data
            let block_timestamp = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u128;
            
            // TODO: Fetch actual block data and transaction list
            // For now, we'll simulate some TTI samples
            let mut tracker = mempool_tracker.lock().await;
            
            // In a real implementation, you'd:
            // 1. Fetch the full block data using eth_getBlockByNumber
            // 2. Extract all transaction hashes from the block
            // 3. For each tx_hash in the block, check if it exists in mempool_tracker
            // 4. Calculate TTI = block_timestamp - mempool_first_seen
            // 5. Create TimeToInclusionSample and publish to NATS
            
            println!("📊 Block {} processed, mempool tracking {} transactions", 
                block_sample.block_number, tracker.len());
        }
    }
}

fn get_mempool_endpoints(config: &basilisk_bot::config::Config) -> Result<Vec<String>> {
    let mut endpoints = Vec::new();
    
    // Determine mempool endpoints based on active chain ID
    let chain_id = config.chains.keys().next().cloned().unwrap_or(8453);
    match chain_id {
        8453 => {
            // Base Mainnet - use endpoints that support mempool subscriptions
            endpoints.extend_from_slice(&[
                "wss://base.publicnode.com".to_string(),
                "wss://rpc.ankr.com/base/ws".to_string(),
            ]);
        },
        84532 => {
            // Base Sepolia Testnet
            endpoints.extend_from_slice(&[
                "wss://sepolia.base.org".to_string(),
            ]);
        },
        1337 | 31337 => {
            // Local development (Anvil/Hardhat)
            endpoints.push("ws://127.0.0.1:8545".to_string());
        },
        _ => {
            // Fallback to configured WebSocket URL
            if let Some(chain_config) = config.chains.get(&chain_id) {
                if !chain_config.rpc_url.is_empty() {
                    endpoints.push(chain_config.rpc_url.clone());
                } else {
                    return Err(anyhow::anyhow!("No mempool endpoints configured for chain ID {}", chain_id));
                }
            }
        }
    }
    
    Ok(endpoints)
}