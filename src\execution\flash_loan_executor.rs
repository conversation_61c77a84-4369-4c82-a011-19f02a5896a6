// MISSION: FlashLoanExecutor - Business Logic for Cross-Chain Flash Loan Arbitrage
// WHY: Encapsulate the specific recipe for preparing Stargate-powered cross-chain trades
// HOW: Bridge between high-level opportunity data and low-level TransactionBuilder

use anyhow::Result;
use ethers::types::{Address, TransactionRequest, U256, Bytes};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::sync::Arc;
use tracing::{debug, info, warn};
use crate::error::BasiliskError;

use super::transaction_builder::TransactionBuilder;
use crate::shared_types::Opportunity;

/// Configuration for flash loan execution
#[derive(Debug, Clone)]
pub struct FlashLoanExecutorConfig {
    /// Stargate Compass contract address on Base
    pub compass_address: Address,
    /// Default DEX router on Degen Chain
    pub degen_router_address: Address,
    /// USDC address on Base (for flash loans)
    pub base_usdc_address: Address,
    /// USDC address on Degen Chain (for swaps)
    pub degen_usdc_address: Address,
    /// Maximum flash loan amount (safety limit)
    pub max_flash_loan_amount: U256,
    /// Flash loan fee percentage (Aave typically 0.09%)
    pub flash_loan_fee_percentage: Decimal,
}

impl Default for FlashLoanExecutorConfig {
    fn default() -> Self {
        Self {
            compass_address: Address::zero(), // Must be configured via config
            // DegenSwap Router V2 address on Degen chain
            degen_router_address: "0x2Da10A1e27bF85cEdD8FFb1AbBe97e53391C0295".parse().expect("Invalid degen router address"),
            base_usdc_address: "0x833589fCd6EDB6e08F4c7C32D4F71aD50eDB05fC".parse().expect("Invalid base USDC address"), // USDC on Base
            degen_usdc_address: "0x4ed4E862860beD51a9570b96d89aF5E1B0Efefed".parse().expect("Invalid degen USDC address"), // USDC on Degen
            max_flash_loan_amount: U256::from(1_000_000_000_000u64), // 1M USDC (6 decimals)
            flash_loan_fee_percentage: dec!(0.0009), // 0.09%
        }
    }
}

/// Flash loan executor for cross-chain arbitrage strategies
pub struct FlashLoanExecutor {
    config: FlashLoanExecutorConfig,
}

impl FlashLoanExecutor {
    /// Create a new FlashLoanExecutor
    pub fn new(config: FlashLoanExecutorConfig) -> Self {
        info!(
            "FlashLoanExecutor initialized - Compass: {:?}, Max loan: {}",
            config.compass_address,
            config.max_flash_loan_amount
        );
        
        Self { config }
    }
    
    /// Prepare a flash loan trade for cross-chain arbitrage via StargateCompass
    /// This function contains the core business logic for this strategy
    pub fn prepare_flash_loan_trade(&self, opportunity: &Opportunity) -> Result<TransactionRequest> {
        info!(
            "Preparing flash loan trade for opportunity {}",
            opportunity.base().id
        );
        
        // Extract loan amount and path based on opportunity type
        let (loan_amount, swap_path) = self.extract_trade_parameters(opportunity)?;
        
        // Validate loan amount against safety limits
        self.validate_loan_amount(loan_amount)?;
        
        // Calculate expected profit after fees
        let expected_profit = self.calculate_expected_profit(opportunity, loan_amount)?;
        
        info!(
            "Flash loan parameters - Amount: {}, Expected profit: ${:.2}",
            loan_amount,
            expected_profit
        );
        
        // Encode the remote calldata for the swap on Degen Chain
        let remote_calldata = self.encode_degen_swap_calldata(&swap_path, loan_amount)?;
        
        // Build the StargateCompass transaction
        let tx_request = TransactionBuilder::build_stargate_compass_call(
            self.config.compass_address,
            loan_amount,
            remote_calldata,
            self.config.degen_router_address,
        )?;
        
        info!("Flash loan transaction prepared successfully");
        
        Ok(tx_request)
    }
    
    /// Extract trade parameters from opportunity data
    fn extract_trade_parameters(&self, opportunity: &Opportunity) -> Result<(U256, Vec<Address>)> {
        match opportunity {
            Opportunity::PilotFish { data, .. } => {
                // For Pilot Fish opportunities, use the capital requirement as loan amount
                let loan_amount = self.decimal_to_usdc_wei(data.capital_requirement_usd)?;
                let swap_path = data.backrun_path.clone();
                Ok((loan_amount, swap_path))
            },
            Opportunity::ZenGeometer { data, .. } => {
                // For Zen Geometer opportunities, use the specified loan amount
                let loan_amount = data.loan_amount;
                let swap_path = data.path.clone();
                Ok((loan_amount, swap_path))
            },
            Opportunity::DexArbitrage { data, .. } if data.bottleneck_liquidity_usd > dec!(0.0) => {
                // For DEX arbitrage requiring flash liquidity
                let loan_amount = data.input_amount;
                let swap_path = data.path.clone();
                Ok((loan_amount, swap_path))
            },
            _ => {
                Err(BasiliskError::InvalidOpportunityType(format!(
                    "Opportunity type does not support flash loan execution: {:?}",
                    opportunity
                )))
            }
        }
    }
    
    /// Validate loan amount against safety limits
    fn validate_loan_amount(&self, loan_amount: U256) -> Result<()> {
        if loan_amount > self.config.max_flash_loan_amount {
            return Err(BasiliskError::Execution(ExecutionError::InvalidInput(format!(
                "Loan amount {} exceeds maximum allowed {}",
                loan_amount,
                self.config.max_flash_loan_amount
            ))));
        }
        
        if loan_amount.is_zero() {
            return Err(BasiliskError::Execution(ExecutionError::InvalidInput("Loan amount cannot be zero".to_string())));
        }
        
        debug!("Loan amount validation passed: {}", loan_amount);
        Ok(())
    }
    
    /// Calculate expected profit after flash loan fees
    fn calculate_expected_profit(&self, opportunity: &Opportunity, loan_amount: U256) -> Result<Decimal> {
        let gross_profit = opportunity.base().estimated_gross_profit_usd;
        
        // Calculate flash loan fee
        let loan_amount_usd = self.usdc_wei_to_decimal(loan_amount);
        let flash_loan_fee = loan_amount_usd * self.config.flash_loan_fee_percentage;
        
        let net_profit = gross_profit - flash_loan_fee;
        
        debug!(
            "Profit calculation - Gross: ${:.2}, Flash loan fee: ${:.2}, Net: ${:.2}",
            gross_profit,
            flash_loan_fee,
            net_profit
        );
        
        if net_profit <= dec!(0.0) {
            return Err(BasiliskError::Execution(ExecutionError::SimulationFailed(format!(
                "Trade would be unprofitable after flash loan fees. Net profit: ${:.2}",
                net_profit
            ))));
        }
        
        Ok(net_profit)
    }
    
    /// Encode the calldata for the swap on Degen Chain
    fn encode_degen_swap_calldata(&self, swap_path: &[Address], amount_in: U256) -> Result<Bytes> {
        // This is a simplified implementation
        // In production, this would encode the actual DEX router call
        
        if swap_path.len() < 2 {
            return Err(BasiliskError::Execution(ExecutionError::InvalidInput("Swap path must have at least 2 tokens".to_string())));
        }
        
        debug!(
            "Encoding Degen swap calldata - Path length: {}, Amount: {}",
            swap_path.len(),
            amount_in
        );
        
        // Placeholder calldata - in production this would be a proper DEX router call
        let mut calldata = Vec::new();
        
        // Function selector for swapExactTokensForTokens (placeholder)
        calldata.extend_from_slice(&[0x38, 0xed, 0x17, 0x39]);
        
        // Add amount_in (32 bytes)
        let amount_bytes = {
            let mut bytes = [0u8; 32];
            amount_in.to_big_endian(&mut bytes);
            bytes
        };
        calldata.extend_from_slice(&amount_bytes);
        
        // Add path (simplified - just first and last token)
        calldata.extend_from_slice(swap_path[0].as_bytes());
        calldata.extend_from_slice(swap_path[swap_path.len() - 1].as_bytes());
        
        Ok(Bytes::from(calldata))
    }
    
    /// Convert USD decimal to USDC wei (6 decimals)
    fn decimal_to_usdc_wei(&self, usd_amount: Decimal) -> Result<U256> {
        let wei_decimal = usd_amount * Decimal::from(1_000_000); // USDC has 6 decimals
        let wei_str = wei_decimal.to_string();
        let wei_str = wei_str.split('.').next().unwrap_or(&wei_str);
        
        U256::from_dec_str(wei_str)
            .map_err(|e| anyhow::anyhow!("Failed to convert USD to USDC wei: {}", e))
    }
    
    /// Convert USDC wei to USD decimal
    fn usdc_wei_to_decimal(&self, wei_amount: U256) -> Decimal {
        let wei_str = wei_amount.to_string();
        let wei_decimal = Decimal::from_str_exact(&wei_str).unwrap_or_default();
        wei_decimal / Decimal::from(1_000_000) // USDC has 6 decimals
    }
    
    /// Get flash loan fee for a given amount
    pub fn calculate_flash_loan_fee(&self, loan_amount_usd: Decimal) -> Decimal {
        loan_amount_usd * self.config.flash_loan_fee_percentage
    }
    
    /// Update configuration
    pub fn update_config(&mut self, config: FlashLoanExecutorConfig) {
        info!("Updating FlashLoanExecutor configuration");
        self.config = config;
    }
    
    /// Get current configuration
    pub fn get_config(&self) -> &FlashLoanExecutorConfig {
        &self.config
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::shared_types::{OpportunityBase, PilotFishData};
    
    #[test]
    fn test_flash_loan_executor_creation() {
        let config = FlashLoanExecutorConfig::default();
        let executor = FlashLoanExecutor::new(config);
        
        assert_eq!(executor.config.flash_loan_fee_percentage, dec!(0.0009));
    }
    
    #[test]
    fn test_loan_amount_validation() {
        let config = FlashLoanExecutorConfig {
            max_flash_loan_amount: U256::from(1000000),
            ..Default::default()
        };
        let executor = FlashLoanExecutor::new(config);
        
        // Valid amount
        assert!(executor.validate_loan_amount(U256::from(500000)).is_ok());
        
        // Too large
        assert!(executor.validate_loan_amount(U256::from(2000000)).is_err());
        
        // Zero amount
        assert!(executor.validate_loan_amount(U256::zero()).is_err());
    }
    
    #[test]
    fn test_usdc_conversion() {
        let config = FlashLoanExecutorConfig::default();
        let executor = FlashLoanExecutor::new(config);
        
        // Test USD to USDC wei conversion
        let usd_amount = dec!(100.5);
        let wei_amount = executor.decimal_to_usdc_wei(usd_amount).unwrap();
        let expected_wei = U256::from(100_500_000u64); // 100.5 * 10^6
        assert_eq!(wei_amount, expected_wei);
        
        // Test USDC wei to USD conversion
        let converted_back = executor.usdc_wei_to_decimal(wei_amount);
        assert_eq!(converted_back, usd_amount);
    }
    
    #[test]
    fn test_flash_loan_fee_calculation() {
        let config = FlashLoanExecutorConfig::default();
        let executor = FlashLoanExecutor::new(config);
        
        let loan_amount = dec!(10000.0); // $10,000
        let fee = executor.calculate_flash_loan_fee(loan_amount);
        let expected_fee = dec!(9.0); // 0.09% of $10,000
        
        assert_eq!(fee, expected_fee);
    }
}