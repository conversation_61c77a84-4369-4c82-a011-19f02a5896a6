// src/mev/mev_share_client.rs
use ethers::types::{Bytes, H256};
use reqwest::{Client, header};
use serde::{Deserialize, Serialize};
use crate::error::{BasiliskError, ExecutionError, Result};
use anyhow::Context;

// Structure for the JSON-RPC request body
#[derive(Serialize)]
struct JsonRpcRequest<T> {
    jsonrpc: &'static str,
    id: u64,
    method: &'static str,
    params: Vec<T>,
}

// Structure for the JSON-RPC response body
#[derive(Deserialize)]
struct JsonRpcResponse {
    result: BundleResult,
}

#[derive(Deserialize)]
struct BundleResult {
    #[serde(rename = "bundleHash")]
    bundle_hash: H256,
}

#[derive(Debug)]
pub struct MevShareClient {
    http_client: Client,
    relay_endpoint: String,
}

impl MevShareClient {
    pub fn new(relay_endpoint: String) -> Self {
        Self {
            http_client: Client::new(),
            relay_endpoint,
        }
    }

    /// Sends a signed bundle of transactions to the MEV-Share relay.
    pub async fn send_bundle(
        &self,
        raw_transactions: Vec<String>,
        target_block_number: u64,
    ) -> Result<H256> {
        let params = serde_json::json!({
            "txs": raw_transactions,
            "blockNumber": format!("0x{:x}", target_block_number),
        });

        let rpc_request = JsonRpcRequest {
            jsonrpc: "2.0",
            id: 1,
            method: "eth_sendBundle",
            params: vec![params],
        };

        let res = self.http_client.post(&self.relay_endpoint)
            .header(header::CONTENT_TYPE, "application/json")
            .json(&rpc_request)
            .send()
            .await
            .map_err(|e| BasiliskError::Execution(ExecutionError::RpcError(e.to_string())))?
            .json::<JsonRpcResponse>()
            .await
            .map_err(|e| BasiliskError::Execution(ExecutionError::RpcError(e.to_string())))?;
        
        Ok(res.result.bundle_hash)
    }
}
