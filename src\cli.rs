use clap::{Parser, Subcommand, ValueEnum};
use crate::shared_types::RunMode;

#[derive(Parser)]
#[command(author, version, about, long_about = None)]
pub struct Cli {
    /// Optional path to config file
    #[arg(short, long, value_name = "FILE")]
    pub config: Option<String>,

    #[command(subcommand)]
    pub command: Commands,
}

#[derive(Subcommand)]
pub enum Commands {
    /// Run the bot in a specific operational mode
    Run {
        /// Operational mode for the bot
        #[arg(long, value_enum, default_value_t = RunMode::Simulate)]
        mode: RunMode,
        /// Enable verbose output (shows detailed transaction information)
        #[arg(long)]
        verbose: bool,
    },

    /// Run the bot in educational simulation mode against live mainnet data
    Simulate {
        /// Show detailed lifecycle reports for every opportunity
        #[arg(long)]
        detailed: bool,
        /// Filter to only show opportunities from specific scanner
        #[arg(long)]
        scanner: Option<String>,
        /// Minimum profit threshold to show in reports (USD)
        #[arg(long)]
        min_profit: Option<f64>,
    },

    /// Validate configuration and connections
    Validate {
        /// Show detailed reasoning for each validation check
        #[arg(long)]
        reason: bool,
        /// Validate mathematical models (all, hurst, vesica, kelly, golden_ratio, pathfinding)
        #[arg(long)]
        math: Option<String>,
    },

    /// Configuration management commands
    Config {
        #[command(subcommand)]
        command: ConfigCommands,
    },

    /// Utility commands
    Utils {
        #[command(subcommand)]
        command: UtilsCommands,
    },

    /// Launch the Terminal User Interface (TUI)
    Tui,
}

#[derive(Subcommand, Debug)]
pub enum ConfigCommands {
    /// Show current configuration
    Show,

    /// Validate configuration file with detailed analysis
    Validate {
        /// Enable strict validation mode
        #[arg(long)]
        strict: bool,
        /// Test network connectivity
        #[arg(long)]
        check_network: bool,
        /// Output format (text, json, yaml)
        #[arg(long, default_value = "text")]
        format: String,
    },

    /// Run the configuration wizard for guided setup
    Wizard,

    /// Profile management commands
    Profile {
        #[command(subcommand)]
        command: ProfileCommands,
    },

    /// Set Basilisk Gaze parameters
    SetGaze {
        /// Pool A address (UniV3 WETH/USDC)
        #[arg(long)]
        pool_a: Option<String>,

        /// Pool B address (Aerodrome WETH/USDC)
        #[arg(long)]
        pool_b: Option<String>,

        /// Minimum profit threshold in USD
        #[arg(long)]
        min_profit: Option<f64>,

        /// Test amount in ETH
        #[arg(long)]
        test_amount: Option<f64>,
    },

    /// Interactive configuration editor
    Edit {
        /// Configuration section to edit
        #[arg(long)]
        section: Option<String>,
    },

    /// Generate configuration templates
    Templates,

    /// Import a configuration profile from a file
    Import {
        /// Path to the configuration file to import
        #[arg(long)]
        path: String,
        /// Name to assign to the imported profile
        #[arg(long)]
        name: String,
        /// Import format (toml, json, yaml, auto)
        #[arg(long, default_value = "auto")]
        format: String,
    },

    /// Export a configuration profile to a file or stdout
    Export {
        /// Name of the profile to export
        #[arg(long)]
        name: String,
        /// Optional path to save the exported file. If not provided, prints to stdout.
        #[arg(long)]
        path: Option<String>,
        /// Export format (toml, json, yaml)
        #[arg(long, default_value = "toml")]
        format: String,
    },
}

#[derive(Subcommand, Debug)]
pub enum ProfileCommands {
    /// List all available profiles
    List {
        /// Show detailed information
        #[arg(long)]
        detailed: bool,
    },

    /// Show profile information
    Show {
        /// Profile name
        name: String,
    },

    /// Create a new profile
    Create {
        /// Profile name
        name: String,
        /// Base profile to copy from
        #[arg(long)]
        from: Option<String>,
    },

    /// Delete a profile
    Delete {
        /// Profile name
        name: String,
        /// Force deletion without confirmation
        #[arg(long)]
        force: bool,
    },

    /// Copy a profile
    Copy {
        /// Source profile name
        source: String,
        /// Target profile name
        target: String,
    },

    /// Search profiles by tags or description
    Search {
        /// Search query
        query: String,
    },

    /// Show profile statistics
    Stats,

    /// Validate a specific profile
    Validate {
        /// Profile name
        name: String,
        /// Enable strict validation
        #[arg(long)]
        strict: bool,
    },
}

#[derive(Subcommand)]
pub enum UtilsCommands {
    /// Check balances of configured wallets
    Balances,

    /// Ping all configured nodes and services
    PingNodes,

    /// Show current Basilisk Gaze configuration
    ShowConfig,

    /// Test Basilisk Gaze strategy with current configuration
    TestGaze {
        /// Number of test cycles to run
        #[arg(long, default_value = "5")]
        cycles: u32,
    },
}