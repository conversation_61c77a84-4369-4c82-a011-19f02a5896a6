// MISSION: Circuit Breaker Pattern for Error Recovery
// WHY: Prevent cascading failures and provide graceful degradation
// HOW: Implement circuit breaker with configurable thresholds and recovery logic

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Duration;
use tokio::time::Instant;
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};

use crate::error::{BasiliskError, ErrorCode};
use crate::logging::AlertSeverity;
use crate::time_provider::{TimeProvider, SystemTimeProvider};

/// Circuit breaker states
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CircuitState {
    Closed,    // Normal operation
    Open,      // Failing fast, not executing operations
    HalfOpen,  // Testing if service has recovered
}

/// Circuit breaker configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    pub failure_threshold: u32,
    pub success_threshold: u32,
    pub timeout_duration: Duration,
    pub half_open_max_calls: u32,
    pub minimum_throughput: u32,
    pub sliding_window_size: Duration,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            failure_threshold: 5,
            success_threshold: 3,
            timeout_duration: Duration::from_secs(60),
            half_open_max_calls: 3,
            minimum_throughput: 10,
            sliding_window_size: Duration::from_secs(60),
        }
    }
}

impl CircuitBreakerConfig {
    pub fn for_rpc_calls() -> Self {
        Self {
            failure_threshold: 3,
            success_threshold: 2,
            timeout_duration: Duration::from_secs(30),
            half_open_max_calls: 2,
            minimum_throughput: 5,
            sliding_window_size: Duration::from_secs(30),
        }
    }

    pub fn for_data_providers() -> Self {
        Self {
            failure_threshold: 5,
            success_threshold: 3,
            timeout_duration: Duration::from_secs(120),
            half_open_max_calls: 3,
            minimum_throughput: 10,
            sliding_window_size: Duration::from_secs(60),
        }
    }

    pub fn for_execution_operations() -> Self {
        Self {
            failure_threshold: 2,
            success_threshold: 1,
            timeout_duration: Duration::from_secs(300), // 5 minutes
            half_open_max_calls: 1,
            minimum_throughput: 3,
            sliding_window_size: Duration::from_secs(120),
        }
    }
}

/// Call result for tracking success/failure
#[derive(Debug, Clone)]
struct CallResult {
    timestamp: Instant,
    success: bool,
    error_code: Option<ErrorCode>,
}

/// Circuit breaker statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerStats {
    pub state: CircuitState,
    pub failure_count: u32,
    pub success_count: u32,
    pub total_calls: u64,
    pub last_failure_time: Option<chrono::DateTime<chrono::Utc>>,
    pub state_changed_at: chrono::DateTime<chrono::Utc>,
    pub half_open_calls: u32,
}

/// Circuit breaker implementation
pub struct CircuitBreaker<T: TimeProvider> {
    name: String,
    config: CircuitBreakerConfig,
    state: Arc<RwLock<CircuitState>>,
    stats: Arc<RwLock<CircuitBreakerStats>>,
    call_history: Arc<RwLock<Vec<CallResult>>>,
    state_changed_at: Arc<RwLock<Instant>>,
    time_provider: Arc<T>,
}

impl CircuitBreaker<SystemTimeProvider> {
    /// Creates a new CircuitBreaker with the default SystemTimeProvider.
    pub fn new_with_system_time(name: String, config: CircuitBreakerConfig) -> Self {
        Self::new(name, config, Arc::new(SystemTimeProvider))
    }
}

impl<T: TimeProvider> CircuitBreaker<T> {
    pub fn new(name: String, config: CircuitBreakerConfig, time_provider: Arc<T>) -> Self {
        let now = chrono::Utc::now();
        
        Self {
            name,
            config,
            state: Arc::new(RwLock::new(CircuitState::Closed)),
            stats: Arc::new(RwLock::new(CircuitBreakerStats {
                state: CircuitState::Closed,
                failure_count: 0,
                success_count: 0,
                total_calls: 0,
                last_failure_time: None,
                state_changed_at: now,
                half_open_calls: 0,
            })),
            call_history: Arc::new(RwLock::new(Vec::new())),
            state_changed_at: Arc::new(RwLock::new(time_provider.now())),
            time_provider,
        }
    }

    /// Execute an operation with circuit breaker protection
    pub async fn execute<F, OpResult, E>(&self, operation: F) -> Result<OpResult, CircuitBreakerError>
    where
        F: FnOnce() -> Result<OpResult, E>,
        E: Into<BasiliskError>,
    {
        // Check if we can execute the operation
        if !self.can_execute().await {
            return Err(CircuitBreakerError::CircuitOpen {
                circuit_name: self.name.clone(),
                state: self.get_state().await,
            });
        }

        // Execute the operation
        let start_time = self.time_provider.now();
        let result = operation();
        let execution_time = start_time.elapsed();

        match result {
            Ok(value) => {
                self.record_success().await;
                debug!(
                    circuit_name = %self.name,
                    execution_time_ms = execution_time.as_millis(),
                    "Circuit breaker: operation succeeded"
                );
                Ok(value)
            }
            Err(error) => {
                let basilisk_error = error.into();
                let error_code = self.extract_error_code(&basilisk_error);
                
                self.record_failure(error_code.clone()).await;
                
                warn!(
                    circuit_name = %self.name,
                    error = %basilisk_error,
                    error_code = ?error_code,
                    execution_time_ms = execution_time.as_millis(),
                    "Circuit breaker: operation failed"
                );
                
                Err(CircuitBreakerError::OperationFailed {
                    circuit_name: self.name.clone(),
                    error: basilisk_error,
                })
            }
        }
    }

    /// Check if the circuit breaker allows execution
    async fn can_execute(&self) -> bool {
        let state = self.get_state().await;
        
        match state {
            CircuitState::Closed => true,
            CircuitState::Open => {
                // Check if timeout has elapsed
                let state_changed_at = *self.state_changed_at.read().await;
                if self.time_provider.now().duration_since(state_changed_at) >= self.config.timeout_duration {
                    self.transition_to_half_open().await;
                    true
                } else {
                    false
                }
            }
            CircuitState::HalfOpen => {
                let stats = self.stats.read().await;
                stats.half_open_calls < self.config.half_open_max_calls
            }
        }
    }

    /// Record a successful operation
    async fn record_success(&self) {
        let now = self.time_provider.now();
        
        // Add to call history
        {
            let mut history = self.call_history.write().await;
            history.push(CallResult {
                timestamp: now,
                success: true,
                error_code: None,
            });
            self.cleanup_old_calls(&mut history, now).await;
        }

        // Update stats
        {
            let mut stats = self.stats.write().await;
            stats.success_count += 1;
            stats.total_calls += 1;
            
            let current_state = stats.state.clone();
            match current_state {
                CircuitState::HalfOpen => {
                    stats.half_open_calls += 1;
                    if stats.success_count >= self.config.success_threshold {
                        self.transition_to_closed().await;
                    }
                }
                CircuitState::Closed => {
                    // Reset failure count on success
                    stats.failure_count = 0;
                }
                CircuitState::Open => {
                    // This shouldn't happen, but handle gracefully
                    warn!(circuit_name = %self.name, "Recorded success while circuit is open");
                }
            }
        }
    }

    /// Record a failed operation
    async fn record_failure(&self, error_code: Option<ErrorCode>) {
        let now = self.time_provider.now();
        
        // Add to call history
        {
            let mut history = self.call_history.write().await;
            history.push(CallResult {
                timestamp: now,
                success: false,
                error_code,
            });
            self.cleanup_old_calls(&mut history, now).await;
        }

        // Update stats
        {
            let mut stats = self.stats.write().await;
            stats.failure_count += 1;
            stats.total_calls += 1;
            stats.last_failure_time = Some(chrono::Utc::now());
            
            let current_state = stats.state.clone();
            match current_state {
                CircuitState::Closed => {
                    if self.should_open_circuit().await {
                        self.transition_to_open().await;
                    }
                }
                CircuitState::HalfOpen => {
                    stats.half_open_calls += 1;
                    self.transition_to_open().await;
                }
                CircuitState::Open => {
                    // Already open, just update stats
                }
            }
        }
    }

    /// Check if circuit should be opened based on failure rate
    async fn should_open_circuit(&self) -> bool {
        let history = self.call_history.read().await;
        let now = self.time_provider.now();
        
        // Get calls within the sliding window
        let recent_calls: Vec<_> = history.iter()
            .filter(|call| now.duration_since(call.timestamp) <= self.config.sliding_window_size)
            .collect();
        
        if recent_calls.len() < self.config.minimum_throughput as usize {
            return false; // Not enough calls to make a decision
        }
        
        let failure_count = recent_calls.iter()
            .filter(|call| !call.success)
            .count() as u32;
        
        failure_count >= self.config.failure_threshold
    }

    /// Transition to closed state
    async fn transition_to_closed(&self) {
        {
            let mut state = self.state.write().await;
            *state = CircuitState::Closed;
        }
        
        {
            let mut stats = self.stats.write().await;
            stats.state = CircuitState::Closed;
            stats.failure_count = 0;
            stats.success_count = 0;
            stats.half_open_calls = 0;
            stats.state_changed_at = chrono::Utc::now();
        }
        
        *self.state_changed_at.write().await = self.time_provider.now();
        
        info!(
            circuit_name = %self.name,
            "Circuit breaker transitioned to CLOSED state"
        );
    }

    /// Transition to open state
    async fn transition_to_open(&self) {
        {
            let mut state = self.state.write().await;
            *state = CircuitState::Open;
        }
        
        {
            let mut stats = self.stats.write().await;
            stats.state = CircuitState::Open;
            stats.half_open_calls = 0;
            stats.state_changed_at = chrono::Utc::now();
        }
        
        *self.state_changed_at.write().await = self.time_provider.now();
        
        error!(
            circuit_name = %self.name,
            failure_count = self.stats.read().await.failure_count,
            "Circuit breaker transitioned to OPEN state"
        );
    }

    /// Transition to half-open state
    async fn transition_to_half_open(&self) {
        {
            let mut state = self.state.write().await;
            *state = CircuitState::HalfOpen;
        }
        
        {
            let mut stats = self.stats.write().await;
            stats.state = CircuitState::HalfOpen;
            stats.success_count = 0;
            stats.half_open_calls = 0;
            stats.state_changed_at = chrono::Utc::now();
        }
        
        *self.state_changed_at.write().await = self.time_provider.now();
        
        info!(
            circuit_name = %self.name,
            "Circuit breaker transitioned to HALF-OPEN state"
        );
    }

    /// Get current circuit state
    pub async fn get_state(&self) -> CircuitState {
        self.state.read().await.clone()
    }

    /// Get circuit breaker statistics
    pub async fn get_stats(&self) -> CircuitBreakerStats {
        self.stats.read().await.clone()
    }

    /// Clean up old call results outside the sliding window
    async fn cleanup_old_calls(&self, history: &mut Vec<CallResult>, now: Instant) {
        history.retain(|call| {
            now.duration_since(call.timestamp) <= self.config.sliding_window_size
        });
    }

    /// Extract error code from BasiliskError for classification
    fn extract_error_code(&self, error: &BasiliskError) -> Option<ErrorCode> {
        match error {
            BasiliskError::Network(net_err) => Some(net_err.error_code()),
            BasiliskError::DataProvider(data_err) => Some(data_err.error_code()),
            BasiliskError::Execution(exec_err) => Some(exec_err.error_code()),
            BasiliskError::Strategy(strat_err) => Some(strat_err.error_code()),
            BasiliskError::Critical(crit_err) => Some(crit_err.error_code()),
            _ => None,
        }
    }

    /// Reset circuit breaker to closed state (for testing/manual intervention)
    pub async fn reset(&self) {
        self.transition_to_closed().await;
        
        // Clear call history
        {
            let mut history = self.call_history.write().await;
            history.clear();
        }
        
        info!(circuit_name = %self.name, "Circuit breaker manually reset");
    }
}

/// Circuit breaker specific errors
#[derive(Debug, thiserror::Error)]
pub enum CircuitBreakerError {
    #[error("Circuit breaker '{circuit_name}' is open (state: {state:?})")]
    CircuitOpen {
        circuit_name: String,
        state: CircuitState,
    },
    
    #[error("Operation failed in circuit '{circuit_name}': {error}")]
    OperationFailed {
        circuit_name: String,
        error: BasiliskError,
    },
}

/// Circuit breaker registry for managing multiple circuit breakers
pub struct CircuitBreakerRegistry {
    breakers: Arc<RwLock<std::collections::HashMap<String, Arc<CircuitBreaker<SystemTimeProvider>>>>>, 
}

impl CircuitBreakerRegistry {
    pub fn new() -> Self {
        Self {
            breakers: Arc::new(RwLock::new(std::collections::HashMap::new())),
        }
    }

    /// Register a new circuit breaker
    pub async fn register(&self, name: String, config: CircuitBreakerConfig) -> Arc<CircuitBreaker<SystemTimeProvider>> {
        let breaker = Arc::new(CircuitBreaker::new_with_system_time(name.clone(), config));
        
        {
            let mut breakers = self.breakers.write().await;
            breakers.insert(name, breaker.clone());
        }
        
        breaker
    }

    /// Get a circuit breaker by name
    pub async fn get(&self, name: &str) -> Option<Arc<CircuitBreaker<SystemTimeProvider>>> {
        let breakers = self.breakers.read().await;
        breakers.get(name).cloned()
    }

    /// Get all circuit breaker statistics
    pub async fn get_all_stats(&self) -> std::collections::HashMap<String, CircuitBreakerStats> {
        let breakers = self.breakers.read().await;
        let mut stats = std::collections::HashMap::new();
        
        for (name, breaker) in breakers.iter() {
            stats.insert(name.clone(), breaker.get_stats().await);
        }
        
        stats
    }

    /// Reset all circuit breakers
    pub async fn reset_all(&self) {
        let breakers = self.breakers.read().await;
        
        for breaker in breakers.values() {
            breaker.reset().await;
        }
        
        info!("All circuit breakers reset");
    }
}

/// Convenience macro for executing operations with circuit breaker protection
#[macro_export]
macro_rules! with_circuit_breaker {
    ($registry:expr, $name:expr, $operation:expr) => {
        {
            if let Some(breaker) = $registry.get($name).await {
                breaker.execute(|| $operation).await
            } else {
                Err($crate::error::circuit_breaker::CircuitBreakerError::CircuitOpen {
                    circuit_name: $name.to_string(),
                    state: $crate::error::circuit_breaker::CircuitState::Open,
                })
            }
        }
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::error::NetworkError;
    use crate::time_provider::MockTimeProvider;
    use tokio::time::Duration;

    #[tokio::test]
    async fn test_circuit_breaker_closed_to_open() {
        let time_provider = Arc::new(MockTimeProvider::new());
        let config = CircuitBreakerConfig {
            failure_threshold: 2,
            success_threshold: 1,
            timeout_duration: Duration::from_millis(100),
            half_open_max_calls: 1,
            minimum_throughput: 1,
            sliding_window_size: Duration::from_secs(10),
        };
        
        let breaker = CircuitBreaker::new("test".to_string(), config, time_provider.clone());
        
        // Initially closed
        assert_eq!(breaker.get_state().await, CircuitState::Closed);
        
        // First failure
        let result = breaker.execute(|| -> Result<(), NetworkError> {
            Err(NetworkError::RpcTimeout {
                endpoint: "test".to_string(),
                timeout_ms: 1000,
            })
        }).await;
        assert!(result.is_err());
        assert_eq!(breaker.get_state().await, CircuitState::Closed);
        
        // Second failure should open the circuit
        let result = breaker.execute(|| -> Result<(), NetworkError> {
            Err(NetworkError::RpcTimeout {
                endpoint: "test".to_string(),
                timeout_ms: 1000,
            })
        }).await;
        assert!(result.is_err());
        assert_eq!(breaker.get_state().await, CircuitState::Open);
    }

    #[tokio::test]
    async fn test_circuit_breaker_open_to_half_open() {
        let time_provider = Arc::new(MockTimeProvider::new());
        let config = CircuitBreakerConfig {
            failure_threshold: 1,
            success_threshold: 1,
            timeout_duration: Duration::from_millis(50),
            half_open_max_calls: 1,
            minimum_throughput: 1,
            sliding_window_size: Duration::from_secs(10),
        };
        
        let breaker = CircuitBreaker::new("test".to_string(), config, time_provider.clone());
        
        // Trigger failure to open circuit
        let _ = breaker.execute(|| -> Result<(), NetworkError> {
            Err(NetworkError::RpcTimeout {
                endpoint: "test".to_string(),
                timeout_ms: 1000,
            })
        }).await;
        
        assert_eq!(breaker.get_state().await, CircuitState::Open);
        
        // Advance time to exceed timeout
        time_provider.advance_time(Duration::from_millis(60));
        
        // Next call should transition to half-open
        let result = breaker.execute(|| -> Result<String, NetworkError> {
            Ok("success".to_string())
        }).await;
        
        assert!(result.is_ok());
        assert_eq!(breaker.get_state().await, CircuitState::Closed);
    }

    #[tokio::test]
    async fn test_circuit_breaker_registry() {
        let registry = CircuitBreakerRegistry::new();
        
        let config = CircuitBreakerConfig::default();
        let breaker = registry.register("test_breaker".to_string(), config).await;
        
        assert!(registry.get("test_breaker").await.is_some());
        assert!(registry.get("nonexistent").await.is_none());
        
        let stats = registry.get_all_stats().await;
        assert!(stats.contains_key("test_breaker"));
    }
}