// NOMADIC HUNTER: EcologicalSurveyor - The Eyes on All Horizons
// WHY: Continuously monitor all potential hunting territories for strategic intelligence
// HOW: Multi-chain connections with territory health scoring and NATS publishing

use async_nats::Client as NatsClient;
use ethers::providers::{Http, Middleware, Provider};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::interval;
use tracing::{debug, error, info, warn};

use crate::config::{ChainConfig, Settings};
use crate::shared_types::NatsTopics;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerritoryHealth {
    pub chain_id: u64,
    pub chain_name: String,
    pub health_score: f64,
    pub avg_dex_volume_24h: f64,
    pub avg_gas_price_gwei: f64,
    pub volatility: f64,
    pub status: TerritoryStatus,
    pub last_updated: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum TerritoryStatus {
    Active,      // Currently active hunting ground
    Recommended, // High health score, good for migration
    Viable,      // Acceptable health score
    Poor,        // Low health score, avoid
    Unreachable, // RPC connection issues
}

pub struct EcologicalSurveyor {
    nats_client: NatsClient,
    config: Settings,
    chain_providers: HashMap<u64, Arc<Provider<Http>>>,
    territory_health: HashMap<u64, TerritoryHealth>,
}

impl EcologicalSurveyor {
    pub fn new(nats_client: NatsClient, config: Settings) -> Self {
        Self {
            nats_client,
            config,
            chain_providers: HashMap::new(),
            territory_health: HashMap::new(),
        }
    }

    pub async fn start(&mut self) -> crate::error::Result<()> {
        info!("ECOLOGICAL SURVEYOR: Awakening the multi-chain consciousness...");

        // Initialize connections to all configured chains
        self.initialize_chain_connections().await?;

        // Start the survey loop
        self.run_survey_loop().await;

        Ok(())
    }

    async fn initialize_chain_connections(&mut self) -> crate::error::Result<()> {
        info!("ECOLOGICAL SURVEYOR: Establishing connections to all territories...");

        for (chain_id, chain_config) in &self.config.chains {
            match self.connect_to_chain(*chain_id, chain_config).await {
                Ok(provider) => {
                    self.chain_providers.insert(*chain_id, provider);
                    info!(
                        "ECOLOGICAL SURVEYOR: Connected to {} (Chain ID: {})",
                        chain_config.name, chain_id
                    );
                }
                Err(e) => {
                    error!(
                        "ECOLOGICAL SURVEYOR: Failed to connect to {} (Chain ID: {}): {}",
                        chain_config.name, chain_id, e
                    );

                    // Initialize with unreachable status
                    self.territory_health.insert(
                        *chain_id,
                        TerritoryHealth {
                            chain_id: *chain_id,
                            chain_name: chain_config.name.clone(),
                            health_score: 0.0,
                            avg_dex_volume_24h: 0.0,
                            avg_gas_price_gwei: 0.0,
                            volatility: 0.0,
                            status: TerritoryStatus::Unreachable,
                            last_updated: chrono::Utc::now().timestamp() as u64,
                        },
                    );
                }
            }
        }

        let connected_count = self.chain_providers.len();
        let total_count = self.config.chains.len();
        info!(
            "ECOLOGICAL SURVEYOR: Connected to {}/{} territories",
            connected_count, total_count
        );

        Ok(())
    }

    async fn connect_to_chain(
        &self,
        chain_id: u64,
        chain_config: &ChainConfig,
    ) -> crate::error::Result<Arc<Provider<Http>>> {
        // Try RPC endpoints in priority order (OROBOROS-style failover)
        let mut sorted_endpoints = if let Some(endpoints) = chain_config.rpc_endpoints.clone() {
            endpoints
        } else {
            vec![]
        };
        sorted_endpoints.sort_by_key(|endpoint| endpoint.priority);

        for endpoint in &sorted_endpoints {
            match Provider::<Http>::try_from(&endpoint.url) {
                Ok(provider) => {
                    // Test the connection
                    match provider.get_block_number().await {
                        Ok(_) => {
                            debug!(
                                "ECOLOGICAL SURVEYOR: Successfully connected to {} via {}",
                                chain_config.name, endpoint.url
                            );
                            return Ok(Arc::new(provider));
                        }
                        Err(e) => {
                            warn!(
                                "ECOLOGICAL SURVEYOR: RPC test failed for {} ({}): {}",
                                chain_config.name, endpoint.url, e
                            );
                            continue;
                        }
                    }
                }
                Err(e) => {
                    warn!(
                        "ECOLOGICAL SURVEYOR: Failed to create provider for {} ({}): {}",
                        chain_config.name, endpoint.url, e
                    );
                    continue;
                }
            }
        }

        Err(crate::error::BasiliskError::DataIngestion { message: format!("All RPC endpoints failed for chain {}", chain_id) })
    }

    async fn run_survey_loop(&mut self) -> crate::error::Result<()> {
        info!("ECOLOGICAL SURVEYOR: Beginning continuous territorial survey...");
        let mut survey_interval = interval(Duration::from_secs(300)); // 5 minutes

        loop {
            survey_interval.tick().await;

            debug!("ECOLOGICAL SURVEYOR: Conducting territorial health assessment...");

            // Survey all connected territories
            for (chain_id, provider) in &self.chain_providers {
                match self
                    .calculate_territory_health(*chain_id, provider.clone())
                    .await
                {
                    Ok(health) => {
                        self.territory_health.insert(*chain_id, health);
                    }
                    Err(e) => {
                        error!(
                            "ECOLOGICAL SURVEYOR: Failed to assess territory {}: {}",
                            chain_id, e
                        );

                        // Mark as unreachable if assessment fails
                        if let Some(health) = self.territory_health.get_mut(chain_id) {
                            health.status = TerritoryStatus::Unreachable;
                            health.last_updated = chrono::Utc::now().timestamp() as u64;
                        }
                    }
                }
            }

            // Publish survey results
            self.publish_survey_results().await?;
        }
    }

    async fn calculate_territory_health(
        &self,
        chain_id: u64,
        provider: Arc<Provider<Http>>,
    ) -> crate::error::Result<TerritoryHealth> {
        let chain_config = self
            .config
            .chains
            .get(&chain_id)
            .ok_or(crate::error::BasiliskError::DataIngestion { message: format!("Chain configuration not found for chain_id {}", chain_id) })?;

        // Get current gas price
        let gas_price = provider.get_gas_price().await?;
        let gas_price_gwei = gas_price.as_u64() as f64 / 1_000_000_000.0;

        // Simulate DEX volume and volatility (in production, this would query actual DEX data)
        let simulated_volume = self.simulate_dex_volume(chain_id).await;
        let simulated_volatility = self.simulate_volatility(chain_id).await;

        // AUDIT-FIX-5: Implement the corrected, logically sound Health Score formula.
        // The original formula incorrectly multiplied by volatility, rewarding risk.
        // The corrected formula divides by volatility, correctly penalizing it.
        // NOTE: A full implementation should also normalize each metric (volume, gas, volatility)
        // across all chains to ensure scores are comparable, but this is a larger architectural change.
        let health_score = if gas_price_gwei > 0.0 && simulated_volatility > 0.0 {
            simulated_volume / (gas_price_gwei * simulated_volatility)
        } else {
            0.0
        };

        // Determine status based on health score and current active chain
        let status = if chain_id == self.config.active_chain_id {
            TerritoryStatus::Active
        } else if health_score > 1000.0 {
            TerritoryStatus::Recommended
        } else if health_score > 100.0 {
            TerritoryStatus::Viable
        } else {
            TerritoryStatus::Poor
        };

        Ok(TerritoryHealth {
            chain_id,
            chain_name: chain_config.name.clone(),
            health_score,
            avg_dex_volume_24h: simulated_volume,
            avg_gas_price_gwei: gas_price_gwei,
            volatility: simulated_volatility,
            status,
            last_updated: chrono::Utc::now().timestamp() as u64,
        })
    }

    async fn simulate_dex_volume(&self, chain_id: u64) -> f64 {
        // Simulate DEX volume based on chain characteristics
        // In production, this would query actual DEX protocols
        match chain_id {
            8453 => 50_000_000.0,  // Base - high volume
            137 => 30_000_000.0,   // Polygon - medium volume
            42161 => 40_000_000.0, // Arbitrum - high volume
            _ => 10_000_000.0,     // Default
        }
    }

    async fn simulate_volatility(&self, chain_id: u64) -> f64 {
        // Simulate volatility based on chain characteristics
        // In production, this would analyze actual price movements
        match chain_id {
            8453 => 1.5,  // Base - moderate volatility
            137 => 1.2,   // Polygon - lower volatility
            42161 => 1.4, // Arbitrum - moderate volatility
            _ => 1.0,     // Default
        }
    }

    async fn publish_survey_results(&self) -> crate::error::Result<()> {
        let survey_results: Vec<TerritoryHealth> =
            self.territory_health.values().cloned().collect();

        let payload = serde_json::to_vec(&survey_results)?;

        self.nats_client
            .publish(crate::shared_types::NatsTopics::STATE_SYSTEM_HEALTH, payload.into())
            .await?;

        debug!(
            "ECOLOGICAL SURVEYOR: Published health assessment for {} territories",
            survey_results.len()
        );

        // Log summary
        for health in &survey_results {
            let status_emoji = match health.status {
                TerritoryStatus::Active => "🎯",
                TerritoryStatus::Recommended => "🌟",
                TerritoryStatus::Viable => "✅",
                TerritoryStatus::Poor => "⚠️",
                TerritoryStatus::Unreachable => "❌",
            };

            info!(
                "ECOLOGICAL SURVEYOR: {} {} | Health: {:.1} | Gas: {:.1} gwei | Volume: ${:.0}M",
                status_emoji,
                health.chain_name,
                health.health_score,
                health.avg_gas_price_gwei,
                health.avg_dex_volume_24h / 1_000_000.0
            );
        }

        Ok(())
    }
}
