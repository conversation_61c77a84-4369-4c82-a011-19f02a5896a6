// MISSION: Enhanced Logging Infrastructure for Production Trading
// WHY: Provide structured, contextual, and actionable logging for high-frequency trading
// HOW: JSON-structured logs with trace IDs, error codes, and rich context

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{field::Field, Event, Subscriber};
use tracing_subscriber::{
    fmt::{format::Writer, FmtContext, FormatEvent, FormatFields},
    registry::LookupSpan,
};
use uuid::Uuid;

// Dynamic configuration module
pub mod dynamic_config;
pub use dynamic_config::{DynamicLogConfig, DynamicLogManager, init_dynamic_logging};

// Enhanced heartbeat system
pub mod heartbeat;
pub use heartbeat::{EnhancedHeartbeatLogger, SystemHealthMetrics as EnhancedSystemHealthMetrics, HealthAlertThresholds};

/// Unique trace ID for following operations across the system
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TraceId(pub String);

impl TraceId {
    pub fn new() -> Self {
        Self(Uuid::new_v4().to_string())
    }
    
    pub fn from_opportunity_id(opportunity_id: &str) -> Self {
        Self(format!("opp_{}", &opportunity_id[..8]))
    }
}

impl std::fmt::Display for TraceId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Well-defined error codes for predictable failure states
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ErrorCode {
    // Network & RPC Errors
    ERpcTimeout,
    ERpcConnectionFailed,
    ERpcInvalidResponse,
    ERpcRateLimited,
    ENetworkLatencyHigh,
    
    // Data Provider Errors
    EDataSourceUnavailable,
    EDataStale,
    EDataValidationFailed,
    EPriceOracleFailure,
    
    // Execution Errors
    EInsufficientLiquidity,
    EHighSlippage,
    EGasEstimationFailed,
    ETransactionReverted,
    ETransactionTimeout,
    ENonceConflict,
    EMevAttackDetected,
    EWalletAccessFailed,
    
    // Risk Management Errors
    EPositionSizeExceeded,
    EDailyLossLimitExceeded,
    EVolatilityTooHigh,
    ECircuitBreakerTripped,
    
    // Strategy Errors
    EOpportunityExpired,
    EHoneypotDetected,
    EInsufficientProfit,
    EPathNotFound,
    
    // System Errors
    ECriticalConfigError,
    ESystemOverloaded,
    EUnrecoverableError,
    ESecurityViolation,
}

impl ErrorCode {
    pub fn as_str(&self) -> &'static str {
        match self {
            ErrorCode::ERpcTimeout => "E_RPC_TIMEOUT",
            ErrorCode::ERpcConnectionFailed => "E_RPC_CONNECTION_FAILED",
            ErrorCode::ERpcInvalidResponse => "E_RPC_INVALID_RESPONSE",
            ErrorCode::ERpcRateLimited => "E_RPC_RATE_LIMITED",
            ErrorCode::EDataSourceUnavailable => "E_DATA_SOURCE_UNAVAILABLE",
            ErrorCode::EDataStale => "E_DATA_STALE",
            ErrorCode::EDataValidationFailed => "E_DATA_VALIDATION_FAILED",
            ErrorCode::EPriceOracleFailure => "E_PRICE_ORACLE_FAILURE",
            ErrorCode::EInsufficientLiquidity => "E_INSUFFICIENT_LIQUIDITY",
            ErrorCode::EHighSlippage => "E_HIGH_SLIPPAGE",
            ErrorCode::EGasEstimationFailed => "E_GAS_ESTIMATION_FAILED",
            ErrorCode::ETransactionReverted => "E_TRANSACTION_REVERTED",
            ErrorCode::ETransactionTimeout => "E_TRANSACTION_TIMEOUT",
            ErrorCode::ENonceConflict => "E_NONCE_CONFLICT",
            ErrorCode::EPositionSizeExceeded => "E_POSITION_SIZE_EXCEEDED",
            ErrorCode::EDailyLossLimitExceeded => "E_DAILY_LOSS_LIMIT_EXCEEDED",
            ErrorCode::EVolatilityTooHigh => "E_VOLATILITY_TOO_HIGH",
            ErrorCode::ECircuitBreakerTripped => "E_CIRCUIT_BREAKER_TRIPPED",
            ErrorCode::EOpportunityExpired => "E_OPPORTUNITY_EXPIRED",
            ErrorCode::EHoneypotDetected => "E_HONEYPOT_DETECTED",
            ErrorCode::EInsufficientProfit => "E_INSUFFICIENT_PROFIT",
            ErrorCode::EPathNotFound => "E_PATH_NOT_FOUND",
            ErrorCode::ECriticalConfigError => "E_CRITICAL_CONFIG_ERROR",
            ErrorCode::EWalletAccessFailed => "E_WALLET_ACCESS_FAILED",
            ErrorCode::ESystemOverloaded => "E_SYSTEM_OVERLOADED",
            ErrorCode::EUnrecoverableError => "E_UNRECOVERABLE_ERROR",
            ErrorCode::ESecurityViolation => "E_SECURITY_VIOLATION",
            ErrorCode::EMevAttackDetected => "E_MEV_ATTACK_DETECTED",
            ErrorCode::ENetworkLatencyHigh => "E_NETWORK_LATENCY_HIGH",
        }
    }
    
    pub fn severity(&self) -> AlertSeverity {
        match self {
            ErrorCode::ECriticalConfigError | 
            ErrorCode::EWalletAccessFailed | 
            ErrorCode::EUnrecoverableError |
            ErrorCode::ESecurityViolation |
            ErrorCode::EMevAttackDetected => AlertSeverity::Critical,
            
            ErrorCode::ETransactionReverted |
            ErrorCode::ETransactionTimeout |
            ErrorCode::ERpcConnectionFailed |
            ErrorCode::EDataSourceUnavailable => AlertSeverity::Error,
            
            ErrorCode::EHighSlippage |
            ErrorCode::ERpcTimeout |
            ErrorCode::EDataStale |
            ErrorCode::EVolatilityTooHigh => AlertSeverity::Warning,
            
            _ => AlertSeverity::Error,
        }
    }
}

/// Alert severity levels for operational monitoring
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum AlertSeverity {
    Critical,  // Immediate page to on-call developer
    Error,     // Recoverable error impacting single operation
    Warning,   // Degraded performance or increased risk
    Info,      // Normal operational information
}

impl AlertSeverity {
    pub fn as_str(&self) -> &'static str {
        match self {
            AlertSeverity::Critical => "CRITICAL",
            AlertSeverity::Error => "ERROR", 
            AlertSeverity::Warning => "WARNING",
            AlertSeverity::Info => "INFO",
        }
    }
}

/// Structured log context for trading operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingContext {
    pub trace_id: TraceId,
    pub opportunity_id: Option<String>,
    pub component: String,
    pub function: String,
    pub chain_id: Option<u64>,
    pub token_path: Option<Vec<String>>,
    pub estimated_profit_usd: Option<rust_decimal::Decimal>,
    pub gas_estimate_gwei: Option<rust_decimal::Decimal>,
    pub market_regime: Option<String>,
    pub strategy_type: Option<String>,
}

impl TradingContext {
    pub fn new(component: &str, function: &str) -> Self {
        Self {
            trace_id: TraceId::new(),
            opportunity_id: None,
            component: component.to_string(),
            function: function.to_string(),
            chain_id: None,
            token_path: None,
            estimated_profit_usd: None,
            gas_estimate_gwei: None,
            market_regime: None,
            strategy_type: None,
        }
    }
    
    pub fn with_opportunity(mut self, opportunity_id: &str) -> Self {
        self.trace_id = TraceId::from_opportunity_id(opportunity_id);
        self.opportunity_id = Some(opportunity_id.to_string());
        self
    }
    
    pub fn with_chain(mut self, chain_id: u64) -> Self {
        self.chain_id = Some(chain_id);
        self
    }
    
    pub fn with_path(mut self, path: Vec<String>) -> Self {
        self.token_path = Some(path);
        self
    }
    
    pub fn with_profit(mut self, profit_usd: rust_decimal::Decimal) -> Self {
        self.estimated_profit_usd = Some(profit_usd);
        self
    }
    
    pub fn with_gas(mut self, gas_gwei: rust_decimal::Decimal) -> Self {
        self.gas_estimate_gwei = Some(gas_gwei);
        self
    }
    
    pub fn with_regime(mut self, regime: &str) -> Self {
        self.market_regime = Some(regime.to_string());
        self
    }
    
    pub fn with_strategy(mut self, strategy: &str) -> Self {
        self.strategy_type = Some(strategy.to_string());
        self
    }
}

/// Structured log entry for JSON output
#[derive(Debug, Serialize, Deserialize)]
pub struct StructuredLogEntry {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub level: String,
    pub message: String,
    pub context: TradingContext,
    pub error_code: Option<ErrorCode>,
    pub severity: Option<AlertSeverity>,
    pub additional_fields: HashMap<String, serde_json::Value>,
}

/// Custom JSON formatter for structured logging
pub struct JsonFormatter;

impl<S, N> FormatEvent<S, N> for JsonFormatter
where
    S: Subscriber + for<'a> LookupSpan<'a>,
    N: for<'a> FormatFields<'a> + 'static,
{
    fn format_event(
        &self,
        ctx: &FmtContext<'_, S, N>,
        mut writer: Writer<'_>,
        event: &Event<'_>,
    ) -> std::fmt::Result {
        let metadata = event.metadata();
        
        // Extract context from event fields
        let mut context = TradingContext::new("unknown", "unknown");
        let mut error_code: Option<ErrorCode> = None;
        let mut additional_fields = HashMap::new();
        
        // Visit event fields to extract structured data
        event.record(&mut FieldVisitor {
            context: &mut context,
            error_code: &mut error_code,
            additional_fields: &mut additional_fields,
        });
        
        let entry = StructuredLogEntry {
            timestamp: chrono::Utc::now(),
            level: metadata.level().to_string(),
            message: format!("{}", event.metadata().target()),
            context,
            error_code: error_code.clone(),
            severity: error_code.map(|ec| ec.severity()),
            additional_fields,
        };
        
        let json = serde_json::to_string(&entry).unwrap_or_else(|_| "{}".to_string());
        writeln!(writer, "{}", json)
    }
}

/// Field visitor for extracting structured data from log events
struct FieldVisitor<'a> {
    context: &'a mut TradingContext,
    error_code: &'a mut Option<ErrorCode>,
    additional_fields: &'a mut HashMap<String, serde_json::Value>,
}

impl<'a> tracing::field::Visit for FieldVisitor<'a> {
    fn record_debug(&mut self, field: &Field, value: &dyn std::fmt::Debug) {
        let field_name = field.name();
        let value_str = format!("{:?}", value);
        
        match field_name {
            "component" => self.context.component = value_str,
            "function" => self.context.function = value_str,
            "opportunity_id" => self.context.opportunity_id = Some(value_str),
            "error_code" => {
                // Parse error code from string
                *self.error_code = match value_str.as_str() {
                    "E_RPC_TIMEOUT" => Some(ErrorCode::ERpcTimeout),
                    "E_INSUFFICIENT_LIQUIDITY" => Some(ErrorCode::EInsufficientLiquidity),
                    "E_HIGH_SLIPPAGE" => Some(ErrorCode::EHighSlippage),
                    // Add more mappings as needed
                    _ => None,
                };
            }
            _ => {
                self.additional_fields.insert(
                    field_name.to_string(),
                    serde_json::Value::String(value_str),
                );
            }
        }
    }
    
    fn record_str(&mut self, field: &Field, value: &str) {
        let field_name = field.name();
        
        match field_name {
            "component" => self.context.component = value.to_string(),
            "function" => self.context.function = value.to_string(),
            "opportunity_id" => self.context.opportunity_id = Some(value.to_string()),
            "market_regime" => self.context.market_regime = Some(value.to_string()),
            "strategy_type" => self.context.strategy_type = Some(value.to_string()),
            _ => {
                self.additional_fields.insert(
                    field_name.to_string(),
                    serde_json::Value::String(value.to_string()),
                );
            }
        }
    }
    
    fn record_u64(&mut self, field: &Field, value: u64) {
        let field_name = field.name();
        
        match field_name {
            "chain_id" => self.context.chain_id = Some(value),
            _ => {
                self.additional_fields.insert(
                    field_name.to_string(),
                    serde_json::Value::Number(serde_json::Number::from(value)),
                );
            }
        }
    }
}

/// System health metrics for heartbeat logging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthMetrics {
    pub opportunities_scanned_last_minute: u64,
    pub trades_executed_last_hour: u64,
    pub active_strategy: String,
    pub current_market_regime: String,
    pub data_stream_status: HashMap<String, String>,
    pub rpc_latency_ms: HashMap<String, u64>,
    pub memory_usage_mb: u64,
    pub cpu_usage_percent: f64,
    pub uptime_seconds: u64,
}

/// Heartbeat logger for periodic system health reporting
pub struct HeartbeatLogger {
    metrics: Arc<RwLock<SystemHealthMetrics>>,
    last_heartbeat: Arc<RwLock<chrono::DateTime<chrono::Utc>>>,
}

impl HeartbeatLogger {
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(RwLock::new(SystemHealthMetrics {
                opportunities_scanned_last_minute: 0,
                trades_executed_last_hour: 0,
                active_strategy: "zen_geometer".to_string(),
                current_market_regime: "unknown".to_string(),
                data_stream_status: HashMap::new(),
                rpc_latency_ms: HashMap::new(),
                memory_usage_mb: 0,
                cpu_usage_percent: 0.0,
                uptime_seconds: 0,
            })),
            last_heartbeat: Arc::new(RwLock::new(chrono::Utc::now())),
        }
    }
    
    pub async fn update_metrics(&self, metrics: SystemHealthMetrics) {
        *self.metrics.write().await = metrics;
    }
    
    pub async fn log_heartbeat(&self) {
        let metrics = self.metrics.read().await.clone();
        let mut last_heartbeat = self.last_heartbeat.write().await;
        let now = chrono::Utc::now();
        
        tracing::info!(
            component = "HeartbeatLogger",
            function = "log_heartbeat",
            opportunities_scanned = metrics.opportunities_scanned_last_minute,
            trades_executed = metrics.trades_executed_last_hour,
            active_strategy = %metrics.active_strategy,
            market_regime = %metrics.current_market_regime,
            uptime_seconds = metrics.uptime_seconds,
            memory_mb = metrics.memory_usage_mb,
            cpu_percent = metrics.cpu_usage_percent,
            "System heartbeat - bot operational"
        );
        
        *last_heartbeat = now;
    }
    
    pub async fn should_log_heartbeat(&self, interval_seconds: u64) -> bool {
        let last = *self.last_heartbeat.read().await;
        let now = chrono::Utc::now();
        (now - last).num_seconds() >= interval_seconds as i64
    }
}

/// Macro for structured error logging with context
#[macro_export]
macro_rules! log_error {
    ($context:expr, $error_code:expr, $($arg:tt)*) => {
        tracing::error!(
            component = %$context.component,
            function = %$context.function,
            trace_id = %$context.trace_id,
            opportunity_id = ?$context.opportunity_id,
            error_code = %$error_code.as_str(),
            chain_id = ?$context.chain_id,
            token_path = ?$context.token_path,
            estimated_profit_usd = ?$context.estimated_profit_usd,
            gas_estimate_gwei = ?$context.gas_estimate_gwei,
            market_regime = ?$context.market_regime,
            strategy_type = ?$context.strategy_type,
            $($arg)*
        );
    };
}

/// Macro for structured warning logging with context
#[macro_export]
macro_rules! log_warning {
    ($context:expr, $error_code:expr, $($arg:tt)*) => {
        tracing::warn!(
            component = %$context.component,
            function = %$context.function,
            trace_id = %$context.trace_id,
            opportunity_id = ?$context.opportunity_id,
            error_code = %$error_code.as_str(),
            chain_id = ?$context.chain_id,
            token_path = ?$context.token_path,
            estimated_profit_usd = ?$context.estimated_profit_usd,
            gas_estimate_gwei = ?$context.gas_estimate_gwei,
            market_regime = ?$context.market_regime,
            strategy_type = ?$context.strategy_type,
            $($arg)*
        );
    };
}

/// Macro for structured info logging with context
#[macro_export]
macro_rules! log_info {
    ($context:expr, $($arg:tt)*) => {
        tracing::info!(
            component = %$context.component,
            function = %$context.function,
            trace_id = %$context.trace_id,
            opportunity_id = ?$context.opportunity_id,
            chain_id = ?$context.chain_id,
            token_path = ?$context.token_path,
            estimated_profit_usd = ?$context.estimated_profit_usd,
            gas_estimate_gwei = ?$context.gas_estimate_gwei,
            market_regime = ?$context.market_regime,
            strategy_type = ?$context.strategy_type,
            $($arg)*
        );
    };
}

/// Initialize structured logging with JSON formatter
pub fn init_structured_logging() -> Result<(), Box<dyn std::error::Error>> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
    
    let json_layer = tracing_subscriber::fmt::layer()
        .event_format(JsonFormatter);
    
    tracing_subscriber::registry()
        .with(json_layer)
        .with(tracing_subscriber::EnvFilter::from_default_env())
        .init();
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_error_code_severity() {
        assert_eq!(ErrorCode::ECriticalConfigError.severity(), AlertSeverity::Critical);
        assert_eq!(ErrorCode::EHighSlippage.severity(), AlertSeverity::Warning);
        assert_eq!(ErrorCode::ETransactionReverted.severity(), AlertSeverity::Error);
    }
    
    #[test]
    fn test_trading_context_builder() {
        let context = TradingContext::new("StrategyManager", "evaluate_opportunity")
            .with_opportunity("opp_12345678")
            .with_chain(8453)
            .with_path(vec!["WETH".to_string(), "USDC".to_string()])
            .with_profit(rust_decimal_macros::dec!(10.50));
        
        assert_eq!(context.component, "StrategyManager");
        assert_eq!(context.function, "evaluate_opportunity");
        assert_eq!(context.opportunity_id, Some("opp_12345678".to_string()));
        assert_eq!(context.chain_id, Some(8453));
        assert_eq!(context.estimated_profit_usd, Some(rust_decimal_macros::dec!(10.50)));
    }
}