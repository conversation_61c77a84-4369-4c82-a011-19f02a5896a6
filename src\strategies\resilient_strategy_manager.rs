// MISSION: Resilient Strategy Manager with Graceful Degradation
// WHY: Ensure trading strategies can adapt to component failures and data staleness
// HOW: Stale data detection, fallback scoring, conservative mode operation

use async_nats::Client as NatsClient;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::{mpsc, Mutex};
use tokio_stream::StreamExt;
use tracing::{debug, error, info, warn};

use crate::error::BasiliskError;
use crate::execution::circuit_breaker::CircuitBreaker;
use crate::execution::resilient_honeypot_detector::ResilientHoneypotDetector;
use crate::shared_types::{
    MarketRegime, NatsTopics, Opportunity, 
    degradation::{DegradationLevel, DegradationState, SecurityStatus, DataFreshness}
};
use crate::config::AethericResonanceEngineConfig;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;

pub struct ResilientStrategyManager {
    nats_client: NatsClient,
    circuit_breaker: Arc<CircuitBreaker>,
    honeypot_detector: ResilientHoneypotDetector,
    
    // Data freshness tracking
    market_regime_freshness: Arc<Mutex<DataFreshness>>,
    temporal_harmonics_freshness: Arc<Mutex<DataFreshness>>,
    network_resonance_freshness: Arc<Mutex<DataFreshness>>,
    
    // Current state
    current_market_regime: Arc<Mutex<MarketRegime>>,
    degradation_state: Arc<Mutex<DegradationState>>,
    
    // Configuration
    staleness_threshold: Duration,
    min_execution_score: Decimal,
    conservative_mode: Arc<Mutex<bool>>,
    
    // Opportunity processing
    opportunity_rx: mpsc::Receiver<Opportunity>,
    execution_request_tx: Option<mpsc::Sender<Opportunity>>,
}

impl ResilientStrategyManager {
    pub fn new(
        nats_client: NatsClient,
        opportunity_rx: mpsc::Receiver<Opportunity>,
        circuit_breaker: Arc<CircuitBreaker>,
        honeypot_detector: ResilientHoneypotDetector,
        config: AethericResonanceEngineConfig,
    ) -> Self {
        let staleness_threshold = Duration::from_secs(60); // 1 minute default
        
        Self {
            nats_client,
            circuit_breaker,
            honeypot_detector,
            market_regime_freshness: Arc::new(Mutex::new(DataFreshness::new(staleness_threshold))),
            temporal_harmonics_freshness: Arc::new(Mutex::new(DataFreshness::new(staleness_threshold))),
            network_resonance_freshness: Arc::new(Mutex::new(DataFreshness::new(staleness_threshold))),
            current_market_regime: Arc::new(Mutex::new(MarketRegime::CalmOrderly)),
            degradation_state: Arc::new(Mutex::new(DegradationState::new(
                DegradationLevel::Operational,
                "System starting up".to_string()
            ))),
            staleness_threshold,
            min_execution_score: config.min_resonance_score.unwrap_or_default(),
            conservative_mode: Arc::new(Mutex::new(false)),
            opportunity_rx,
            execution_request_tx: None,
        }
    }

    pub async fn start(&mut self) -> Result<(), BasiliskError> {
        info!("Starting Resilient Strategy Manager");
        
        // Start data monitoring
        self.start_data_monitoring().await?;
        
        // Start opportunity processing
        self.start_opportunity_processing().await;
        
        // Start health monitoring
        self.start_health_monitoring().await;
        
        Ok(())
    }

    async fn start_data_monitoring(&self) -> Result<(), BasiliskError> {
        let nats_client = self.nats_client.clone();
        let market_regime_freshness = Arc::clone(&self.market_regime_freshness);
        let current_market_regime = Arc::clone(&self.current_market_regime);
        let degradation_state = Arc::clone(&self.degradation_state);
        let conservative_mode = Arc::clone(&self.conservative_mode);
        let staleness_threshold = self.staleness_threshold;

        tokio::spawn(async move {
            let mut subscriber = match nats_client.subscribe(NatsTopics::STATE_MARKET_REGIME).await {
                Ok(sub) => sub,
                Err(e) => {
                    error!("Failed to subscribe to market regime updates: {}", e);
                    return;
                }
            };

            while let Some(message) = subscriber.next().await {
                match serde_json::from_slice::<MarketRegime>(&message.payload) {
                    Ok(regime) => {
                        // Update freshness
                        market_regime_freshness.lock().await.update();
                        
                        // Update current regime
                        *current_market_regime.lock().await = regime.clone();
                        
                        // Exit conservative mode if we were in it due to stale data
                        if *conservative_mode.lock().await {
                            *conservative_mode.lock().await = false;
                            info!("Exiting conservative mode - fresh market regime data received");
                            
                            *degradation_state.lock().await = DegradationState::new(
                                DegradationLevel::Operational,
                                "Fresh market data restored".to_string()
                            );
                        }
                        
                        debug!("Market regime updated: {:?}", regime);
                    }
                    Err(e) => {
                        warn!("Failed to deserialize market regime: {}", e);
                    }
                }
            }
        });

        Ok(())
    }

    async fn start_opportunity_processing(&mut self) {
        let circuit_breaker = Arc::clone(&self.circuit_breaker);
        let honeypot_detector = self.honeypot_detector.clone();
        let market_regime_freshness = Arc::clone(&self.market_regime_freshness);
        let current_market_regime = Arc::clone(&self.current_market_regime);
        let conservative_mode = Arc::clone(&self.conservative_mode);
        let degradation_state = Arc::clone(&self.degradation_state);
        let min_execution_score = self.min_execution_score;

        // Move the receiver out of self
        let mut opportunity_rx = std::mem::replace(&mut self.opportunity_rx, {
            let (_, rx) = mpsc::channel(1);
            rx
        });

        tokio::spawn(async move {
            while let Some(opportunity) = opportunity_rx.recv().await {
                match Self::process_opportunity_with_resilience(
                    opportunity,
                    &circuit_breaker,
                    &honeypot_detector,
                    &market_regime_freshness,
                    &current_market_regime,
                    &conservative_mode,
                    &degradation_state,
                    min_execution_score,
                ).await {
                    Ok(processed_opportunity) => {
                        if let Some(opp) = processed_opportunity {
                            info!("Opportunity approved for execution: {:?}", opp.base().id);
                            // Would send to execution manager here
                        }
                    }
                    Err(e) => {
                        warn!("Failed to process opportunity: {}", e);
                    }
                }
            }
        });
    }

    async fn process_opportunity_with_resilience(
        mut opportunity: Opportunity,
        circuit_breaker: &CircuitBreaker,
        honeypot_detector: &ResilientHoneypotDetector,
        market_regime_freshness: &Arc<Mutex<DataFreshness>>,
        current_market_regime: &Arc<Mutex<MarketRegime>>,
        conservative_mode: &Arc<Mutex<bool>>,
        degradation_state: &Arc<Mutex<DegradationState>>,
        min_execution_score: Decimal,
    ) -> Result<Option<Opportunity>, BasiliskError> {
        
        // Step 1: Check data freshness
        let is_data_stale = market_regime_freshness.lock().await.is_stale();
        
        if is_data_stale {
            warn!("Market regime data is stale. Reverting to conservative scoring.");
            
            // Enter conservative mode
            *conservative_mode.lock().await = true;
            
            // Update degradation state
            *degradation_state.lock().await = DegradationState::new(
                DegradationLevel::SafeMode,
                "Market regime data is stale".to_string()
            )
            .with_affected_services(vec!["StrategyManager".to_string()])
            .with_fallback_actions(vec!["Using conservative scoring".to_string()]);
        }

        // Step 2: Get current market regime (or fallback)
        let market_regime = if is_data_stale {
            MarketRegime::CalmOrderly // Hardcoded safe default
        } else {
            current_market_regime.lock().await.clone()
        };

        // Step 3: Security check with resilient honeypot detector
        let security_status = circuit_breaker.execute("security_check", || async {
            // Extract token addresses from opportunity
            let token_addresses = Self::extract_token_addresses(&opportunity);
            
            for address in token_addresses {
                let status = honeypot_detector.check_security_status(address).await;
                if !status.is_safe_to_trade() {
                    return Ok(status);
                }
            }
            
            Ok(SecurityStatus::Safe)
        }).await.unwrap_or(SecurityStatus::Uncertain);

        // Step 4: Apply security penalty to scoring
        let security_penalty = security_status.risk_penalty();
        
        if security_penalty >= 1.0 {
            info!("Opportunity rejected due to security concerns: {:?}", opportunity.base().id);
            return Ok(None);
        }

        // Step 5: Calculate score with regime and security adjustments
        let base_score = Self::calculate_base_score(&opportunity, &market_regime);
        let security_penalty_decimal = Decimal::try_from(security_penalty).unwrap_or(dec!(0.0));
        let adjusted_score = base_score * (dec!(1.0) - security_penalty_decimal);
        
        // Step 6: Apply conservative mode penalty if active
        let final_score = if *conservative_mode.lock().await {
            adjusted_score * dec!(0.5) // 50% penalty in conservative mode
        } else {
            adjusted_score
        };

        // Step 7: Check if score meets threshold
        if final_score < min_execution_score {
            debug!(
                "Opportunity score {} below threshold {}: {:?}",
                final_score, min_execution_score, opportunity.base().id
            );
            return Ok(None);
        }

        // Step 8: Update opportunity with final score
        opportunity.base_mut().aetheric_resonance_score = Some(final_score);
        
        info!(
            "Opportunity approved: {} (base: {}, security_penalty: {}, final: {})",
            opportunity.base().id, base_score, security_penalty, final_score
        );

        Ok(Some(opportunity))
    }

    fn extract_token_addresses(opportunity: &Opportunity) -> Vec<ethers::types::Address> {
        // Extract token addresses from opportunity for security checking
        // This would be implemented based on the specific opportunity structure
        vec![] // Placeholder
    }

    fn calculate_base_score(opportunity: &Opportunity, regime: &MarketRegime) -> Decimal {
        // Implement base scoring logic with regime adjustments
        let base = dec!(0.7); // Placeholder base score
        
        match regime {
            MarketRegime::CalmOrderly => base * dec!(0.8),
            MarketRegime::RetailFomoSpike => base * dec!(1.2),
            MarketRegime::BotGasWar => base * dec!(0.6),
            MarketRegime::HighVolatilityCorrection => base * dec!(0.4),
            MarketRegime::Trending => base,
            MarketRegime::Unknown => base * dec!(0.5),
        }
    }

    async fn start_health_monitoring(&self) {
        let circuit_breaker = Arc::clone(&self.circuit_breaker);
        let degradation_state = Arc::clone(&self.degradation_state);
        let conservative_mode = Arc::clone(&self.conservative_mode);

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30));
            
            loop {
                interval.tick().await;
                
                // Check circuit breaker states
                let states = circuit_breaker.get_all_states().await;
                let mut degraded_services = Vec::new();
                let mut max_degradation = DegradationLevel::Operational;
                
                for (service, (state, degradation)) in states {
                    if degradation != DegradationLevel::Operational {
                        degraded_services.push(service);
                        if matches!(degradation, DegradationLevel::Emergency | DegradationLevel::Shutdown) {
                            max_degradation = degradation;
                        } else if max_degradation == DegradationLevel::Operational {
                            max_degradation = degradation;
                        }
                    }
                }
                
                // Update system degradation state
                if !degraded_services.is_empty() {
                    let reason = format!("Services degraded: {}", degraded_services.join(", "));
                    let degradation_level_copy = max_degradation.clone();
                    *degradation_state.lock().await = DegradationState::new(max_degradation, reason)
                        .with_affected_services(degraded_services);
                    
                    // Enter conservative mode for significant degradation
                    if matches!(degradation_level_copy, DegradationLevel::SafeMode | DegradationLevel::Emergency) {
                        *conservative_mode.lock().await = true;
                    }
                } else if *conservative_mode.lock().await {
                    // Check if we can exit conservative mode
                    *conservative_mode.lock().await = false;
                    *degradation_state.lock().await = DegradationState::new(
                        DegradationLevel::Operational,
                        "All services restored".to_string()
                    );
                }
            }
        });
    }

    pub async fn get_health_status(&self) -> DegradationState {
        self.degradation_state.lock().await.clone()
    }

    pub async fn is_conservative_mode(&self) -> bool {
        *self.conservative_mode.lock().await
    }

    pub async fn force_conservative_mode(&self, reason: String) {
        *self.conservative_mode.lock().await = true;
        *self.degradation_state.lock().await = DegradationState::new(
            DegradationLevel::SafeMode,
            reason
        )
        .with_fallback_actions(vec!["Forced conservative mode".to_string()]);
        
        warn!("Forced conservative mode activated");
    }

    pub async fn exit_conservative_mode(&self) {
        *self.conservative_mode.lock().await = false;
        *self.degradation_state.lock().await = DegradationState::new(
            DegradationLevel::Operational,
            "Conservative mode manually disabled".to_string()
        );
        
        info!("Conservative mode manually disabled");
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_security_penalty_application() {
        let base_score = dec!(0.8);
        let security_penalty = 0.3; // 30% penalty
        let security_penalty_decimal = Decimal::try_from(security_penalty).unwrap_or(dec!(0.0));
        let adjusted_score = base_score * (dec!(1.0) - security_penalty_decimal);
        
        assert_eq!(adjusted_score, dec!(0.56));
    }

    #[test]
    fn test_conservative_mode_penalty() {
        let adjusted_score = dec!(0.8);
        let conservative_penalty = dec!(0.5);
        let final_score = adjusted_score * conservative_penalty;
        
        assert_eq!(final_score, dec!(0.4));
    }
}