// Utility functions for Stargate Compass integration testing
// Common operations and helper functions used across test components

use super::core::*;
use ethers::types::{Address, H256, U256};
use std::path::{Path, PathBuf};
use std::fs;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use std::collections::HashMap;
use anyhow::{Result, anyhow};
use serde_json;
use toml;
use regex::Regex;

// ============= FILE SYSTEM UTILITIES =============

/// Find configuration files in the project
pub fn find_config_files() -> Result<Vec<PathBuf>> {
    let mut config_files = Vec::new();
    let config_dir = Path::new("config");
    
    if !config_dir.exists() {
        return Err(anyhow!("Config directory not found"));
    }
    
    // Look for specific config files
    let target_files = ["local.toml", "testnet.toml", "default.toml"];
    
    for file_name in &target_files {
        let file_path = config_dir.join(file_name);
        if file_path.exists() {
            config_files.push(file_path);
        }
    }
    
    if config_files.is_empty() {
        return Err(anyhow!("No configuration files found"));
    }
    
    Ok(config_files)
}

/// Create backup of a file
pub fn backup_file(file_path: &Path) -> Result<PathBuf> {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)?
        .as_secs();
    
    let backup_name = format!(
        "{}.backup.{}",
        file_path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown"),
        timestamp
    );
    
    let backup_path = file_path.parent()
        .unwrap_or_else(|| Path::new("."))
        .join(backup_name);
    
    fs::copy(file_path, &backup_path)?;
    Ok(backup_path)
}

/// Restore file from backup
pub fn restore_file(original_path: &Path, backup_path: &Path) -> Result<()> {
    fs::copy(backup_path, original_path)?;
    fs::remove_file(backup_path)?;
    Ok(())
}

/// Read TOML configuration file
pub fn read_toml_config(file_path: &Path) -> Result<toml::Value> {
    let content = fs::read_to_string(file_path)?;
    let config: toml::Value = toml::from_str(&content)?;
    Ok(config)
}

/// Write TOML configuration file
pub fn write_toml_config(file_path: &Path, config: &toml::Value) -> Result<()> {
    let content = toml::to_string_pretty(config)?;
    fs::write(file_path, content)?;
    Ok(())
}

/// Update contract address in TOML configuration
pub fn update_contract_address_in_toml(
    config: &mut toml::Value,
    new_address: Address,
) -> Result<bool> {
    let address_str = format!("{:?}", new_address);
    let mut updated = false;
    
    // Comprehensive list of paths where Stargate Compass contract addresses might be stored
    let address_paths = [
        // Standard contract section patterns
        "contracts.stargate_compass_v1",
        "contracts.stargate_compass",
        "contracts.compass_v1", 
        "contracts.compass",
        
        // Alternative contract section patterns
        "contract_addresses.stargate_compass_v1",
        "contract_addresses.stargate_compass",
        "contract_addresses.compass_v1",
        "contract_addresses.compass",
        
        // Address section patterns
        "addresses.stargate_compass_v1",
        "addresses.stargate_compass",
        "addresses.compass_v1",
        "addresses.compass",
        
        // Stargate-specific section patterns
        "stargate.compass_v1",
        "stargate.compass",
        "stargate_compass.address",
        "stargate_compass_v1.address",
        
        // Execution-specific patterns
        "execution.contracts.stargate_compass_v1",
        "execution.contracts.stargate_compass",
        "execution.addresses.stargate_compass_v1",
        "execution.addresses.stargate_compass",
        
        // Network-specific patterns
        "network.contracts.stargate_compass_v1",
        "network.contracts.stargate_compass",
        
        // Legacy patterns that might exist
        "stargate_compass_address",
        "compass_address",
        "stargate_compass_v1_address",
    ];
    
    // First pass: try exact path matches
    for path in &address_paths {
        if let Some(value) = get_nested_value_mut(config, path) {
            *value = toml::Value::String(address_str.clone());
            updated = true;
        }
    }
    
    // Second pass: search for any key containing "stargate" or "compass" in contracts section
    if !updated {
        if let Some(contracts_section) = config.get_mut("contracts") {
            if let Some(contracts_table) = contracts_section.as_table_mut() {
                let keys_to_update: Vec<String> = contracts_table
                    .keys()
                    .filter(|key| {
                        let key_lower = key.to_lowercase();
                        key_lower.contains("stargate") || key_lower.contains("compass")
                    })
                    .cloned()
                    .collect();
                
                for key in keys_to_update {
                    if let Some(value) = contracts_table.get_mut(&key) {
                        *value = toml::Value::String(address_str.clone());
                        updated = true;
                    }
                }
            }
        }
    }
    
    // Third pass: search in any section that might contain addresses
    if !updated {
        let sections_to_search = ["addresses", "contract_addresses", "stargate", "execution"];
        
        for section_name in &sections_to_search {
            if let Some(section) = config.get_mut(section_name) {
                if let Some(section_table) = section.as_table_mut() {
                    let keys_to_update: Vec<String> = section_table
                        .keys()
                        .filter(|key| {
                            let key_lower = key.to_lowercase();
                            key_lower.contains("stargate") || key_lower.contains("compass")
                        })
                        .cloned()
                        .collect();
                    
                    for key in keys_to_update {
                        if let Some(value) = section_table.get_mut(&key) {
                            *value = toml::Value::String(address_str.clone());
                            updated = true;
                        }
                    }
                }
            }
        }
    }
    
    Ok(updated)
}

/// Get nested value from TOML configuration
fn get_nested_value_mut<'a>(config: &'a mut toml::Value, path: &str) -> Option<&'a mut toml::Value> {
    let parts: Vec<&str> = path.split('.').collect();
    let mut current = config;
    
    for part in parts {
        match current {
            toml::Value::Table(table) => {
                current = table.get_mut(part)?;
            }
            _ => return None,
        }
    }
    
    Some(current)
}

/// Validate Ethereum address format
pub fn validate_ethereum_address(address_str: &str) -> Result<Address> {
    let address_regex = Regex::new(r"^0x[a-fA-F0-9]{40}$")?;
    
    if !address_regex.is_match(address_str) {
        return Err(anyhow!("Invalid Ethereum address format: {}", address_str));
    }
    
    address_str.parse::<Address>()
        .map_err(|e| anyhow!("Failed to parse address: {}", e))
}

// ============= PROCESS UTILITIES =============

/// Execute command with timeout
pub async fn execute_command_with_timeout(
    command: &str,
    args: &[&str],
    timeout_secs: u64,
) -> Result<CommandOutput> {
    use tokio::process::Command;
    use tokio::time::timeout;
    
    let start_time = Instant::now();
    
    let mut cmd = Command::new(command);
    cmd.args(args);
    
    let output = timeout(
        Duration::from_secs(timeout_secs),
        cmd.output()
    ).await??;
    
    Ok(CommandOutput {
        stdout: String::from_utf8_lossy(&output.stdout).to_string(),
        stderr: String::from_utf8_lossy(&output.stderr).to_string(),
        exit_code: output.status.code(),
        execution_time: start_time.elapsed(),
        timeout_occurred: false,
    })
}

/// Command execution output
#[derive(Debug, Clone)]
pub struct CommandOutput {
    pub stdout: String,
    pub stderr: String,
    pub exit_code: Option<i32>,
    pub execution_time: Duration,
    pub timeout_occurred: bool,
}

/// Check if a process is running
pub fn is_process_running(process_name: &str) -> bool {
    use std::process::Command;
    
    #[cfg(target_os = "windows")]
    {
        Command::new("tasklist")
            .arg("/FI")
            .arg(&format!("IMAGENAME eq {}", process_name))
            .output()
            .map(|output| {
                String::from_utf8_lossy(&output.stdout)
                    .contains(process_name)
            })
            .unwrap_or(false)
    }
    
    #[cfg(not(target_os = "windows"))]
    {
        Command::new("pgrep")
            .arg(process_name)
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false)
    }
}

// ============= DATA VALIDATION UTILITIES =============

/// Compare two decimal values with tolerance
pub fn compare_decimals_with_tolerance(
    expected: rust_decimal::Decimal,
    actual: rust_decimal::Decimal,
    tolerance_percent: rust_decimal::Decimal,
) -> bool {
    if expected.is_zero() && actual.is_zero() {
        return true;
    }
    
    if expected.is_zero() {
        return actual.abs() <= tolerance_percent;
    }
    
    let diff = (actual - expected).abs();
    let tolerance = expected.abs() * tolerance_percent / rust_decimal::Decimal::from(100);
    
    diff <= tolerance
}

/// Validate transaction hash format
pub fn validate_transaction_hash(hash_str: &str) -> Result<H256> {
    let hash_regex = Regex::new(r"^0x[a-fA-F0-9]{64}$")?;
    
    if !hash_regex.is_match(hash_str) {
        return Err(anyhow!("Invalid transaction hash format: {}", hash_str));
    }
    
    hash_str.parse::<H256>()
        .map_err(|e| anyhow!("Failed to parse transaction hash: {}", e))
}

/// Extract numeric value from string (handles various formats)
pub fn extract_numeric_value(text: &str) -> Option<rust_decimal::Decimal> {
    use rust_decimal::Decimal;
    use std::str::FromStr;
    
    // Remove common prefixes and suffixes
    let cleaned = text
        .replace("$", "")
        .replace(",", "")
        .replace(" ", "")
        .replace("ETH", "")
        .replace("USDC", "")
        .replace("USD", "");
    
    // Try to parse as decimal
    Decimal::from_str(&cleaned).ok()
}

/// Parse balance string (e.g., "1.234 ETH" -> Decimal)
pub fn parse_balance_string(balance_str: &str) -> Result<rust_decimal::Decimal> {
    let numeric_part = extract_numeric_value(balance_str)
        .ok_or_else(|| anyhow!("Could not extract numeric value from: {}", balance_str))?;
    
    Ok(numeric_part)
}

// ============= TIMING UTILITIES =============

/// Measure execution time of an async operation
pub async fn measure_async<F, T>(operation: F) -> (T, Duration)
where
    F: std::future::Future<Output = T>,
{
    let start = Instant::now();
    let result = operation.await;
    let duration = start.elapsed();
    (result, duration)
}

/// Wait with exponential backoff
pub async fn wait_with_backoff(
    initial_delay_ms: u64,
    max_delay_ms: u64,
    attempt: u32,
) {
    let delay_ms = std::cmp::min(
        initial_delay_ms * 2_u64.pow(attempt),
        max_delay_ms,
    );
    
    tokio::time::sleep(Duration::from_millis(delay_ms)).await;
}

/// Retry operation with exponential backoff
pub async fn retry_with_backoff<F, T, E>(
    operation: F,
    max_attempts: u32,
    initial_delay_ms: u64,
    max_delay_ms: u64,
) -> Result<T>
where
    F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T, E>> + Send>>,
    E: std::fmt::Display,
{
    let mut last_error = None;
    
    for attempt in 0..max_attempts {
        match operation().await {
            Ok(result) => return Ok(result),
            Err(e) => {
                last_error = Some(format!("{}", e));
                if attempt < max_attempts - 1 {
                    wait_with_backoff(initial_delay_ms, max_delay_ms, attempt).await;
                }
            }
        }
    }
    
    Err(anyhow!(
        "Operation failed after {} attempts. Last error: {}",
        max_attempts,
        last_error.unwrap_or_else(|| "Unknown error".to_string())
    ))
}

// ============= LOGGING UTILITIES =============

/// Create a timestamped log entry
pub fn create_log_entry(level: LogLevel, message: &str, context: Option<&str>) -> LogEntry {
    LogEntry {
        timestamp: chrono::Utc::now(),
        level,
        message: message.to_string(),
        context: context.map(|s| s.to_string()),
        component: "integration_test".to_string(),
    }
}

/// Log levels for integration testing
#[derive(Debug, Clone)]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
    Critical,
}

/// Log entry structure
#[derive(Debug, Clone)]
pub struct LogEntry {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub level: LogLevel,
    pub message: String,
    pub context: Option<String>,
    pub component: String,
}

impl LogEntry {
    pub fn format(&self) -> String {
        let context_str = self.context
            .as_ref()
            .map(|c| format!(" [{}]", c))
            .unwrap_or_default();
        
        format!(
            "{} [{:?}] {}{}: {}",
            self.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
            self.level,
            self.component,
            context_str,
            self.message
        )
    }
}

// ============= SYSTEM INFORMATION =============

/// System information structure for test reports
#[derive(Debug, Clone)]
pub struct SystemInfo {
    pub os: String,
    pub arch: String,
    pub rust_version: String,
    pub cargo_version: String,
    pub available_memory_mb: u64,
    pub cpu_cores: u32,
}

/// Get system information for test reports
pub fn get_system_info() -> SystemInfo {
    SystemInfo {
        os: std::env::consts::OS.to_string(),
        arch: std::env::consts::ARCH.to_string(),
        rust_version: get_rust_version(),
        cargo_version: get_cargo_version(),
        available_memory_mb: get_available_memory_mb(),
        cpu_cores: num_cpus::get() as u32,
    }
}

/// Get Rust version
fn get_rust_version() -> String {
    std::process::Command::new("rustc")
        .arg("--version")
        .output()
        .ok()
        .and_then(|output| String::from_utf8(output.stdout).ok())
        .unwrap_or_else(|| "unknown".to_string())
        .trim()
        .to_string()
}

/// Get Cargo version
fn get_cargo_version() -> String {
    std::process::Command::new("cargo")
        .arg("--version")
        .output()
        .ok()
        .and_then(|output| String::from_utf8(output.stdout).ok())
        .unwrap_or_else(|| "unknown".to_string())
        .trim()
        .to_string()
}

/// Get available memory in MB (simplified)
fn get_available_memory_mb() -> u64 {
    // This is a simplified implementation
    // In production, you might use a crate like `sysinfo`
    8192 // Default to 8GB
}

// ============= TEST DATA GENERATION =============

/// Generate test Ethereum address
pub fn generate_test_address(seed: u8) -> Address {
    let mut bytes = [0u8; 20];
    bytes[0] = seed;
    bytes[19] = seed;
    Address::from(bytes)
}

/// Generate test transaction hash
pub fn generate_test_tx_hash(seed: u8) -> H256 {
    let mut bytes = [0u8; 32];
    bytes[0] = seed;
    bytes[31] = seed;
    H256::from(bytes)
}

/// Create mock opportunity data for testing
pub fn create_mock_opportunity() -> HashMap<String, String> {
    let mut opportunity = HashMap::new();
    opportunity.insert("token_in".to_string(), "USDC".to_string());
    opportunity.insert("token_out".to_string(), "WETH".to_string());
    opportunity.insert("amount_in".to_string(), "1000.0".to_string());
    opportunity.insert("expected_profit".to_string(), "10.5".to_string());
    opportunity.insert("gas_estimate".to_string(), "150000".to_string());
    opportunity
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    #[test]
    fn test_validate_ethereum_address() {
        // Valid address
        let valid_addr = "******************************************";
        assert!(validate_ethereum_address(valid_addr).is_ok());
        
        // Invalid addresses
        let invalid_addrs = [
            "0x742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4", // Too short
            "742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4e",  // Missing 0x
            "0x742d35Cc6634C0532925a3b8D4C9db4C2b7e9b4g", // Invalid character
        ];
        
        for addr in &invalid_addrs {
            assert!(validate_ethereum_address(addr).is_err());
        }
    }

    #[test]
    fn test_compare_decimals_with_tolerance() {
        use rust_decimal::Decimal;
        use std::str::FromStr;
        
        let expected = Decimal::from_str("100.0").unwrap();
        let actual = Decimal::from_str("101.0").unwrap();
        let tolerance = Decimal::from_str("2.0").unwrap(); // 2%
        
        assert!(compare_decimals_with_tolerance(expected, actual, tolerance));
        
        let actual_far = Decimal::from_str("105.0").unwrap();
        assert!(!compare_decimals_with_tolerance(expected, actual_far, tolerance));
    }

    #[test]
    fn test_extract_numeric_value() {
        assert_eq!(
            extract_numeric_value("$1,234.56 USD"),
            Some(rust_decimal::Decimal::from_str("1234.56").unwrap())
        );
        
        assert_eq!(
            extract_numeric_value("10.5 ETH"),
            Some(rust_decimal::Decimal::from_str("10.5").unwrap())
        );
        
        assert_eq!(extract_numeric_value("invalid"), None);
    }

    #[test]
    fn test_generate_test_data() {
        let addr1 = generate_test_address(1);
        let addr2 = generate_test_address(2);
        assert_ne!(addr1, addr2);
        
        let hash1 = generate_test_tx_hash(1);
        let hash2 = generate_test_tx_hash(2);
        assert_ne!(hash1, hash2);
    }
}