// Main integration test for Stargate Compass
// Demonstrates the complete testing framework structure

#[path = "backend_tester.rs"]
mod backend_tester;
#[path = "tui_tester.rs"]
mod tui_tester;
#[path = "config_manager.rs"]
mod config_manager;
#[path = "test_reporter.rs"]
mod test_reporter;
#[path = "anvil_client.rs"]
mod anvil_client;
#[path = "utils.rs"]
mod utils;
#[path = "core.rs"]
mod core;

use backend_tester::BackendIntegrationTester;
use config_manager::ConfigurationManager;
use tui_tester::TuiFunctionalityTester;

use anvil_client::AnvilClient;
use utils::{validate_ethereum_address, extract_numeric_value, generate_test_address, get_system_info, update_contract_address_in_toml};
use core::{TestResult, TestDetails, IntegrationTestError, ConfigErrorType, ConfigurationTestResult, BackendTestResult, <PERSON>iTestR<PERSON>ult, EndToEndTestResult, AnvilTestResult, BackendErrorType, TuiErrorType, ContractErrorType, EnvironmentErrorType, AnvilErrorType, TestError};
use anyhow::Result;

#[tokio::test]
async fn test_integration_framework_structure() -> Result<()> {
    // Test that all core components can be instantiated
    let config_manager = ConfigurationManager::new();
    assert!(config_manager.is_ok(), "ConfigurationManager should be creatable");
    
    let backend_tester = BackendIntegrationTester::new();
    assert_eq!(backend_tester.component_name(), "BackendIntegration");
    
    let tui_tester = TuiFunctionalityTester::new();
    assert_eq!(tui_tester.component_name(), "TuiFunctionality");
    
    let mut test_reporter = TestReporter::new();
    
    // Test that components implement the required traits
    assert!(backend_tester.is_ready().await?);
    assert!(tui_tester.is_ready().await?);
    
    // Test basic test execution
    let backend_result = backend_tester.run_test().await?;
    assert!(backend_result.success, "Backend test should succeed");
    
    let tui_result = tui_tester.run_test().await?;
    assert!(tui_result.success, "TUI test should succeed");
    
    println!("✅ Integration test framework structure validated");
    Ok(())
}

#[tokio::test]
async fn test_anvil_client_availability() -> Result<()> {
    if !is_anvil_available() {
        println!("⚠️  Anvil not available - skipping blockchain tests");
        return Ok(());
    }
    
    // Test Anvil client creation
    let client = AnvilClient::new_base_fork(None).await;
    assert!(client.is_ok(), "Should be able to create Anvil client");
    
    if let Ok(client) = client {
        // Test basic blockchain operations
        let health = client.health_check().await;
        assert!(health.is_ok(), "Health check should pass");
        
        let block_number = client.current_block().await;
        assert!(block_number.is_ok(), "Should be able to get block number");
        
        println!("✅ Anvil client functionality validated");
    }
    
    Ok(())
}

#[tokio::test]
async fn test_utility_functions() -> Result<()> {
    // Test address validation
    let valid_addr = "******************************************";
    assert!(validate_ethereum_address(valid_addr).is_ok());
    
    // Test numeric extraction
    let balance_str = "$1,234.56 USD";
    let extracted = extract_numeric_value(balance_str);
    assert!(extracted.is_some());
    
    // Test test data generation
    let addr1 = generate_test_address(1);
    let addr2 = generate_test_address(2);
    assert_ne!(addr1, addr2);
    
    // Test system info
    let sys_info = get_system_info();
    assert!(!sys_info.os.is_empty());
    assert!(!sys_info.arch.is_empty());
    
    println!("✅ Utility functions validated");
    Ok(())
}

#[test]
fn test_core_data_structures() {
    // Test TestResult creation
    let success_result = TestResult::success(
        "TestComponent".to_string(),
        TestDetails::Configuration(ConfigurationTestResult {
            success: true,
            config_files_updated: vec!["test.toml".to_string()],
            validation_errors: Vec::new(),
            backup_created: true,
            contract_address_updated: true,
            original_address: None,
            new_address: None,
        })
    );
    
    assert!(success_result.success);
    assert_eq!(success_result.component_name, "TestComponent");
    
    // Test error creation
    let error = TestError::new(
        IntegrationTestError::ConfigurationError {
            config_file: "test.toml".to_string(),
            error_type: ConfigErrorType::FileNotFound,
            message: "File not found".to_string(),
        },
        "Test context".to_string(),
        true
    );
    
    assert!(error.recoverable);
    assert_eq!(error.context, "Test context");
    
    // Test execution ID generation
    let id1 = generate_execution_id();
    let id2 = generate_execution_id();
    assert_ne!(id1, id2);
    
    println!("✅ Core data structures validated");
}

#[test]
fn test_configuration_utilities() {
    // Test TOML configuration handling
    let mut config = toml::Value::Table(toml::map::Map::new());
    
    // Add a nested structure
    let mut contracts = toml::map::Map::new();
    contracts.insert("stargate_compass_v1".to_string(), toml::Value::String("0x0000000000000000000000000000000000000000".to_string()));
    config.as_table_mut().unwrap().insert("contracts".to_string(), toml::Value::Table(contracts));
    
    // Test address update
    let new_address = generate_test_address(42);
    let updated = update_contract_address_in_toml(&mut config, new_address);
    assert!(updated.is_ok());
    
    println!("✅ Configuration utilities validated");
}