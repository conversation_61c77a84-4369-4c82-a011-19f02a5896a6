// MISSION: Demonstrate Enhanced Error Propagation and Alerting
// WHY: Show how the enhanced error system works with ARE components
// HOW: Create examples of error handling with propagation chains, retry logic, and alerting

use basilisk_bot::error::{
    BasiliskError, NetworkError, DataProviderError, ExecutionError, StrategyError, CriticalError,
    ErrorPropagationManager, default_alert_channels,
    enhanced::{AlertChannelConfig, AlertChannelType},
    circuit_breaker::CircuitBreakerConfig,
};
use basilisk_bot::logging::{ErrorCode, AlertSeverity};
use std::collections::HashMap;
use tokio::time::{sleep, Duration};
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    info!("Starting Enhanced Error Propagation and Alerting Demo");

    // Create error propagation manager with default alert channels
    let mut alert_channels = default_alert_channels();
    
    // Enable console logging for demo
    alert_channels[0].enabled = true;
    
    let error_manager = ErrorPropagationManager::new(alert_channels);
    error_manager.initialize_circuit_breakers().await;

    // Demo 1: RPC Error with Circuit Breaker
    info!("=== Demo 1: RPC Error Handling ===");
    demo_rpc_error_handling(&error_manager).await;

    // Demo 2: Data Provider Error with Retry Logic
    info!("=== Demo 2: Data Provider Error Handling ===");
    demo_data_provider_error_handling(&error_manager).await;

    // Demo 3: Execution Error with Context Propagation
    info!("=== Demo 3: Execution Error Handling ===");
    demo_execution_error_handling(&error_manager).await;

    // Demo 4: Strategy Error with Recovery Suggestions
    info!("=== Demo 4: Strategy Error Handling ===");
    demo_strategy_error_handling(&error_manager).await;

    // Demo 5: Critical Error with Immediate Alerting
    info!("=== Demo 5: Critical Error Handling ===");
    demo_critical_error_handling(&error_manager).await;

    // Demo 6: Circuit Breaker Protection
    info!("=== Demo 6: Circuit Breaker Protection ===");
    demo_circuit_breaker_protection(&error_manager).await;

    // Show circuit breaker statistics
    info!("=== Circuit Breaker Statistics ===");
    let stats = error_manager.get_circuit_breaker_stats().await;
    for (name, stat) in stats {
        info!(
            circuit_name = %name,
            state = ?stat.state,
            failure_count = stat.failure_count,
            success_count = stat.success_count,
            total_calls = stat.total_calls,
            "Circuit breaker statistics"
        );
    }

    info!("Enhanced Error Propagation and Alerting Demo completed");
    Ok(())
}

async fn demo_rpc_error_handling(error_manager: &ErrorPropagationManager) {
    let rpc_error = NetworkError::RpcTimeout {
        endpoint: "https://mainnet.infura.io/v3/demo".to_string(),
        timeout_ms: 5000,
    };

    let enhanced_error = error_manager.handle_rpc_error(
        rpc_error,
        1, // Ethereum mainnet
        "https://mainnet.infura.io/v3/demo",
        "get_latest_block"
    ).await;

    info!(
        error_code = %enhanced_error.error_code.as_str(),
        severity = ?enhanced_error.severity,
        is_retryable = enhanced_error.is_retryable,
        recovery_suggestions = ?enhanced_error.recovery_suggestions,
        "RPC error handled with context"
    );
}

async fn demo_data_provider_error_handling(error_manager: &ErrorPropagationManager) {
    let data_error = DataProviderError::StaleData {
        data_source: "CoinGecko".to_string(),
        age_ms: 300000, // 5 minutes
        threshold_ms: 60000, // 1 minute threshold
    };

    let enhanced_error = error_manager.handle_data_provider_error(
        data_error,
        "CoinGecko",
        "fetch_token_prices"
    ).await;

    info!(
        error_code = %enhanced_error.error_code.as_str(),
        severity = ?enhanced_error.severity,
        recovery_suggestions = ?enhanced_error.recovery_suggestions,
        "Data provider error handled with context"
    );
}

async fn demo_execution_error_handling(error_manager: &ErrorPropagationManager) {
    let execution_error = ExecutionError::InsufficientLiquidity {
        token_path: vec!["WETH".to_string(), "USDC".to_string()],
        available_usd: rust_decimal_macros::dec!(1000.0),
        required_usd: rust_decimal_macros::dec!(5000.0),
    };

    let enhanced_error = error_manager.handle_execution_error(
        execution_error,
        "opp_12345678",
        8453, // Base chain
        "execute_arbitrage"
    ).await;

    info!(
        error_code = %enhanced_error.error_code.as_str(),
        severity = ?enhanced_error.severity,
        opportunity_id = ?enhanced_error.context.opportunity_id,
        chain_id = ?enhanced_error.context.chain_id,
        recovery_suggestions = ?enhanced_error.recovery_suggestions,
        "Execution error handled with full context"
    );
}

async fn demo_strategy_error_handling(error_manager: &ErrorPropagationManager) {
    let strategy_error = StrategyError::OpportunityExpired {
        opportunity_id: "opp_87654321".to_string(),
        age_ms: 15000, // 15 seconds
    };

    let enhanced_error = error_manager.handle_strategy_error(
        strategy_error,
        "opp_87654321",
        "zen_geometer",
        "evaluate_opportunity"
    ).await;

    info!(
        error_code = %enhanced_error.error_code.as_str(),
        severity = ?enhanced_error.severity,
        opportunity_id = ?enhanced_error.context.opportunity_id,
        strategy_type = ?enhanced_error.context.additional_data.get("strategy_type"),
        "Strategy error handled with opportunity context"
    );
}

async fn demo_critical_error_handling(error_manager: &ErrorPropagationManager) {
    let critical_error = CriticalError::SecurityViolation {
        violation_type: "Unauthorized wallet access attempt".to_string(),
        details: "Multiple failed authentication attempts detected".to_string(),
    };

    let enhanced_error = error_manager.handle_critical_error(
        critical_error,
        "WalletManager",
        "authenticate_transaction"
    ).await;

    info!(
        error_code = %enhanced_error.error_code.as_str(),
        severity = ?enhanced_error.severity,
        component = %enhanced_error.context.component,
        requires_attention = ?enhanced_error.context.additional_data.get("requires_immediate_attention"),
        "Critical error handled with immediate alerting"
    );
}

async fn demo_circuit_breaker_protection(error_manager: &ErrorPropagationManager) {
    info!("Testing circuit breaker protection with repeated failures");

    // Simulate multiple failures to trigger circuit breaker
    for i in 1..=5 {
        let result = error_manager.execute_with_protection(
            "rpc_ethereum",
            "RpcProvider",
            "get_block_number",
            || -> Result<u64, NetworkError> {
                Err(NetworkError::RpcConnectionFailed {
                    endpoint: "test".to_string(),
                    reason: format!("Connection failed attempt {}", i),
                })
            }
        ).await;

        match result {
            Ok(_) => info!(attempt = i, "Operation succeeded"),
            Err(enhanced_error) => {
                info!(
                    attempt = i,
                    error_code = %enhanced_error.error_code.as_str(),
                    message = %enhanced_error.inner,
                    "Operation failed"
                );
            }
        }

        // Small delay between attempts
        sleep(Duration::from_millis(100)).await;
    }

    // Try one more operation - should be blocked by circuit breaker
    info!("Attempting operation with circuit breaker likely open");
    let result = error_manager.execute_with_protection(
        "rpc_ethereum",
        "RpcProvider", 
        "get_block_number",
        || -> Result<u64, NetworkError> {
            Ok(12345)
        }
    ).await;

    match result {
        Ok(block_number) => info!(block_number = block_number, "Operation succeeded despite circuit breaker"),
        Err(enhanced_error) => {
            info!(
                error_code = %enhanced_error.error_code.as_str(),
                message = %enhanced_error.inner,
                "Operation blocked by circuit breaker"
            );
        }
    }
}

/// Example of custom alert channel configuration
#[allow(dead_code)]
fn create_custom_alert_channels() -> Vec<AlertChannelConfig> {
    vec![
        // Console logging for all levels
        AlertChannelConfig {
            channel_type: AlertChannelType::Log,
            enabled: true,
            min_severity: AlertSeverity::Info,
            rate_limit_seconds: 0,
            config: HashMap::new(),
        },
        // Slack for critical and error alerts
        AlertChannelConfig {
            channel_type: AlertChannelType::Slack,
            enabled: true,
            min_severity: AlertSeverity::Error,
            rate_limit_seconds: 300, // 5 minutes
            config: {
                let mut config = HashMap::new();
                config.insert("webhook_url".to_string(), "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK".to_string());
                config
            },
        },
        // Discord for warnings and above
        AlertChannelConfig {
            channel_type: AlertChannelType::Discord,
            enabled: true,
            min_severity: AlertSeverity::Warning,
            rate_limit_seconds: 180, // 3 minutes
            config: {
                let mut config = HashMap::new();
                config.insert("webhook_url".to_string(), "https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK".to_string());
                config
            },
        },
        // Custom webhook for external monitoring
        AlertChannelConfig {
            channel_type: AlertChannelType::Webhook,
            enabled: true,
            min_severity: AlertSeverity::Critical,
            rate_limit_seconds: 60, // 1 minute
            config: {
                let mut config = HashMap::new();
                config.insert("url".to_string(), "https://your-monitoring-system.com/alerts".to_string());
                config.insert("auth_header".to_string(), "Bearer your-auth-token".to_string());
                config
            },
        },
    ]
}

/// Example of custom circuit breaker configuration
#[allow(dead_code)]
fn create_custom_circuit_breaker_config() -> CircuitBreakerConfig {
    CircuitBreakerConfig {
        failure_threshold: 3,           // Open after 3 failures
        success_threshold: 2,           // Close after 2 successes in half-open
        timeout_duration: Duration::from_secs(30), // Try half-open after 30 seconds
        half_open_max_calls: 2,         // Allow 2 calls in half-open state
        minimum_throughput: 5,          // Need at least 5 calls to evaluate
        sliding_window_size: Duration::from_secs(60), // Evaluate over 60 second window
    }
}