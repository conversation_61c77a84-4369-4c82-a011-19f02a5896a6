//! AUDIT-FIX: Test data providers for consistent test scenarios - Task 6.1
//! This module provides test data for all major components

use super::validation_framework::*;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use serde_json::json;
use std::collections::HashMap;

/// Test data provider for mathematical components (Vesica Piscis, FFT, etc.)
pub struct MathComponentTestDataProvider;

impl TestDataProvider for MathComponentTestDataProvider {
    fn get_test_scenarios(&self) -> Vec<TestScenario> {
        vec![
            // Vesica Piscis test scenarios
            TestScenario {
                name: "vesica_positive_deviation".to_string(),
                description: "Test Vesica Piscis with positive price deviation".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("pool_a_reserves".to_string(), json!(1000.0));
                        params.insert("pool_b_reserves".to_string(), json!(2000.0));
                        params.insert("price_deviation".to_string(), json!(0.1));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("amount_to_equalize".to_string(), json!(100.0)); // Expected positive result
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::Unit,
                tolerance: Some(dec!(0.01)),
            },
            TestScenario {
                name: "vesica_negative_deviation".to_string(),
                description: "Test Vesica Piscis with negative price deviation (critical fix)".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("pool_a_reserves".to_string(), json!(2000.0));
                        params.insert("pool_b_reserves".to_string(), json!(1000.0));
                        params.insert("price_deviation".to_string(), json!(-0.1));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("amount_to_equalize".to_string(), json!(100.0)); // Should be positive (absolute value)
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::Unit,
                tolerance: Some(dec!(0.01)),
            },
            TestScenario {
                name: "vesica_zero_deviation".to_string(),
                description: "Test Vesica Piscis with zero price deviation".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("pool_a_reserves".to_string(), json!(1000.0));
                        params.insert("pool_b_reserves".to_string(), json!(1000.0));
                        params.insert("price_deviation".to_string(), json!(0.0));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("amount_to_equalize".to_string(), json!(0.0));
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::Unit,
                tolerance: Some(dec!(0.0001)),
            },
            // FFT test scenarios
            TestScenario {
                name: "fft_small_dataset".to_string(),
                description: "Test FFT with small dataset (buffer size fix)".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("data_size".to_string(), json!(10));
                        params.insert("data_type".to_string(), json!("sine_wave"));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("dominant_cycles".to_string(), json!([60, 240]));
                        results.insert("market_rhythm_stability".to_string(), json!(0.8));
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::Unit,
                tolerance: Some(dec!(0.1)),
            },
        ]
    }

    fn get_edge_cases(&self) -> Vec<TestScenario> {
        vec![
            TestScenario {
                name: "vesica_extreme_reserves".to_string(),
                description: "Test Vesica Piscis with extreme reserve values".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("pool_a_reserves".to_string(), json!(1e18)); // Very large
                        params.insert("pool_b_reserves".to_string(), json!(1.0)); // Very small
                        params.insert("price_deviation".to_string(), json!(0.5));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: None, // We just want to ensure it doesn't crash
                scenario_type: ScenarioType::EdgeCase,
                tolerance: Some(dec!(0.01)),
            },
            TestScenario {
                name: "fft_empty_dataset".to_string(),
                description: "Test FFT with empty dataset".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("data_size".to_string(), json!(0));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: None,
                scenario_type: ScenarioType::EdgeCase,
                tolerance: None,
            },
            TestScenario {
                name: "fft_single_point".to_string(),
                description: "Test FFT with single data point".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("data_size".to_string(), json!(1));
                        params.insert("data_value".to_string(), json!(100.0));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: None,
                scenario_type: ScenarioType::EdgeCase,
                tolerance: None,
            },
        ]
    }

    fn get_stress_test_data(&self) -> Vec<TestScenario> {
        vec![
            TestScenario {
                name: "vesica_high_frequency".to_string(),
                description: "High-frequency Vesica Piscis calculations".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("pool_a_reserves".to_string(), json!(1000.0));
                        params.insert("pool_b_reserves".to_string(), json!(1500.0));
                        params.insert("price_deviation".to_string(), json!(0.05));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: None,
                scenario_type: ScenarioType::StressTest,
                tolerance: Some(dec!(0.01)),
            },
            TestScenario {
                name: "fft_large_dataset".to_string(),
                description: "FFT with large dataset for performance testing".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("data_size".to_string(), json!(10000));
                        params.insert("data_type".to_string(), json!("random"));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: None,
                scenario_type: ScenarioType::StressTest,
                tolerance: None,
            },
        ]
    }
}

/// Test data provider for execution components
pub struct ExecutionComponentTestDataProvider;

impl TestDataProvider for ExecutionComponentTestDataProvider {
    fn get_test_scenarios(&self) -> Vec<TestScenario> {
        vec![
            TestScenario {
                name: "nonce_management_basic".to_string(),
                description: "Basic nonce management functionality".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("initial_nonce".to_string(), json!(0));
                        params.insert("transaction_count".to_string(), json!(5));
                        params
                    },
                    market_conditions: None,
                    network_state: Some(NetworkState {
                        block_number: 1000,
                        timestamp: **********,
                        congestion_level: 0.5,
                        sequencer_health: "Healthy".to_string(),
                    }),
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("final_nonce".to_string(), json!(5));
                        results.insert("pending_transactions".to_string(), json!(0));
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::Unit,
                tolerance: None,
            },
            TestScenario {
                name: "gas_estimation_standard".to_string(),
                description: "Standard gas estimation scenarios".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("urgency".to_string(), json!("Standard"));
                        params.insert("transaction_type".to_string(), json!("swap"));
                        params
                    },
                    market_conditions: Some(MarketConditions {
                        volatility: dec!(0.2),
                        liquidity: dec!(1000000.0),
                        gas_price_gwei: dec!(20.0),
                        regime: "CalmOrderly".to_string(),
                    }),
                    network_state: Some(NetworkState {
                        block_number: 1000,
                        timestamp: **********,
                        congestion_level: 0.3,
                        sequencer_health: "Healthy".to_string(),
                    }),
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("gas_price_gwei".to_string(), json!(20.0));
                        results.insert("gas_limit".to_string(), json!(200000));
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::Unit,
                tolerance: Some(dec!(1.0)),
            },
            TestScenario {
                name: "circuit_breaker_functionality".to_string(),
                description: "Circuit breaker open/close functionality".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("max_failures".to_string(), json!(5));
                        params.insert("failure_count".to_string(), json!(3));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("is_open".to_string(), json!(false));
                        results.insert("failure_count".to_string(), json!(3));
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::Unit,
                tolerance: None,
            },
        ]
    }

    fn get_edge_cases(&self) -> Vec<TestScenario> {
        vec![
            TestScenario {
                name: "nonce_gap_handling".to_string(),
                description: "Handle nonce gaps in transaction sequence".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("nonce_gap".to_string(), json!(true));
                        params.insert("gap_size".to_string(), json!(3));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: None,
                scenario_type: ScenarioType::EdgeCase,
                tolerance: None,
            },
            TestScenario {
                name: "gas_price_spike".to_string(),
                description: "Handle extreme gas price spikes".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("urgency".to_string(), json!("Emergency"));
                        params.insert("base_gas_price".to_string(), json!(1000.0)); // 1000 gwei spike
                        params
                    },
                    market_conditions: Some(MarketConditions {
                        volatility: dec!(0.8),
                        liquidity: dec!(100000.0),
                        gas_price_gwei: dec!(1000.0),
                        regime: "HighVolatility".to_string(),
                    }),
                    network_state: Some(NetworkState {
                        block_number: 1000,
                        timestamp: **********,
                        congestion_level: 0.95,
                        sequencer_health: "Degraded".to_string(),
                    }),
                },
                expected_output: None,
                scenario_type: ScenarioType::EdgeCase,
                tolerance: None,
            },
        ]
    }

    fn get_stress_test_data(&self) -> Vec<TestScenario> {
        vec![
            TestScenario {
                name: "high_frequency_nonce_requests".to_string(),
                description: "High-frequency nonce management under load".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("concurrent_requests".to_string(), json!(100));
                        params.insert("request_rate_per_second".to_string(), json!(1000));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: None,
                scenario_type: ScenarioType::StressTest,
                tolerance: None,
            },
            TestScenario {
                name: "circuit_breaker_rapid_failures".to_string(),
                description: "Circuit breaker under rapid failure conditions".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("failure_rate_per_second".to_string(), json!(50));
                        params.insert("duration_seconds".to_string(), json!(10));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: None,
                scenario_type: ScenarioType::StressTest,
                tolerance: None,
            },
        ]
    }
}

/// Test data provider for risk management components
pub struct RiskManagementTestDataProvider;

impl TestDataProvider for RiskManagementTestDataProvider {
    fn get_test_scenarios(&self) -> Vec<TestScenario> {
        vec![
            TestScenario {
                name: "kelly_criterion_profitable".to_string(),
                description: "Kelly Criterion with profitable scenario".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("win_rate".to_string(), json!(0.6));
                        params.insert("avg_win".to_string(), json!(150.0));
                        params.insert("avg_loss".to_string(), json!(100.0));
                        params.insert("kelly_cap".to_string(), json!(0.25));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("kelly_fraction".to_string(), json!(0.2)); // Expected positive fraction
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::Unit,
                tolerance: Some(dec!(0.05)),
            },
            TestScenario {
                name: "risk_manager_position_sizing".to_string(),
                description: "Risk manager position sizing validation".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("max_position_usd".to_string(), json!(1000.0));
                        params.insert("requested_position_usd".to_string(), json!(500.0));
                        params.insert("current_daily_pnl".to_string(), json!(-100.0));
                        params
                    },
                    market_conditions: Some(MarketConditions {
                        volatility: dec!(0.3),
                        liquidity: dec!(500000.0),
                        gas_price_gwei: dec!(25.0),
                        regime: "CalmOrderly".to_string(),
                    }),
                    network_state: None,
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("position_approved".to_string(), json!(true));
                        results.insert("adjusted_position_usd".to_string(), json!(500.0));
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::Unit,
                tolerance: None,
            },
        ]
    }

    fn get_edge_cases(&self) -> Vec<TestScenario> {
        vec![
            TestScenario {
                name: "kelly_criterion_unprofitable".to_string(),
                description: "Kelly Criterion with unprofitable scenario".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("win_rate".to_string(), json!(0.3));
                        params.insert("avg_win".to_string(), json!(100.0));
                        params.insert("avg_loss".to_string(), json!(150.0));
                        params.insert("kelly_cap".to_string(), json!(0.25));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: Some(TestOutput {
                    results: {
                        let mut results = HashMap::new();
                        results.insert("kelly_fraction".to_string(), json!(0.0)); // Should be zero or negative
                        results
                    },
                    metrics: HashMap::new(),
                    errors: vec![],
                }),
                scenario_type: ScenarioType::EdgeCase,
                tolerance: Some(dec!(0.01)),
            },
        ]
    }

    fn get_stress_test_data(&self) -> Vec<TestScenario> {
        vec![
            TestScenario {
                name: "risk_manager_high_frequency_updates".to_string(),
                description: "Risk manager under high-frequency PnL updates".to_string(),
                input: TestInput {
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("update_frequency_hz".to_string(), json!(1000));
                        params.insert("duration_seconds".to_string(), json!(60));
                        params
                    },
                    market_conditions: None,
                    network_state: None,
                },
                expected_output: None,
                scenario_type: ScenarioType::StressTest,
                tolerance: None,
            },
        ]
    }
}
