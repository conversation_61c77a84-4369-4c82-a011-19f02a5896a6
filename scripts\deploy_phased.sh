#!/bin/bash
# Phased deployment script for Aetheric Resonance Engine fixes
# This script implements the phased deployment strategy with validation checkpoints

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/deployment_$(date +%Y%m%d_%H%M%S).log"
CONFIG_FILE="$PROJECT_ROOT/config/deployment.toml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        WARN)  echo -e "${YELLOW}[WARN]${NC} $message" | tee -a "$LOG_FILE" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        DEBUG) echo -e "${BLUE}[DEBUG]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
}

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

# Deployment phases in order
PHASES=(
    "development"
    "core-scoring"
    "mathematical-components"
    "component-integration"
    "data-quality"
    "configuration-monitoring"
    "full-production"
)

# Traffic percentages for each phase
declare -A TRAFFIC_PERCENTAGES=(
    ["development"]="0"
    ["core-scoring"]="5"
    ["mathematical-components"]="15"
    ["component-integration"]="35"
    ["data-quality"]="60"
    ["configuration-monitoring"]="85"
    ["full-production"]="100"
)

# Function to get current deployment phase
get_current_phase() {
    if [[ -f "$CONFIG_FILE" ]]; then
        if command -v toml &> /dev/null; then
            toml get "$CONFIG_FILE" "deployment.current_phase" 2>/dev/null | tr -d '"' || echo "development"
        else
            grep "current_phase" "$CONFIG_FILE" | head -1 | cut -d'"' -f2 || echo "development"
        fi
    else
        echo "development"
    fi
}

# Function to update current phase
update_current_phase() {
    local phase=$1
    log INFO "Updating current phase to: $phase"
    
    if command -v toml &> /dev/null; then
        toml set "$CONFIG_FILE" "deployment.current_phase" "\"$phase\""
    else
        sed -i "s/^current_phase = .*/current_phase = \"$phase\"/" "$CONFIG_FILE"
    fi
}

# Function to update traffic routing
update_traffic_routing() {
    local percentage=$1
    local legacy_percentage=$(echo "scale=1; 100.0 - $percentage" | bc)
    
    log INFO "Updating traffic routing: ${percentage}% new, ${legacy_percentage}% legacy"
    
    if command -v toml &> /dev/null; then
        toml set "$CONFIG_FILE" "traffic_routing.new_implementation_percentage" "$percentage"
        toml set "$CONFIG_FILE" "traffic_routing.legacy_implementation_percentage" "$legacy_percentage"
    else
        sed -i "s/^new_implementation_percentage = .*/new_implementation_percentage = $percentage/" "$CONFIG_FILE"
        sed -i "s/^legacy_implementation_percentage = .*/legacy_implementation_percentage = $legacy_percentage/" "$CONFIG_FILE"
    fi
}

# Function to enable feature flags for a phase
enable_phase_features() {
    local phase=$1
    log INFO "Enabling feature flags for phase: $phase"
    
    case $phase in
        "core-scoring")
            enable_feature_flag "scoring_engine_weight_fix"
            enable_feature_flag "neutral_score_fallbacks"
            enable_feature_flag "complete_geometric_score"
            ;;
        "mathematical-components")
            enable_feature_flag "hurst_exponent_fix"
            enable_feature_flag "market_rhythm_stability_fix"
            enable_feature_flag "vesica_piscis_negative_fix"
            enable_feature_flag "temporal_harmonics_integration"
            enable_feature_flag "liquidity_centroid_bias_fix"
            ;;
        "component-integration")
            enable_feature_flag "network_state_integration"
            enable_feature_flag "asset_centrality_initialization"
            enable_feature_flag "token_registry_integration"
            enable_feature_flag "vesica_piscis_geometric_integration"
            ;;
        "data-quality")
            enable_feature_flag "network_coherence_fix"
            enable_feature_flag "censorship_detection"
            enable_feature_flag "sequencer_health_monitoring"
            ;;
        "configuration-monitoring")
            enable_feature_flag "enhanced_configuration_validation"
            enable_feature_flag "graceful_degradation_patterns"
            enable_feature_flag "performance_monitoring"
            enable_feature_flag "enhanced_error_propagation"
            ;;
    esac
}

# Function to enable a specific feature flag
enable_feature_flag() {
    local flag=$1
    log DEBUG "Enabling feature flag: $flag"
    
    if command -v toml &> /dev/null; then
        toml set "$CONFIG_FILE" "feature_flags.$flag" true
    else
        sed -i "s/^$flag = false/$flag = true/" "$CONFIG_FILE"
    fi
}

# Function to run validation checkpoints
run_validation_checkpoints() {
    local phase=$1
    log INFO "Running validation checkpoints for phase: $phase"
    
    local checkpoints=()
    case $phase in
        "development")
            checkpoints=("basic_functionality" "configuration_loading")
            ;;
        "core-scoring")
            checkpoints=("scoring_engine_functionality" "weight_application_validation" "neutral_score_fallback_validation" "geometric_score_completeness")
            ;;
        "mathematical-components")
            checkpoints=("hurst_exponent_calculation" "market_rhythm_stability" "vesica_piscis_negative_handling" "temporal_harmonics_integration" "liquidity_centroid_calculation")
            ;;
        "component-integration")
            checkpoints=("network_state_integration" "asset_centrality_initialization" "token_registry_integration" "vesica_piscis_geometric_integration")
            ;;
        "data-quality")
            checkpoints=("network_coherence_calculation" "censorship_detection" "sequencer_health_monitoring" "data_validation_comprehensive")
            ;;
        "configuration-monitoring")
            checkpoints=("configuration_validation_enhanced" "graceful_degradation_patterns" "performance_monitoring" "error_propagation_enhanced")
            ;;
        "full-production")
            checkpoints=("full_system_integration" "performance_under_load" "error_handling_comprehensive" "monitoring_alerting_complete")
            ;;
    esac
    
    for checkpoint in "${checkpoints[@]}"; do
        log INFO "Running checkpoint: $checkpoint"
        if run_checkpoint "$checkpoint"; then
            log INFO "Checkpoint passed: $checkpoint"
        else
            log ERROR "Checkpoint failed: $checkpoint"
            return 1
        fi
    done
    
    log INFO "All validation checkpoints passed for phase: $phase"
    return 0
}

# Function to run a specific checkpoint
run_checkpoint() {
    local checkpoint=$1
    
    case $checkpoint in
        "basic_functionality")
            # Test basic system functionality
            if cargo test --lib basic_functionality_tests --quiet; then
                return 0
            else
                return 1
            fi
            ;;
        "configuration_loading")
            # Test configuration loading
            if cargo run --bin validate_config -- --config "$CONFIG_FILE" > /dev/null 2>&1; then
                return 0
            else
                return 1
            fi
            ;;
        "scoring_engine_functionality")
            # Test scoring engine
            if cargo test --lib scoring_engine_tests --quiet; then
                return 0
            else
                return 1
            fi
            ;;
        *)
            # For other checkpoints, simulate validation
            log DEBUG "Simulating checkpoint: $checkpoint"
            sleep 2
            return 0
            ;;
    esac
}

# Function to run health checks
run_health_checks() {
    log INFO "Running health checks..."
    
    # Check if service is running (look for basilisk or health_server)
    if ! pgrep -f "basilisk\|health_server" > /dev/null; then
        log ERROR "Service is not running (looking for basilisk or health_server)"
        return 1
    fi
    
    # Check if service is responding
    if ! curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log ERROR "Service health check failed"
        return 1
    fi
    
    # Check metrics endpoint
    if ! curl -f http://localhost:8080/metrics > /dev/null 2>&1; then
        log ERROR "Metrics endpoint check failed"
        return 1
    fi
    
    log INFO "Health checks passed"
    return 0
}

# Function to gradually increase traffic
gradual_traffic_increase() {
    local target_percentage=$1
    local current_percentage=$(get_current_traffic_percentage)
    
    log INFO "Gradually increasing traffic from ${current_percentage}% to ${target_percentage}%"
    
    # Calculate steps
    local steps=()
    if (( $(echo "$target_percentage > $current_percentage" | bc -l) )); then
        local step_size=5
        local current_step=$current_percentage
        
        while (( $(echo "$current_step < $target_percentage" | bc -l) )); do
            current_step=$(echo "$current_step + $step_size" | bc)
            if (( $(echo "$current_step > $target_percentage" | bc -l) )); then
                current_step=$target_percentage
            fi
            steps+=("$current_step")
        done
    else
        steps=("$target_percentage")
    fi
    
    # Execute traffic increase steps
    for step_percentage in "${steps[@]}"; do
        log INFO "Increasing traffic to ${step_percentage}%"
        
        # Update traffic routing
        update_traffic_routing "$step_percentage"
        
        # Restart service to apply changes
        restart_service
        
        # Wait for stabilization
        log INFO "Waiting for stabilization (30 seconds)..."
        sleep 30
        
        # Run health checks
        if ! run_health_checks; then
            log ERROR "Health checks failed during traffic increase"
            return 1
        fi
        
        # Check for deployment issues
        if check_deployment_issues; then
            log ERROR "Deployment issues detected during traffic increase"
            return 1
        fi
    done
    
    log INFO "Traffic increase completed to ${target_percentage}%"
    return 0
}

# Function to get current traffic percentage
get_current_traffic_percentage() {
    if [[ -f "$CONFIG_FILE" ]]; then
        if command -v toml &> /dev/null; then
            toml get "$CONFIG_FILE" "traffic_routing.new_implementation_percentage" 2>/dev/null || echo "0"
        else
            grep "new_implementation_percentage" "$CONFIG_FILE" | head -1 | cut -d'=' -f2 | tr -d ' ' || echo "0"
        fi
    else
        echo "0"
    fi
}

# Function to restart service
restart_service() {
    log INFO "Restarting service to apply changes..."
    
    # Check if systemd service exists
    if systemctl is-active --quiet basilisk-bot; then
        sudo systemctl restart basilisk-bot
        log INFO "Service restarted via systemd"
    else
        # Fallback to process management
        if pgrep -f "basilisk\|health_server" > /dev/null; then
            pkill -f "basilisk\|health_server"
            sleep 2
        fi
        
        # Start the service in background
        nohup cargo run --release > "$PROJECT_ROOT/logs/service.log" 2>&1 &
        log INFO "Service started in background"
    fi
    
    # Wait for service to start
    sleep 5
}

# Function to check for deployment issues
check_deployment_issues() {
    # Check error rate
    local error_rate=$(get_error_rate)
    if (( $(echo "$error_rate > 10.0" | bc -l) )); then
        log ERROR "High error rate detected: ${error_rate}%"
        return 1
    fi
    
    # Check response time
    local response_time=$(get_response_time)
    if (( $(echo "$response_time > 2000" | bc -l) )); then
        log ERROR "High response time detected: ${response_time}ms"
        return 1
    fi
    
    return 0
}

# Function to get error rate (simulated)
get_error_rate() {
    # In a real implementation, this would query monitoring systems
    echo "2.5"
}

# Function to get response time (simulated)
get_response_time() {
    # In a real implementation, this would query monitoring systems
    echo "450"
}

# Function to deploy to a specific phase
deploy_to_phase() {
    local target_phase=$1
    local current_phase=$(get_current_phase)
    
    log INFO "Deploying from $current_phase to $target_phase"
    
    # Validate deployment path
    if ! validate_deployment_path "$current_phase" "$target_phase"; then
        log ERROR "Invalid deployment path from $current_phase to $target_phase"
        return 1
    fi
    
    # Get target traffic percentage
    local target_percentage=${TRAFFIC_PERCENTAGES[$target_phase]}
    
    # Step 1: Enable feature flags for the target phase
    enable_phase_features "$target_phase"
    
    # Step 2: Gradually increase traffic
    if ! gradual_traffic_increase "$target_percentage"; then
        log ERROR "Failed to increase traffic for phase $target_phase"
        return 1
    fi
    
    # Step 3: Run validation checkpoints
    if ! run_validation_checkpoints "$target_phase"; then
        log ERROR "Validation checkpoints failed for phase $target_phase"
        return 1
    fi
    
    # Step 4: Monitor deployment health
    log INFO "Monitoring deployment health for 5 minutes..."
    for i in {1..10}; do
        if ! run_health_checks; then
            log ERROR "Health checks failed during monitoring"
            return 1
        fi
        
        if check_deployment_issues; then
            log ERROR "Deployment issues detected during monitoring"
            return 1
        fi
        
        sleep 30
    done
    
    # Step 5: Update current phase
    update_current_phase "$target_phase"
    
    log INFO "Successfully deployed to phase: $target_phase"
    return 0
}

# Function to validate deployment path
validate_deployment_path() {
    local current=$1
    local target=$2
    
    # Find current phase index
    local current_index=-1
    local target_index=-1
    
    for i in "${!PHASES[@]}"; do
        if [[ "${PHASES[$i]}" == "$current" ]]; then
            current_index=$i
        fi
        if [[ "${PHASES[$i]}" == "$target" ]]; then
            target_index=$i
        fi
    done
    
    if [[ $current_index -eq -1 ]] || [[ $target_index -eq -1 ]]; then
        return 1
    fi
    
    # Can only deploy to next phase or same phase
    if [[ $target_index -le $((current_index + 1)) ]] && [[ $target_index -ge $current_index ]]; then
        return 0
    else
        return 1
    fi
}

# Function to show deployment status
show_deployment_status() {
    local current_phase=$(get_current_phase)
    local current_traffic=$(get_current_traffic_percentage)
    
    echo
    echo "=== Aetheric Resonance Engine Deployment Status ==="
    echo "Current Phase: $current_phase"
    echo "Traffic to New Implementation: ${current_traffic}%"
    echo "Traffic to Legacy Implementation: $(echo "100 - $current_traffic" | bc)%"
    echo
    
    # Show enabled feature flags
    echo "Enabled Feature Flags:"
    if command -v toml &> /dev/null; then
        toml get "$CONFIG_FILE" "feature_flags" | grep "true" | cut -d'=' -f1 | sed 's/^/  - /'
    else
        grep "= true" "$CONFIG_FILE" | grep -v "enabled = true" | cut -d'=' -f1 | sed 's/^/  - /'
    fi
    echo
    
    # Show next available phase
    local current_index=-1
    for i in "${!PHASES[@]}"; do
        if [[ "${PHASES[$i]}" == "$current_phase" ]]; then
            current_index=$i
            break
        fi
    done
    
    if [[ $current_index -ne -1 ]] && [[ $current_index -lt $((${#PHASES[@]} - 1)) ]]; then
        local next_phase=${PHASES[$((current_index + 1))]}
        echo "Next Available Phase: $next_phase"
    else
        echo "Next Available Phase: None (at final phase)"
    fi
    echo
}

# Function to run preflight checks
run_preflight_checks() {
    log INFO "Running preflight checks..."
    
    # Check if configuration file exists
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log ERROR "Configuration file not found: $CONFIG_FILE"
        return 1
    fi
    
    # Validate configuration
    if ! cargo run --bin validate_config -- --config "$CONFIG_FILE" > /dev/null 2>&1; then
        log ERROR "Configuration validation failed"
        return 1
    fi
    
    # Check if required tools are available
    if ! command -v cargo &> /dev/null; then
        log ERROR "Cargo not found"
        return 1
    fi
    
    if ! command -v bc &> /dev/null; then
        log ERROR "bc (calculator) not found"
        return 1
    fi
    
    # Check if project builds
    if ! cargo check --quiet; then
        log ERROR "Project build check failed"
        return 1
    fi
    
    log INFO "Preflight checks passed"
    return 0
}

# Main function
main() {
    local command=${1:-"status"}
    
    case $command in
        "deploy")
            local target_phase=${2:-""}
            
            if [[ -z "$target_phase" ]]; then
                # Deploy to next phase
                local current_phase=$(get_current_phase)
                local current_index=-1
                
                for i in "${!PHASES[@]}"; do
                    if [[ "${PHASES[$i]}" == "$current_phase" ]]; then
                        current_index=$i
                        break
                    fi
                done
                
                if [[ $current_index -eq -1 ]]; then
                    log ERROR "Unknown current phase: $current_phase"
                    exit 1
                fi
                
                if [[ $current_index -ge $((${#PHASES[@]} - 1)) ]]; then
                    log INFO "Already at final phase: $current_phase"
                    exit 0
                fi
                
                target_phase=${PHASES[$((current_index + 1))]}
            fi
            
            # Run preflight checks
            if ! run_preflight_checks; then
                log ERROR "Preflight checks failed"
                exit 1
            fi
            
            # Deploy to target phase
            if deploy_to_phase "$target_phase"; then
                log INFO "Deployment completed successfully"
            else
                log ERROR "Deployment failed"
                exit 1
            fi
            ;;
        "status")
            show_deployment_status
            ;;
        "rollback")
            local target_phase=${2:-""}
            if [[ -n "$target_phase" ]]; then
                log INFO "Initiating rollback to phase: $target_phase"
                "$SCRIPT_DIR/rollback_are_fixes.sh" rollback "$target_phase"
            else
                log INFO "Initiating rollback to previous phase"
                "$SCRIPT_DIR/rollback_are_fixes.sh" rollback
            fi
            ;;
        "health")
            if run_health_checks; then
                log INFO "System is healthy"
            else
                log ERROR "System health check failed"
                exit 1
            fi
            ;;
        "preflight")
            if run_preflight_checks; then
                log INFO "Preflight checks passed"
            else
                log ERROR "Preflight checks failed"
                exit 1
            fi
            ;;
        *)
            echo "Usage: $0 {deploy [phase]|status|rollback [phase]|health|preflight}"
            echo
            echo "Commands:"
            echo "  deploy [phase]  - Deploy to next phase or specified phase"
            echo "  status          - Show current deployment status"
            echo "  rollback [phase] - Rollback to previous phase or specified phase"
            echo "  health          - Run health checks"
            echo "  preflight       - Run preflight checks"
            echo
            echo "Available phases:"
            for phase in "${PHASES[@]}"; do
                echo "  $phase"
            done
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"