#!/bin/bash
# Rollback script for phased deployment
# This script implements safe rollback mechanisms for deployment phases

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/rollback_$(date +%Y%m%d_%H%M%S).log"
CONFIG_FILE="$PROJECT_ROOT/config/deployment.toml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        WARN)  echo -e "${YELLOW}[WARN]${NC} $message" | tee -a "$LOG_FILE" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        DEBUG) echo -e "${BLUE}[DEBUG]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
}

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

# Deployment phases in reverse order for rollback
PHASES=(
    "full-production"
    "configuration-monitoring"
    "data-quality"
    "component-integration"
    "mathematical-components"
    "core-scoring"
    "development"
)

# Traffic percentages for each phase
declare -A TRAFFIC_PERCENTAGES=(
    ["development"]="0"
    ["core-scoring"]="5"
    ["mathematical-components"]="15"
    ["component-integration"]="35"
    ["data-quality"]="60"
    ["configuration-monitoring"]="85"
    ["full-production"]="100"
)

# Rollback strategies
ROLLBACK_STRATEGY="gradual" # Options: immediate, gradual, blue-green

# Function to get current deployment phase
get_current_phase() {
    if [[ -f "$CONFIG_FILE" ]]; then
        if command -v toml &> /dev/null; then
            toml get "$CONFIG_FILE" "deployment.current_phase" 2>/dev/null | tr -d '"' || echo "development"
        else
            grep "current_phase" "$CONFIG_FILE" | cut -d'"' -f2 || echo "development"
        fi
    else
        echo "development"
    fi
}

# Function to get previous phase
get_previous_phase() {
    local current=$1
    
    case $current in
        "full-production") echo "configuration-monitoring" ;;
        "configuration-monitoring") echo "data-quality" ;;
        "data-quality") echo "component-integration" ;;
        "component-integration") echo "mathematical-components" ;;
        "mathematical-components") echo "core-scoring" ;;
        "core-scoring") echo "development" ;;
        "development") echo "" ;;
        *) echo "development" ;;
    esac
}

# Function to update current phase
update_current_phase() {
    local phase=$1
    log INFO "Updating current phase to: $phase"
    
    if command -v toml &> /dev/null; then
        toml set "$CONFIG_FILE" "deployment.current_phase" "\"$phase\""
    else
        sed -i "s/^current_phase = .*/current_phase = \"$phase\"/" "$CONFIG_FILE"
    fi
}

# Function to update traffic routing
update_traffic_routing() {
    local percentage=$1
    local legacy_percentage=$(echo "scale=1; 100.0 - $percentage" | bc)
    
    log INFO "Updating traffic routing: ${percentage}% new, ${legacy_percentage}% legacy"
    
    if command -v toml &> /dev/null; then
        toml set "$CONFIG_FILE" "traffic_routing.new_implementation_percentage" "$percentage"
        toml set "$CONFIG_FILE" "traffic_routing.legacy_implementation_percentage" "$legacy_percentage"
    else
        sed -i "s/^new_implementation_percentage = .*/new_implementation_percentage = $percentage/" "$CONFIG_FILE"
        sed -i "s/^legacy_implementation_percentage = .*/legacy_implementation_percentage = $legacy_percentage/" "$CONFIG_FILE"
    fi
}

# Function to disable feature flags for a phase
disable_phase_features() {
    local phase=$1
    log INFO "Disabling feature flags for phase: $phase"
    
    case $phase in
        "full-production")
            # All features are enabled in full production, disable in reverse dependency order
            disable_feature_flag "enhanced_error_propagation"
            disable_feature_flag "performance_monitoring"
            disable_feature_flag "graceful_degradation_patterns"
            disable_feature_flag "enhanced_configuration_validation"
            ;;
        "configuration-monitoring")
            disable_feature_flag "enhanced_error_propagation"
            disable_feature_flag "performance_monitoring"
            disable_feature_flag "graceful_degradation_patterns"
            disable_feature_flag "enhanced_configuration_validation"
            ;;
        "data-quality")
            disable_feature_flag "sequencer_health_monitoring"
            disable_feature_flag "censorship_detection"
            disable_feature_flag "network_coherence_fix"
            ;;
        "component-integration")
            disable_feature_flag "vesica_piscis_geometric_integration"
            disable_feature_flag "token_registry_integration"
            disable_feature_flag "asset_centrality_initialization"
            disable_feature_flag "network_state_integration"
            ;;
        "mathematical-components")
            disable_feature_flag "liquidity_centroid_bias_fix"
            disable_feature_flag "temporal_harmonics_integration"
            disable_feature_flag "vesica_piscis_negative_fix"
            disable_feature_flag "market_rhythm_stability_fix"
            disable_feature_flag "hurst_exponent_fix"
            ;;
        "core-scoring")
            disable_feature_flag "complete_geometric_score"
            disable_feature_flag "neutral_score_fallbacks"
            disable_feature_flag "scoring_engine_weight_fix"
            ;;
    esac
}

# Function to disable a specific feature flag
disable_feature_flag() {
    local flag=$1
    log DEBUG "Disabling feature flag: $flag"
    
    if command -v toml &> /dev/null; then
        toml set "$CONFIG_FILE" "feature_flags.$flag" false
    else
        sed -i "s/^$flag = true/$flag = false/" "$CONFIG_FILE"
    fi
}

# Function to restart service
restart_service() {
    log INFO "Restarting service to apply rollback changes"
    
    # Check if systemd service exists
    if systemctl is-active --quiet basilisk-bot; then
        sudo systemctl restart basilisk-bot
        log INFO "Service restarted via systemd"
    else
        # Fallback to process management
        if pgrep -f "basilisk" > /dev/null; then
            pkill -f "basilisk"
            sleep 2
        fi
        
        # Start the service in background
        nohup cargo run --release > "$PROJECT_ROOT/logs/service.log" 2>&1 &
        log INFO "Service started in background"
    fi
    
    # Wait for service to start
    sleep 5
}

# Function to run health checks
run_health_checks() {
    log INFO "Running health checks"
    
    # Check if service is running
    if ! pgrep -f "basilisk" > /dev/null; then
        log ERROR "Service is not running"
        return 1
    fi
    
    # Check service endpoints
    local endpoints=("/health" "/metrics" "/ready")
    for endpoint in "${endpoints[@]}"; do
        if ! curl -f -s "http://localhost:8080$endpoint" > /dev/null; then
            log ERROR "Health check failed for endpoint: $endpoint"
            return 1
        fi
    done
    
    log INFO "Health checks passed"
    return 0
}

# Function to perform immediate rollback
immediate_rollback() {
    local current_phase=$1
    local target_phase=$2
    
    log INFO "Performing immediate rollback from $current_phase to $target_phase"
    
    # Step 1: Disable new implementation features
    log INFO "Step 1: Disabling new implementation features"
    disable_phase_features "$current_phase"
    
    # Step 2: Switch traffic to legacy implementation
    log INFO "Step 2: Switching traffic to legacy implementation"
    local target_percentage=${TRAFFIC_PERCENTAGES[$target_phase]}
    update_traffic_routing "$target_percentage"
    
    # Step 3: Restart service
    log INFO "Step 3: Restarting service"
    restart_service
    
    # Step 4: Verify rollback success
    log INFO "Step 4: Verifying rollback success"
    if verify_rollback_success "$target_phase"; then
        log INFO "Immediate rollback completed successfully"
        return 0
    else
        log ERROR "Immediate rollback verification failed"
        return 1
    fi
}

# Function to perform gradual rollback
gradual_rollback() {
    local current_phase=$1
    local target_phase=$2
    
    log INFO "Performing gradual rollback from $current_phase to $target_phase"
    
    local current_percentage=${TRAFFIC_PERCENTAGES[$current_phase]}
    local target_percentage=${TRAFFIC_PERCENTAGES[$target_phase]}
    
    # Calculate rollback steps
    local steps=()
    if [[ $current_percentage -gt $target_percentage ]]; then
        # Decreasing traffic
        local step_size=$(( (current_percentage - target_percentage) / 3 ))
        if [[ $step_size -eq 0 ]]; then
            step_size=5
        fi
        
        local current_step=$current_percentage
        while [[ $current_step -gt $target_percentage ]]; do
            current_step=$((current_step - step_size))
            if [[ $current_step -le $target_percentage ]]; then
                steps+=($target_percentage)
                break
            else
                steps+=($current_step)
            fi
        done
    else
        # Already at or below target
        steps+=($target_percentage)
    fi
    
    # Execute gradual traffic shift
    for step_percentage in "${steps[@]}"; do
        log INFO "Shifting traffic to ${step_percentage}% new implementation"
        
        # Update traffic routing
        update_traffic_routing "$step_percentage"
        
        # Restart service to apply changes
        restart_service
        
        # Wait for stabilization
        log INFO "Waiting for stabilization (15 seconds)..."
        sleep 15
        
        # Check health after each step
        if ! run_health_checks; then
            log WARN "Health check failed during gradual rollback step"
            # Continue with rollback even if health checks fail
        fi
    done
    
    # Disable features after traffic is shifted
    log INFO "Disabling features for phase: $current_phase"
    disable_phase_features "$current_phase"
    
    # Final restart
    restart_service
    
    # Final verification
    if verify_rollback_success "$target_phase"; then
        log INFO "Gradual rollback completed successfully"
        return 0
    else
        log ERROR "Gradual rollback verification failed"
        return 1
    fi
}

# Function to verify rollback success
verify_rollback_success() {
    local target_phase=$1
    log INFO "Verifying rollback success for phase: $target_phase"
    
    # Check if service is healthy
    if ! run_health_checks; then
        log ERROR "Service health check failed after rollback"
        return 1
    fi
    
    # Check if traffic routing is correct
    local expected_percentage=${TRAFFIC_PERCENTAGES[$target_phase]}
    local actual_percentage
    if command -v toml &> /dev/null; then
        actual_percentage=$(toml get "$CONFIG_FILE" "traffic_routing.new_implementation_percentage" | tr -d '"')
    else
        actual_percentage=$(grep "new_implementation_percentage" "$CONFIG_FILE" | cut -d'=' -f2 | tr -d ' ')
    fi
    
    if [[ "$actual_percentage" != "$expected_percentage" ]]; then
        log ERROR "Traffic routing mismatch: expected ${expected_percentage}%, got ${actual_percentage}%"
        return 1
    fi
    
    log INFO "Rollback verification successful"
    return 0
}

# Function to show rollback status
show_rollback_status() {
    local current_phase=$(get_current_phase)
    local previous_phase=$(get_previous_phase "$current_phase")
    
    echo
    echo "=== Aetheric Resonance Engine Rollback Status ==="
    echo "Current Phase: $current_phase"
    if [[ -n "$previous_phase" ]]; then
        echo "Available Rollback Target: $previous_phase"
    else
        echo "Available Rollback Target: None (already at initial phase)"
    fi
    echo "Rollback Strategy: $ROLLBACK_STRATEGY"
    echo
    
    # Show recent rollback history
    echo "Recent Rollback History:"
    if [[ -d "$PROJECT_ROOT/logs" ]]; then
        ls -lt "$PROJECT_ROOT/logs"/rollback_*.log 2>/dev/null | head -5 | while read -r line; do
            echo "  $line"
        done
    else
        echo "  No rollback history found"
    fi
    echo
}

# Function to list available rollback targets
list_rollback_targets() {
    local current_phase=$(get_current_phase)
    
    echo "Available rollback targets from current phase ($current_phase):"
    
    local found_current=false
    for phase in full-production configuration-monitoring data-quality component-integration mathematical-components core-scoring development; do
        if [[ "$phase" == "$current_phase" ]]; then
            found_current=true
            continue
        fi
        
        if [[ "$found_current" == true ]]; then
            local percentage=${TRAFFIC_PERCENTAGES[$phase]}
            echo "  - $phase (${percentage}% traffic)"
        fi
    done
}

# Main rollback function
main() {
    local command=${1:-"status"}
    
    case $command in
        "rollback")
            local target_phase=${2:-""}
            local current_phase=$(get_current_phase)
            
            if [[ -z "$target_phase" ]]; then
                # Rollback to previous phase
                target_phase=$(get_previous_phase "$current_phase")
                
                if [[ -z "$target_phase" ]]; then
                    log INFO "Already at initial deployment phase. No rollback available."
                    exit 0
                fi
            fi
            
            log INFO "Rolling back from $current_phase to $target_phase"
            
            # Validate rollback target
            local valid_target=false
            local found_current=false
            for phase in full-production configuration-monitoring data-quality component-integration mathematical-components core-scoring development; do
                if [[ "$phase" == "$current_phase" ]]; then
                    found_current=true
                    continue
                fi
                
                if [[ "$found_current" == true && "$phase" == "$target_phase" ]]; then
                    valid_target=true
                    break
                fi
            done
            
            if [[ "$valid_target" != true ]]; then
                log ERROR "Invalid rollback target: $target_phase"
                list_rollback_targets
                exit 1
            fi
            
            # Perform rollback based on strategy
            case $ROLLBACK_STRATEGY in
                "immediate")
                    if immediate_rollback "$current_phase" "$target_phase"; then
                        log INFO "Immediate rollback completed successfully"
                    else
                        log ERROR "Immediate rollback failed"
                        exit 1
                    fi
                    ;;
                "gradual")
                    if gradual_rollback "$current_phase" "$target_phase"; then
                        log INFO "Gradual rollback completed successfully"
                    else
                        log ERROR "Gradual rollback failed"
                        exit 1
                    fi
                    ;;
                *)
                    log ERROR "Unknown rollback strategy: $ROLLBACK_STRATEGY"
                    exit 1
                    ;;
            esac
            
            # Update current phase
            update_current_phase "$target_phase"
            
            log INFO "Rollback completed successfully to phase: $target_phase"
            ;;
        "status")
            show_rollback_status
            ;;
        "list")
            list_rollback_targets
            ;;
        "strategy")
            local new_strategy=${2:-""}
            if [[ -n "$new_strategy" ]]; then
                case $new_strategy in
                    "immediate"|"gradual"|"blue-green")
                        ROLLBACK_STRATEGY="$new_strategy"
                        log INFO "Rollback strategy set to: $new_strategy"
                        ;;
                    *)
                        log ERROR "Invalid rollback strategy: $new_strategy"
                        log INFO "Valid strategies: immediate, gradual, blue-green"
                        exit 1
                        ;;
                esac
            else
                echo "Current rollback strategy: $ROLLBACK_STRATEGY"
            fi
            ;;
        "health")
            log INFO "Running health check..."
            if run_health_checks; then
                log INFO "Health check passed"
            else
                log ERROR "Health check failed"
                exit 1
            fi
            ;;
        *)
            echo "Usage: $0 {rollback [target_phase]|status|list|strategy [new_strategy]|health}"
            echo
            echo "Commands:"
            echo "  rollback [target_phase] - Rollback to previous phase or specified phase"
            echo "  status                  - Show current rollback status"
            echo "  list                    - List available rollback targets"
            echo "  strategy [new_strategy] - Show or set rollback strategy"
            echo "  health                  - Run health check"
            echo
            echo "Available rollback strategies:"
            echo "  immediate  - Immediately switch all traffic back"
            echo "  gradual    - Gradually shift traffic back (default)"
            echo "  blue-green - Blue-green deployment rollback"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"