// src/validation/risk_management_validator_demo.rs

//! Risk Management Validation Demo
//! 
//! This module provides demonstration functionality for the risk management
//! validation framework, showing how to use the validator and interpret results.

use crate::validation::{
    ValidationFramework, ValidationConfig,
    risk_management_validator::{
        RiskManagementValidator, RiskTestDataGenerator, RiskTestScenario,
        RiskManagementValidationMetrics
};
use tracing::{info, warn, error};
use anyhow::Result;
use std::time::Duration;

/// Run a comprehensive risk management validation demo
pub async fn run_risk_management_demo() -> Result<()> {
    info!("🚀 Starting Risk Management Validation Demo");
    
    // Initialize the validation framework
    let config = ValidationConfig::default();
    let validator = RiskManagementValidator::new(config);
    let test_data_generator = RiskTestDataGenerator::new(42);
    
    println!("\n=== Risk Management Validation Demo ===\n");
    
    // Generate test scenarios
    let scenarios = test_data_generator.generate_risk_test_scenarios();
    println!("📊 Generated {} test scenarios for validation", scenarios.len());
    
    // Display scenario overview
    display_scenario_overview(&scenarios);
    
    // Run individual validation components
    println!("\n🔍 Running Individual Validation Components...\n");
    
    // 1. Kelly Criterion Validation
    println!("1️⃣ Kelly Criterion Position Sizing Validation");
    let kelly_result = validator.validate_kelly_criterion_position_sizing(&scenarios).await?;
    display_kelly_results(&kelly_result);
    
    // 2. Daily Loss Limits Validation
    println!("\n2️⃣ Daily Loss Limit Enforcement Validation");
    let loss_result = validator.validate_daily_loss_limits(&scenarios).await?;
    display_loss_limit_results(&loss_result);
    
    // 3. Volatility Adjustments Validation
    println!("\n3️⃣ Volatility-Based Position Adjustment Validation");
    let volatility_result = validator.validate_volatility_adjustments(&scenarios).await?;
    display_volatility_results(&volatility_result);
    
    // 4. Consecutive Failures Validation
    println!("\n4️⃣ Consecutive Failure Threshold Validation");
    let failure_result = validator.validate_consecutive_failure_thresholds(&scenarios).await?;
    display_consecutive_failure_results(&failure_result);
    
    // 5. Emergency Shutdown Validation
    println!("\n5️⃣ Emergency Shutdown and Graceful Degradation Validation");
    let emergency_result = validator.validate_emergency_shutdown(&scenarios).await?;
    display_emergency_shutdown_results(&emergency_result);
    
    // 6. Circuit Breaker Validation
    println!("\n6️⃣ Circuit Breaker Functionality Validation");
    let circuit_result = validator.validate_circuit_breakers(&scenarios).await?;
    display_circuit_breaker_results(&circuit_result);
    
    // Run comprehensive validation
    println!("\n🎯 Running Comprehensive Risk Management Validation...\n");
    let comprehensive_result = validator.generate_comprehensive_validation_report(&scenarios).await?;
    display_comprehensive_results(&comprehensive_result);
    
    // Show usage examples
    show_usage_examples();
    
    info!("✅ Risk Management Validation Demo completed successfully");
    Ok(())
}

/// Run a quick risk management validation for testing
pub async fn run_quick_risk_management_validation() -> Result<()> {
    info!("⚡ Running Quick Risk Management Validation");
    
    let config = ValidationConfig::default();
    let validator = RiskManagementValidator::new(config);
    let test_data_generator = RiskTestDataGenerator::new(42);
    
    // Generate a subset of scenarios for quick testing
    let mut scenarios = test_data_generator.generate_risk_test_scenarios();
    scenarios.truncate(3); // Use only first 3 scenarios for quick test
    
    println!("\n=== Quick Risk Management Validation ===");
    println!("📊 Using {} test scenarios for quick validation\n", scenarios.len());
    
    // Run Kelly Criterion validation only for quick test
    let kelly_result = validator.validate_kelly_criterion_position_sizing(&scenarios).await?;
    
    println!("✅ Kelly Criterion Validation Results:");
    let metrics = &kelly_result.metrics;
    println!("   • Calculations Tested: {}", metrics.calculations_tested);
    println!("   • Average Accuracy: {:.1}%", metrics.average_accuracy);
    println!("   • Status: {}", kelly_result.status);
    
    println!("\n⚡ Quick validation completed in {}ms", kelly_result.execution_time.as_millis());
    Ok(())
}

/// Display scenario overview
fn display_scenario_overview(scenarios: &[RiskTestScenario]) {
    println!("📋 Test Scenario Overview:");
    for (i, scenario) in scenarios.iter().enumerate() {
    println!("   {}. {} - {} regime, {} consecutive failures", 
                 i + 1, 
                 scenario.name, 
                 format!("{:?}", scenario.market_regime),
                 scenario.consecutive_failures);
}

/// Display Kelly Criterion validation results
fn display_kelly_results(result: &crate::validation::ValidationResult<crate::validation::risk_management_validator::KellyCriterionValidationMetrics>) {
    println!("   Status: {} ({}ms)", result.status, result.execution_time.as_millis());
    
    let metrics = &result.metrics;
    println!("   • Calculations Tested: {}", metrics.calculations_tested);
    println!("   • Calculations Passed: {}", metrics.calculations_passed);
    println!("   • Average Accuracy: {:.1}%", metrics.average_accuracy);
    println!("   • Position Enforcement Rate: {:.1}%", metrics.max_position_enforcement_rate);
    println!("   • Regime Multiplier Accuracy: {:.1}%", metrics.regime_multiplier_accuracy);
        
        if !metrics.kelly_fraction_capping_results.is_empty() {
        println!("   • Sample Kelly Fraction Tests:");
            for (i, test) in metrics.kelly_fraction_capping_results.iter().take(3).enumerate() {
            println!("     {}. {}: Expected {:.4}, Actual {:.4}, Passed: {}", 
                         i + 1, test.scenario_name, test.expected_kelly_fraction, 
                         test.actual_kelly_fraction, test.passed);
            }
        }
    
    display_errors_and_warnings(result);
}

/// Display loss limit validation results
fn display_loss_limit_results(result: &crate::validation::ValidationResult<crate::validation::risk_management_validator::DailyLossLimitMetrics>) {
    println!("   Status: {} ({}ms)", result.status, result.execution_time.as_millis());
    
    let metrics = &result.metrics;
    println!("   • Scenarios Tested: {}", metrics.scenarios_tested);
    println!("   • Limits Enforced Correctly: {}", metrics.limits_enforced_correctly);
    println!("   • Circuit Breaker Activation Rate: {:.1}%", metrics.circuit_breaker_activation_rate);
    println!("   • Position Reduction Effectiveness: {:.1}%", metrics.position_reduction_effectiveness);
    println!("   • Trading Halt Accuracy: {:.1}%", metrics.trading_halt_accuracy);
        
        if !metrics.breach_detection_latency_ms.is_empty() {
            let avg_latency = metrics.breach_detection_latency_ms.iter().sum::<u64>() as f64 / metrics.breach_detection_latency_ms.len() as f64;
        println!("   • Average Breach Detection Latency: {:.1}ms", avg_latency);
        }
    
    display_errors_and_warnings(result);
}

/// Display volatility adjustment results
fn display_volatility_results(result: &crate::validation::ValidationResult<crate::validation::risk_management_validator::VolatilityAdjustmentMetrics>) {
    println!("   Status: {} ({}ms)", result.status, result.execution_time.as_millis());
    
    let metrics = &result.metrics;
    println!("   • Volatility Scenarios Tested: {}", metrics.volatility_scenarios_tested);
    println!("   • Adjustment Accuracy Rate: {:.1}%", metrics.adjustment_accuracy_rate);
    println!("   • High Volatility Multiplier Success: {:.1}%", metrics.high_volatility_multiplier_success);
    println!("   • Low Volatility Multiplier Success: {:.1}%", metrics.low_volatility_multiplier_success);
    println!("   • Regime Detection Accuracy: {:.1}%", metrics.regime_detection_accuracy);
        
        if !metrics.adjustment_response_time_ms.is_empty() {
            let avg_response = metrics.adjustment_response_time_ms.iter().sum::<u64>() as f64 / metrics.adjustment_response_time_ms.len() as f64;
        println!("   • Average Adjustment Response Time: {:.1}ms", avg_response);
        }
    
    display_errors_and_warnings(result);
}

/// Display consecutive failure results
fn display_consecutive_failure_results(result: &crate::validation::ValidationResult<crate::validation::risk_management_validator::ConsecutiveFailureMetrics>) {
    println!("   Status: {} ({}ms)", result.status, result.execution_time.as_millis());
    
    let metrics = &result.metrics;
    println!("   • Failure Scenarios Tested: {}", metrics.failure_scenarios_tested);
    println!("   • Halt Trigger Accuracy: {:.1}%", metrics.halt_trigger_accuracy);
    println!("   • Failure Count Accuracy: {:.1}%", metrics.failure_count_accuracy);
    println!("   • Recovery Accuracy: {:.1}%", metrics.recovery_accuracy);
    println!("   • Threshold Enforcement Rate: {:.1}%", metrics.threshold_enforcement_rate);
        
        if !metrics.failure_response_latency_ms.is_empty() {
            let avg_response = metrics.failure_response_latency_ms.iter().sum::<u64>() as f64 / metrics.failure_response_latency_ms.len() as f64;
        println!("   • Average Failure Response Latency: {:.1}ms", avg_response);
        }
    
    display_errors_and_warnings(result);
}

/// Display emergency shutdown results
fn display_emergency_shutdown_results(result: &crate::validation::ValidationResult<crate::validation::risk_management_validator::EmergencyShutdownMetrics>) {
    println!("   Status: {} ({}ms)", result.status, result.execution_time.as_millis());
    
    let metrics = &result.metrics;
    println!("   • Emergency Scenarios Tested: {}", metrics.emergency_scenarios_tested);
    println!("   • Shutdown Trigger Accuracy: {:.1}%", metrics.shutdown_trigger_accuracy);
    println!("   • Graceful Degradation Rate: {:.1}%", metrics.graceful_degradation_rate);
    println!("   • Transaction Preservation Rate: {:.1}%", metrics.transaction_preservation_rate);
    println!("   • State Consistency Rate: {:.1}%", metrics.state_consistency_rate);
        
        if !metrics.emergency_response_time_ms.is_empty() {
            let avg_response = metrics.emergency_response_time_ms.iter().sum::<u64>() as f64 / metrics.emergency_response_time_ms.len() as f64;
        println!("   • Average Emergency Response Time: {:.1}ms", avg_response);
        }
    
    display_errors_and_warnings(result);
}

/// Display circuit breaker results
fn display_circuit_breaker_results(result: &crate::validation::ValidationResult<crate::validation::risk_management_validator::CircuitBreakerValidationMetrics>) {
    println!("   Status: {} ({}ms)", result.status, result.execution_time.as_millis());
    
    let metrics = &result.metrics;
    println!("   • Scenarios Tested: {}", metrics.scenarios_tested);
    println!("   • State Transition Accuracy: {:.1}%", metrics.state_transition_accuracy);
    println!("   • Failure Threshold Accuracy: {:.1}%", metrics.failure_threshold_accuracy);
    println!("   • Recovery Timeout Accuracy: {:.1}%", metrics.recovery_timeout_accuracy);
    println!("   • Half-Open Behavior Accuracy: {:.1}%", metrics.half_open_behavior_accuracy);
        
        if !metrics.response_time_ms.is_empty() {
            let avg_response = metrics.response_time_ms.iter().sum::<u64>() as f64 / metrics.response_time_ms.len() as f64;
        println!("   • Average Circuit Breaker Response Time: {:.1}ms", avg_response);
        }
    
    display_errors_and_warnings(result);
}

/// Display comprehensive validation results
fn display_comprehensive_results(result: &crate::validation::ValidationResult<RiskManagementValidationMetrics>) {
    println!("🎯 Comprehensive Validation Results:");
    println!("   Overall Status: {} ({}ms)", result.status, result.execution_time.as_millis());
    
    let metrics = &result.metrics;
        let summary = &metrics.validation_summary;
        
    println!("\n📊 Summary Statistics:");
    println!("   • Overall Success Rate: {:.1}%", summary.overall_success_rate);
    println!("   • Total Tests Executed: {}", summary.total_tests_executed);
    println!("   • Total Tests Passed: {}", summary.total_tests_passed);
        
    println!("\n⚡ Performance Metrics:");
    println!("   • Average Risk Decision Time: {:.1}ms", summary.performance_summary.average_risk_decision_time_ms);
    println!("   • Max Response Time: {}ms", summary.performance_summary.max_response_time_ms);
    println!("   • Min Response Time: {}ms", summary.performance_summary.min_response_time_ms);
    println!("   • Memory Usage: {:.1}MB", summary.performance_summary.memory_usage_mb);
    println!("   • CPU Usage: {:.1}%", summary.performance_summary.cpu_usage_percent);
        
        if !summary.critical_failures.is_empty() {
        println!("\n❌ Critical Failures:");
            for failure in &summary.critical_failures {
            println!("   • {}", failure);
            }
        }
        
        if !summary.warnings.is_empty() {
        println!("\n⚠️  Warnings:");
            for warning in &summary.warnings {
            println!("   • {}", warning);
            }
        }
        
    println!("\n🔍 Component Breakdown:");
    println!("   • Kelly Criterion: {:.1}% accuracy ({} tests)", 
                 metrics.kelly_criterion_metrics.average_accuracy,
                 metrics.kelly_criterion_metrics.calculations_tested);
    println!("   • Loss Limits: {:.1}% enforcement rate ({} scenarios)", 
                 metrics.daily_loss_limit_metrics.circuit_breaker_activation_rate,
                 metrics.daily_loss_limit_metrics.scenarios_tested);
    println!("   • Volatility Adjustments: {:.1}% accuracy ({} scenarios)", 
                 metrics.volatility_adjustment_metrics.adjustment_accuracy_rate,
                 metrics.volatility_adjustment_metrics.volatility_scenarios_tested);
    println!("   • Consecutive Failures: {:.1}% halt accuracy ({} scenarios)", 
                 metrics.consecutive_failure_metrics.halt_trigger_accuracy,
                 metrics.consecutive_failure_metrics.failure_scenarios_tested);
    println!("   • Emergency Shutdown: {:.1}% trigger accuracy ({} scenarios)", 
                 metrics.emergency_shutdown_metrics.shutdown_trigger_accuracy,
                 metrics.emergency_shutdown_metrics.emergency_scenarios_tested);
    println!("   • Circuit Breakers: {:.1}% state transition accuracy ({} scenarios)", 
                 metrics.circuit_breaker_metrics.state_transition_accuracy,
                 metrics.circuit_breaker_metrics.scenarios_tested);
    
    display_errors_and_warnings(result);
}

/// Display errors and warnings for any validation result
fn display_errors_and_warnings<T>(result: &crate::validation::ValidationResult<T>) {
    if !result.errors.is_empty() {
    println!("   ❌ Errors:");
        for error in &result.errors {
        println!("      • {}: {}", error.code, error.message);
        }
    
    if !result.warnings.is_empty() {
    println!("   ⚠️  Warnings:");
        for warning in &result.warnings {
        println!("      • {}: {}", warning.code, warning.message);
        }
}

/// Show usage examples for the risk management validator
pub fn show_usage_examples() {
    println!("\n📚 Risk Management Validator Usage Examples:\n");
    
    println!("1️⃣ Basic Kelly Criterion Validation:");
    println!("   ```rust");
    println!("   let validator = RiskManagementValidator::new(config);");
    println!("   let scenarios = test_data_generator.generate_risk_test_scenarios();");
    println!("   let result = validator.validate_kelly_criterion_position_sizing(&scenarios).await?;");
    println!("   ```\n");
    
    println!("2️⃣ Daily Loss Limit Validation:");
    println!("   ```rust");
    println!("   let result = validator.validate_daily_loss_limits(&scenarios).await?;");
    println!("   if result.status == ValidationStatus::Passed {{");
    println!("       println!(\"Loss limits properly enforced!\");");
    println!("   }}");
    println!("   ```\n");
    
    println!("3️⃣ Comprehensive Risk Validation:");
    println!("   ```rust");
    println!("   let comprehensive_result = validator");
    println!("       .generate_comprehensive_validation_report(&scenarios)");
    println!("       .await?;");
    println!("   ");
    println!("   println!(\"Overall success rate: {{:.1}}%\", ");
    println!("            comprehensive_result.metrics.validation_summary.overall_success_rate);");
    println!("            comprehensive_result.metrics.validation_summary.overall_success_rate);");
    println!("   ```\n");
    
    println!("4️⃣ Custom Test Scenarios:");
    println!("   ```rust");
    println!("   let custom_scenario = RiskTestScenario {{");
    println!("       name: \"custom_high_risk\".to_string(),");
    println!("       market_regime: MarketRegime::HighVolatilityCorrection,");
    println!("       portfolio_value: dec!(50000.0),");
    println!("       daily_pnl: dec!(-1000.0),");
    println!("       max_daily_loss: dec!(-2000.0),");
    println!("       // ... other fields");
    println!("   }};");
    println!("   ```\n");
    
    println!("5️⃣ CLI Usage:");
    println!("   ```bash");
    println!("   # Run Kelly Criterion validation");
    println!("   cargo run -- validation risk-management kelly --scenarios 20");
    println!("   ");
    println!("   # Run comprehensive validation with detailed output");
    println!("   cargo run -- validation risk-management comprehensive --detailed");
    println!("   ");
    println!("   # Generate HTML report");
    println!("   cargo run -- validation risk-management report \\");
    println!("     --input results.json --output report.html --format html");
    println!("   ```\n");
    
    println!("💡 Tips:");
    println!("   • Use different market regimes to test regime-specific multipliers");
    println!("   • Test edge cases like exactly hitting loss limits");
    println!("   • Validate both normal and emergency scenarios");
    println!("   • Monitor performance metrics for production readiness");
    println!("   • Use the comprehensive validation for full system testing");
}

/// Run framework integration demo
pub async fn run_framework_integration_demo() -> Result<()> {
    info!("🔗 Running Risk Management Framework Integration Demo");
    
    println!("\n=== Framework Integration Demo ===\n");
    
    // Initialize validation framework
    let mut config = ValidationConfig::default();
    config.continue_on_failure = true; // Continue even if some tests fail
    
    let framework = ValidationFramework::new(config)?;
    
    // Create validator
    let validator = RiskManagementValidator::new(ValidationConfig::default());
    let test_data_generator = RiskTestDataGenerator::new(42);
    let scenarios = test_data_generator.generate_risk_test_scenarios();
    
    println!("🏗️  Framework Integration Test:");
    
    // Execute validation through the framework
    let result = framework.execute_validation(
        "risk_management_integration_test",
        || async {
            validator.generate_comprehensive_validation_report(&scenarios).await
        }
    ).await?;
    
    println!("   • Framework Test ID: {}", result.test_id);
    println!("   • Framework Status: {}", result.status);
    println!("   • Framework Execution Time: {}ms", result.execution_time.as_millis());
    
    if let Some(metrics) = result.metrics {
        let risk_metrics = &metrics.metrics;
        println!("   • Risk Validation Success Rate: {:.1}%", 
                     risk_metrics.validation_summary.overall_success_rate);
        }
    
    // Get framework status
    let framework_status = framework.get_framework_status();
    println!("\n📊 Framework Status:");
    println!("   • Total Validations: {}", framework_status.validation_metrics.total_validations);
    println!("   • Framework Success Rate: {:.1}%", framework_status.validation_metrics.success_rate * 100.0);
    println!("   • Active Validations: {}", framework_status.framework_metrics.active_validations_count);
    
    info!("✅ Framework integration demo completed");
    Ok(())
}