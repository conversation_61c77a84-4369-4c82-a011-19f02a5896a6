// src/deployment/health_checks.rs
// Health check manager for deployment monitoring

use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::Duration;
use tokio::time::timeout;
use tracing::{debug, error, info, warn};
use std::pin::Pin;
use std::boxed::Box;

use super::HealthCheckConfig;

/// Health check manager for monitoring deployment health
#[derive(Debug)]
pub struct HealthCheckManager {
    config: Arc<RwLock<HealthCheckConfig>>,
    check_results: Arc<RwLock<HashMap<String, HealthCheckResult>>>,
    check_history: Arc<RwLock<Vec<HealthCheckExecution>>>,
}

impl HealthCheckManager {
    /// Create a new health check manager
    pub fn new() -> Self {
        Self {
            config: Arc::new(RwLock::new(HealthCheckConfig::default())),
            check_results: Arc::new(RwLock::new(HashMap::new())),
            check_history: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Create health check manager with custom configuration
    pub fn with_config(config: HealthCheckConfig) -> Self {
        Self {
            config: Arc::new(RwLock::new(config)),
            check_results: Arc::new(RwLock::new(HashMap::new())),
            check_history: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Run all health checks
    pub async fn run_all_checks(&self) -> Result<bool> {
        let config = self.config.read().unwrap().clone();
        
        if !config.enabled {
            debug!("Health checks are disabled");
            return Ok(true);
        }

        info!("Running all health checks");

        let mut all_passed = true;
        let execution_id = uuid::Uuid::new_v4().to_string();
        let started_at = Utc::now();

        // Run basic health checks
        let basic_checks: Vec<(&str, Pin<Box<dyn std::future::Future<Output = Result<HealthCheckResult>> + Send>>)> = vec![
            ("service_health", Box::pin(self.check_service_health())),
            ("database_connectivity", Box::pin(self.check_database_connectivity())),
            ("redis_connectivity", Box::pin(self.check_redis_connectivity())),
            ("nats_connectivity", Box::pin(self.check_nats_connectivity())),
            ("memory_usage", Box::pin(self.check_memory_usage())),
            ("cpu_usage", Box::pin(self.check_cpu_usage())),
            ("disk_usage", Box::pin(self.check_disk_usage())),
        ];

        let mut check_results = HashMap::new();

        for (check_name, check_future) in basic_checks {
            let result = timeout(
                Duration::from_secs(config.timeout_seconds),
                check_future
            ).await;

            let check_result = match result {
                Ok(Ok(result)) => result,
                Ok(Err(e)) => {
                    error!("Health check '{}' failed: {}", check_name, e);
                    HealthCheckResult {
                        name: check_name.to_string(),
                        status: HealthCheckStatus::Failed,
                        message: e.to_string(),
                        timestamp: Utc::now(),
                        duration_ms: 0,
                        metadata: HashMap::new(),
                    }
                }
                Err(_) => {
                    error!("Health check '{}' timed out", check_name);
                    HealthCheckResult {
                        name: check_name.to_string(),
                        status: HealthCheckStatus::Failed,
                        message: "Health check timed out".to_string(),
                        timestamp: Utc::now(),
                        duration_ms: config.timeout_seconds * 1000,
                        metadata: HashMap::new(),
                    }
                }
            };

            if check_result.status != HealthCheckStatus::Passed {
                all_passed = false;
            }

            check_results.insert(check_name.to_string(), check_result.clone());
        }

        // Run endpoint health checks
        for endpoint in &config.endpoints {
            let check_name = format!("endpoint_{}", endpoint.replace('/', "_"));
            let result = timeout(
                Duration::from_secs(config.timeout_seconds),
                self.check_endpoint_health(endpoint)
            ).await;

            let check_result = match result {
                Ok(Ok(result)) => result,
                Ok(Err(e)) => {
                    error!("Endpoint health check '{}' failed: {}", endpoint, e);
                    HealthCheckResult {
                        name: check_name.clone(),
                        status: HealthCheckStatus::Failed,
                        message: e.to_string(),
                        timestamp: Utc::now(),
                        duration_ms: 0,
                        metadata: HashMap::new(),
                    }
                }
                Err(_) => {
                    error!("Endpoint health check '{}' timed out", endpoint);
                    HealthCheckResult {
                        name: check_name.clone(),
                        status: HealthCheckStatus::Failed,
                        message: "Endpoint health check timed out".to_string(),
                        timestamp: Utc::now(),
                        duration_ms: config.timeout_seconds * 1000,
                        metadata: HashMap::new(),
                    }
                }
            };

            if check_result.status != HealthCheckStatus::Passed {
                all_passed = false;
            }

            check_results.insert(check_name, check_result);
        }

        // Update check results
        {
            let mut results = self.check_results.write().unwrap();
            *results = check_results.clone();
        }

        // Record execution
        let execution = HealthCheckExecution {
            id: execution_id,
            started_at,
            completed_at: Utc::now(),
            overall_status: if all_passed { 
                HealthCheckStatus::Passed 
            } else { 
                HealthCheckStatus::Failed 
            },
            check_results,
        };

        {
            let mut history = self.check_history.write().unwrap();
            history.push(execution);
            
            // Keep only last 100 executions
            if history.len() > 100 {
                history.remove(0);
            }
        }

        info!("Health checks completed: {}", if all_passed { "PASSED" } else { "FAILED" });
        Ok(all_passed)
    }

    /// Check service health
    async fn check_service_health(&self) -> Result<HealthCheckResult> {
        let start_time = std::time::Instant::now();
        
        // Check if the main service is running and responsive
        // This would typically involve checking process status, memory usage, etc.
        
        let duration_ms = start_time.elapsed().as_millis() as u64;
        
        Ok(HealthCheckResult {
            name: "service_health".to_string(),
            status: HealthCheckStatus::Passed,
            message: "Service is healthy".to_string(),
            timestamp: Utc::now(),
            duration_ms,
            metadata: HashMap::new(),
        })
    }

    /// Check database connectivity
    async fn check_database_connectivity(&self) -> Result<HealthCheckResult> {
        let start_time = std::time::Instant::now();
        
        // This would typically involve:
        // 1. Attempting to connect to the database
        // 2. Running a simple query
        // 3. Checking connection pool status
        
        let duration_ms = start_time.elapsed().as_millis() as u64;
        
        // Simulate database check
        tokio::time::sleep(Duration::from_millis(10)).await;
        
        Ok(HealthCheckResult {
            name: "database_connectivity".to_string(),
            status: HealthCheckStatus::Passed,
            message: "Database connection is healthy".to_string(),
            timestamp: Utc::now(),
            duration_ms,
            metadata: {
                let mut metadata = HashMap::new();
                metadata.insert("connection_pool_size".to_string(), "10".to_string());
                metadata.insert("active_connections".to_string(), "3".to_string());
                metadata
            },
        })
    }

    /// Check Redis connectivity
    async fn check_redis_connectivity(&self) -> Result<HealthCheckResult> {
        let start_time = std::time::Instant::now();
        
        // This would typically involve:
        // 1. Attempting to connect to Redis
        // 2. Running a PING command
        // 3. Checking memory usage
        
        let duration_ms = start_time.elapsed().as_millis() as u64;
        
        // Simulate Redis check
        tokio::time::sleep(Duration::from_millis(5)).await;
        
        Ok(HealthCheckResult {
            name: "redis_connectivity".to_string(),
            status: HealthCheckStatus::Passed,
            message: "Redis connection is healthy".to_string(),
            timestamp: Utc::now(),
            duration_ms,
            metadata: {
                let mut metadata = HashMap::new();
                metadata.insert("memory_usage_mb".to_string(), "128".to_string());
                metadata.insert("connected_clients".to_string(), "5".to_string());
                metadata
            },
        })
    }

    /// Check NATS connectivity
    async fn check_nats_connectivity(&self) -> Result<HealthCheckResult> {
        let start_time = std::time::Instant::now();
        
        // This would typically involve:
        // 1. Checking NATS connection status
        // 2. Publishing and subscribing to a test message
        // 3. Checking JetStream status if enabled
        
        let duration_ms = start_time.elapsed().as_millis() as u64;
        
        // Simulate NATS check
        tokio::time::sleep(Duration::from_millis(15)).await;
        
        Ok(HealthCheckResult {
            name: "nats_connectivity".to_string(),
            status: HealthCheckStatus::Passed,
            message: "NATS connection is healthy".to_string(),
            timestamp: Utc::now(),
            duration_ms,
            metadata: {
                let mut metadata = HashMap::new();
                metadata.insert("server_version".to_string(), "2.10.0".to_string());
                metadata.insert("jetstream_enabled".to_string(), "true".to_string());
                metadata
            },
        })
    }

    /// Check memory usage
    async fn check_memory_usage(&self) -> Result<HealthCheckResult> {
        let start_time = std::time::Instant::now();
        
        // Get system memory information
        let memory_usage_percentage = self.get_memory_usage_percentage().await?;
        
        let duration_ms = start_time.elapsed().as_millis() as u64;
        
        let status = if memory_usage_percentage > 90.0 {
            HealthCheckStatus::Failed
        } else if memory_usage_percentage > 80.0 {
            HealthCheckStatus::Warning
        } else {
            HealthCheckStatus::Passed
        };
        
        let message = format!("Memory usage: {:.1}%", memory_usage_percentage);
        
        Ok(HealthCheckResult {
            name: "memory_usage".to_string(),
            status,
            message,
            timestamp: Utc::now(),
            duration_ms,
            metadata: {
                let mut metadata = HashMap::new();
                metadata.insert("usage_percentage".to_string(), memory_usage_percentage.to_string());
                metadata
            },
        })
    }

    /// Check CPU usage
    async fn check_cpu_usage(&self) -> Result<HealthCheckResult> {
        let start_time = std::time::Instant::now();
        
        // Get system CPU information
        let cpu_usage_percentage = self.get_cpu_usage_percentage().await?;
        
        let duration_ms = start_time.elapsed().as_millis() as u64;
        
        let status = if cpu_usage_percentage > 90.0 {
            HealthCheckStatus::Failed
        } else if cpu_usage_percentage > 80.0 {
            HealthCheckStatus::Warning
        } else {
            HealthCheckStatus::Passed
        };
        
        let message = format!("CPU usage: {:.1}%", cpu_usage_percentage);
        
        Ok(HealthCheckResult {
            name: "cpu_usage".to_string(),
            status,
            message,
            timestamp: Utc::now(),
            duration_ms,
            metadata: {
                let mut metadata = HashMap::new();
                metadata.insert("usage_percentage".to_string(), cpu_usage_percentage.to_string());
                metadata
            },
        })
    }

    /// Check disk usage
    async fn check_disk_usage(&self) -> Result<HealthCheckResult> {
        let start_time = std::time::Instant::now();
        
        // Get disk usage information
        let disk_usage_percentage = self.get_disk_usage_percentage().await?;
        
        let duration_ms = start_time.elapsed().as_millis() as u64;
        
        let status = if disk_usage_percentage > 95.0 {
            HealthCheckStatus::Failed
        } else if disk_usage_percentage > 85.0 {
            HealthCheckStatus::Warning
        } else {
            HealthCheckStatus::Passed
        };
        
        let message = format!("Disk usage: {:.1}%", disk_usage_percentage);
        
        Ok(HealthCheckResult {
            name: "disk_usage".to_string(),
            status,
            message,
            timestamp: Utc::now(),
            duration_ms,
            metadata: {
                let mut metadata = HashMap::new();
                metadata.insert("usage_percentage".to_string(), disk_usage_percentage.to_string());
                metadata
            },
        })
    }

    /// Check endpoint health
    async fn check_endpoint_health(&self, endpoint: &str) -> Result<HealthCheckResult> {
        let start_time = std::time::Instant::now();
        
        // This would typically involve making an HTTP request to the endpoint
        let url = format!("http://localhost:8080{}", endpoint);
        
        // Simulate HTTP request
        tokio::time::sleep(Duration::from_millis(20)).await;
        
        let duration_ms = start_time.elapsed().as_millis() as u64;
        
        // For simulation, assume endpoint is healthy
        Ok(HealthCheckResult {
            name: format!("endpoint_{}", endpoint.replace('/', "_")),
            status: HealthCheckStatus::Passed,
            message: format!("Endpoint {} is responding", endpoint),
            timestamp: Utc::now(),
            duration_ms,
            metadata: {
                let mut metadata = HashMap::new();
                metadata.insert("url".to_string(), url);
                metadata.insert("status_code".to_string(), "200".to_string());
                metadata
            },
        })
    }

    /// Get memory usage percentage
    async fn get_memory_usage_percentage(&self) -> Result<f64> {
        // This would typically use system APIs to get actual memory usage
        // For simulation, return a reasonable value
        Ok(45.2)
    }

    /// Get CPU usage percentage
    async fn get_cpu_usage_percentage(&self) -> Result<f64> {
        // This would typically use system APIs to get actual CPU usage
        // For simulation, return a reasonable value
        Ok(23.8)
    }

    /// Get disk usage percentage
    async fn get_disk_usage_percentage(&self) -> Result<f64> {
        // This would typically use system APIs to get actual disk usage
        // For simulation, return a reasonable value
        Ok(67.3)
    }

    /// Run a specific health check
    pub async fn run_check(&self, check_name: &str) -> Result<HealthCheckResult> {
        match check_name {
            "service_health" => self.check_service_health().await,
            "database_connectivity" => self.check_database_connectivity().await,
            "redis_connectivity" => self.check_redis_connectivity().await,
            "nats_connectivity" => self.check_nats_connectivity().await,
            "memory_usage" => self.check_memory_usage().await,
            "cpu_usage" => self.check_cpu_usage().await,
            "disk_usage" => self.check_disk_usage().await,
            _ => {
                if check_name.starts_with("endpoint_") {
                    let endpoint = check_name.strip_prefix("endpoint_")
                        .unwrap_or("")
                        .replace('_', "/");
                    self.check_endpoint_health(&format!("/{}", endpoint)).await
                } else {
                    Err(anyhow::anyhow!("Unknown health check: {}", check_name))
                }
            }
        }
    }

    /// Get latest check results
    pub fn get_latest_results(&self) -> HashMap<String, HealthCheckResult> {
        self.check_results.read().unwrap().clone()
    }

    /// Get check history
    pub fn get_check_history(&self, limit: Option<usize>) -> Vec<HealthCheckExecution> {
        let history = self.check_history.read().unwrap();
        let limit = limit.unwrap_or(history.len());
        history.iter().rev().take(limit).cloned().collect()
    }

    /// Get overall health status
    pub fn get_overall_health_status(&self) -> HealthCheckStatus {
        let results = self.check_results.read().unwrap();
        
        if results.is_empty() {
            return HealthCheckStatus::Unknown;
        }

        let mut has_failed = false;
        let mut has_warning = false;

        for result in results.values() {
            match result.status {
                HealthCheckStatus::Failed => has_failed = true,
                HealthCheckStatus::Warning => has_warning = true,
                _ => {}
            }
        }

        if has_failed {
            HealthCheckStatus::Failed
        } else if has_warning {
            HealthCheckStatus::Warning
        } else {
            HealthCheckStatus::Passed
        }
    }

    /// Update health check configuration
    pub fn update_config(&self, config: HealthCheckConfig) {
        let mut current_config = self.config.write().unwrap();
        *current_config = config;
        info!("Health check configuration updated");
    }

    /// Enable or disable health checks
    pub fn set_enabled(&self, enabled: bool) {
        let mut config = self.config.write().unwrap();
        config.enabled = enabled;
        info!("Health checks {}", if enabled { "enabled" } else { "disabled" });
    }
}

/// Health check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckResult {
    pub name: String,
    pub status: HealthCheckStatus,
    pub message: String,
    pub timestamp: DateTime<Utc>,
    pub duration_ms: u64,
    pub metadata: HashMap<String, String>,
}

/// Health check status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum HealthCheckStatus {
    Passed,
    Warning,
    Failed,
    Unknown,
}

impl std::fmt::Display for HealthCheckStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            HealthCheckStatus::Passed => write!(f, "passed"),
            HealthCheckStatus::Warning => write!(f, "warning"),
            HealthCheckStatus::Failed => write!(f, "failed"),
            HealthCheckStatus::Unknown => write!(f, "unknown"),
        }
    }
}

/// Health check execution record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckExecution {
    pub id: String,
    pub started_at: DateTime<Utc>,
    pub completed_at: DateTime<Utc>,
    pub overall_status: HealthCheckStatus,
    pub check_results: HashMap<String, HealthCheckResult>,
}

impl HealthCheckExecution {
    /// Get execution duration
    pub fn duration(&self) -> chrono::Duration {
        self.completed_at.signed_duration_since(self.started_at)
    }

    /// Get failed checks
    pub fn failed_checks(&self) -> Vec<&HealthCheckResult> {
        self.check_results.values()
            .filter(|result| result.status == HealthCheckStatus::Failed)
            .collect()
    }

    /// Get warning checks
    pub fn warning_checks(&self) -> Vec<&HealthCheckResult> {
        self.check_results.values()
            .filter(|result| result.status == HealthCheckStatus::Warning)
            .collect()
    }

    /// Get passed checks
    pub fn passed_checks(&self) -> Vec<&HealthCheckResult> {
        self.check_results.values()
            .filter(|result| result.status == HealthCheckStatus::Passed)
            .collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_health_check_manager_creation() {
        let manager = HealthCheckManager::new();
        
        // Should start with no results
        let results = manager.get_latest_results();
        assert!(results.is_empty());
        
        // Overall status should be unknown initially
        assert_eq!(manager.get_overall_health_status(), HealthCheckStatus::Unknown);
    }

    #[tokio::test]
    async fn test_run_all_checks() {
        let manager = HealthCheckManager::new();
        
        let result = manager.run_all_checks().await;
        assert!(result.is_ok());
        
        // Should have results after running checks
        let results = manager.get_latest_results();
        assert!(!results.is_empty());
        
        // Should have basic checks
        assert!(results.contains_key("service_health"));
        assert!(results.contains_key("database_connectivity"));
        assert!(results.contains_key("memory_usage"));
    }

    #[tokio::test]
    async fn test_individual_health_check() {
        let manager = HealthCheckManager::new();
        
        let result = manager.run_check("service_health").await;
        assert!(result.is_ok());
        
        let check_result = result.unwrap();
        assert_eq!(check_result.name, "service_health");
        assert_eq!(check_result.status, HealthCheckStatus::Passed);
    }

    #[tokio::test]
    async fn test_health_check_history() {
        let manager = HealthCheckManager::new();
        
        // Run checks to generate history
        let _ = manager.run_all_checks().await;
        
        let history = manager.get_check_history(Some(1));
        assert_eq!(history.len(), 1);
        
        let execution = &history[0];
        assert!(!execution.check_results.is_empty());
    }

    #[tokio::test]
    async fn test_health_check_configuration() {
        let mut config = HealthCheckConfig::default();
        config.enabled = false;
        
        let manager = HealthCheckManager::with_config(config);
        
        // Should return true when disabled
        let result = manager.run_all_checks().await;
        assert!(result.is_ok());
        assert!(result.unwrap());
    }

    #[test]
    fn test_health_check_status_display() {
        assert_eq!(HealthCheckStatus::Passed.to_string(), "passed");
        assert_eq!(HealthCheckStatus::Warning.to_string(), "warning");
        assert_eq!(HealthCheckStatus::Failed.to_string(), "failed");
        assert_eq!(HealthCheckStatus::Unknown.to_string(), "unknown");
    }
}