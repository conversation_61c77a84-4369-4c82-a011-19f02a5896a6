// MISSION: Enhanced Error Propagation and Alerting System
// WHY: Provide comprehensive error handling with proper propagation chains, structured logging, and alerting
// HOW: Implement error context propagation, retry logic, and multi-channel alerting mechanisms

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Mutex};
use tracing::{error, warn, info, debug};
use uuid::Uuid;
use rand::{self, Rng};

use crate::error::{BasiliskError, NetworkError, DataProviderError, ExecutionError, StrategyError, CriticalError};
use crate::logging::{ErrorCode, AlertSeverity, TraceId, TradingContext};

/// Enhanced error context with propagation chain
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    pub trace_id: TraceId,
    pub error_id: String,
    pub component: String,
    pub function: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub opportunity_id: Option<String>,
    pub chain_id: Option<u64>,
    pub propagation_chain: Vec<ErrorPropagationStep>,
    pub additional_data: HashMap<String, serde_json::Value>,
    pub retry_count: u32,
    pub max_retries: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorPropagationStep {
    pub component: String,
    pub function: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub error_type: String,
    pub message: String,
}

impl ErrorContext {
    pub fn new(component: &str, function: &str) -> Self {
        Self {
            trace_id: TraceId::new(),
            error_id: Uuid::new_v4().to_string(),
            component: component.to_string(),
            function: function.to_string(),
            timestamp: chrono::Utc::now(),
            opportunity_id: None,
            chain_id: None,
            propagation_chain: Vec::new(),
            additional_data: HashMap::new(),
            retry_count: 0,
            max_retries: 3,
        }
    }

    pub fn with_opportunity(mut self, opportunity_id: &str) -> Self {
        self.opportunity_id = Some(opportunity_id.to_string());
        self.trace_id = TraceId::from_opportunity_id(opportunity_id);
        self
    }

    pub fn with_chain(mut self, chain_id: u64) -> Self {
        self.chain_id = Some(chain_id);
        self
    }

    pub fn with_data<T: serde::Serialize>(mut self, key: &str, value: T) -> Self {
        if let Ok(json_value) = serde_json::to_value(value) {
            self.additional_data.insert(key.to_string(), json_value);
        }
        self
    }

    pub fn with_max_retries(mut self, max_retries: u32) -> Self {
        self.max_retries = max_retries;
        self
    }

    pub fn propagate(&mut self, component: &str, function: &str, error: &BasiliskError) {
        let step = ErrorPropagationStep {
            component: component.to_string(),
            function: function.to_string(),
            timestamp: chrono::Utc::now(),
            error_type: std::any::type_name_of_val(error).to_string(),
            message: error.to_string(),
        };
        self.propagation_chain.push(step);
    }

    pub fn increment_retry(&mut self) {
        self.retry_count += 1;
    }

    pub fn can_retry(&self) -> bool {
        self.retry_count < self.max_retries
    }

    pub fn is_final_attempt(&self) -> bool {
        self.retry_count >= self.max_retries
    }
}

/// Enhanced error with context and propagation
#[derive(Debug)]
pub struct EnhancedError {
    pub inner: BasiliskError,
    pub context: ErrorContext,
    pub error_code: ErrorCode,
    pub severity: AlertSeverity,
    pub is_retryable: bool,
    pub recovery_suggestions: Vec<String>,
}

impl EnhancedError {
    pub fn new(error: BasiliskError, context: ErrorContext) -> Self {
        let (error_code, is_retryable) = Self::classify_error(&error);
        let severity = error_code.severity();
        let recovery_suggestions = Self::generate_recovery_suggestions(&error);

        Self {
            inner: error,
            context,
            error_code,
            severity,
            is_retryable,
            recovery_suggestions,
        }
    }

    fn classify_error(error: &BasiliskError) -> (ErrorCode, bool) {
        match error {
            BasiliskError::Network(net_err) => (net_err.error_code(), net_err.is_retryable()),
            BasiliskError::DataProvider(data_err) => (data_err.error_code(), true),
            BasiliskError::Execution(exec_err) => (exec_err.error_code(), false),
            BasiliskError::Strategy(strat_err) => (strat_err.error_code(), false),
            BasiliskError::Critical(crit_err) => (crit_err.error_code(), false),
            _ => (ErrorCode::EUnrecoverableError, false),
        }
    }

    fn generate_recovery_suggestions(error: &BasiliskError) -> Vec<String> {
        match error {
            BasiliskError::Network(NetworkError::RpcTimeout { .. }) => vec![
                "Switch to backup RPC endpoint".to_string(),
                "Increase timeout configuration".to_string(),
                "Check network connectivity".to_string(),
            ],
            BasiliskError::Network(NetworkError::RpcRateLimited { .. }) => vec![
                "Implement exponential backoff".to_string(),
                "Use different RPC endpoint".to_string(),
                "Reduce request frequency".to_string(),
            ],
            BasiliskError::DataProvider(DataProviderError::StaleData { .. }) => vec![
                "Refresh data from primary source".to_string(),
                "Use backup data provider".to_string(),
                "Increase data refresh frequency".to_string(),
            ],
            BasiliskError::Execution(ExecutionError::InsufficientLiquidity { .. }) => vec![
                "Split trade into smaller chunks".to_string(),
                "Find alternative trading path".to_string(),
                "Wait for liquidity to improve".to_string(),
            ],
            BasiliskError::Execution(ExecutionError::HighSlippage { .. }) => vec![
                "Reduce trade size".to_string(),
                "Adjust slippage tolerance".to_string(),
                "Wait for better market conditions".to_string(),
            ],
            _ => vec!["Review error details and system logs".to_string()],
        }
    }
}

impl std::fmt::Display for EnhancedError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "[{}] {}: {}", self.error_code.as_str(), self.context.component, self.inner)
    }
}

impl std::error::Error for EnhancedError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        Some(&self.inner)
    }
}

/// Retry configuration for different error types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    pub max_attempts: u32,
    pub initial_delay_ms: u64,
    pub max_delay_ms: u64,
    pub backoff_multiplier: f64,
    pub jitter_enabled: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            initial_delay_ms: 100,
            max_delay_ms: 5000,
            backoff_multiplier: 2.0,
            jitter_enabled: true,
        }
    }
}

impl RetryConfig {
    pub fn for_network_errors() -> Self {
        Self {
            max_attempts: 5,
            initial_delay_ms: 200,
            max_delay_ms: 10000,
            backoff_multiplier: 2.0,
            jitter_enabled: true,
        }
    }

    pub fn for_data_provider_errors() -> Self {
        Self {
            max_attempts: 3,
            initial_delay_ms: 500,
            max_delay_ms: 5000,
            backoff_multiplier: 1.5,
            jitter_enabled: true,
        }
    }

    pub fn for_execution_errors() -> Self {
        Self {
            max_attempts: 1, // Most execution errors shouldn't be retried
            initial_delay_ms: 0,
            max_delay_ms: 0,
            backoff_multiplier: 1.0,
            jitter_enabled: false,
        }
    }
}

/// Enhanced retry mechanism with exponential backoff and jitter
pub struct RetryManager {
    configs: HashMap<ErrorCode, RetryConfig>,
}

impl RetryManager {
    pub fn new() -> Self {
        let mut configs = HashMap::new();
        
        // Network error retry configs
        configs.insert(ErrorCode::ERpcTimeout, RetryConfig::for_network_errors());
        configs.insert(ErrorCode::ERpcConnectionFailed, RetryConfig::for_network_errors());
        configs.insert(ErrorCode::ERpcRateLimited, RetryConfig {
            max_attempts: 3,
            initial_delay_ms: 1000,
            max_delay_ms: 30000,
            backoff_multiplier: 2.0,
            jitter_enabled: true,
        });

        // Data provider error retry configs
        configs.insert(ErrorCode::EDataSourceUnavailable, RetryConfig::for_data_provider_errors());
        configs.insert(ErrorCode::EDataStale, RetryConfig::for_data_provider_errors());
        configs.insert(ErrorCode::EPriceOracleFailure, RetryConfig::for_data_provider_errors());

        // Execution errors (mostly non-retryable)
        configs.insert(ErrorCode::EGasEstimationFailed, RetryConfig {
            max_attempts: 2,
            initial_delay_ms: 1000,
            max_delay_ms: 3000,
            backoff_multiplier: 1.5,
            jitter_enabled: true,
        });

        Self { configs }
    }

    pub fn get_config(&self, error_code: &ErrorCode) -> RetryConfig {
        self.configs.get(error_code).cloned().unwrap_or_default()
    }

    pub fn calculate_delay(&self, error_code: &ErrorCode, attempt: u32) -> Duration {
        let config = self.get_config(error_code);
        
        if attempt == 0 {
            return Duration::from_millis(0);
        }

        let base_delay = config.initial_delay_ms as f64 * 
            config.backoff_multiplier.powi((attempt - 1) as i32);
        
        let delay_ms = base_delay.min(config.max_delay_ms as f64) as u64;
        
        if config.jitter_enabled {
            let mut rng = rand::thread_rng();
            let jitter = (rng.gen::<f64>() * 0.1 + 0.95) * delay_ms as f64;
            Duration::from_millis(jitter as u64)
        } else {
            Duration::from_millis(delay_ms)
        }
    }

    pub async fn execute_with_retry<F, T, E>(&self, 
        mut operation: F, 
        error_code: ErrorCode,
        context: &mut ErrorContext
    ) -> Result<T, EnhancedError>
    where
        F: FnMut() -> Result<T, E>,
        E: Into<BasiliskError>,
    {
        let config = self.get_config(&error_code);
        let mut last_error: Option<BasiliskError> = None;

        for attempt in 0..config.max_attempts {
            context.retry_count = attempt;
            
            if attempt > 0 {
                let delay = self.calculate_delay(&error_code, attempt);
                debug!(
                    component = %context.component,
                    function = %context.function,
                    trace_id = %context.trace_id,
                    attempt = attempt,
                    delay_ms = delay.as_millis(),
                    "Retrying operation after delay"
                );
                tokio::time::sleep(delay).await;
            }

            match operation() {
                Ok(result) => {
                    if attempt > 0 {
                        info!(
                            component = %context.component,
                            function = %context.function,
                            trace_id = %context.trace_id,
                            attempt = attempt,
                            "Operation succeeded after retry"
                        );
                    }
                    return Ok(result);
                }
                Err(e) => {
                    let basilisk_error = e.into();
                    context.propagate(&context.component.clone(), &context.function.clone(), &basilisk_error);
                    last_error = Some(basilisk_error);
                }
            }
        }

        let final_error = last_error.unwrap();
        Err(EnhancedError::new(final_error, context.clone()))
    }
}

/// Alert channel configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertChannelConfig {
    pub channel_type: AlertChannelType,
    pub enabled: bool,
    pub min_severity: AlertSeverity,
    pub rate_limit_seconds: u64,
    pub config: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum AlertChannelType {
    Slack,
    Discord,
    Email,
    PagerDuty,
    Webhook,
    Log,
}

/// Alert message structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertMessage {
    pub alert_id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub severity: AlertSeverity,
    pub error_code: ErrorCode,
    pub title: String,
    pub message: String,
    pub context: ErrorContext,
    pub recovery_suggestions: Vec<String>,
    pub runbook_url: Option<String>,
}

impl AlertMessage {
    pub fn from_enhanced_error(error: &EnhancedError) -> Self {
        Self {
            alert_id: Uuid::new_v4().to_string(),
            timestamp: chrono::Utc::now(),
            severity: error.severity.clone(),
            error_code: error.error_code.clone(),
            title: format!("{} in {}", error.error_code.as_str(), error.context.component),
            message: error.inner.to_string(),
            context: error.context.clone(),
            recovery_suggestions: error.recovery_suggestions.clone(),
            runbook_url: Self::get_runbook_url(&error.error_code),
        }
    }

    fn get_runbook_url(error_code: &ErrorCode) -> Option<String> {
        match error_code {
            ErrorCode::ERpcTimeout => Some("https://docs.basilisk.bot/runbooks/rpc-timeout".to_string()),
            ErrorCode::EInsufficientLiquidity => Some("https://docs.basilisk.bot/runbooks/liquidity".to_string()),
            ErrorCode::EHighSlippage => Some("https://docs.basilisk.bot/runbooks/slippage".to_string()),
            ErrorCode::ECriticalConfigError => Some("https://docs.basilisk.bot/runbooks/config".to_string()),
            _ => None,
        }
    }
}

/// Rate limiter for alerts to prevent spam
#[derive(Debug)]
struct AlertRateLimiter {
    last_sent: HashMap<String, Instant>,
    rate_limits: HashMap<AlertChannelType, Duration>,
}

impl AlertRateLimiter {
    fn new() -> Self {
        let mut rate_limits = HashMap::new();
        rate_limits.insert(AlertChannelType::Slack, Duration::from_secs(300)); // 5 minutes
        rate_limits.insert(AlertChannelType::Discord, Duration::from_secs(300));
        rate_limits.insert(AlertChannelType::Email, Duration::from_secs(600)); // 10 minutes
        rate_limits.insert(AlertChannelType::PagerDuty, Duration::from_secs(60)); // 1 minute
        rate_limits.insert(AlertChannelType::Webhook, Duration::from_secs(30));
        rate_limits.insert(AlertChannelType::Log, Duration::from_secs(0)); // No rate limit

        Self {
            last_sent: HashMap::new(),
            rate_limits,
        }
    }

    fn should_send(&mut self, channel_type: &AlertChannelType, error_code: &ErrorCode) -> bool {
        let key = format!("{:?}:{:?}", channel_type, error_code);
        let rate_limit = self.rate_limits.get(channel_type).copied().unwrap_or(Duration::from_secs(300));
        
        if let Some(last_sent) = self.last_sent.get(&key) {
            if last_sent.elapsed() < rate_limit {
                return false;
            }
        }

        self.last_sent.insert(key, Instant::now());
        true
    }
}

/// Multi-channel alerting system
pub struct AlertManager {
    channels: Vec<AlertChannelConfig>,
    rate_limiter: Arc<Mutex<AlertRateLimiter>>,
    client: reqwest::Client,
}

impl AlertManager {
    pub fn new(channels: Vec<AlertChannelConfig>) -> Self {
        Self {
            channels,
            rate_limiter: Arc::new(Mutex::new(AlertRateLimiter::new())),
            client: reqwest::Client::new(),
        }
    }

    pub async fn send_alert(&self, alert: AlertMessage) {
        for channel in &self.channels {
            if !channel.enabled {
                continue;
            }

            // Check severity threshold
            if !self.meets_severity_threshold(&alert.severity, &channel.min_severity) {
                continue;
            }

            // Check rate limiting
            {
                let mut rate_limiter = self.rate_limiter.lock().await;
                if !rate_limiter.should_send(&channel.channel_type, &alert.error_code) {
                    debug!(
                        channel_type = ?channel.channel_type,
                        error_code = %alert.error_code.as_str(),
                        "Alert rate limited"
                    );
                    continue;
                }
            }

            if let Err(e) = self.send_to_channel(channel, &alert).await {
                error!(
                    channel_type = ?channel.channel_type,
                    error = %e,
                    alert_id = %alert.alert_id,
                    "Failed to send alert to channel"
                );
            }
        }
    }

    fn meets_severity_threshold(&self, alert_severity: &AlertSeverity, min_severity: &AlertSeverity) -> bool {
        match (alert_severity, min_severity) {
            (AlertSeverity::Critical, _) => true,
            (AlertSeverity::Error, AlertSeverity::Critical) => false,
            (AlertSeverity::Error, _) => true,
            (AlertSeverity::Warning, AlertSeverity::Critical | AlertSeverity::Error) => false,
            (AlertSeverity::Warning, _) => true,
            (AlertSeverity::Info, AlertSeverity::Info) => true,
            (AlertSeverity::Info, _) => false,
        }
    }

    async fn send_to_channel(&self, channel: &AlertChannelConfig, alert: &AlertMessage) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        match channel.channel_type {
            AlertChannelType::Slack => self.send_slack_alert(channel, alert).await,
            AlertChannelType::Discord => self.send_discord_alert(channel, alert).await,
            AlertChannelType::Email => self.send_email_alert(channel, alert).await,
            AlertChannelType::PagerDuty => self.send_pagerduty_alert(channel, alert).await,
            AlertChannelType::Webhook => self.send_webhook_alert(channel, alert).await,
            AlertChannelType::Log => {
                self.send_log_alert(alert).await;
                Ok(())
            }
        }
    }

    async fn send_slack_alert(&self, channel: &AlertChannelConfig, alert: &AlertMessage) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let webhook_url = channel.config.get("webhook_url")
            .ok_or("Missing webhook_url for Slack channel")?;

        let color = match alert.severity {
            AlertSeverity::Critical => "#FF0000",
            AlertSeverity::Error => "#FF8C00",
            AlertSeverity::Warning => "#FFD700",
            AlertSeverity::Info => "#00FF00",
        };

        let payload = serde_json::json!({
            "attachments": [{
                "color": color,
                "title": alert.title,
                "text": alert.message,
                "fields": [
                    {
                        "title": "Error Code",
                        "value": alert.error_code.as_str(),
                        "short": true
                    },
                    {
                        "title": "Component",
                        "value": alert.context.component,
                        "short": true
                    },
                    {
                        "title": "Trace ID",
                        "value": alert.context.trace_id.to_string(),
                        "short": true
                    },
                    {
                        "title": "Recovery Suggestions",
                        "value": alert.recovery_suggestions.join("\n• "),
                        "short": false
                    }
                ],
                "ts": alert.timestamp.timestamp()
            }]
        });

        self.client
            .post(webhook_url)
            .json(&payload)
            .send()
            .await?;

        Ok(())
    }

    async fn send_discord_alert(&self, channel: &AlertChannelConfig, alert: &AlertMessage) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let webhook_url = channel.config.get("webhook_url")
            .ok_or("Missing webhook_url for Discord channel")?;

        let color = match alert.severity {
            AlertSeverity::Critical => 16711680, // Red
            AlertSeverity::Error => 16753920,   // Orange
            AlertSeverity::Warning => 16776960, // Yellow
            AlertSeverity::Info => 65280,       // Green
        };

        let payload = serde_json::json!({
            "embeds": [{
                "title": alert.title,
                "description": alert.message,
                "color": color,
                "timestamp": alert.timestamp.to_rfc3339(),
                "fields": [
                    {
                        "name": "Error Code",
                        "value": alert.error_code.as_str(),
                        "inline": true
                    },
                    {
                        "name": "Component",
                        "value": alert.context.component,
                        "inline": true
                    },
                    {
                        "name": "Trace ID",
                        "value": alert.context.trace_id.to_string(),
                        "inline": true
                    },
                    {
                        "name": "Recovery Suggestions",
                        "value": format!("• {}", alert.recovery_suggestions.join("\n• ")),
                        "inline": false
                    }
                ]
            }]
        });

        self.client
            .post(webhook_url)
            .json(&payload)
            .send()
            .await?;

        Ok(())
    }

    async fn send_email_alert(&self, _channel: &AlertChannelConfig, _alert: &AlertMessage) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Email implementation would go here
        // For now, just log that email would be sent
        info!("Email alert would be sent here");
        Ok(())
    }

    async fn send_pagerduty_alert(&self, _channel: &AlertChannelConfig, _alert: &AlertMessage) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // PagerDuty implementation would go here
        // For now, just log that PagerDuty alert would be sent
        info!("PagerDuty alert would be sent here");
        Ok(())
    }

    async fn send_webhook_alert(&self, channel: &AlertChannelConfig, alert: &AlertMessage) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let webhook_url = channel.config.get("url")
            .ok_or("Missing url for webhook channel")?;

        self.client
            .post(webhook_url)
            .json(alert)
            .send()
            .await?;

        Ok(())
    }

    async fn send_log_alert(&self, alert: &AlertMessage) {
        match alert.severity {
            AlertSeverity::Critical => {
                error!(
                    alert_id = %alert.alert_id,
                    error_code = %alert.error_code.as_str(),
                    component = %alert.context.component,
                    function = %alert.context.function,
                    trace_id = %alert.context.trace_id,
                    opportunity_id = ?alert.context.opportunity_id,
                    title = %alert.title,
                    message = %alert.message,
                    recovery_suggestions = ?alert.recovery_suggestions,
                    "CRITICAL ALERT"
                );
            }
            AlertSeverity::Error => {
                error!(
                    alert_id = %alert.alert_id,
                    error_code = %alert.error_code.as_str(),
                    component = %alert.context.component,
                    function = %alert.context.function,
                    trace_id = %alert.context.trace_id,
                    opportunity_id = ?alert.context.opportunity_id,
                    title = %alert.title,
                    message = %alert.message,
                    "ERROR ALERT"
                );
            }
            AlertSeverity::Warning => {
                warn!(
                    alert_id = %alert.alert_id,
                    error_code = %alert.error_code.as_str(),
                    component = %alert.context.component,
                    function = %alert.context.function,
                    trace_id = %alert.context.trace_id,
                    opportunity_id = ?alert.context.opportunity_id,
                    title = %alert.title,
                    message = %alert.message,
                    "WARNING ALERT"
                );
            }
            AlertSeverity::Info => {
                info!(
                    alert_id = %alert.alert_id,
                    error_code = %alert.error_code.as_str(),
                    component = %alert.context.component,
                    function = %alert.context.function,
                    trace_id = %alert.context.trace_id,
                    opportunity_id = ?alert.context.opportunity_id,
                    title = %alert.title,
                    message = %alert.message,
                    "INFO ALERT"
                );
            }
        }
    }
}

/// Global error handler that coordinates error propagation, retry logic, and alerting
pub struct ErrorHandler {
    retry_manager: RetryManager,
    alert_manager: Arc<AlertManager>,
}

impl ErrorHandler {
    pub fn new(alert_channels: Vec<AlertChannelConfig>) -> Self {
        Self {
            retry_manager: RetryManager::new(),
            alert_manager: Arc::new(AlertManager::new(alert_channels)),
        }
    }

    pub async fn handle_error(&self, error: BasiliskError, context: ErrorContext) -> EnhancedError {
        let enhanced_error = EnhancedError::new(error, context);
        
        // Send alert for critical and error severity issues
        if matches!(enhanced_error.severity, AlertSeverity::Critical | AlertSeverity::Error) {
            let alert = AlertMessage::from_enhanced_error(&enhanced_error);
            self.alert_manager.send_alert(alert).await;
        }

        enhanced_error
    }

    pub async fn execute_with_retry_and_alerting<F, T, E>(&self,
        operation: F,
        mut context: ErrorContext
    ) -> Result<T, EnhancedError>
    where
        F: FnMut() -> Result<T, E> + Send,
        E: Into<BasiliskError>,
    {
        // First, try to determine the error code for retry configuration
        // We'll use a default and adjust based on the actual error
        let mut operation = operation;
        let result = self.retry_manager.execute_with_retry(
            || operation(),
            ErrorCode::EUnrecoverableError, // Default, will be updated
            &mut context
        ).await;

        match result {
            Ok(value) => Ok(value),
            Err(enhanced_error) => {
                // Send alert for the final error
                let alert = AlertMessage::from_enhanced_error(&enhanced_error);
                self.alert_manager.send_alert(alert).await;
                Err(enhanced_error)
            }
        }
    }
}

/// Convenience macros for enhanced error handling
#[macro_export]
macro_rules! enhanced_error {
    ($error:expr, $component:expr, $function:expr) => {
        {
            let context = $crate::error::enhanced::ErrorContext::new($component, $function);
            $crate::error::enhanced::EnhancedError::new($error.into(), context)
        }
    };
    ($error:expr, $component:expr, $function:expr, $opportunity_id:expr) => {
        {
            let context = $crate::error::enhanced::ErrorContext::new($component, $function)
                .with_opportunity($opportunity_id);
            $crate::error::enhanced::EnhancedError::new($error.into(), context)
        }
    };
}

#[macro_export]
macro_rules! with_error_context {
    ($component:expr, $function:expr, $block:block) => {
        {
            let mut context = $crate::error::enhanced::ErrorContext::new($component, $function);
            $block
        }
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::error::NetworkError;

    #[test]
    fn test_error_context_propagation() {
        let mut context = ErrorContext::new("TestComponent", "test_function");
        let error = BasiliskError::Network(NetworkError::RpcTimeout {
            endpoint: "test".to_string(),
            timeout_ms: 5000,
        });
        
        context.propagate("TestComponent", "test_function", &error);
        
        assert_eq!(context.propagation_chain.len(), 1);
        assert_eq!(context.propagation_chain[0].component, "TestComponent");
    }

    #[test]
    fn test_retry_config() {
        let retry_manager = RetryManager::new();
        let config = retry_manager.get_config(&ErrorCode::ERpcTimeout);
        
        assert_eq!(config.max_attempts, 5);
        assert!(config.jitter_enabled);
    }

    #[test]
    fn test_enhanced_error_classification() {
        let context = ErrorContext::new("TestComponent", "test_function");
        let network_error = BasiliskError::Network(NetworkError::RpcTimeout {
            endpoint: "test".to_string(),
            timeout_ms: 5000,
        });
        
        let enhanced = EnhancedError::new(network_error, context);
        
        assert_eq!(enhanced.error_code, ErrorCode::ERpcTimeout);
        assert!(enhanced.is_retryable);
        assert!(!enhanced.recovery_suggestions.is_empty());
    }

    #[tokio::test]
    async fn test_alert_rate_limiting() {
        let mut rate_limiter = AlertRateLimiter::new();
        
        // First call should be allowed
        assert!(rate_limiter.should_send(&AlertChannelType::Slack, &ErrorCode::ERpcTimeout));
        
        // Second call immediately should be rate limited
        assert!(!rate_limiter.should_send(&AlertChannelType::Slack, &ErrorCode::ERpcTimeout));
    }

    #[test]
    fn test_alert_severity_threshold() {
        let alert_manager = AlertManager::new(vec![]);
        
        assert!(alert_manager.meets_severity_threshold(&AlertSeverity::Critical, &AlertSeverity::Warning));
        assert!(alert_manager.meets_severity_threshold(&AlertSeverity::Error, &AlertSeverity::Warning));
        assert!(!alert_manager.meets_severity_threshold(&AlertSeverity::Warning, &AlertSeverity::Error));
        assert!(!alert_manager.meets_severity_threshold(&AlertSeverity::Info, &AlertSeverity::Warning));
    }
}