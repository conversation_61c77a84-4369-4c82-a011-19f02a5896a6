// src/validation/tests/cross_chain_validator_test.rs

//! Tests for Cross-Chain Execution Validation Framework

#[cfg(test)]
mod tests {
    use crate::validation::{CrossChainValidator, CrossChainValidationConfig};

    #[test]
    fn test_cross_chain_validator_creation() {
        let config = CrossChainValidationConfig::default();
        let validator = CrossChainValidator::new(config);
        
        // Verify validator was created successfully
        let validator_config = validator.get_config();
        assert_eq!(validator_config.base_config.chain_id, 8453);
        assert_eq!(validator_config.degen_config.chain_id, 666666666);
        assert_eq!(validator_config.base_config.chain_name, "Base");
        assert_eq!(validator_config.degen_config.chain_name, "Degen Chain");
    }

    #[test]
    fn test_cross_chain_validation_config_defaults() {
        let config = CrossChainValidationConfig::default();
        
        // Test Base config
        assert_eq!(config.base_config.chain_id, 8453);
        assert_eq!(config.base_config.anvil_port, 8545);
        assert!(config.base_config.contract_addresses.stargate_compass.is_some());
        assert!(config.base_config.contract_addresses.aave_pool.is_some());
        
        // Test Degen config
        assert_eq!(config.degen_config.chain_id, 666666666);
        assert_eq!(config.degen_config.anvil_port, 8546);
        assert!(config.degen_config.contract_addresses.stargate_compass.is_none());
        assert!(config.degen_config.contract_addresses.aave_pool.is_none());
        
        // Test Stargate config
        assert!(config.stargate_config.layerzero_endpoints.contains_key(&8453));
        assert!(config.stargate_config.layerzero_endpoints.contains_key(&666666666));
        
        // Test arbitrage config
        assert_eq!(config.arbitrage_config.min_profit_margin, 0.5);
        assert!(!config.arbitrage_config.gas_price_scenarios.is_empty());
        assert!(!config.arbitrage_config.slippage_scenarios.is_empty());
        
        // Test bridge config
        assert_eq!(config.bridge_config.fee_prediction_tolerance, 10.0);
        assert_eq!(config.bridge_config.slippage_prediction_tolerance, 15.0);
        assert!(!config.bridge_config.test_amounts_usd.is_empty());
    }

    #[test]
    fn test_capital_management_config() {
        let config = CrossChainValidationConfig::default();
        
        // Base should have higher limits as the hub
        assert!(config.base_config.capital_config.max_flash_loan_usd > rust_decimal::Decimal::ZERO);
        assert!(config.base_config.capital_config.max_position_size_usd > config.degen_config.capital_config.max_position_size_usd);
        assert!(config.base_config.capital_config.capital_utilization_limit > config.degen_config.capital_config.capital_utilization_limit);
        
        // Degen should have no flash loans
        assert_eq!(config.degen_config.capital_config.max_flash_loan_usd, rust_decimal::Decimal::ZERO);
    }

    #[test]
    fn test_validation_metrics_defaults() {
        let metrics = crate::validation::CrossChainValidationMetrics::default();
        
        // All metrics should start at zero
        assert_eq!(metrics.hub_spoke_metrics.architecture_consistency_score, 0.0);
        assert_eq!(metrics.base_hub_metrics.capital_management_effectiveness, 0.0);
        assert_eq!(metrics.degen_execution_metrics.execution_performance_score, 0.0);
        assert_eq!(metrics.stargate_integration_metrics.bridge_success_rate, 0.0);
        assert_eq!(metrics.arbitrage_profitability_metrics.profitable_opportunities, 0);
        assert_eq!(metrics.bridge_prediction_metrics.fee_prediction_accuracy, 0.0);
    }
}