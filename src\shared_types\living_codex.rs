// MISSION: Living Codex - Comprehensive Trade Execution Intelligence
// WHY: Provide operators with deep insights into why trades were executed and how the bot's features contributed
// HOW: Rich data structures capturing market conditions, bot capabilities, and execution reasoning

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use uuid::Uuid;

use super::{
    GeometricScore, NetworkResonanceState, TemporalHarmonics, MarketRegime,
    AethericResonanceScoreDetail, OpportunityType
};

/// Comprehensive trade execution report for the Living Codex
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeExecutionReport {
    /// Unique identifiers
    pub trade_id: String,
    pub opportunity_id: String,
    pub trace_id: String,
    
    /// Timing information
    pub detected_at: DateTime<Utc>,
    pub executed_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub total_execution_time_ms: u64,
    
    /// Trade details
    pub opportunity_type: OpportunityType,
    pub strategy_used: String,
    pub execution_path: TradingPath,
    pub financial_outcome: FinancialOutcome,
    
    /// Market conditions at execution
    pub market_conditions: MarketConditionsSnapshot,
    
    /// Bot capabilities that enabled this trade
    pub bot_capabilities_used: BotCapabilitiesUsed,
    
    /// Aetheric Resonance Engine analysis
    pub are_analysis: AREAnalysisSnapshot,
    
    /// Execution details
    pub execution_details: ExecutionDetails,
    
    /// Educational insights
    pub why_executed: WhyExecutedAnalysis,
    pub how_executed: HowExecutedAnalysis,
    pub lessons_learned: Vec<String>,
    
    /// Risk management
    pub risk_assessment: RiskAssessmentSnapshot,
    
    /// Network and timing analysis
    pub network_analysis: NetworkTimingAnalysis,
}

/// Detailed trading path with cross-chain information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingPath {
    pub description: String,
    pub steps: Vec<TradingStep>,
    pub total_hops: u32,
    pub chains_involved: Vec<ChainInfo>,
    pub estimated_vs_actual_slippage: SlippageComparison,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingStep {
    pub step_number: u32,
    pub action: String, // "Flash Loan", "Swap", "Bridge", "Arbitrage"
    pub chain_id: u64,
    pub protocol: String, // "Aave", "Uniswap V3", "Stargate"
    pub input_token: String,
    pub output_token: String,
    pub amount_in: Decimal,
    pub amount_out: Decimal,
    pub gas_used: Option<u64>,
    pub execution_time_ms: u64,
    pub success: bool,
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChainInfo {
    pub chain_id: u64,
    pub name: String,
    pub role: String, // "Settlement", "Execution", "Bridge"
    pub gas_price_gwei: Decimal,
    pub network_congestion: String, // "Low", "Medium", "High"
    pub block_time_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlippageComparison {
    pub estimated_slippage_bps: u32,
    pub actual_slippage_bps: u32,
    pub slippage_accuracy: String, // "Excellent", "Good", "Poor"
    pub slippage_protection_triggered: bool,
}

/// Financial outcome with detailed breakdown
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FinancialOutcome {
    pub gross_profit_usd: Decimal,
    pub total_gas_cost_usd: Decimal,
    pub bridge_fees_usd: Decimal,
    pub protocol_fees_usd: Decimal,
    pub net_profit_usd: Decimal,
    pub profit_margin_percent: Decimal,
    pub roi_percent: Decimal,
    pub cost_breakdown: HashMap<String, Decimal>,
    pub profitability_tier: String, // "Excellent", "Good", "Marginal", "Loss"
}

/// Market conditions snapshot at execution time
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketConditionsSnapshot {
    pub market_regime: MarketRegime,
    pub volatility_24h_percent: Decimal,
    pub volume_24h_usd: Decimal,
    pub liquidity_depth: LiquidityAnalysis,
    pub price_impact_analysis: PriceImpactAnalysis,
    pub competing_arbitrageurs: u32,
    pub mev_competition_level: String, // "Low", "Medium", "High", "Extreme"
    pub gas_price_percentile: u32, // 0-100
    pub network_congestion_score: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityAnalysis {
    pub total_liquidity_usd: Decimal,
    pub liquidity_distribution: HashMap<String, Decimal>, // Protocol -> Liquidity
    pub depth_at_1_percent: Decimal,
    pub depth_at_5_percent: Decimal,
    pub liquidity_quality_score: Decimal, // 0-1
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceImpactAnalysis {
    pub estimated_price_impact_bps: u32,
    pub actual_price_impact_bps: u32,
    pub price_impact_accuracy: String,
    pub market_efficiency_score: Decimal, // 0-1
}

/// Bot capabilities that were utilized for this trade
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BotCapabilitiesUsed {
    pub aetheric_resonance_engine: ARECapabilities,
    pub cross_chain_execution: CrossChainCapabilities,
    pub mev_protection: MEVProtectionCapabilities,
    pub risk_management: RiskManagementCapabilities,
    pub timing_optimization: TimingOptimizationCapabilities,
    pub mathematical_analysis: MathematicalCapabilities,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ARECapabilities {
    pub chronos_sieve_used: bool,
    pub mandorla_gauge_used: bool,
    pub network_seismology_used: bool,
    pub temporal_analysis_accuracy: Option<String>,
    pub geometric_analysis_quality: Option<String>,
    pub network_timing_precision: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrossChainCapabilities {
    pub stargate_compass_used: bool,
    pub flash_loan_optimization: bool,
    pub multi_chain_coordination: bool,
    pub atomic_execution: bool,
    pub chain_selection_reasoning: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MEVProtectionCapabilities {
    pub private_mempool_used: bool,
    pub bundle_submission: bool,
    pub golden_ratio_bidding: bool,
    pub sandwich_protection: bool,
    pub frontrunning_detection: bool,
    pub protection_effectiveness: String, // "Excellent", "Good", "Partial", "None"
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskManagementCapabilities {
    pub kelly_criterion_sizing: bool,
    pub volatility_adjustment: bool,
    pub circuit_breaker_status: String,
    pub honeypot_detection: bool,
    pub position_size_optimization: bool,
    pub risk_score: Decimal, // 0-1
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimingOptimizationCapabilities {
    pub harmonic_timing_oracle: bool,
    pub network_propagation_analysis: bool,
    pub gas_price_optimization: bool,
    pub block_timing_prediction: bool,
    pub optimal_execution_window: bool,
    pub timing_accuracy_score: Decimal, // 0-1
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MathematicalCapabilities {
    pub sacred_geometry_analysis: bool,
    pub fractal_market_analysis: bool,
    pub fft_spectral_analysis: bool,
    pub vesica_piscis_calculation: bool,
    pub pagerank_centrality: bool,
    pub hurst_exponent_analysis: bool,
}

/// Aetheric Resonance Engine analysis snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AREAnalysisSnapshot {
    pub final_resonance_score: Decimal,
    pub score_breakdown: AethericScoreBreakdown,
    pub temporal_harmonics: Option<TemporalHarmonics>,
    pub geometric_score: Option<GeometricScore>,
    pub network_resonance: Option<NetworkResonanceState>,
    pub regime_multiplier: Decimal,
    pub confidence_level: String, // "Very High", "High", "Medium", "Low"
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AethericScoreBreakdown {
    pub base_score: Decimal,
    pub temporal_contribution: Decimal,
    pub geometric_contribution: Decimal,
    pub network_contribution: Decimal,
    pub regime_adjustment: Decimal,
    pub sigint_adjustment: Option<Decimal>,
    pub weight_distribution: HashMap<String, Decimal>,
}

/// Detailed execution information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionDetails {
    pub execution_strategy: String,
    pub transaction_hashes: Vec<String>,
    pub gas_strategy_used: String,
    pub nonce_management: NonceManagementDetails,
    pub broadcaster_selection: BroadcasterDetails,
    pub simulation_results: SimulationResults,
    pub circuit_breaker_status: String,
    pub emergency_procedures: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NonceManagementDetails {
    pub nonce_used: u64,
    pub nonce_conflicts: u32,
    pub nonce_recovery_used: bool,
    pub nonce_strategy: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BroadcasterDetails {
    pub broadcaster_type: String, // "Public", "Private", "Bundle"
    pub relay_used: Option<String>,
    pub broadcast_timing: String,
    pub inclusion_probability: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimulationResults {
    pub simulation_success: bool,
    pub simulated_profit: Decimal,
    pub actual_vs_simulated_variance: Decimal,
    pub simulation_accuracy: String,
    pub gas_estimation_accuracy: String,
}

/// Analysis of why the trade was executed
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhyExecutedAnalysis {
    pub primary_trigger: String,
    pub market_opportunity_explanation: String,
    pub profit_potential_analysis: String,
    pub risk_reward_justification: String,
    pub timing_rationale: String,
    pub competitive_advantage: String,
    pub regime_suitability: String,
    pub confidence_factors: Vec<String>,
    pub decision_tree_path: Vec<DecisionNode>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecisionNode {
    pub condition: String,
    pub result: bool,
    pub weight: Decimal,
    pub explanation: String,
}

/// Analysis of how the trade was executed
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HowExecutedAnalysis {
    pub execution_methodology: String,
    pub feature_utilization: Vec<FeatureUsage>,
    pub optimization_techniques: Vec<String>,
    pub risk_mitigation_measures: Vec<String>,
    pub performance_optimizations: Vec<String>,
    pub cross_chain_coordination: String,
    pub mev_protection_strategy: String,
    pub timing_optimization_strategy: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureUsage {
    pub feature_name: String,
    pub usage_type: String, // "Primary", "Secondary", "Fallback"
    pub contribution_to_success: String, // "Critical", "Important", "Helpful", "Minimal"
    pub performance_impact: String,
    pub explanation: String,
}

/// Risk assessment snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessmentSnapshot {
    pub overall_risk_score: Decimal, // 0-1
    pub risk_factors: Vec<RiskFactor>,
    pub mitigation_measures: Vec<String>,
    pub kelly_fraction_used: Decimal,
    pub position_size_reasoning: String,
    pub worst_case_scenario: String,
    pub risk_monitoring: RiskMonitoringStatus,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFactor {
    pub factor_type: String,
    pub severity: String, // "Low", "Medium", "High", "Critical"
    pub probability: Decimal,
    pub impact: String,
    pub mitigation: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMonitoringStatus {
    pub active_monitors: Vec<String>,
    pub alert_thresholds: HashMap<String, Decimal>,
    pub circuit_breaker_armed: bool,
    pub emergency_procedures_ready: bool,
}

/// Network timing analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkTimingAnalysis {
    pub p_wave_timing_ms: u64,
    pub s_wave_timing_ms: u64,
    pub sp_time_delta_ms: u64,
    pub network_coherence_score: Decimal,
    pub propagation_efficiency: String,
    pub timing_advantage: String,
    pub sequencer_performance: SequencerAnalysis,
    pub mev_timing_analysis: MEVTimingAnalysis,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SequencerAnalysis {
    pub sequencer_latency_ms: u64,
    pub sequencer_reliability: String,
    pub batch_inclusion_probability: Decimal,
    pub sequencer_mev_protection: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MEVTimingAnalysis {
    pub mev_opportunity_window_ms: u64,
    pub competition_detected: bool,
    pub timing_advantage_ms: i64, // Positive = advantage, negative = disadvantage
    pub execution_priority: String,
    pub bundle_position: Option<u32>,
}

impl TradeExecutionReport {
    /// Create a new trade execution report
    pub fn new(
        trade_id: String,
        opportunity_id: String,
        opportunity_type: OpportunityType,
        strategy_used: String,
    ) -> Self {
        Self {
            trade_id,
            opportunity_id,
            trace_id: Uuid::new_v4().to_string(),
            detected_at: Utc::now(),
            executed_at: Utc::now(),
            completed_at: None,
            total_execution_time_ms: 0,
            opportunity_type,
            strategy_used,
            execution_path: TradingPath::default(),
            financial_outcome: FinancialOutcome::default(),
            market_conditions: MarketConditionsSnapshot::default(),
            bot_capabilities_used: BotCapabilitiesUsed::default(),
            are_analysis: AREAnalysisSnapshot::default(),
            execution_details: ExecutionDetails::default(),
            why_executed: WhyExecutedAnalysis::default(),
            how_executed: HowExecutedAnalysis::default(),
            lessons_learned: Vec::new(),
            risk_assessment: RiskAssessmentSnapshot::default(),
            network_analysis: NetworkTimingAnalysis::default(),
        }
    }

    /// Generate a human-readable summary for the TUI
    pub fn generate_summary(&self) -> String {
        format!(
            "Trade {} executed via {} strategy. Net profit: ${:.2} ({:.1}% margin). \
            ARE Score: {:.3}. Execution time: {}ms. Risk level: {}. \
            Primary trigger: {}",
            &self.trade_id[..8],
            self.strategy_used,
            self.financial_outcome.net_profit_usd,
            self.financial_outcome.profit_margin_percent,
            self.are_analysis.final_resonance_score,
            self.total_execution_time_ms,
            self.risk_assessment.overall_risk_score,
            self.why_executed.primary_trigger
        )
    }

    /// Generate detailed explanation for debugging
    pub fn generate_debug_explanation(&self) -> String {
        format!(
            "TRADE EXECUTION ANALYSIS\n\
            ========================\n\
            Trade ID: {}\n\
            Opportunity: {}\n\
            Strategy: {}\n\
            \n\
            FINANCIAL OUTCOME:\n\
            - Gross Profit: ${:.2}\n\
            - Total Costs: ${:.2}\n\
            - Net Profit: ${:.2}\n\
            - ROI: {:.2}%\n\
            \n\
            WHY EXECUTED:\n\
            - Primary Trigger: {}\n\
            - Market Opportunity: {}\n\
            - Timing Rationale: {}\n\
            \n\
            HOW EXECUTED:\n\
            - Execution Method: {}\n\
            - Cross-chain Coordination: {}\n\
            - MEV Protection: {}\n\
            \n\
            AETHERIC RESONANCE ENGINE:\n\
            - Final Score: {:.3}\n\
            - Confidence: {}\n\
            - Temporal Contribution: {:.3}\n\
            - Geometric Contribution: {:.3}\n\
            - Network Contribution: {:.3}\n\
            \n\
            RISK ASSESSMENT:\n\
            - Overall Risk Score: {:.3}\n\
            - Kelly Fraction Used: {:.3}\n\
            - Position Size Reasoning: {}\n\
            \n\
            NETWORK TIMING:\n\
            - S-P Time Delta: {}ms\n\
            - Network Coherence: {:.3}\n\
            - Timing Advantage: {}\n\
            \n\
            LESSONS LEARNED:\n\
            {}",
            self.trade_id,
            self.opportunity_id,
            self.strategy_used,
            self.financial_outcome.gross_profit_usd,
            self.financial_outcome.gross_profit_usd - self.financial_outcome.net_profit_usd,
            self.financial_outcome.net_profit_usd,
            self.financial_outcome.roi_percent,
            self.why_executed.primary_trigger,
            self.why_executed.market_opportunity_explanation,
            self.why_executed.timing_rationale,
            self.how_executed.execution_methodology,
            self.how_executed.cross_chain_coordination,
            self.how_executed.mev_protection_strategy,
            self.are_analysis.final_resonance_score,
            self.are_analysis.confidence_level,
            self.are_analysis.score_breakdown.temporal_contribution,
            self.are_analysis.score_breakdown.geometric_contribution,
            self.are_analysis.score_breakdown.network_contribution,
            self.risk_assessment.overall_risk_score,
            self.risk_assessment.kelly_fraction_used,
            self.risk_assessment.position_size_reasoning,
            self.network_analysis.sp_time_delta_ms,
            self.network_analysis.network_coherence_score,
            self.network_analysis.timing_advantage,
            self.lessons_learned.join("\n- ")
        )
    }
}

// Default implementations for all the structs
impl Default for TradingPath {
    fn default() -> Self {
        Self {
            description: "Unknown trading path".to_string(),
            steps: Vec::new(),
            total_hops: 0,
            chains_involved: Vec::new(),
            estimated_vs_actual_slippage: SlippageComparison::default(),
        }
    }
}

impl Default for SlippageComparison {
    fn default() -> Self {
        Self {
            estimated_slippage_bps: 0,
            actual_slippage_bps: 0,
            slippage_accuracy: "Unknown".to_string(),
            slippage_protection_triggered: false,
        }
    }
}

impl Default for FinancialOutcome {
    fn default() -> Self {
        Self {
            gross_profit_usd: Decimal::ZERO,
            total_gas_cost_usd: Decimal::ZERO,
            bridge_fees_usd: Decimal::ZERO,
            protocol_fees_usd: Decimal::ZERO,
            net_profit_usd: Decimal::ZERO,
            profit_margin_percent: Decimal::ZERO,
            roi_percent: Decimal::ZERO,
            cost_breakdown: HashMap::new(),
            profitability_tier: "Unknown".to_string(),
        }
    }
}

impl Default for MarketConditionsSnapshot {
    fn default() -> Self {
        Self {
            market_regime: MarketRegime::CalmOrderly,
            volatility_24h_percent: Decimal::ZERO,
            volume_24h_usd: Decimal::ZERO,
            liquidity_depth: LiquidityAnalysis::default(),
            price_impact_analysis: PriceImpactAnalysis::default(),
            competing_arbitrageurs: 0,
            mev_competition_level: "Unknown".to_string(),
            gas_price_percentile: 50,
            network_congestion_score: Decimal::ZERO,
        }
    }
}

impl Default for LiquidityAnalysis {
    fn default() -> Self {
        Self {
            total_liquidity_usd: Decimal::ZERO,
            liquidity_distribution: HashMap::new(),
            depth_at_1_percent: Decimal::ZERO,
            depth_at_5_percent: Decimal::ZERO,
            liquidity_quality_score: Decimal::ZERO,
        }
    }
}

impl Default for PriceImpactAnalysis {
    fn default() -> Self {
        Self {
            estimated_price_impact_bps: 0,
            actual_price_impact_bps: 0,
            price_impact_accuracy: "Unknown".to_string(),
            market_efficiency_score: Decimal::ZERO,
        }
    }
}

impl Default for BotCapabilitiesUsed {
    fn default() -> Self {
        Self {
            aetheric_resonance_engine: ARECapabilities::default(),
            cross_chain_execution: CrossChainCapabilities::default(),
            mev_protection: MEVProtectionCapabilities::default(),
            risk_management: RiskManagementCapabilities::default(),
            timing_optimization: TimingOptimizationCapabilities::default(),
            mathematical_analysis: MathematicalCapabilities::default(),
        }
    }
}

impl Default for ARECapabilities {
    fn default() -> Self {
        Self {
            chronos_sieve_used: false,
            mandorla_gauge_used: false,
            network_seismology_used: false,
            temporal_analysis_accuracy: None,
            geometric_analysis_quality: None,
            network_timing_precision: None,
        }
    }
}

impl Default for CrossChainCapabilities {
    fn default() -> Self {
        Self {
            stargate_compass_used: false,
            flash_loan_optimization: false,
            multi_chain_coordination: false,
            atomic_execution: false,
            chain_selection_reasoning: "Unknown".to_string(),
        }
    }
}

impl Default for MEVProtectionCapabilities {
    fn default() -> Self {
        Self {
            private_mempool_used: false,
            bundle_submission: false,
            golden_ratio_bidding: false,
            sandwich_protection: false,
            frontrunning_detection: false,
            protection_effectiveness: "Unknown".to_string(),
        }
    }
}

impl Default for RiskManagementCapabilities {
    fn default() -> Self {
        Self {
            kelly_criterion_sizing: false,
            volatility_adjustment: false,
            circuit_breaker_status: "Unknown".to_string(),
            honeypot_detection: false,
            position_size_optimization: false,
            risk_score: Decimal::ZERO,
        }
    }
}

impl Default for TimingOptimizationCapabilities {
    fn default() -> Self {
        Self {
            harmonic_timing_oracle: false,
            network_propagation_analysis: false,
            gas_price_optimization: false,
            block_timing_prediction: false,
            optimal_execution_window: false,
            timing_accuracy_score: Decimal::ZERO,
        }
    }
}

impl Default for MathematicalCapabilities {
    fn default() -> Self {
        Self {
            sacred_geometry_analysis: false,
            fractal_market_analysis: false,
            fft_spectral_analysis: false,
            vesica_piscis_calculation: false,
            pagerank_centrality: false,
            hurst_exponent_analysis: false,
        }
    }
}

impl Default for AREAnalysisSnapshot {
    fn default() -> Self {
        Self {
            final_resonance_score: Decimal::ZERO,
            score_breakdown: AethericScoreBreakdown::default(),
            temporal_harmonics: None,
            geometric_score: None,
            network_resonance: None,
            regime_multiplier: Decimal::ONE,
            confidence_level: "Unknown".to_string(),
        }
    }
}

impl Default for AethericScoreBreakdown {
    fn default() -> Self {
        Self {
            base_score: Decimal::ZERO,
            temporal_contribution: Decimal::ZERO,
            geometric_contribution: Decimal::ZERO,
            network_contribution: Decimal::ZERO,
            regime_adjustment: Decimal::ZERO,
            sigint_adjustment: None,
            weight_distribution: HashMap::new(),
        }
    }
}

impl Default for ExecutionDetails {
    fn default() -> Self {
        Self {
            execution_strategy: "Unknown".to_string(),
            transaction_hashes: Vec::new(),
            gas_strategy_used: "Unknown".to_string(),
            nonce_management: NonceManagementDetails::default(),
            broadcaster_selection: BroadcasterDetails::default(),
            simulation_results: SimulationResults::default(),
            circuit_breaker_status: "Unknown".to_string(),
            emergency_procedures: Vec::new(),
        }
    }
}

impl Default for NonceManagementDetails {
    fn default() -> Self {
        Self {
            nonce_used: 0,
            nonce_conflicts: 0,
            nonce_recovery_used: false,
            nonce_strategy: "Unknown".to_string(),
        }
    }
}

impl Default for BroadcasterDetails {
    fn default() -> Self {
        Self {
            broadcaster_type: "Unknown".to_string(),
            relay_used: None,
            broadcast_timing: "Unknown".to_string(),
            inclusion_probability: Decimal::ZERO,
        }
    }
}

impl Default for SimulationResults {
    fn default() -> Self {
        Self {
            simulation_success: false,
            simulated_profit: Decimal::ZERO,
            actual_vs_simulated_variance: Decimal::ZERO,
            simulation_accuracy: "Unknown".to_string(),
            gas_estimation_accuracy: "Unknown".to_string(),
        }
    }
}

impl Default for WhyExecutedAnalysis {
    fn default() -> Self {
        Self {
            primary_trigger: "Unknown".to_string(),
            market_opportunity_explanation: "Unknown".to_string(),
            profit_potential_analysis: "Unknown".to_string(),
            risk_reward_justification: "Unknown".to_string(),
            timing_rationale: "Unknown".to_string(),
            competitive_advantage: "Unknown".to_string(),
            regime_suitability: "Unknown".to_string(),
            confidence_factors: Vec::new(),
            decision_tree_path: Vec::new(),
        }
    }
}

impl Default for HowExecutedAnalysis {
    fn default() -> Self {
        Self {
            execution_methodology: "Unknown".to_string(),
            feature_utilization: Vec::new(),
            optimization_techniques: Vec::new(),
            risk_mitigation_measures: Vec::new(),
            performance_optimizations: Vec::new(),
            cross_chain_coordination: "Unknown".to_string(),
            mev_protection_strategy: "Unknown".to_string(),
            timing_optimization_strategy: "Unknown".to_string(),
        }
    }
}

impl Default for RiskAssessmentSnapshot {
    fn default() -> Self {
        Self {
            overall_risk_score: Decimal::ZERO,
            risk_factors: Vec::new(),
            mitigation_measures: Vec::new(),
            kelly_fraction_used: Decimal::ZERO,
            position_size_reasoning: "Unknown".to_string(),
            worst_case_scenario: "Unknown".to_string(),
            risk_monitoring: RiskMonitoringStatus::default(),
        }
    }
}

impl Default for RiskMonitoringStatus {
    fn default() -> Self {
        Self {
            active_monitors: Vec::new(),
            alert_thresholds: HashMap::new(),
            circuit_breaker_armed: false,
            emergency_procedures_ready: false,
        }
    }
}

impl Default for NetworkTimingAnalysis {
    fn default() -> Self {
        Self {
            p_wave_timing_ms: 0,
            s_wave_timing_ms: 0,
            sp_time_delta_ms: 0,
            network_coherence_score: Decimal::ZERO,
            propagation_efficiency: "Unknown".to_string(),
            timing_advantage: "Unknown".to_string(),
            sequencer_performance: SequencerAnalysis::default(),
            mev_timing_analysis: MEVTimingAnalysis::default(),
        }
    }
}

impl Default for SequencerAnalysis {
    fn default() -> Self {
        Self {
            sequencer_latency_ms: 0,
            sequencer_reliability: "Unknown".to_string(),
            batch_inclusion_probability: Decimal::ZERO,
            sequencer_mev_protection: "Unknown".to_string(),
        }
    }
}

impl Default for MEVTimingAnalysis {
    fn default() -> Self {
        Self {
            mev_opportunity_window_ms: 0,
            competition_detected: false,
            timing_advantage_ms: 0,
            execution_priority: "Unknown".to_string(),
            bundle_position: None,
        }
    }
}