//! AUDIT-FIX: Edge case and stress testing - Task 6.2
//! This module provides comprehensive edge case testing for all components

use std::time::Duration;
use tokio::time::timeout;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use anyhow::Result;

/// Edge case test suite for mathematical components
#[cfg(test)]
mod math_edge_cases {
    use super::*;
    use basilisk_bot::math::vesica;
    use basilisk_bot::data::fractal_analyzer::FractalAnalyzer;

    /// Test Vesica Piscis with extreme input values
    #[test]
    fn test_vesica_extreme_values() {
        // Test with very large reserves
        let result_large = vesica::calculate_amount_to_equalize(
            dec!(1e18), // 1 quintillion
            dec!(1e6),  // 1 million
            dec!(0.1),
        );
        assert!(result_large.is_finite(), "Should handle large values without overflow");
        assert!(result_large >= dec!(0.0), "Should always return non-negative result");

        // Test with very small reserves
        let result_small = vesica::calculate_amount_to_equalize(
            dec!(0.000001), // 1 micro unit
            dec!(0.000002), // 2 micro units
            dec!(0.5),
        );
        assert!(result_small.is_finite(), "Should handle small values without underflow");

        // Test with zero reserves (edge case)
        let result_zero_a = vesica::calculate_amount_to_equalize(
            dec!(0.0),
            dec!(1000.0),
            dec!(0.1),
        );
        assert!(result_zero_a.is_finite(), "Should handle zero pool A reserves");

        let result_zero_b = vesica::calculate_amount_to_equalize(
            dec!(1000.0),
            dec!(0.0),
            dec!(0.1),
        );
        assert!(result_zero_b.is_finite(), "Should handle zero pool B reserves");

        // Test with extreme deviation values
        let result_extreme_deviation = vesica::calculate_amount_to_equalize(
            dec!(1000.0),
            dec!(2000.0),
            dec!(10.0), // 1000% deviation
        );
        assert!(result_extreme_deviation.is_finite(), "Should handle extreme deviations");
    }

    /// Test Vesica Piscis numerical stability
    #[test]
    fn test_vesica_numerical_stability() {
        let base_reserves_a = dec!(1000.0);
        let base_reserves_b = dec!(1500.0);
        let base_deviation = dec!(0.1);

        // Test with slight variations to ensure stability
        for i in 0..100 {
            let variation = dec!(0.0001) * Decimal::from(i);
            
            let result1 = vesica::calculate_amount_to_equalize(
                base_reserves_a + variation,
                base_reserves_b,
                base_deviation,
            );
            
            let result2 = vesica::calculate_amount_to_equalize(
                base_reserves_a,
                base_reserves_b + variation,
                base_deviation,
            );
            
            assert!(result1.is_finite(), "Result should be finite with variation {}", i);
            assert!(result2.is_finite(), "Result should be finite with variation {}", i);
            
            // Results should be continuous (small input changes = small output changes)
            let base_result = vesica::calculate_amount_to_equalize(
                base_reserves_a,
                base_reserves_b,
                base_deviation,
            );
            
            let diff1 = (result1 - base_result).abs();
            let diff2 = (result2 - base_result).abs();
            
            assert!(diff1 < dec!(10.0), "Small input change should cause small output change");
            assert!(diff2 < dec!(10.0), "Small input change should cause small output change");
        }
    }

    /// Test FFT with problematic input sizes
    #[tokio::test]
    async fn test_fft_edge_cases() {
        let analyzer = FractalAnalyzer::new();

        // Test with prime number sizes (not power of 2)
        let prime_sizes = vec![7, 11, 13, 17, 19, 23];
        for size in prime_sizes {
            let test_data: Vec<f64> = (0..size).map(|i| (i as f64 * 0.1).sin()).collect();
            
            let result = analyzer.calculate_temporal_harmonics(&test_data).await;
            match result {
                Ok(harmonics) => {
                    assert!(harmonics.market_rhythm_stability >= 0.0 && harmonics.market_rhythm_stability <= 1.0,
                           "Market rhythm stability should be in [0,1] range for size {}", size);
                },
                Err(_) => {
                    // Some FFT implementations may not handle non-power-of-2 sizes
                    // This is acceptable as long as it fails gracefully
                }
            }
        }

        // Test with very large dataset
        let large_size = 65536; // 2^16
        let large_data: Vec<f64> = (0..large_size).map(|i| (i as f64 * 0.001).sin()).collect();
        
        let result = timeout(Duration::from_secs(5), analyzer.calculate_temporal_harmonics(&large_data)).await;
        assert!(result.is_ok(), "Large FFT should complete within 5 seconds");

        // Test with constant data (no variation)
        let constant_data = vec![1.0; 100];
        let result = analyzer.calculate_temporal_harmonics(&constant_data).await;
        assert!(result.is_ok(), "Should handle constant data gracefully");

        // Test with NaN data
        let nan_data = vec![f64::NAN; 10];
        let result = analyzer.calculate_temporal_harmonics(&nan_data).await;
        assert!(result.is_err(), "Should reject NaN data");

        // Test with infinite data
        let inf_data = vec![f64::INFINITY; 10];
        let result = analyzer.calculate_temporal_harmonics(&inf_data).await;
        assert!(result.is_err(), "Should reject infinite data");
    }

    /// Test mathematical symmetry properties
    #[test]
    fn test_vesica_mathematical_properties() {
        // Test symmetry: f(a,b,d) should relate to f(b,a,-d)
        let pool_a = dec!(1000.0);
        let pool_b = dec!(1500.0);
        let deviation = dec!(0.2);

        let forward = vesica::calculate_amount_to_equalize(pool_a, pool_b, deviation);
        let reverse = vesica::calculate_amount_to_equalize(pool_b, pool_a, -deviation);

        // The results should be related (though not necessarily equal due to the fix)
        assert!(forward >= dec!(0.0), "Forward calculation should be non-negative");
        assert!(reverse >= dec!(0.0), "Reverse calculation should be non-negative");

        // Test idempotency: f(a,a,0) = 0
        let zero_result = vesica::calculate_amount_to_equalize(pool_a, pool_a, dec!(0.0));
        assert_eq!(zero_result, dec!(0.0), "Equal pools with zero deviation should yield zero");

        // Test monotonicity: larger deviation should generally yield larger result
        let small_deviation = vesica::calculate_amount_to_equalize(pool_a, pool_b, dec!(0.1));
        let large_deviation = vesica::calculate_amount_to_equalize(pool_a, pool_b, dec!(0.3));
        
        assert!(large_deviation >= small_deviation, "Larger deviation should yield larger or equal result");
    }
}

/// Edge case test suite for execution components
#[cfg(test)]
mod execution_edge_cases {
    use super::*;
    use basilisk_bot::execution::{NonceManager, GasEstimator, CircuitBreaker};
    use basilisk_bot::shared_types::GasUrgency;
    use ethers::types::{Address, U256, H256};

    /// Test nonce manager with rapid concurrent requests
    #[tokio::test]
    async fn test_nonce_manager_concurrency() {
        let nonce_manager = NonceManager::new(Address::zero(), 1).await.unwrap();
        
        // Spawn multiple concurrent nonce requests
        let mut handles = Vec::new();
        for _ in 0..100 {
            let nm = nonce_manager.clone();
            let handle = tokio::spawn(async move {
                nm.get_next_nonce().await
            });
            handles.push(handle);
        }

        // Collect all results
        let mut nonces = Vec::new();
        for handle in handles {
            match handle.await {
                Ok(Ok(nonce)) => nonces.push(nonce),
                _ => panic!("Nonce request should not fail"),
            }
        }

        // All nonces should be unique
        nonces.sort();
        for i in 1..nonces.len() {
            assert_ne!(nonces[i], nonces[i-1], "All nonces should be unique");
        }
    }

    /// Test gas estimator with extreme network conditions
    #[tokio::test]
    async fn test_gas_estimator_extreme_conditions() {
        let gas_estimator = GasEstimator::new(
            ethers::providers::Provider::try_from("https://mainnet.base.org").unwrap(),
            1,
        );

        // Test all urgency levels
        let urgencies = vec![
            GasUrgency::Low,
            GasUrgency::Standard,
            GasUrgency::High,
            GasUrgency::Emergency,
        ];

        for urgency in urgencies {
            let result = timeout(
                Duration::from_secs(10),
                gas_estimator.estimate_gas_params(urgency)
            ).await;

            assert!(result.is_ok(), "Gas estimation should complete within timeout for {:?}", urgency);
            
            if let Ok(Ok(params)) = result {
                assert!(params.gas_price > U256::zero(), "Gas price should be positive");
                assert!(params.gas_limit > U256::zero(), "Gas limit should be positive");
                
                // Sanity bounds
                assert!(params.gas_price < U256::from(1000_000_000_000u64), "Gas price should be reasonable (< 1000 gwei)");
                assert!(params.gas_limit < U256::from(10_000_000u64), "Gas limit should be reasonable (< 10M)");
            }
        }
    }

    /// Test circuit breaker under rapid failure conditions
    #[tokio::test]
    async fn test_circuit_breaker_rapid_failures() {
        let circuit_breaker = CircuitBreaker::new(
            10, // max_failures
            Duration::from_millis(100), // short reset timeout for testing
        );

        // Rapid failure injection
        for i in 0..20 {
            circuit_breaker.record_failure().await;
            
            if i < 9 {
                assert!(!circuit_breaker.is_open().await, "Circuit should remain closed at {} failures", i + 1);
            } else {
                assert!(circuit_breaker.is_open().await, "Circuit should be open after {} failures", i + 1);
            }
        }

        // Test recovery after timeout
        tokio::time::sleep(Duration::from_millis(150)).await;
        
        // Record a success to potentially close the circuit
        circuit_breaker.record_success().await;
        
        // Circuit behavior after timeout is implementation-dependent
        // but it should not crash
    }

    /// Test transaction replacement scenarios
    #[tokio::test]
    async fn test_transaction_replacement_edge_cases() {
        let nonce_manager = NonceManager::new(Address::zero(), 1).await.unwrap();
        
        let initial_nonce = nonce_manager.get_next_nonce().await.unwrap();
        let tx_hash = H256::random();
        
        // Register a pending transaction
        nonce_manager.register_pending_transaction(
            initial_nonce,
            tx_hash,
            U256::from(20_000_000_000u64), // 20 gwei
            None,
            None,
            ethers::types::TransactionRequest::default(),
        );

        // Test multiple replacements of the same nonce
        for i in 1..5 {
            let replacement_hash = H256::random();
            let higher_gas_price = U256::from((20 + i * 5) * 1_000_000_000u64); // Increasing gas price
            
            let result = nonce_manager.replace_transaction(
                initial_nonce,
                replacement_hash,
                higher_gas_price,
            ).await;
            
            assert!(result.is_ok(), "Transaction replacement {} should succeed", i);
        }

        // Test replacement with lower gas price (should fail)
        let low_gas_replacement = H256::random();
        let low_gas_price = U256::from(15_000_000_000u64); // 15 gwei (lower than original)
        
        let result = nonce_manager.replace_transaction(
            initial_nonce,
            low_gas_replacement,
            low_gas_price,
        ).await;
        
        assert!(result.is_err(), "Replacement with lower gas price should fail");
    }
}

/// Edge case test suite for risk management components
#[cfg(test)]
mod risk_management_edge_cases {
    use super::*;
    use basilisk_bot::risk::manager::RiskManager;
    use basilisk_bot::risk::kelly::KellyCriterion;
    use basilisk_bot::config::RiskConfig;
    use basilisk_bot::shared_types::RunMode;

    /// Test Kelly Criterion with extreme scenarios
    #[test]
    fn test_kelly_criterion_extreme_scenarios() {
        let kelly = KellyCriterion::new(dec!(0.25)); // 25% cap

        // Test with 100% win rate and zero loss
        let perfect_scenario = kelly.calculate_optimal_fraction(
            dec!(1.0), // 100% win rate
            dec!(100.0), // avg win
            dec!(0.0), // zero loss
        );
        assert_eq!(perfect_scenario, dec!(0.25), "Perfect scenario should hit the cap");

        // Test with 0% win rate
        let zero_win_rate = kelly.calculate_optimal_fraction(
            dec!(0.0), // 0% win rate
            dec!(100.0), // avg win
            dec!(50.0), // avg loss
        );
        assert_eq!(zero_win_rate, dec!(0.0), "Zero win rate should give zero fraction");

        // Test with very high win rate but small edge
        let high_win_small_edge = kelly.calculate_optimal_fraction(
            dec!(0.99), // 99% win rate
            dec!(1.01), // tiny win
            dec!(100.0), // large loss
        );
        assert!(high_win_small_edge <= dec!(0.0), "High win rate with negative expectation should give zero fraction");

        // Test with extreme values that could cause overflow
        let extreme_values = kelly.calculate_optimal_fraction(
            dec!(0.6),
            dec!(1e10), // Very large win
            dec!(1e9), // Very large loss
        );
        assert!(extreme_values.is_finite(), "Should handle extreme values without overflow");
        assert!(extreme_values <= dec!(0.25), "Should respect the cap even with extreme values");
    }

    /// Test risk manager under extreme market conditions
    #[tokio::test]
    async fn test_risk_manager_extreme_conditions() {
        let risk_config = RiskConfig {
            max_position_size_usd: dec!(1000.0),
            max_daily_loss: dec!(500.0),
            max_consecutive_failures: 3,
            kelly_fraction_cap: dec!(0.25),
            volatility_lookback_periods: 20,
            regime_risk_multipliers: std::collections::HashMap::new(),
        };

        let nats_client = basilisk_bot::nats::NatsClient::new("nats://localhost:4222").await.unwrap();
        let risk_manager = RiskManager::new(
            nats_client,
            risk_config,
            RunMode::Live,
        ).await.unwrap();

        // Test rapid consecutive failures
        for i in 0..10 {
            risk_manager.record_trade_failure().await;
            
            if i >= 2 { // After 3 failures (0, 1, 2)
                assert!(risk_manager.is_trading_halted().await, "Should halt after {} failures", i + 1);
            }
        }

        // Reset and test rapid PnL updates
        risk_manager.reset_circuit_breaker().await;
        
        // Simulate rapid loss accumulation
        for i in 0..100 {
            let loss = dec!(-10.0); // $10 loss per trade
            risk_manager.update_pnl(loss, &format!("rapid_trade_{}", i)).await;
            
            // Should halt when daily loss exceeds limit
            if (i + 1) * 10 >= 500 { // $500 limit
                assert!(risk_manager.is_trading_halted().await, "Should halt when daily loss exceeds limit");
                break;
            }
        }
    }

    /// Test risk manager with concurrent operations
    #[tokio::test]
    async fn test_risk_manager_concurrency() {
        let risk_config = RiskConfig {
            max_position_size_usd: dec!(1000.0),
            max_daily_loss: dec!(1000.0),
            max_consecutive_failures: 10,
            kelly_fraction_cap: dec!(0.25),
            volatility_lookback_periods: 20,
            regime_risk_multipliers: std::collections::HashMap::new(),
        };

        let nats_client = basilisk_bot::nats::NatsClient::new("nats://localhost:4222").await.unwrap();
        let risk_manager = RiskManager::new(
            nats_client,
            risk_config,
            RunMode::Live,
        ).await.unwrap();

        // Spawn multiple concurrent operations
        let mut handles = Vec::new();
        
        // Concurrent trade acceptance checks
        for i in 0..50 {
            let rm = risk_manager.clone();
            let handle = tokio::spawn(async move {
                rm.is_trade_acceptable(dec!(100.0)).await
            });
            handles.push(handle);
        }

        // Concurrent PnL updates
        for i in 0..50 {
            let rm = risk_manager.clone();
            let handle = tokio::spawn(async move {
                rm.update_pnl(dec!(10.0), &format!("concurrent_trade_{}", i)).await;
            });
            handles.push(handle);
        }

        // Wait for all operations to complete
        for handle in handles {
            assert!(handle.await.is_ok(), "Concurrent operation should not panic");
        }

        // System should still be functional
        assert!(risk_manager.is_trade_acceptable(dec!(100.0)).await, "System should remain functional after concurrent operations");
    }
}
