// src/deployment/deployment_manager.rs
// Deployment manager for phased rollout with validation checkpoints

use super::*;
use crate::deployment::feature_flags::FeatureFlagManager;
use crate::deployment::health_checks::HealthCheckManager;
use crate::deployment::rollback_manager::RollbackManager;
use crate::deployment::validation::ValidationManager;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// Deployment manager orchestrates phased deployment with validation and rollback
pub struct DeploymentManager {
    config: Arc<RwLock<DeploymentConfig>>,
    feature_flags: Arc<FeatureFlagManager>,
    health_checks: Arc<HealthCheckManager>,
    rollback_manager: Arc<RollbackManager>,
    validation_manager: Arc<ValidationManager>,
    deployment_history: Arc<RwLock<Vec<DeploymentRecord>>>,
}

impl DeploymentManager {
    /// Create a new deployment manager
    pub fn new(config: DeploymentConfig) -> Self {
        let feature_flags = Arc::new(FeatureFlagManager::new());
        let health_checks = Arc::new(HealthCheckManager::new(config.health_checks.clone()));
        let rollback_manager = Arc::new(RollbackManager::new(config.rollback_config.clone()));
        let validation_manager = Arc::new(ValidationManager::new());
        
        Self {
            config: Arc::new(RwLock::new(config)),
            feature_flags,
            health_checks,
            rollback_manager,
            validation_manager,
            deployment_history: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    /// Deploy to the next phase with validation checkpoints
    pub async fn deploy_next_phase(&self) -> Result<DeploymentResult> {
        let current_phase = {
            let config = self.config.read().await;
            config.current_phase
        };
        
        let next_phase = match current_phase.next_phase() {
            Some(phase) => phase,
            None => {
                info!("Already at final deployment phase: {:?}", current_phase);
                return Ok(DeploymentResult {
                    success: true,
                    phase: current_phase,
                    validation_results: HashMap::new(),
                    error_message: None,
                    timestamp: chrono::Utc::now(),
                });
            }
        };
        
        info!("Starting deployment to phase: {:?}", next_phase);
        
        let deployment_start = Instant::now();
        let mut deployment_record = DeploymentRecord {
            phase: next_phase,
            start_time: chrono::Utc::now(),
            end_time: None,
            success: false,
            validation_results: HashMap::new(),
            error_message: None,
            rollback_triggered: false,
        };
        
        // Step 1: Run validation checkpoints
        info!("Running validation checkpoints for phase: {:?}", next_phase);
        let validation_results = self.run_validation_checkpoints(next_phase).await?;
        deployment_record.validation_results = validation_results.clone();
        
        // Check if all validations passed
        let all_validations_passed = validation_results.values().all(|&passed| passed);
        if !all_validations_passed {
            let failed_validations: Vec<_> = validation_results.iter()
                .filter(|(_, &passed)| !passed)
                .map(|(checkpoint, _)| format!("{:?}", checkpoint))
                .collect();
            
            let error_msg = format!("Validation checkpoints failed: {}", failed_validations.join(", "));
            error!("{}", error_msg);
            
            deployment_record.success = false;
            deployment_record.error_message = Some(error_msg.clone());
            deployment_record.end_time = Some(chrono::Utc::now());
            
            self.deployment_history.write().await.push(deployment_record);
            
            return Ok(DeploymentResult {
                success: false,
                phase: next_phase,
                validation_results,
                error_message: Some(error_msg),
                timestamp: chrono::Utc::now(),
            });
        }
        
        info!("All validation checkpoints passed for phase: {:?}", next_phase);
        
        // Step 2: Enable feature flags for the phase
        info!("Enabling feature flags for phase: {:?}", next_phase);
        if let Err(e) = self.feature_flags.enable_phase(next_phase) {
            let error_msg = format!("Failed to enable feature flags: {}", e);
            error!("{}", error_msg);
            
            deployment_record.success = false;
            deployment_record.error_message = Some(error_msg.clone());
            deployment_record.end_time = Some(chrono::Utc::now());
            
            self.deployment_history.write().await.push(deployment_record);
            
            return Ok(DeploymentResult {
                success: false,
                phase: next_phase,
                validation_results,
                error_message: Some(error_msg),
                timestamp: chrono::Utc::now(),
            });
        }
        
        // Step 3: Update traffic routing
        info!("Updating traffic routing for phase: {:?}", next_phase);
        {
            let mut config = self.config.write().await;
            config.current_phase = next_phase;
            config.traffic_routing.new_implementation_percentage = next_phase.traffic_percentage();
            config.traffic_routing.legacy_implementation_percentage = 1.0 - next_phase.traffic_percentage();
        }
        
        // Step 4: Start health monitoring
        info!("Starting health monitoring for phase: {:?}", next_phase);
        let health_monitor_handle = self.start_health_monitoring(next_phase).await;
        
        // Step 5: Wait for stabilization period
        let stabilization_period = Duration::from_secs(300); // 5 minutes
        info!("Waiting for stabilization period: {:?}", stabilization_period);
        tokio::time::sleep(stabilization_period).await;
        
        // Step 6: Check final health status
        let health_status = self.health_checks.get_overall_health().await;
        if !health_status.healthy {
            warn!("Health checks failed after deployment, triggering rollback");
            
            // Trigger rollback
            if let Err(rollback_error) = self.rollback_manager.rollback_to_previous_phase(current_phase).await {
                error!("Rollback failed: {}", rollback_error);
            } else {
                deployment_record.rollback_triggered = true;
            }
            
            let error_msg = format!("Health checks failed: {}", health_status.error_message.unwrap_or_default());
            deployment_record.success = false;
            deployment_record.error_message = Some(error_msg.clone());
            deployment_record.end_time = Some(chrono::Utc::now());
            
            self.deployment_history.write().await.push(deployment_record);
            
            return Ok(DeploymentResult {
                success: false,
                phase: next_phase,
                validation_results,
                error_message: Some(error_msg),
                timestamp: chrono::Utc::now(),
            });
        }
        
        // Step 7: Deployment successful
        let deployment_duration = deployment_start.elapsed();
        info!("Deployment to phase {:?} completed successfully in {:?}", next_phase, deployment_duration);
        
        deployment_record.success = true;
        deployment_record.end_time = Some(chrono::Utc::now());
        
        self.deployment_history.write().await.push(deployment_record);
        
        Ok(DeploymentResult {
            success: true,
            phase: next_phase,
            validation_results,
            error_message: None,
            timestamp: chrono::Utc::now(),
        })
    }
    
    /// Run validation checkpoints for a deployment phase
    async fn run_validation_checkpoints(&self, phase: DeploymentPhase) -> Result<HashMap<ValidationCheckpoint, bool>> {
        let checkpoints = phase.validation_checkpoints();
        let mut results = HashMap::new();
        
        for checkpoint in checkpoints {
            info!("Running validation checkpoint: {:?}", checkpoint);
            let result = self.validation_manager.run_checkpoint(checkpoint.clone()).await?;
            results.insert(checkpoint, result);
        }
        
        Ok(results)
    }
    
    /// Start health monitoring for a deployment phase
    async fn start_health_monitoring(&self, phase: DeploymentPhase) -> tokio::task::JoinHandle<()> {
        let health_checks = Arc::clone(&self.health_checks);
        let rollback_manager = Arc::clone(&self.rollback_manager);
        let config = Arc::clone(&self.config);
        
        tokio::spawn(async move {
            let mut check_interval = tokio::time::interval(Duration::from_secs(30));
            let mut consecutive_failures = 0;
            let failure_threshold = 3;
            
            loop {
                check_interval.tick().await;
                
                let health_status = health_checks.get_overall_health().await;
                
                if health_status.healthy {
                    consecutive_failures = 0;
                } else {
                    consecutive_failures += 1;
                    warn!("Health check failed ({}/{}): {}", 
                        consecutive_failures, 
                        failure_threshold,
                        health_status.error_message.unwrap_or_default()
                    );
                    
                    if consecutive_failures >= failure_threshold {
                        error!("Health check failure threshold reached, triggering automatic rollback");
                        
                        let current_phase = {
                            let config = config.read().await;
                            config.current_phase
                        };
                        
                        if let Some(previous_phase) = current_phase.previous_phase() {
                            if let Err(e) = rollback_manager.rollback_to_previous_phase(previous_phase).await {
                                error!("Automatic rollback failed: {}", e);
                            } else {
                                info!("Automatic rollback completed successfully");
                            }
                        }
                        
                        break;
                    }
                }
            }
        })
    }
    
    /// Rollback to previous phase
    pub async fn rollback_to_previous_phase(&self) -> Result<RollbackResult> {
        let current_phase = {
            let config = self.config.read().await;
            config.current_phase
        };
        
        let previous_phase = match current_phase.previous_phase() {
            Some(phase) => phase,
            None => {
                info!("Already at initial deployment phase: {:?}", current_phase);
                return Ok(RollbackResult {
                    success: true,
                    from_phase: current_phase,
                    to_phase: current_phase,
                    error_message: None,
                    timestamp: chrono::Utc::now(),
                });
            }
        };
        
        info!("Starting rollback from {:?} to {:?}", current_phase, previous_phase);
        
        let rollback_result = self.rollback_manager.rollback_to_previous_phase(previous_phase).await?;
        
        if rollback_result.success {
            // Update configuration
            {
                let mut config = self.config.write().await;
                config.current_phase = previous_phase;
                config.traffic_routing.new_implementation_percentage = previous_phase.traffic_percentage();
                config.traffic_routing.legacy_implementation_percentage = 1.0 - previous_phase.traffic_percentage();
            }
            
            // Disable feature flags for current phase
            if let Err(e) = self.feature_flags.disable_phase(current_phase) {
                warn!("Failed to disable feature flags during rollback: {}", e);
            }
            
            info!("Rollback completed successfully");
        }
        
        Ok(rollback_result)
    }
    
    /// Get current deployment status
    pub async fn get_deployment_status(&self) -> DeploymentStatus {
        let config = self.config.read().await;
        let health_status = self.health_checks.get_overall_health().await;
        let feature_flag_status = self.feature_flags.get_status();
        
        DeploymentStatus {
            current_phase: config.current_phase,
            traffic_routing: config.traffic_routing.clone(),
            health_status,
            feature_flags: feature_flag_status,
            deployment_history: self.deployment_history.read().await.clone(),
        }
    }
    
    /// Force deployment to a specific phase (emergency use only)
    pub async fn force_deploy_to_phase(&self, target_phase: DeploymentPhase) -> Result<DeploymentResult> {
        warn!("Force deploying to phase: {:?} - this bypasses normal validation!", target_phase);
        
        // Enable all feature flags up to target phase
        let mut current = DeploymentPhase::Development;
        while current != target_phase {
            if let Err(e) = self.feature_flags.enable_phase(current) {
                error!("Failed to enable phase {:?}: {}", current, e);
            }
            
            current = match current.next_phase() {
                Some(phase) => phase,
                None => break,
            };
        }
        
        // Update configuration
        {
            let mut config = self.config.write().await;
            config.current_phase = target_phase;
            config.traffic_routing.new_implementation_percentage = target_phase.traffic_percentage();
            config.traffic_routing.legacy_implementation_percentage = 1.0 - target_phase.traffic_percentage();
        }
        
        Ok(DeploymentResult {
            success: true,
            phase: target_phase,
            validation_results: HashMap::new(),
            error_message: Some("Force deployment - validation bypassed".to_string()),
            timestamp: chrono::Utc::now(),
        })
    }
}

/// Deployment record for history tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentRecord {
    pub phase: DeploymentPhase,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub success: bool,
    pub validation_results: HashMap<ValidationCheckpoint, bool>,
    pub error_message: Option<String>,
    pub rollback_triggered: bool,
}

/// Current deployment status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentStatus {
    pub current_phase: DeploymentPhase,
    pub traffic_routing: TrafficRoutingConfig,
    pub health_status: super::health_checks::HealthStatus,
    pub feature_flags: HashMap<String, super::feature_flags::FeatureFlagStatus>,
    pub deployment_history: Vec<DeploymentRecord>,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_deployment_manager_creation() {
        let config = DeploymentConfig::default();
        let manager = DeploymentManager::new(config);
        
        let status = manager.get_deployment_status().await;
        assert_eq!(status.current_phase, DeploymentPhase::Development);
    }
    
    #[tokio::test]
    async fn test_phase_progression() {
        let config = DeploymentConfig::default();
        let manager = DeploymentManager::new(config);
        
        // Mock successful validation
        // In real implementation, this would require proper validation setup
        
        let initial_status = manager.get_deployment_status().await;
        assert_eq!(initial_status.current_phase, DeploymentPhase::Development);
    }
}