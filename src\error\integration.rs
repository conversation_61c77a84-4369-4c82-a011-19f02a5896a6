// MISSION: Integrated Error Handling System
// WHY: Provide a unified interface for all error handling, recovery, and monitoring
// HOW: Integrate enhanced errors, recovery mechanisms, metrics, and alerting into a cohesive system

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, warn, info, debug};

use crate::error::{BasiliskError, Result};
use crate::error::enhanced::{
    <PERSON><PERSON>r<PERSON><PERSON><PERSON>, ErrorContext, EnhancedError, AlertManager, AlertChannelConfig, AlertMessage
};
use crate::error::recovery::{RecoveryManager, CircuitBreakerState};
use crate::error::metrics::{ErrorMetricsCollector, MetricsConfig, ErrorImpactAnalyzer};
use crate::logging::{ErrorCode, AlertSeverity, TradingContext};

/// Comprehensive error handling configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct IntegratedErrorConfig {
    pub metrics_config: MetricsConfig,
    pub alert_channels: Vec<AlertChannelConfig>,
    pub enable_recovery: bool,
    pub enable_circuit_breakers: bool,
    pub enable_metrics_collection: bool,
    pub health_check_interval_seconds: u64,
}

impl Default for IntegratedErrorConfig {
    fn default() -> Self {
        Self {
            metrics_config: MetricsConfig::default(),
            alert_channels: vec![],
            enable_recovery: true,
            enable_circuit_breakers: true,
            enable_metrics_collection: true,
            health_check_interval_seconds: 300, // 5 minutes
        }
    }
}

/// Integrated error handling system that coordinates all error-related functionality
pub struct IntegratedErrorSystem {
    error_handler: Arc<ErrorHandler>,
    recovery_manager: Arc<RecoveryManager>,
    metrics_collector: Arc<ErrorMetricsCollector>,
    impact_analyzer: Arc<ErrorImpactAnalyzer>,
    config: IntegratedErrorConfig,
    health_monitor: Arc<RwLock<SystemHealthMonitor>>,
}

impl IntegratedErrorSystem {
    pub fn new(config: IntegratedErrorConfig) -> Self {
        let error_handler = Arc::new(ErrorHandler::new(config.alert_channels.clone()));
        let recovery_manager = Arc::new(RecoveryManager::new());
        let metrics_collector = Arc::new(ErrorMetricsCollector::new(config.metrics_config.clone()));
        let impact_analyzer = Arc::new(ErrorImpactAnalyzer::new(metrics_collector.clone()));
        let health_monitor = Arc::new(RwLock::new(SystemHealthMonitor::new()));

        Self {
            error_handler,
            recovery_manager,
            metrics_collector,
            impact_analyzer,
            config,
            health_monitor,
        }
    }

    /// Handle an error with full integration of recovery, metrics, and alerting
    pub async fn handle_error_integrated(&self, 
        error: BasiliskError, 
        context: ErrorContext
    ) -> EnhancedError {
        // Create enhanced error
        let enhanced_error = self.error_handler.handle_error(error, context).await;

        // Record metrics if enabled
        if self.config.enable_metrics_collection {
            self.metrics_collector.record_error(&enhanced_error).await;
        }

        // Update health monitor
        self.health_monitor.write().await.record_error(&enhanced_error).await;

        enhanced_error
    }

    /// Execute operation with full error handling, recovery, and monitoring
    pub async fn execute_with_full_handling<F, T, E>(&self,
        service_name: &str,
        operation: F,
        mut context: ErrorContext,
    ) -> Result<T>
    where
        F: FnOnce() -> std::result::Result<T, E> + Send,
        E: Into<BasiliskError>,
    {
        let start_time = std::time::Instant::now();

        // Execute with recovery if enabled
        let result = if self.config.enable_recovery {
            self.recovery_manager.execute_with_recovery(
                service_name,
                operation,
                &mut context,
            ).await
        } else {
            // Execute without recovery
            match operation() {
                Ok(value) => Ok(value),
                Err(e) => {
                    let basilisk_error = e.into();
                    context.propagate(&context.component.clone(), &context.function.clone(), &basilisk_error);
                    Err(EnhancedError::new(basilisk_error, context.clone()))
                }
            }
        };

        let execution_time = start_time.elapsed();

        // Handle result
        match result {
            Ok(value) => {
                // Record successful operation
                debug!(
                    component = %context.component,
                    function = %context.function,
                    trace_id = %context.trace_id,
                    service = service_name,
                    execution_time_ms = execution_time.as_millis(),
                    "Operation completed successfully"
                );
                Ok(value)
            }
            Err(enhanced_error) => {
                // Record metrics and handle error
                if self.config.enable_metrics_collection {
                    self.metrics_collector.record_error(&enhanced_error).await;
                }

                // Update health monitor
                self.health_monitor.write().await.record_error(&enhanced_error).await;

                // Log error with full context
                error!(
                    component = %enhanced_error.context.component,
                    function = %enhanced_error.context.function,
                    trace_id = %enhanced_error.context.trace_id,
                    error_code = %enhanced_error.error_code.as_str(),
                    service = service_name,
                    execution_time_ms = execution_time.as_millis(),
                    retry_count = enhanced_error.context.retry_count,
                    propagation_chain_length = enhanced_error.context.propagation_chain.len(),
                    error = %enhanced_error.inner,
                    "Operation failed with enhanced error handling"
                );

                Err(enhanced_error.inner)
            }
        }
    }

    /// Get comprehensive system health status
    pub async fn get_system_health(&self) -> SystemHealthStatus {
        let health_monitor = self.health_monitor.read().await;
        let error_metrics = self.metrics_collector.get_metrics().await;
        let health_score = self.metrics_collector.get_health_score().await;
        let trading_impact = self.impact_analyzer.analyze_trading_impact().await;
        let circuit_breaker_status = self.recovery_manager.get_circuit_breaker_status().await;

        SystemHealthStatus {
            overall_health_score: health_score,
            error_metrics,
            trading_impact,
            circuit_breaker_status,
            system_alerts: health_monitor.get_active_alerts().await,
            recommendations: health_monitor.get_recommendations().await,
            last_updated: std::time::SystemTime::now(),
        }
    }

    /// Start background health monitoring
    pub async fn start_health_monitoring(&self) {
        let health_monitor = self.health_monitor.clone();
        let metrics_collector = self.metrics_collector.clone();
        let interval = self.config.health_check_interval_seconds;

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(
                std::time::Duration::from_secs(interval)
            );

            loop {
                interval_timer.tick().await;

                // Generate health report
                let health_report = metrics_collector.generate_health_report().await;
                
                // Update health monitor
                {
                    let mut monitor = health_monitor.write().await;
                    monitor.update_health_report(health_report.clone()).await;
                }

                info!(
                    health_score = health_report.health_score,
                    total_errors = health_report.metrics.total_errors,
                    error_rate_per_minute = health_report.metrics.error_rate_per_minute,
                    alerts_count = health_report.alerts.len(),
                    "System health check completed"
                );
            }
        });
    }

    /// Get error handling statistics
    pub async fn get_error_statistics(&self) -> ErrorHandlingStatistics {
        let metrics = self.metrics_collector.get_metrics().await;
        let health_monitor = self.health_monitor.read().await;
        
        ErrorHandlingStatistics {
            total_errors_handled: metrics.total_errors,
            errors_by_severity: metrics.errors_by_severity.clone(),
            errors_by_component: metrics.errors_by_component.clone(),
            recovery_attempts: health_monitor.get_recovery_attempts().await,
            successful_recoveries: health_monitor.get_successful_recoveries().await,
            circuit_breaker_trips: health_monitor.get_circuit_breaker_trips().await,
            average_error_resolution_time: health_monitor.get_average_resolution_time().await,
        }
    }

    /// Create error context with trading information
    pub fn create_trading_context(&self, 
        component: &str, 
        function: &str, 
        trading_context: &TradingContext
    ) -> ErrorContext {
        let mut context = ErrorContext::new(component, function);
        
        if let Some(ref opportunity_id) = trading_context.opportunity_id {
            context = context.with_opportunity(opportunity_id);
        }
        
        if let Some(chain_id) = trading_context.chain_id {
            context = context.with_chain(chain_id);
        }
        
        if let Some(ref token_path) = trading_context.token_path {
            context = context.with_data("token_path", token_path);
        }
        
        if let Some(profit) = trading_context.estimated_profit_usd {
            context = context.with_data("estimated_profit_usd", profit);
        }
        
        if let Some(gas) = trading_context.gas_estimate_gwei {
            context = context.with_data("gas_estimate_gwei", gas);
        }
        
        context
    }
}

/// System health monitor that tracks overall system state
#[derive(Debug)]
struct SystemHealthMonitor {
    active_alerts: Vec<SystemAlert>,
    error_count_by_hour: std::collections::VecDeque<(std::time::SystemTime, u64)>,
    recovery_attempts: u64,
    successful_recoveries: u64,
    circuit_breaker_trips: u64,
    last_health_report: Option<crate::error::metrics::HealthReport>,
}

impl SystemHealthMonitor {
    fn new() -> Self {
        Self {
            active_alerts: Vec::new(),
            error_count_by_hour: std::collections::VecDeque::new(),
            recovery_attempts: 0,
            successful_recoveries: 0,
            circuit_breaker_trips: 0,
            last_health_report: None,
        }
    }

    async fn record_error(&mut self, error: &EnhancedError) {
        // Update error counts
        let now = std::time::SystemTime::now();
        self.error_count_by_hour.push_back((now, 1));
        
        // Keep only last 24 hours
        let cutoff = now - std::time::Duration::from_secs(24 * 3600);
        while let Some((timestamp, _)) = self.error_count_by_hour.front() {
            if *timestamp < cutoff {
                self.error_count_by_hour.pop_front();
            } else {
                break;
            }
        }

        // Create alert for critical errors
        if error.severity == AlertSeverity::Critical {
            let alert = SystemAlert {
                id: uuid::Uuid::new_v4().to_string(),
                timestamp: now,
                severity: error.severity.clone(),
                title: format!("Critical Error in {}", error.context.component),
                description: error.inner.to_string(),
                error_code: error.error_code.clone(),
                component: error.context.component.clone(),
                resolved: false,
            };
            self.active_alerts.push(alert);
        }
    }

    async fn update_health_report(&mut self, report: crate::error::metrics::HealthReport) {
        self.last_health_report = Some(report);
        
        // Clear resolved alerts based on health improvements
        if let Some(ref report) = self.last_health_report {
            if report.health_score > 90.0 {
                // Mark non-critical alerts as resolved
                for alert in &mut self.active_alerts {
                    if alert.severity != AlertSeverity::Critical {
                        alert.resolved = true;
                    }
                }
            }
        }
    }

    async fn get_active_alerts(&self) -> Vec<SystemAlert> {
        self.active_alerts.iter()
            .filter(|alert| !alert.resolved)
            .cloned()
            .collect()
    }

    async fn get_recommendations(&self) -> Vec<String> {
        if let Some(ref report) = self.last_health_report {
            report.recommendations.clone()
        } else {
            vec![]
        }
    }

    async fn get_recovery_attempts(&self) -> u64 {
        self.recovery_attempts
    }

    async fn get_successful_recoveries(&self) -> u64 {
        self.successful_recoveries
    }

    async fn get_circuit_breaker_trips(&self) -> u64 {
        self.circuit_breaker_trips
    }

    async fn get_average_resolution_time(&self) -> Option<std::time::Duration> {
        // Placeholder implementation
        Some(std::time::Duration::from_secs(30))
    }
}

/// System alert structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemAlert {
    pub id: String,
    pub timestamp: std::time::SystemTime,
    pub severity: AlertSeverity,
    pub title: String,
    pub description: String,
    pub error_code: ErrorCode,
    pub component: String,
    pub resolved: bool,
}

/// Comprehensive system health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthStatus {
    pub overall_health_score: f64,
    pub error_metrics: crate::error::metrics::ErrorMetrics,
    pub trading_impact: crate::error::metrics::TradingImpactReport,
    pub circuit_breaker_status: std::collections::HashMap<String, crate::error::recovery::CircuitBreakerMetrics>,
    pub system_alerts: Vec<SystemAlert>,
    pub recommendations: Vec<String>,
    pub last_updated: std::time::SystemTime,
}

/// Error handling statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorHandlingStatistics {
    pub total_errors_handled: u64,
    pub errors_by_severity: std::collections::HashMap<AlertSeverity, u64>,
    pub errors_by_component: std::collections::HashMap<String, u64>,
    pub recovery_attempts: u64,
    pub successful_recoveries: u64,
    pub circuit_breaker_trips: u64,
    pub average_error_resolution_time: Option<std::time::Duration>,
}

/// Convenience macros for integrated error handling
#[macro_export]
macro_rules! handle_with_recovery {
    ($system:expr, $service:expr, $component:expr, $function:expr, $operation:expr) => {
        {
            let context = $crate::error::enhanced::ErrorContext::new($component, $function);
            $system.execute_with_full_handling($service, $operation, context).await
        }
    };
}

#[macro_export]
macro_rules! handle_trading_error {
    ($system:expr, $service:expr, $trading_context:expr, $operation:expr) => {
        {
            let context = $system.create_trading_context(
                $trading_context.component.as_str(),
                $trading_context.function.as_str(),
                $trading_context
            );
            $system.execute_with_full_handling($service, $operation, context).await
        }
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::error::{NetworkError, BasiliskError};
    use crate::error::enhanced::ErrorContext;

    #[tokio::test]
    async fn test_integrated_error_system() {
        let config = IntegratedErrorConfig::default();
        let system = IntegratedErrorSystem::new(config);
        
        // Test error handling
        let context = ErrorContext::new("TestComponent", "test_function");
        let error = BasiliskError::Network(NetworkError::RpcTimeout {
            endpoint: "test".to_string(),
            timeout_ms: 5000,
        });
        
        let enhanced_error = system.handle_error_integrated(error, context).await;
        assert_eq!(enhanced_error.error_code, crate::logging::ErrorCode::ERpcTimeout);
        
        // Test health status
        let health_status = system.get_system_health().await;
        assert!(health_status.overall_health_score <= 100.0);
    }

    #[tokio::test]
    async fn test_execute_with_full_handling() {
        let config = IntegratedErrorConfig::default();
        let system = IntegratedErrorSystem::new(config);
        
        let context = ErrorContext::new("TestComponent", "test_function");
        
        // Test successful operation
        let result = system.execute_with_full_handling(
            "test_service",
            || -> std::result::Result<String, String> { Ok("success".to_string()) },
            context.clone(),
        ).await;
        
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "success");
        
        // Test failed operation
        let result = system.execute_with_full_handling(
            "test_service",
            || -> std::result::Result<String, String> { Err("test error".to_string()) },
            context,
        ).await;
        
        assert!(result.is_err());
    }
}