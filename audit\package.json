{"name": "stargate-compass-audit", "version": "1.0.0", "description": "Security audit workspace for StargateCompassV1 contract", "main": "index.js", "scripts": {"audit:static": "npm run slither && npm run mythril && npm run solhint", "audit:test": "hardhat test --network hardhat", "audit:test-fork": "hardhat test --network localhost", "audit:report": "node scripts/generate-report.js", "slither": "slither . --config-file tools/slither.config.json --hardhat-ignore-compile", "mythril": "myth analyze ../geometer-contracts/contracts/StargateCompassV1.sol --config-file tools/mythril.config.json", "solhint": "solhint 'contracts/**/*.sol' --config tools/solhint.config.json", "setup": "npm install && python3 -m pip install slither-analyzer mythril", "test": "hardhat test", "compile": "hardhat compile", "fork-mainnet": "hardhat node --fork https://mainnet.base.org"}, "keywords": ["security", "audit", "solidity", "defi"], "author": "Basilisk Security Team", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@typechain/ethers-v6": "^0.5.0", "@typechain/hardhat": "^9.0.0", "chai": "^4.5.0", "ethers": "^6.15.0", "hardhat": "^2.26.1", "hardhat-gas-reporter": "^1.0.8", "solhint": "^5.2.0", "solidity-coverage": "^0.8.1", "typechain": "^8.3.0"}, "dependencies": {"@aave/core-v3": "^1.19.3", "@openzeppelin/contracts": "^5.3.0", "dotenv": "^16.6.1"}}