// MISSION: Error Metrics and Monitoring System
// WHY: Provide comprehensive error tracking, analysis, and operational insights
// HOW: Implement error rate tracking, pattern detection, and health monitoring

use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tracing::{info, warn, error};

use crate::error::enhanced::{EnhancedError, ErrorContext};
use crate::logging::{ErrorCode, AlertSeverity};

/// Time-windowed error metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorMetrics {
    pub total_errors: u64,
    pub error_rate_per_minute: f64,
    pub error_rate_per_hour: f64,
    pub errors_by_code: HashMap<ErrorCode, u64>,
    pub errors_by_component: HashMap<String, u64>,
    pub errors_by_severity: HashMap<AlertSeverity, u64>,
    pub mean_time_between_failures: Option<Duration>,
    pub error_burst_detected: bool,
    pub top_error_patterns: Vec<ErrorPattern>,
}

/// Error pattern for trend analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorPattern {
    pub error_code: ErrorCode,
    pub component: String,
    pub frequency: u64,
    pub first_seen: SystemTime,
    pub last_seen: SystemTime,
    pub trend: ErrorTrend,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorTrend {
    Increasing,
    Decreasing,
    Stable,
    Spike,
}

/// Individual error event for tracking
#[derive(Debug, Clone)]
struct ErrorEvent {
    pub timestamp: Instant,
    pub error_code: ErrorCode,
    pub component: String,
    pub severity: AlertSeverity,
    pub trace_id: String,
    pub opportunity_id: Option<String>,
}

/// Error metrics collector and analyzer
pub struct ErrorMetricsCollector {
    events: Arc<RwLock<VecDeque<ErrorEvent>>>,
    patterns: Arc<RwLock<HashMap<String, ErrorPattern>>>,
    config: MetricsConfig,
    last_analysis: Arc<RwLock<Instant>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsConfig {
    pub max_events: usize,
    pub analysis_interval: Duration,
    pub burst_threshold: u32,
    pub burst_window: Duration,
    pub pattern_min_frequency: u64,
}

impl Default for MetricsConfig {
    fn default() -> Self {
        Self {
            max_events: 10000,
            analysis_interval: Duration::from_secs(60),
            burst_threshold: 10,
            burst_window: Duration::from_secs(60),
            pattern_min_frequency: 5,
        }
    }
}

impl ErrorMetricsCollector {
    pub fn new(config: MetricsConfig) -> Self {
        Self {
            events: Arc::new(RwLock::new(VecDeque::new())),
            patterns: Arc::new(RwLock::new(HashMap::new())),
            config,
            last_analysis: Arc::new(RwLock::new(Instant::now())),
        }
    }

    pub async fn record_error(&self, error: &EnhancedError) {
        let event = ErrorEvent {
            timestamp: Instant::now(),
            error_code: error.error_code.clone(),
            component: error.context.component.clone(),
            severity: error.severity.clone(),
            trace_id: error.context.trace_id.to_string(),
            opportunity_id: error.context.opportunity_id.clone(),
        };

        // Add event to queue
        {
            let mut events = self.events.write().await;
            events.push_back(event);
            
            // Maintain max size
            while events.len() > self.config.max_events {
                events.pop_front();
            }
        }

        // Update patterns
        self.update_patterns(&error.error_code, &error.context.component).await;

        // Check if analysis should run
        if self.should_run_analysis().await {
            self.analyze_patterns().await;
        }
    }

    async fn update_patterns(&self, error_code: &ErrorCode, component: &str) {
        let pattern_key = format!("{}:{}", error_code.as_str(), component);
        let now = SystemTime::now();
        
        let mut patterns = self.patterns.write().await;
        
        if let Some(pattern) = patterns.get_mut(&pattern_key) {
            pattern.frequency += 1;
            pattern.last_seen = now;
        } else {
            patterns.insert(pattern_key, ErrorPattern {
                error_code: error_code.clone(),
                component: component.to_string(),
                frequency: 1,
                first_seen: now,
                last_seen: now,
                trend: ErrorTrend::Stable,
            });
        }
    }

    async fn should_run_analysis(&self) -> bool {
        let last_analysis = *self.last_analysis.read().await;
        last_analysis.elapsed() >= self.config.analysis_interval
    }

    async fn analyze_patterns(&self) {
        *self.last_analysis.write().await = Instant::now();
        
        let mut patterns = self.patterns.write().await;
        let now = Instant::now();
        
        for pattern in patterns.values_mut() {
            // Calculate trend based on recent frequency
            let time_since_first = pattern.last_seen.duration_since(pattern.first_seen)
                .unwrap_or(Duration::from_secs(1));
            
            let rate = pattern.frequency as f64 / time_since_first.as_secs() as f64;
            
            // Simple trend detection (could be more sophisticated)
            pattern.trend = if pattern.frequency >= self.config.pattern_min_frequency * 2 {
                ErrorTrend::Spike
            } else if rate > 0.1 {
                ErrorTrend::Increasing
            } else if rate < 0.01 {
                ErrorTrend::Decreasing
            } else {
                ErrorTrend::Stable
            };
        }

        // Log significant patterns
        for pattern in patterns.values() {
            if matches!(pattern.trend, ErrorTrend::Spike | ErrorTrend::Increasing) 
                && pattern.frequency >= self.config.pattern_min_frequency {
                warn!(
                    error_code = %pattern.error_code.as_str(),
                    component = %pattern.component,
                    frequency = pattern.frequency,
                    trend = ?pattern.trend,
                    "Significant error pattern detected"
                );
            }
        }
    }

    pub async fn get_metrics(&self) -> ErrorMetrics {
        let events = self.events.read().await;
        let patterns = self.patterns.read().await;
        let now = Instant::now();
        
        // Calculate time windows
        let one_minute_ago = now - Duration::from_secs(60);
        let one_hour_ago = now - Duration::from_secs(3600);
        
        // Count errors in different time windows
        let mut errors_last_minute = 0u64;
        let mut errors_last_hour = 0u64;
        let mut errors_by_code = HashMap::new();
        let mut errors_by_component = HashMap::new();
        let mut errors_by_severity = HashMap::new();
        let mut timestamps = Vec::new();
        
        for event in events.iter() {
            if event.timestamp >= one_hour_ago {
                errors_last_hour += 1;
                timestamps.push(event.timestamp);
                
                *errors_by_code.entry(event.error_code.clone()).or_insert(0) += 1;
                *errors_by_component.entry(event.component.clone()).or_insert(0) += 1;
                *errors_by_severity.entry(event.severity.clone()).or_insert(0) += 1;
                
                if event.timestamp >= one_minute_ago {
                    errors_last_minute += 1;
                }
            }
        }
        
        // Calculate rates
        let error_rate_per_minute = errors_last_minute as f64;
        let error_rate_per_hour = errors_last_hour as f64;
        
        // Calculate mean time between failures
        let mean_time_between_failures = if timestamps.len() > 1 {
            let total_time = timestamps.last().unwrap().duration_since(timestamps[0]);
            Some(total_time / (timestamps.len() - 1) as u32)
        } else {
            None
        };
        
        // Detect error bursts
        let error_burst_detected = self.detect_error_burst(&events, now).await;
        
        // Get top error patterns
        let mut top_patterns: Vec<_> = patterns.values().cloned().collect();
        top_patterns.sort_by(|a, b| b.frequency.cmp(&a.frequency));
        top_patterns.truncate(10);
        
        ErrorMetrics {
            total_errors: events.len() as u64,
            error_rate_per_minute,
            error_rate_per_hour,
            errors_by_code,
            errors_by_component,
            errors_by_severity,
            mean_time_between_failures,
            error_burst_detected,
            top_error_patterns: top_patterns,
        }
    }

    async fn detect_error_burst(&self, events: &VecDeque<ErrorEvent>, now: Instant) -> bool {
        let burst_window_start = now - self.config.burst_window;
        
        let recent_errors = events.iter()
            .filter(|event| event.timestamp >= burst_window_start)
            .count();
        
        recent_errors >= self.config.burst_threshold as usize
    }

    pub async fn get_health_score(&self) -> f64 {
        let metrics = self.get_metrics().await;
        
        // Calculate health score based on various factors
        let mut score = 100.0;
        
        // Penalize high error rates
        if metrics.error_rate_per_minute > 10.0 {
            score -= 30.0;
        } else if metrics.error_rate_per_minute > 5.0 {
            score -= 15.0;
        } else if metrics.error_rate_per_minute > 1.0 {
            score -= 5.0;
        }
        
        // Penalize error bursts
        if metrics.error_burst_detected {
            score -= 20.0;
        }
        
        // Penalize critical errors
        if let Some(critical_count) = metrics.errors_by_severity.get(&AlertSeverity::Critical) {
            score -= (*critical_count as f64) * 10.0;
        }
        
        // Penalize increasing error trends
        let increasing_patterns = metrics.top_error_patterns.iter()
            .filter(|p| matches!(p.trend, ErrorTrend::Increasing | ErrorTrend::Spike))
            .count();
        
        score -= (increasing_patterns as f64) * 5.0;
        
        score.max(0.0).min(100.0)
    }

    pub async fn generate_health_report(&self) -> HealthReport {
        let metrics = self.get_metrics().await;
        let health_score = self.get_health_score().await;
        
        let mut recommendations = Vec::new();
        let mut alerts = Vec::new();
        
        // Generate recommendations based on metrics
        if metrics.error_rate_per_minute > 5.0 {
            recommendations.push("High error rate detected. Consider investigating recent changes.".to_string());
        }
        
        if metrics.error_burst_detected {
            alerts.push("Error burst detected in the last minute.".to_string());
        }
        
        // Check for problematic patterns
        for pattern in &metrics.top_error_patterns {
            if matches!(pattern.trend, ErrorTrend::Spike) {
                alerts.push(format!(
                    "Error spike detected: {} in {} (frequency: {})",
                    pattern.error_code.as_str(),
                    pattern.component,
                    pattern.frequency
                ));
            } else if matches!(pattern.trend, ErrorTrend::Increasing) && pattern.frequency > 10 {
                recommendations.push(format!(
                    "Increasing error trend: {} in {} - investigate root cause",
                    pattern.error_code.as_str(),
                    pattern.component
                ));
            }
        }
        
        // Component-specific recommendations
        for (component, count) in &metrics.errors_by_component {
            if *count > 20 {
                recommendations.push(format!(
                    "Component '{}' has high error count ({}). Review implementation.",
                    component, count
                ));
            }
        }
        
        HealthReport {
            timestamp: SystemTime::now(),
            health_score,
            metrics,
            alerts,
            recommendations,
        }
    }
}

/// System health report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthReport {
    pub timestamp: SystemTime,
    pub health_score: f64,
    pub metrics: ErrorMetrics,
    pub alerts: Vec<String>,
    pub recommendations: Vec<String>,
}

/// Error dashboard data for monitoring UIs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorDashboard {
    pub current_health_score: f64,
    pub error_rate_trend: Vec<(SystemTime, f64)>,
    pub top_errors_by_frequency: Vec<(ErrorCode, u64)>,
    pub component_error_distribution: Vec<(String, u64)>,
    pub severity_breakdown: HashMap<AlertSeverity, u64>,
    pub recent_critical_errors: Vec<CriticalErrorSummary>,
    pub system_stability_indicators: StabilityIndicators,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CriticalErrorSummary {
    pub timestamp: SystemTime,
    pub error_code: ErrorCode,
    pub component: String,
    pub message: String,
    pub trace_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StabilityIndicators {
    pub mean_time_between_failures: Option<Duration>,
    pub error_burst_frequency: f64,
    pub recovery_time_average: Option<Duration>,
    pub circuit_breaker_trips: u32,
}

/// Performance impact analyzer
pub struct ErrorImpactAnalyzer {
    metrics_collector: Arc<ErrorMetricsCollector>,
}

impl ErrorImpactAnalyzer {
    pub fn new(metrics_collector: Arc<ErrorMetricsCollector>) -> Self {
        Self { metrics_collector }
    }

    pub async fn analyze_trading_impact(&self) -> TradingImpactReport {
        let metrics = self.metrics_collector.get_metrics().await;
        
        // Analyze impact on trading operations
        let execution_errors = metrics.errors_by_code.iter()
            .filter(|(code, _)| matches!(code, 
                ErrorCode::EInsufficientLiquidity |
                ErrorCode::EHighSlippage |
                ErrorCode::ETransactionReverted |
                ErrorCode::ETransactionTimeout
            ))
            .map(|(_, count)| *count)
            .sum::<u64>();
        
        let data_errors = metrics.errors_by_code.iter()
            .filter(|(code, _)| matches!(code,
                ErrorCode::EDataSourceUnavailable |
                ErrorCode::EDataStale |
                ErrorCode::EPriceOracleFailure
            ))
            .map(|(_, count)| *count)
            .sum::<u64>();
        
        let network_errors = metrics.errors_by_code.iter()
            .filter(|(code, _)| matches!(code,
                ErrorCode::ERpcTimeout |
                ErrorCode::ERpcConnectionFailed |
                ErrorCode::ERpcRateLimited
            ))
            .map(|(_, count)| *count)
            .sum::<u64>();
        
        // Estimate impact scores (0-100)
        let execution_impact = (execution_errors as f64 / metrics.total_errors.max(1) as f64 * 100.0).min(100.0);
        let data_impact = (data_errors as f64 / metrics.total_errors.max(1) as f64 * 100.0).min(100.0);
        let network_impact = (network_errors as f64 / metrics.total_errors.max(1) as f64 * 100.0).min(100.0);
        
        TradingImpactReport {
            execution_impact_score: execution_impact,
            data_reliability_score: 100.0 - data_impact,
            network_stability_score: 100.0 - network_impact,
            overall_trading_health: (100.0 - execution_impact - data_impact - network_impact).max(0.0),
            critical_issues: self.identify_critical_trading_issues(&metrics).await,
        }
    }

    async fn identify_critical_trading_issues(&self, metrics: &ErrorMetrics) -> Vec<String> {
        let mut issues = Vec::new();
        
        // Check for high-impact error patterns
        if let Some(liquidity_errors) = metrics.errors_by_code.get(&ErrorCode::EInsufficientLiquidity) {
            if *liquidity_errors > 10 {
                issues.push("High frequency of liquidity issues affecting trade execution".to_string());
            }
        }
        
        if let Some(slippage_errors) = metrics.errors_by_code.get(&ErrorCode::EHighSlippage) {
            if *slippage_errors > 5 {
                issues.push("Frequent slippage issues indicating market volatility or sizing problems".to_string());
            }
        }
        
        if let Some(rpc_timeouts) = metrics.errors_by_code.get(&ErrorCode::ERpcTimeout) {
            if *rpc_timeouts > 20 {
                issues.push("RPC timeout issues affecting trade timing and execution".to_string());
            }
        }
        
        issues
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingImpactReport {
    pub execution_impact_score: f64,
    pub data_reliability_score: f64,
    pub network_stability_score: f64,
    pub overall_trading_health: f64,
    pub critical_issues: Vec<String>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::error::{BasiliskError, NetworkError};
    use crate::error::enhanced::{EnhancedError, ErrorContext};

    #[tokio::test]
    async fn test_error_metrics_collection() {
        let config = MetricsConfig::default();
        let collector = ErrorMetricsCollector::new(config);
        
        // Create test error
        let context = ErrorContext::new("TestComponent", "test_function");
        let error = BasiliskError::Network(NetworkError::RpcTimeout {
            endpoint: "test".to_string(),
            timeout_ms: 5000,
        });
        let enhanced_error = EnhancedError::new(error, context);
        
        // Record error
        collector.record_error(&enhanced_error).await;
        
        // Get metrics
        let metrics = collector.get_metrics().await;
        
        assert_eq!(metrics.total_errors, 1);
        assert!(metrics.errors_by_code.contains_key(&ErrorCode::ERpcTimeout));
        assert!(metrics.errors_by_component.contains_key("TestComponent"));
    }

    #[tokio::test]
    async fn test_health_score_calculation() {
        let config = MetricsConfig::default();
        let collector = ErrorMetricsCollector::new(config);
        
        // Initially should have perfect health
        let initial_score = collector.get_health_score().await;
        assert_eq!(initial_score, 100.0);
        
        // Add some errors and check score decreases
        let context = ErrorContext::new("TestComponent", "test_function");
        let error = BasiliskError::Network(NetworkError::RpcTimeout {
            endpoint: "test".to_string(),
            timeout_ms: 5000,
        });
        let enhanced_error = EnhancedError::new(error, context);
        
        for _ in 0..3 {
            collector.record_error(&enhanced_error).await;
        }
        
        let score_after_errors = collector.get_health_score().await;
        assert!(score_after_errors < 100.0);
    }

    #[tokio::test]
    async fn test_error_burst_detection() {
        let config = MetricsConfig {
            burst_threshold: 2,
            burst_window: Duration::from_secs(60),
            ..Default::default()
        };
        let collector = ErrorMetricsCollector::new(config);
        
        // Add errors rapidly
        let context = ErrorContext::new("TestComponent", "test_function");
        let error = BasiliskError::Network(NetworkError::RpcTimeout {
            endpoint: "test".to_string(),
            timeout_ms: 5000,
        });
        let enhanced_error = EnhancedError::new(error, context);
        
        for _ in 0..3 {
            collector.record_error(&enhanced_error).await;
        }
        
        let metrics = collector.get_metrics().await;
        assert!(metrics.error_burst_detected);
    }
}