// Example demonstrating the new elegant configuration system
// Run with: cargo run --example elegant_config_demo

use basilisk_bot::config::Config;
use std::env;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔧 Elegant Configuration System Demo");
    println!("=====================================\n");

    // Set up environment for demo
    env::set_var("CONFIG_PATH", "config/elegant-test.toml");
    env::set_var("APP_ENV", "local");
    
    // Demonstrate environment variable override
    env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "0.15");
    env::set_var("APP_LOG_LEVEL", "debug");

    println!("1. Loading configuration with layered approach:");
    println!("   - Base: config/elegant-test.toml");
    println!("   - Environment: config/local.toml (if exists)");
    println!("   - Env vars: APP_* variables");
    println!();

    // Load the new configuration
    match Config::load() {
        Ok(config) => {
            println!("✅ Configuration loaded successfully!");
            println!();
            
            println!("📋 Configuration Summary:");
            println!("   App Name: {}", config.app_name);
            println!("   Log Level: {}", config.log_level);
            println!("   Kelly Fraction Cap: {}", config.strategy.kelly_fraction_cap);
            println!("   Max Slippage: {} bps", config.execution.max_slippage_bps);
            println!();
            
            println!("🔗 Configured Chains:");
            for (chain_id, chain_config) in &config.chains {
                println!("   Chain {}: {}", chain_id, chain_config.name);
                println!("     RPC: {}", chain_config.rpc_url);
                println!("     Private Key Env: {}", chain_config.private_key_env_var);
                
                if let Some(stargate) = &chain_config.contracts.stargate_compass_v1 {
                    println!("     Stargate Compass: {}", stargate);
                }
                
                if let Some(router) = &chain_config.dex.degen_swap_router {
                    println!("     Degen Swap Router: {}", router);
                }
                println!();
            }
            
            println!("🔍 Validation:");
            match config.validate() {
                Ok(()) => println!("   ✅ All validation checks passed!"),
                Err(e) => println!("   ❌ Validation failed: {}", e),
            }
            
            println!();
            println!("🌟 Environment Variable Overrides Demonstrated:");
            println!("   Kelly fraction cap was overridden from 0.25 to {}", config.strategy.kelly_fraction_cap);
            println!("   Log level was overridden to: {}", config.log_level);
            
        }
        Err(e) => {
            println!("❌ Failed to load configuration: {}", e);
            println!();
            println!("💡 Make sure config/elegant-test.toml exists");
            println!("   You can create it by running the main application first.");
        }
    }

    println!();
    println!("🎯 Next Steps:");
    println!("   1. Try setting different APP_* environment variables");
    println!("   2. Create config/local.toml to override specific settings");
    println!("   3. Use different APP_ENV values (production, staging, etc.)");

    Ok(())
}
