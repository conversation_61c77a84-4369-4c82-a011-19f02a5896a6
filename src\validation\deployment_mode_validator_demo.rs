// src/validation/deployment_mode_validator_demo.rs

//! Demo for deployment mode validation

use crate::validation::DeploymentModeValidator;
use crate::shared_types::RunMode;
use crate::config::Config;
use crate::error::BasiliskError;
use std::sync::Arc;
use tracing::info;

/// Run deployment mode validation demo
pub async fn run_deployment_mode_validation_demo() -> Result<(), BasiliskError> {
    println!("\n{}", "=".repeat(80));
    println!("DEPLOYMENT MODE VALIDATION DEMO");
    println!("{}", "=".repeat(80));
    println!("This demo showcases the 5-tier deployment ladder validation:");
    println!("1. Simulate Mode - Transaction interception testing");
    println!("2. Shadow Mode - Anvil fork state consistency");
    println!("3. Sentinel Mode - Minimal risk transaction testing");
    println!("4. Low-Capital Mode - Hardcoded limit enforcement");
    println!("5. Live Mode - Full production capability testing");
    println!("{}", "=".repeat(80));

    // Create a test configuration
    let config = Arc::new(Config::default());
    let mut validator = DeploymentModeValidator::new(config);

    let modes = vec![
        RunMode::Simulate,
        RunMode::Shadow,
        RunMode::Sentinel,
        RunMode::LowCapital,
        RunMode::Live,
    ];

    for (i, mode) in modes.iter().enumerate() {
        println!("\n[{}/5] Validating {} Mode...", i + 1, mode.to_string().to_uppercase());
        
        let start_time = std::time::Instant::now();
        let result = validator.validate_deployment_mode(*mode).await
            .map_err(|e| BasiliskError::execution_error(format!("Demo validation failed: {}", e)))?;
        let duration = start_time.elapsed();

        if result {
            println!("✅ {} Mode: PASSED ({:.2}s)", mode.to_string().to_uppercase(), duration.as_secs_f64());
        } else {
            println!("❌ {} Mode: FAILED ({:.2}s)", mode.to_string().to_uppercase(), duration.as_secs_f64());
        }

        // Show some metrics for each mode
        display_mode_metrics(&validator, *mode);

        // Add a small delay for demo effect
        tokio::time::sleep(std::time::Duration::from_millis(500)).await;
    }

    println!("\n{}", "=".repeat(80));
    println!("DEMO COMPLETED - All deployment modes validated successfully!");
    println!("{}", "=".repeat(80));

    Ok(())
}

/// Display metrics for a specific mode
fn display_mode_metrics(validator: &DeploymentModeValidator, mode: RunMode) {
    let metrics = &validator.validation_metrics;
    
    match mode {
        RunMode::Simulate => {
            println!("  📊 Transactions Intercepted: {}", metrics.simulate_mode.transactions_intercepted);
            println!("  📊 Interception Success Rate: {:.1}%", metrics.simulate_mode.interception_success_rate * 100.0);
            println!("  📊 Educational Data Accuracy: {:.1}%", metrics.simulate_mode.educational_data_accuracy * 100.0);
            println!("  📊 UI Response Time: {:.1}ms", metrics.simulate_mode.ui_response_time_ms);
        }
        RunMode::Shadow => {
            println!("  📊 Fork State Consistency: {:.1}%", metrics.shadow_mode.fork_state_consistency * 100.0);
            println!("  📊 Simulation Accuracy: {:.1}%", metrics.shadow_mode.simulation_accuracy * 100.0);
            println!("  📊 Simulated Transactions: {}", metrics.shadow_mode.simulated_transactions);
            println!("  📊 Performance Score: {:.2}", metrics.shadow_mode.performance_benchmark_score);
        }
        RunMode::Sentinel => {
            println!("  📊 Contract Health Success Rate: {:.1}%", metrics.sentinel_mode.contract_health_success_rate * 100.0);
            println!("  📊 Average Transaction Value: ${:.2}", metrics.sentinel_mode.average_transaction_value_usd);
            println!("  📊 Health Checks Performed: {}", metrics.sentinel_mode.health_checks_performed);
            println!("  📊 Risk Compliance: {:.1}%", metrics.sentinel_mode.risk_limit_compliance * 100.0);
        }
        RunMode::LowCapital => {
            println!("  📊 Position Limit Enforcement: {:.1}%", metrics.low_capital_mode.position_limit_enforcement * 100.0);
            println!("  📊 Daily Loss Limit Enforcement: {:.1}%", metrics.low_capital_mode.daily_loss_limit_enforcement * 100.0);
            println!("  📊 Max Position Size: ${:.2}", metrics.low_capital_mode.max_position_size_usd);
            println!("  📊 Max Daily Loss: ${:.2}", metrics.low_capital_mode.max_daily_loss_usd);
        }
        RunMode::Live => {
            println!("  📊 Production Capability Score: {:.2}", metrics.live_mode.production_capability_score);
            println!("  📊 Strategy Suite Completeness: {:.1}%", metrics.live_mode.strategy_suite_completeness * 100.0);
            println!("  📊 Risk Parameter Compliance: {:.1}%", metrics.live_mode.risk_parameter_compliance * 100.0);
            println!("  📊 Opportunities Processed: {}", metrics.live_mode.opportunities_processed);
        }
    }
}

/// Run a quick deployment mode validation test
pub async fn run_quick_deployment_mode_validation() -> Result<(), BasiliskError> {
    info!("Running quick deployment mode validation test");

    let config = Arc::new(Config::default());
    let mut validator = DeploymentModeValidator::new(config);

    // Test just the Simulate mode for a quick validation
    let result = validator.validate_deployment_mode(RunMode::Simulate).await?;

    if result {
        println!("✅ Quick deployment mode validation: PASSED");
        println!("   Simulate mode validation completed successfully");
    } else {
        println!("❌ Quick deployment mode validation: FAILED");
        return Err(BasiliskError::execution_error("Quick validation failed"));
    }

    Ok(())
}

/// Show deployment mode validation usage examples
pub fn show_deployment_mode_usage_examples() {
    println!("\n{}", "=".repeat(80));
    println!("DEPLOYMENT MODE VALIDATION USAGE EXAMPLES");
    println!("{}", "=".repeat(80));
    
    println!("\n🔧 BASIC USAGE:");
    println!("   // Create validator");
    println!("   let config = Arc::new(Config::default());");
    println!("   let mut validator = DeploymentModeValidator::new(config);");
    
    println!("\n   // Validate all modes");
    println!("   let result = validator.validate_all_modes().await?;");
    
    println!("\n   // Validate specific mode");
    println!("   let result = validator.validate_deployment_mode(RunMode::Simulate).await?;");
    
    println!("\n🎯 DEPLOYMENT MODES:");
    println!("   RunMode::Simulate    - Educational simulation with transaction interception");
    println!("   RunMode::Shadow      - Live simulation with Anvil fork state consistency");
    println!("   RunMode::Sentinel    - Minimal risk transaction testing");
    println!("   RunMode::LowCapital  - Conservative trading with hardcoded limits");
    println!("   RunMode::Live        - Full production capability testing");
    
    println!("\n📊 VALIDATION METRICS:");
    println!("   - Transaction interception rates");
    println!("   - Fork state consistency scores");
    println!("   - Contract health check results");
    println!("   - Risk limit enforcement rates");
    println!("   - Production capability scores");
    
    println!("\n🚀 CLI COMMANDS:");
    println!("   cargo run -- validation deployment-mode validate-all");
    println!("   cargo run -- validation deployment-mode validate-mode simulate");
    println!("   cargo run -- validation deployment-mode demo");
    
    println!("\n{}", "=".repeat(80));
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_quick_deployment_mode_validation() {
        let result = run_quick_deployment_mode_validation().await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_show_usage_examples() {
        // This should not panic
        show_deployment_mode_usage_examples();
    }
}