// MISSION: TUI Functionality Tester - Programmatic TUI Command Execution and Validation
// WHY: Verify TUI commands correctly interact with StargateCompassV1 contract
// HOW: Process spawning, output capture, and data validation framework

use anyhow::{Result, Context};
use serde_json::Value;
use std::collections::HashMap;
use std::process::{Command, Stdio};
use std::time::{Duration, Instant};
use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};
use tokio::process::{Child, Command as TokioCommand};
use tokio::sync::mpsc;
use tokio::time::timeout;
use tracing::{debug, error, info, warn};
"""use super::{
    anvil_client::AnvilClient,
    core::{
        CommandTestResult, ContractInteractionResult, DataValidationResult, TestDetails,
        TestError, TestResult, TuiTestResult, UiStateValidationResult,
    },
    output_parser::{ParsedTuiOutput, TuiOutputParser},
};
use crate::{
    core::{IntegrationTester, TuiCommandResult},
    TuiDataValidator, TuiKeyInput,
};
use anyhow::{anyhow, Result};
use async_trait::async_trait;
use std::{
    collections::HashMap,
    process::Stdio,
    sync::{Arc, Mutex},
    time::Duration,
};
use tokio::{
    io::{AsyncBufReadExt, BufReader},
    process::{Child, Command},
    time::timeout,
};
""

/// TUI command that interacts with contracts
#[derive(Debug, Clone)]
pub struct TuiContractCommand {
    pub name: String,
    pub key_sequence: Vec<TuiKeyInput>,
    pub expected_output_patterns: Vec<String>,
    pub timeout_seconds: u64,
    pub description: String,
    pub contract_interaction: bool,
}

/// TUI key input representation
#[derive(Debug, Clone)]
pub enum TuiKeyInput {
    Char(char),
    Tab,
    Enter,
    Escape,
    Up,
    Down,
    Left,
    Right,
    CtrlC,
    Wait(u64), // Wait for milliseconds
}

/// Result of TUI command execution
#[derive(Debug, Clone)]
pub struct TuiCommandResult {
    pub command_name: String,
    pub success: bool,
    pub execution_time_ms: u64,
    pub output_captured: String,
    pub error_message: Option<String>,
    pub contract_interaction_detected: bool,
    pub data_validation_results: Vec<DataValidationResult>,
}

/// Data validation result for TUI outputs
#[derive(Debug, Clone)]
pub struct DataValidationResult {
    pub data_type: String,
    pub expected_value: String,
    pub actual_value: String,
    pub matches: bool,
    pub validation_method: String,
}

/// TUI functionality tester implementation
pub struct TuiFunctionalityTester {
    anvil_url: String,
    contract_address: String,
    tui_binary_path: String,
    command_definitions: HashMap<String, TuiContractCommand>,
    output_buffer: Vec<String>,
    data_validator: Option<Arc<TuiDataValidator>>,
}

impl TuiFunctionalityTester {
    pub fn new(anvil_url: String, contract_address: String) -> Self {
        let mut tester = Self {
            anvil_url,
            contract_address,
            tui_binary_path: "cargo".to_string(),
            command_definitions: HashMap::new(),
            output_buffer: Vec::new(),
            data_validator: None,
        };
        
        tester.initialize_command_definitions();
        tester
    }

    /// Set up data validator with Anvil client
    pub fn set_data_validator(&mut self, anvil_client: Arc<AnvilClient>) -> Result<()> {
        let contract_address: Address = self.contract_address.parse()
            .context("Failed to parse contract address ")?;
        
        let data_validator = Arc::new(TuiDataValidator::new(anvil_client, contract_address));
        self.data_validator = Some(data_validator);
        
        info!("Data validator initialized for contract: {:?}", contract_address);
        Ok(())
    }

    /// Initialize all TUI commands that interact with contracts
    fn initialize_command_definitions(&mut self) {
        // Emergency stop command
        self.command_definitions.insert(
            "emergency_stop".to_string(),
            TuiContractCommand {
                name: "emergency_stop".to_string(),
                key_sequence: vec![
                    TuiKeyInput::Char('2'), // Navigate to Operations tab
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Tab,       // Navigate to Master Control panel
                    TuiKeyInput::Tab,
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Char('e'), // Emergency stop
                    TuiKeyInput::Wait(1000),
                ],
                expected_output_patterns: vec![
                    "EMERGENCY STOP ACTIVATED ".to_string(),
                    "command sent to all services ".to_string(),
                ],
                timeout_seconds: 10,
                description: "Emergency stop command that halts all trading operations ".to_string(),
                contract_interaction: true,
            },
        );

        // Pause bot command
        self.command_definitions.insert(
            "pause_bot".to_string(),
            TuiContractCommand {
                name: "pause_bot".to_string(),
                key_sequence: vec![
                    TuiKeyInput::Char('2'), // Navigate to Operations tab
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Tab,       // Navigate to Master Control panel
                    TuiKeyInput::Tab,
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Char('p'), // Pause bot
                    TuiKeyInput::Wait(1000),
                ],
                expected_output_patterns: vec![
                    "Bot PAUSED ".to_string(),
                    "command sent to ExecutionManager ".to_string(),
                ],
                timeout_seconds: 10,
                description: "Pause bot command that temporarily halts trading ".to_string(),
                contract_interaction: true,
            },
        );

        // Restart bot command
        self.command_definitions.insert(
            "restart_bot".to_string(),
            TuiContractCommand {
                name: "restart_bot".to_string(),
                key_sequence: vec![
                    TuiKeyInput::Char('2'), // Navigate to Operations tab
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Tab,       // Navigate to Master Control panel
                    TuiKeyInput::Tab,
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Char('r'), // Restart bot
                    TuiKeyInput::Wait(1000),
                ],
                expected_output_patterns: vec![
                    "Bot RESTARTED ".to_string(),
                    "resuming operations ".to_string(),
                ],
                timeout_seconds: 10,
                description: "Restart bot command that resumes trading operations ".to_string(),
                contract_interaction: true,
            },
        );

        // Execute opportunity command (via inspector)
        self.command_definitions.insert(
            "execute_opportunity".to_string(),
            TuiContractCommand {
                name: "execute_opportunity".to_string(),
                key_sequence: vec![
                    TuiKeyInput::Char('2'), // Navigate to Operations tab
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Down,      // Select an opportunity
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Char('i'), // Open inspector
                    TuiKeyInput::Wait(1000),
                    TuiKeyInput::Char('e'), // Execute opportunity
                    TuiKeyInput::Wait(2000),
                ],
                expected_output_patterns: vec![
                    "Executing opportunity ".to_string(),
                    "Est. Profit ".to_string(),
                ],
                timeout_seconds: 15,
                description: "Execute opportunity command via strategy inspector ".to_string(),
                contract_interaction: true,
            },
        );

        // Balance query commands
        self.command_definitions.insert(
            "query_balances".to_string(),
            TuiContractCommand {
                name: "query_balances".to_string(),
                key_sequence: vec![
                    TuiKeyInput::Char('1'), // Navigate to Dashboard tab
                    TuiKeyInput::Wait(1000),
                    TuiKeyInput::Tab,       // Cycle through dashboard panels
                    TuiKeyInput::Wait(1000),
                ],
                expected_output_patterns: vec![
                    "Balance".to_string(),
                    "USD".to_string(),
                    "Available".to_string(),
                ],
                timeout_seconds: 10,
                description: "Query balance information from dashboard ".to_string(),
                contract_interaction: true,
            },
        );

        // Contract status query
        self.command_definitions.insert(
            "query_contract_status".to_string(),
            TuiContractCommand {
                name: "query_contract_status".to_string(),
                key_sequence: vec![
                    TuiKeyInput::Char('3'), // Navigate to Systems tab
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Tab,       // Navigate to Components panel
                    TuiKeyInput::Wait(1000),
                    TuiKeyInput::Down,      // Navigate through components
                    TuiKeyInput::Wait(500),
                    TuiKeyInput::Enter,     // Show component details
                    TuiKeyInput::Wait(1000),
                ],
                expected_output_patterns: vec![
                    "Contract".to_string(),
                    "Status".to_string(),
                    "Connected".to_string(),
                ],
                timeout_seconds: 10,
                description: "Query contract connection status from systems panel ".to_string(),
                contract_interaction: true,
            },
        );
    }

    /// Execute a specific TUI command by name
    pub async fn execute_command(&mut self, command_name: &str) -> Result<TuiCommandResult> {
        let command_def = self.command_definitions.get(command_name)
            .ok_or_else(|| anyhow::anyhow!("Unknown command: {}", command_name))?
            .clone();

        info!("Executing TUI command: {} - {}", command_name, command_def.description);
        
        let start_time = Instant::now();
        
        // Spawn TUI process
        let mut tui_process = self.spawn_tui_process().await
            .context("Failed to spawn TUI process ")?;

        // Execute key sequence
        let execution_result = self.execute_key_sequence(
            &mut tui_process,
            &command_def.key_sequence,
            &command_def.expected_output_patterns,
            Duration::from_secs(command_def.timeout_seconds),
        ).await;

        // Cleanup process
        let _ = tui_process.kill().await;

        let execution_time = start_time.elapsed().as_millis() as u64;

        match execution_result {
            Ok(output) => {
                info!("TUI command "{}" executed successfully in {}ms ", command_name, execution_time);
                
                // Validate output patterns
                let contract_interaction_detected = self.detect_contract_interaction(&output);
                let data_validation_results = self.validate_output_data(&output, &command_def).await?;

                Ok(TuiCommandResult {
                    command_name: command_name.to_string(),
                    success: true,
                    execution_time_ms: execution_time,
                    output_captured: output,
                    error_message: None,
                    contract_interaction_detected,
                    data_validation_results,
                })
            }
            Err(e) => {
                error!("TUI command "{}" failed after {}ms: {}", command_name, execution_time, e);
                
                Ok(TuiCommandResult {
                    command_name: command_name.to_string(),
                    success: false,
                    execution_time_ms: execution_time,
                    output_captured: String::new(),
                    error_message: Some(e.to_string()),
                    contract_interaction_detected: false,
                    data_validation_results: Vec::new(),
                })
            }
        }
    }

    /// Spawn TUI process with proper configuration
    async fn spawn_tui_process(&self) -> Result<Child> {
        info!("Spawning TUI harness process...");
        
        let mut cmd = TokioCommand::new("cargo");
        cmd.args(&["run", "--bin ", "tui_harness"])
            .env("NATS_URL", "nats://localhost:4222")
            .env("ANVIL_URL", &self.anvil_url)
            .env("CONTRACT_ADDRESS", &self.contract_address)
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .kill_on_drop(true);

        let child = cmd.spawn()
            .context("Failed to spawn TUI harness process")?;

        // Wait a moment for TUI to initialize
        tokio::time::sleep(Duration::from_millis(2000)).await;

        Ok(child)
    }

    async fn execute_key_sequence(
        &mut self,
        process: &mut Child,
        key_sequence: &[TuiKeyInput],
        expected_patterns: &[String],
        timeout_duration: Duration,
    ) -> Result<String> {
        let mut output_buffer = String::new();
        let (tx, mut rx) = mpsc::channel(100);

        // Start output capture task
        if let Some(stdout) = process.stdout.take() {
            let tx_clone = tx.clone();
            tokio::spawn(async move {
                let mut reader = BufReader::new(stdout);
                let mut line = String::new();
                while reader.read_line(&mut line).await.is_ok() {
                    if !line.trim().is_empty() {
                        let _ = tx_clone.send(line.clone()).await;
                        line.clear();
                    }
                }
            });
        }

        // Start stderr capture task
        if let Some(stderr) = process.stderr.take() {
            let tx_clone = tx.clone();
            tokio::spawn(async move {
                let mut reader = BufReader::new(stderr);
                let mut line = String::new();
                while reader.read_line(&mut line).await.is_ok() {
                    if !line.trim().is_empty() {
                        let _ = tx_clone.send(format!("STDERR: {}", line)).await;
                        line.clear();
                    }
                }
            });
        }

        // Execute key sequence
        if let Some(stdin) = process.stdin.as_mut() {
            for key_input in key_sequence {
                match key_input {
                    TuiKeyInput::Char(c) => {
                        debug!("Sending key: '{}'", c);
                        // Note: In a real implementation, we'd need to send raw terminal input
                        // For now, we'll simulate by writing to stdin
                        use tokio::io::AsyncWriteExt;
                        stdin.write_all(&[*c as u8]).await?;
                    }
                    TuiKeyInput::Tab => {
                        debug!("Sending Tab key");
                        stdin.write_all(&[9]).await?; // Tab character
                    }
                    TuiKeyInput::Enter => {
                        debug!("Sending Enter key");
                        stdin.write_all(&[13]).await?; // Carriage return
                    }
                    TuiKeyInput::Escape => {
                        debug!("Sending Escape key");
                        stdin.write_all(&[27]).await?; // Escape character
                    }
                    TuiKeyInput::Up => {
                        debug!("Sending Up arrow");
                        stdin.write_all(&[27, 91, 65]).await?; // ESC[A
                    }
                    TuiKeyInput::Down => {
                        debug!("Sending Down arrow");
                        stdin.write_all(&[27, 91, 66]).await?; // ESC[B
                    }
                    TuiKeyInput::Left => {
                        debug!("Sending Left arrow");
                        stdin.write_all(&[27, 91, 68]).await?; // ESC[D
                    }
                    TuiKeyInput::Right => {
                        debug!("Sending Right arrow");
                        stdin.write_all(&[27, 91, 67]).await?; // ESC[C
                    }
                    TuiKeyInput::CtrlC => {
                        debug!("Sending Ctrl+C");
                        stdin.write_all(&[3]).await?; // Ctrl+C
                    }
                    TuiKeyInput::Wait(ms) => {
                        debug!("Waiting for {}ms", ms);
                        tokio::time::sleep(Duration::from_millis(*ms)).await;
                    }
                }
                
                // Small delay between key presses
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
        }

        // Collect output with timeout
        let output_result = timeout(timeout_duration, async {
            let mut collected_output = String::new();
            let mut pattern_matches = 0;
            
            while let Some(line) = rx.recv().await {
                collected_output.push_str(&line);
                
                // Check for expected patterns
                for pattern in expected_patterns {
                    if line.contains(pattern) {
                        pattern_matches += 1;
                        debug!("Found expected pattern: {}", pattern);
                    }
                }
                
                // If we've found all expected patterns, we can return early
                if pattern_matches >= expected_patterns.len() {
                    debug!("All expected patterns found, completing command execution");
                    break;
                }
            }
            
            collected_output
        }).await;

        match output_result {
            Ok(output) => {
                output_buffer = output;
                Ok(output_buffer)
            }
            Err(_) => {
                warn!("TUI command execution timed out after {:?}", timeout_duration);
                Err(anyhow::anyhow!("Command execution timed out"))
            }
        }
    }

    /// Detect if the output indicates contract interaction
    fn detect_contract_interaction(&self, output: &str) -> bool {
        let contract_indicators = [
            "transaction",
            "contract",
            "0x", // Ethereum addresses/hashes
            "gas",
            "wei",
            "gwei",
            "block",
            "StargateCompass",
            "emergency_stop",
            "pause_trading",
        ];

        contract_indicators.iter().any(|indicator| {
            output.to_lowercase().contains(&indicator.to_lowercase())
        })
    }

    /// Validate output data against expected values using comprehensive data validator
    async fn validate_output_data(
        &self,
        output: &str,
        command_def: &TuiContractCommand,
    ) -> Result<Vec<DataValidationResult>> {
        let mut validation_results = Vec::new();

        // Use comprehensive data validator if available
        if let Some(data_validator) = &self.data_validator {
            match command_def.name.as_str() {
                "query_balances" => {
                    // Validate balance display against on-chain data
                    let contract_address: Address = self.contract_address.parse()
                        .context("Failed to parse contract address")?;
                    
                    match data_validator.validate_balance_display(output, contract_address).await {
                        Ok(result) => validation_results.push(result),
                        Err(e) => {
                            warn!("Balance validation failed: {}", e);
                            validation_results.push(self.validate_balance_format(output));
                        }
                    }
                }
                "query_contract_status" => {
                    // Validate contract status display against on-chain state
                    match data_validator.validate_contract_status_display(output).await {
                        Ok(result) => validation_results.push(result),
                        Err(e) => {
                            warn!("Contract status validation failed: {}", e);
                            validation_results.push(self.validate_contract_status(output));
                        }
                    }
                }
                _ => {
                    // For other commands, use basic validation
                    if command_def.name == "query_balances" {
                        validation_results.push(self.validate_balance_format(output));
                    } else if command_def.name == "query_contract_status" {
                        validation_results.push(self.validate_contract_status(output));
                    }
                }
            }
        } else {
            // Fallback to basic validation if data validator not available
            if command_def.name == "query_balances" {
                validation_results.push(self.validate_balance_format(output));
            } else if command_def.name == "query_contract_status" {
                validation_results.push(self.validate_contract_status(output));
            }
        }

        // For control commands, validate command acknowledgment
        if command_def.contract_interaction && 
           ["emergency_stop", "pause_bot", "restart_bot"].contains(&command_def.name.as_str()) {
            validation_results.push(self.validate_command_acknowledgment(output));
        }

        Ok(validation_results)
    }

    /// Run comprehensive data validation on TUI output
    pub async fn validate_comprehensive_output(
        &self,
        tui_output: &str,
        addresses_to_check: Vec<Address>,
        transactions_to_check: Vec<H256>,
    ) -> Result<Vec<DataValidationResult>> {
        if let Some(data_validator) = &self.data_validator {
            let validation_config = ValidationConfig {
                validate_balances: true,
                validate_contract_status: true,
                validate_transaction_history: true,
                addresses_to_check,
                transactions_to_check,
                expected_transaction_count: 10,
            };

            data_validator.validate_comprehensive_tui_output(tui_output, validation_config).await
        } else {
            Err(anyhow::anyhow!("Data validator not initialized"))
        }
    }

    /// Validate specific transaction status in TUI output
    pub async fn validate_transaction_status(
        &self,
        tui_output: &str,
        tx_hash: H256,
    ) -> Result<DataValidationResult> {
        if let Some(data_validator) = &self.data_validator {
            data_validator.validate_transaction_status_display(tui_output, tx_hash).await
        } else {
            Err(anyhow::anyhow!("Data validator not initialized"))
        }
    }

    /// Validate transaction history display in TUI output
    pub async fn validate_transaction_history(
        &self,
        tui_output: &str,
        expected_count: usize,
    ) -> Result<DataValidationResult> {
        if let Some(data_validator) = &self.data_validator {
            data_validator.validate_transaction_history_display(tui_output, expected_count).await
        } else {
            Err(anyhow::anyhow!("Data validator not initialized"))
        }
    }

    /// Validate balance display for specific address
    pub async fn validate_balance_for_address(
        &self,
        tui_output: &str,
        address: Address,
    ) -> Result<DataValidationResult> {
        if let Some(data_validator) = &self.data_validator {
            data_validator.validate_balance_display(tui_output, address).await
        } else {
            Err(anyhow::anyhow!("Data validator not initialized"))
        }
    }

    /// Validate balance format in output
    fn validate_balance_format(&self, output: &str) -> DataValidationResult {
        // Look for balance patterns like "$1,234.56" or "1.234 ETH"
        let balance_regex = regex::Regex::new(r"(\$[\d,]+\.?\d*)|(\d+\.?\d*\s+[A-Z]{3,4})").unwrap();
        let has_balance = balance_regex.is_match(output);

        DataValidationResult {
            data_type: "balance_format".to_string(),
            expected_value: "Valid balance format".to_string(),
            actual_value: if has_balance { "Found valid balance" } else { "No valid balance found" }.to_string(),
            matches: has_balance,
            validation_method: "regex_pattern_match".to_string(),
        }
    }

    /// Validate contract status indicators
    fn validate_contract_status(&self, output: &str) -> DataValidationResult {
        let status_indicators = ["Connected", "Active", "Running", "Online"];
        let has_status = status_indicators.iter().any(|status| output.contains(status));

        DataValidationResult {
            data_type: "contract_status".to_string(),
            expected_value: "Contract status indicator".to_string(),
            actual_value: if has_status { "Status indicator found" } else { "No status indicator" }.to_string(),
            matches: has_status,
            validation_method: "keyword_search".to_string(),
        }
    }

    /// Validate command acknowledgment
    fn validate_command_acknowledgment(&self, output: &str) -> DataValidationResult {
        let ack_indicators = ["command sent", "ACTIVATED", "PAUSED", "RESTARTED"];
        let has_ack = ack_indicators.iter().any(|ack| output.contains(ack));

        DataValidationResult {
            data_type: "command_acknowledgment".to_string(),
            expected_value: "Command acknowledgment".to_string(),
            actual_value: if has_ack { "Acknowledgment found" } else { "No acknowledgment" }.to_string(),
            matches: has_ack,
            validation_method: "keyword_search".to_string(),
        }
    }

    /// Execute all contract-related TUI commands
    pub async fn test_all_contract_commands(&mut self) -> Result<Vec<TuiCommandResult>> {
        let mut results = Vec::new();
        
        info!("Starting comprehensive TUI contract command testing...");

        // Get all command names
        let command_names: Vec<String> = self.command_definitions.keys().cloned().collect();

        for command_name in command_names {
            info!("Testing TUI command: {}", command_name);
            
            match self.execute_command(&command_name).await {
                Ok(result) => {
                    if result.success {
                        info!("✅ Command '{}' executed successfully", command_name);
                    } else {
                        warn!("❌ Command '{}' failed: {:?}", command_name, result.error_message);
                    }
                    results.push(result);
                }
                Err(e) => {
                    error!("Failed to execute command '{}': {}", command_name, e);
                    results.push(TuiCommandResult {
                        command_name: command_name.clone(),
                        success: false,
                        execution_time_ms: 0,
                        output_captured: String::new(),
                        error_message: Some(e.to_string()),
                        contract_interaction_detected: false,
                        data_validation_results: Vec::new(),
                    });
                }
            }

            // Wait between commands to avoid overwhelming the system
            tokio::time::sleep(Duration::from_millis(1000)).await;
        }

        info!("Completed TUI contract command testing. Results: {}/{} successful", 
              results.iter().filter(|r| r.success).count(), 
              results.len());

        Ok(results)
    }

    /// Get list of available TUI commands
    pub fn get_available_commands(&self) -> Vec<String> {
        self.command_definitions.keys().cloned().collect()
    }

    /// Get command definition by name
    pub fn get_command_definition(&self, command_name: &str) -> Option<&TuiContractCommand> {
        self.command_definitions.get(command_name)
    }
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_tui_command_definitions() {
        let tester = TuiFunctionalityTester::new(
            "http://localhost:8545".to_string(),
            "0x1234567890123456789012345678901234567890".to_string(),
        );

        let commands = tester.get_available_commands();
        assert!(!commands.is_empty());
        assert!(commands.contains(&"emergency_stop".to_string()));
        assert!(commands.contains(&"pause_bot".to_string()));
        assert!(commands.contains(&"restart_bot".to_string()));
        assert!(commands.contains(&"execute_opportunity".to_string()));
        assert!(commands.contains(&"query_balances".to_string()));
    }

    #[test]
    fn test_contract_interaction_detection() {
        let tester = TuiFunctionalityTester::new(
            "http://localhost:8545".to_string(),
            "0x1234567890123456789012345678901234567890".to_string(),
        );

        let output_with_contract = "Transaction sent to contract 0x123... with gas 21000";
        let output_without_contract = "System status: All services running";

        assert!(tester.detect_contract_interaction(output_with_contract));
        assert!(!tester.detect_contract_interaction(output_without_contract));
    }
}