// src/validation/contract_integration_validator_demo.rs

//! Demo and examples for the contract integration validator

use crate::validation::{
    ContractIntegrationValidator, ContractIntegrationConfig, ContractAddresses,
    ValidationFramework, ValidationConfig
};
use ethers::types::Address;
use std::time::Duration;
use tracing::{info, warn, error};

/// Run a comprehensive demo of the contract integration validator
pub async fn run_contract_integration_demo() -> crate::error::Result<()> {
    info!("Starting Contract Integration Validator Demo");
    
    println!("🔧 Contract Integration Validator Demo");
    println!("=====================================\n");
    
    let config = ContractIntegrationConfig::default();
    let mut validator = ContractIntegrationValidator::new(config);
    
    // Start Anvil
    println!("🚀 Starting Anvil simulation environment...");
    match validator.start_anvil().await {
        Ok(()) => {
        println!("✅ Anvil started successfully");
        }
        Err(e) => {
            error!("Failed to start Anvil: {}", e);
        println!("❌ Failed to start Anvil: {}", e);
            return Ok(());
        }
    }
    
    // Run comprehensive validation
    match validator.validate_all_integrations().await {
        Ok(result) => {
        println!("Overall Status: {}", result.status);
        println!("Total Execution Time: {}ms", result.execution_time.as_millis());
            
            let metrics = &result.metrics;
        println!("📊 Comprehensive Metrics:");
        println!("  - Total Contracts Validated: {}", metrics.total_contracts_validated);
        println!("  - Total Transactions Simulated: {}", metrics.total_transactions_simulated);
        println!("  - Error Handling Effectiveness: {:.1}%", metrics.error_handling_effectiveness * 100.0);
        }
        Err(e) => {
        println!("❌ Comprehensive validation failed: {}", e);
        }
    }
    
    // Stop Anvil
    let _ = validator.stop_anvil().await;
    println!("✅ Demo complete");
    
    Ok(())
}

/// Run a quick validation test for development
pub async fn run_quick_contract_validation() -> crate::error::Result<()> {
    println!("⚡ Quick Contract Validation Test");
    
    let config = ContractIntegrationConfig::default();
    let mut validator = ContractIntegrationValidator::new(config);
    
    match validator.start_anvil().await {
        Ok(()) => {
        println!("✅ Anvil started");
            
            match validator.validate_contract_addresses().await {
                Ok(result) => {
                println!("✅ Contract validation: {}", result.status);
                    let metrics = result.metrics;
                println!("   Validated {} contracts", metrics.total_contracts_validated);
                }
                Err(e) => {
                println!("❌ Validation failed: {}", e);
                }
            }
            
            let _ = validator.stop_anvil().await;
        }
        Err(e) => {
        println!("❌ Could not start Anvil: {}", e);
        }
    }
    
    Ok(())
}

/// Demonstrate contract integration validation using the validation framework
pub async fn run_framework_integration_demo() -> crate::error::Result<()> {
    println!("🏗️  Framework Integration Demo");
    
    let validation_config = ValidationConfig::default();
    let framework = ValidationFramework::new(validation_config)?;
    
    let integration_config = ContractIntegrationConfig::default();
    let mut validator = ContractIntegrationValidator::new(integration_config);
    
    match validator.start_anvil().await {
        Ok(()) => {
            let result = framework.execute_validation(
                "contract_integration_framework_demo",
                || async {
                    validator.validate_all_integrations().await
                }
            ).await?;
            
        println!("📊 Framework Validation Result:");
        println!("  - Test ID: {}", result.test_id);
        println!("  - Status: {}", result.status);
            
            let _ = validator.stop_anvil().await;
        }
        Err(e) => {
        println!("❌ Failed to start Anvil: {}", e);
        }
    }
    
    Ok(())
}

/// Show usage examples for the contract integration validator
pub fn show_usage_examples() {
    println!("📚 Contract Integration Validator Usage Examples");
    println!("===============================================\n");
    
    println!("Basic Usage:");
    println!("let config = ContractIntegrationConfig::default();");
    println!("let mut validator = ContractIntegrationValidator::new(config);");
    println!("validator.start_anvil().await?;");
    println!("let result = validator.validate_all_integrations().await?;");
    println!("validator.stop_anvil().await?;");
}