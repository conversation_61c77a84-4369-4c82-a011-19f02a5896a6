#!/bin/bash

# Final Integration Testing Script for Aetheric Resonance Engine Fixes
# This script orchestrates comprehensive testing to validate all audit findings have been addressed

set -e

echo "🚀 Starting Final Integration Testing Suite"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
RUST_LOG=${RUST_LOG:-info}
RUST_BACKTRACE=${RUST_BACKTRACE:-1}
TEST_THREADS=${TEST_THREADS:-4}
TIMEOUT=${TIMEOUT:-600} # 10 minutes

# Export environment variables
export RUST_LOG
export RUST_BACKTRACE

echo -e "${BLUE}Configuration:${NC}"
echo "  RUST_LOG: $RUST_LOG"
echo "  RUST_BACKTRACE: $RUST_BACKTRACE"
echo "  TEST_THREADS: $TEST_THREADS"
echo "  TIMEOUT: ${TIMEOUT}s"
echo ""

# Function to run test with timeout and capture output
run_test_with_timeout() {
    local test_name=$1
    local test_command=$2
    
    echo -e "${BLUE}Running: $test_name${NC}"
    echo "Command: $test_command"
    echo ""
    
    if timeout $TIMEOUT bash -c "$test_command"; then
        echo -e "${GREEN}✅ $test_name PASSED${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name FAILED${NC}"
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    echo -e "${BLUE}Checking prerequisites...${NC}"
    
    # Check if cargo is available
    if ! command -v cargo &> /dev/null; then
        echo -e "${RED}❌ Cargo not found. Please install Rust.${NC}"
        exit 1
    fi
    
    # Check if project builds
    if ! cargo check --quiet; then
        echo -e "${RED}❌ Project does not compile. Please fix compilation errors first.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Prerequisites check passed${NC}"
    echo ""
}

# Function to run unit tests first
run_unit_tests() {
    echo -e "${YELLOW}Phase 1: Unit Tests${NC}"
    echo "==================="
    
    run_test_with_timeout \
        "Unit Tests" \
        "cargo test --lib --bins --tests --exclude final_integration_test --exclude load_testing_suite -- --test-threads=$TEST_THREADS"
    
    echo ""
}

# Function to run integration tests
run_integration_tests() {
    echo -e "${YELLOW}Phase 2: Integration Tests${NC}"
    echo "=========================="
    
    run_test_with_timeout \
        "Integration Tests" \
        "cargo test --test integration -- --test-threads=$TEST_THREADS"
    
    echo ""
}

# Function to run final integration tests
run_final_integration_tests() {
    echo -e "${YELLOW}Phase 3: Final Integration Tests${NC}"
    echo "================================="
    
    # Test complete system integration
    run_test_with_timeout \
        "Complete System Integration" \
        "cargo test --test final_integration_test test_complete_system_integration -- --nocapture"
    
    # Test end-to-end with market data
    run_test_with_timeout \
        "End-to-End Market Data Testing" \
        "cargo test --test final_integration_test test_end_to_end_with_market_data -- --nocapture"
    
    # Test audit findings validation
    run_test_with_timeout \
        "Audit Findings Validation" \
        "cargo test --test final_integration_test test_audit_findings_validation -- --nocapture"
    
    # Test system resilience
    run_test_with_timeout \
        "System Resilience Testing" \
        "cargo test --test final_integration_test test_system_resilience -- --nocapture"
    
    echo ""
}

# Function to run load tests
run_load_tests() {
    echo -e "${YELLOW}Phase 4: Load Testing${NC}"
    echo "===================="
    
    run_test_with_timeout \
        "Load Testing Under Production Conditions" \
        "cargo test --test final_integration_test test_load_testing_production_conditions -- --nocapture"
    
    # Run dedicated load testing suite
    run_test_with_timeout \
        "Comprehensive Load Testing Suite" \
        "cargo test --test load_testing_suite -- --nocapture --test-threads=1"
    
    echo ""
}

# Function to run performance benchmarks
run_performance_benchmarks() {
    echo -e "${YELLOW}Phase 5: Performance Benchmarks${NC}"
    echo "==============================="
    
    # Run benchmarks if available
    if cargo test --benches --no-run &> /dev/null; then
        run_test_with_timeout \
            "Performance Benchmarks" \
            "cargo test --benches -- --nocapture"
    else
        echo -e "${YELLOW}⚠ No benchmarks found, skipping performance benchmarks${NC}"
    fi
    
    echo ""
}

# Function to generate test report
generate_test_report() {
    echo -e "${YELLOW}Phase 6: Test Report Generation${NC}"
    echo "==============================="
    
    local report_file="target/final_integration_test_report.txt"
    
    echo "Final Integration Test Report" > $report_file
    echo "Generated: $(date)" >> $report_file
    echo "=============================================" >> $report_file
    echo "" >> $report_file
    
    # Add system information
    echo "System Information:" >> $report_file
    echo "  OS: $(uname -s)" >> $report_file
    echo "  Architecture: $(uname -m)" >> $report_file
    echo "  Rust Version: $(rustc --version)" >> $report_file
    echo "  Cargo Version: $(cargo --version)" >> $report_file
    echo "" >> $report_file
    
    # Add test configuration
    echo "Test Configuration:" >> $report_file
    echo "  RUST_LOG: $RUST_LOG" >> $report_file
    echo "  TEST_THREADS: $TEST_THREADS" >> $report_file
    echo "  TIMEOUT: ${TIMEOUT}s" >> $report_file
    echo "" >> $report_file
    
    # Run test coverage if available
    if command -v cargo-tarpaulin &> /dev/null; then
        echo "Running test coverage analysis..."
        cargo tarpaulin --out Xml --output-dir target/ --timeout $TIMEOUT || true
        
        if [ -f "target/cobertura.xml" ]; then
            echo "Test coverage report generated: target/cobertura.xml"
            echo "Test Coverage: Available in target/cobertura.xml" >> $report_file
        fi
    else
        echo -e "${YELLOW}⚠ cargo-tarpaulin not found, skipping coverage analysis${NC}"
        echo "Test Coverage: Skipped (cargo-tarpaulin not available)" >> $report_file
    fi
    
    echo "" >> $report_file
    echo "All tests completed successfully!" >> $report_file
    
    echo -e "${GREEN}✅ Test report generated: $report_file${NC}"
    echo ""
}

# Function to validate audit findings specifically
validate_audit_findings() {
    echo -e "${YELLOW}Phase 7: Audit Findings Validation${NC}"
    echo "==================================="
    
    echo "Validating specific audit findings have been addressed:"
    
    # Check for scoring engine weight usage
    echo "  • Scoring engine weight usage..."
    if cargo test --test final_integration_test validate_scoring_engine_weight_usage -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    # Check for geometric score completeness
    echo "  • Geometric score completeness..."
    if cargo test --test final_integration_test validate_geometric_score_completeness -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    # Check for neutral score fallbacks
    echo "  • Neutral score fallbacks..."
    if cargo test --test final_integration_test validate_neutral_score_fallbacks -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    # Check for vesica piscis negative handling
    echo "  • Vesica piscis negative handling..."
    if cargo test --test final_integration_test validate_vesica_piscis_negative_handling -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    # Check for Hurst exponent calculation
    echo "  • Hurst exponent calculation..."
    if cargo test --test final_integration_test validate_hurst_exponent_calculation -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    # Check for market rhythm stability
    echo "  • Market rhythm stability..."
    if cargo test --test final_integration_test validate_market_rhythm_stability -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    # Check for network integration
    echo "  • Network integration..."
    if cargo test --test final_integration_test validate_network_integration -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    # Check for centrality scores population
    echo "  • Centrality scores population..."
    if cargo test --test final_integration_test validate_centrality_scores_population -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    # Check for temporal harmonics integration
    echo "  • Temporal harmonics integration..."
    if cargo test --test final_integration_test validate_temporal_harmonics_integration -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    # Check for configuration validation
    echo "  • Configuration validation..."
    if cargo test --test final_integration_test validate_configuration_validation -- --nocapture; then
        echo -e "    ${GREEN}✅ FIXED${NC}"
    else
        echo -e "    ${RED}❌ FAILED${NC}"
    fi
    
    echo ""
}

# Main execution flow
main() {
    local start_time=$(date +%s)
    local failed_tests=0
    
    echo "Starting final integration testing at $(date)"
    echo ""
    
    # Run all test phases
    check_prerequisites || ((failed_tests++))
    run_unit_tests || ((failed_tests++))
    run_integration_tests || ((failed_tests++))
    run_final_integration_tests || ((failed_tests++))
    run_load_tests || ((failed_tests++))
    run_performance_benchmarks || ((failed_tests++))
    validate_audit_findings || ((failed_tests++))
    generate_test_report
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "============================================="
    echo -e "${BLUE}Final Integration Testing Summary${NC}"
    echo "============================================="
    echo "Total execution time: ${duration}s"
    echo "Failed test phases: $failed_tests"
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED! System is ready for production.${NC}"
        echo ""
        echo "✅ Complete system integration validated"
        echo "✅ End-to-end functionality with real market data confirmed"
        echo "✅ Load testing under production conditions successful"
        echo "✅ All audit findings have been properly addressed"
        echo "✅ System resilience and error handling validated"
        echo ""
        echo "The Aetheric Resonance Engine fixes have been successfully implemented and tested."
        exit 0
    else
        echo -e "${RED}❌ $failed_tests test phase(s) failed. Please review the output above.${NC}"
        echo ""
        echo "Please address the failing tests before proceeding to production."
        exit 1
    fi
}

# Handle script interruption
trap 'echo -e "\n${YELLOW}⚠ Test execution interrupted${NC}"; exit 130' INT TERM

# Execute main function
main "$@"