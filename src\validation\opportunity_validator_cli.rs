// src/validation/opportunity_validator_cli.rs

//! CLI commands for opportunity validation

use crate::error::BasiliskError;
use crate::validation::{
    ValidationFramework, ValidationConfig, OpportunityValidator, TestDataProvider,
    MarketConditions, NetworkCongestionLevel, LiquidityDistribution
};
use crate::shared_types::MarketRegime;
use clap::{Args, Subcommand};
use rust_decimal_macros::dec;
use std::sync::Arc;
use tracing::{info, error};

/// CLI arguments for opportunity validation
#[derive(Debug, Args)]
pub struct OpportunityValidationArgs {
    #[command(subcommand)]
    pub command: OpportunityValidationCommand,
}

/// Opportunity validation subcommands
#[derive(Debug, Subcommand)]
pub enum OpportunityValidationCommand {
    /// Run comprehensive opportunity detection validation
    DetectionValidation {
        /// Market regime to test (bull, bear, volatile, stable)
        #[arg(long, default_value = "stable")]
        market_regime: String,
        
        /// Volatility level (0.0 to 1.0)
        #[arg(long, default_value = "0.1")]
        volatility: f64,
        
        /// Generate detailed report
        #[arg(long)]
        detailed: bool,
    },
    
    /// Test specific scanner performance
    ScannerPerformance {
        /// Scanner name (SwapScanner, MempoolScanner, GazeScanner)
        #[arg(long)]
        scanner: String,
        
        /// Test duration in seconds
        #[arg(long, default_value = "60")]
        duration: u64,
    },
}

/// Handle opportunity validation commands
pub async fn handle_opportunity_validation_command(
    args: OpportunityValidationArgs,
) -> Result<(), BasiliskError> {
    match args.command {
        OpportunityValidationCommand::DetectionValidation { market_regime, volatility, detailed } => {
            run_detection_validation(&market_regime, volatility, detailed).await
        }
        OpportunityValidationCommand::ScannerPerformance { scanner, duration } => {
            run_scanner_performance_test(&scanner, duration).await
        }
    }
}

/// Run comprehensive opportunity detection validation
async fn run_detection_validation(
    market_regime_str: &str,
    volatility: f64,
    detailed: bool,
) -> Result<(), BasiliskError> {
    info!("Starting opportunity detection validation");
    
    // Parse market regime
    let market_regime = match market_regime_str.to_lowercase().as_str() {
        "calm" | "stable" => MarketRegime::CalmOrderly,
        "fomo" | "bull" => MarketRegime::RetailFomoSpike,
        "gas-war" | "competitive" => MarketRegime::BotGasWar,
        "volatile" | "correction" => MarketRegime::HighVolatilityCorrection,
        "trending" => MarketRegime::Trending,
        _ => {
            error!("Invalid market regime: {}. Use: calm, fomo, gas-war, volatile, trending", market_regime_str);
            return Err(BasiliskError::strategy("Invalid market regime"));
        }
    };
    
    // Create test data provider
    let test_data_provider = Arc::new(TestDataProvider::new()?);
    
    // Create opportunity validator
    let opportunity_validator = OpportunityValidator::new(test_data_provider)?;
    
    // Create market conditions
    let market_conditions = MarketConditions {
        regime: market_regime.clone(),
        volatility: rust_decimal::Decimal::from_f64_retain(volatility).unwrap_or(dec!(0.1)),
        gas_price_gwei: dec!(20.0),
        network_congestion: NetworkCongestionLevel::Low,
        temporal_harmonics: None,
        network_resonance: None,
        liquidity_distribution: LiquidityDistribution::Concentrated,
    };
    
    // Run validation
    let validation_result = opportunity_validator.validate_opportunity_detection(&market_conditions).await?;
    
    // Display results
    println!("\n=== Opportunity Detection Validation Results ===");
    println!("Market Regime: {:?}", market_regime);
    println!("Volatility: {:.1}%", volatility * 100.0);
    println!();
    
    println!("Detection Metrics:");
    println!("  Total Opportunities Detected: {}", validation_result.total_opportunities_detected);
    println!("  True Positives: {}", validation_result.true_positives);
    println!("  False Positives: {}", validation_result.false_positives);
    println!("  False Negatives: {}", validation_result.false_negatives);
    println!("  Profit Accuracy: {:.1}%", validation_result.profit_accuracy_percentage);
    
    println!("\nQuality Metrics:");
    println!("  Precision: {:.1}%", validation_result.quality_metrics.precision * 100.0);
    println!("  Recall: {:.1}%", validation_result.quality_metrics.recall * 100.0);
    println!("  F1 Score: {:.3}", validation_result.quality_metrics.f1_score);
    println!("  Average Confidence: {:.1}%", validation_result.quality_metrics.average_confidence * 100.0);
    
    if !validation_result.detection_latency_ms.is_empty() {
        let avg_latency = validation_result.detection_latency_ms.iter().sum::<u64>() as f64 
            / validation_result.detection_latency_ms.len() as f64;
        let max_latency = validation_result.detection_latency_ms.iter().max().unwrap_or(&0);
    println!("\nLatency Metrics:");
    println!("  Average Detection Latency: {:.1}ms", avg_latency);
    println!("  Maximum Detection Latency: {}ms", max_latency);
    }
    
    if detailed {
    println!("\nScanner Performance Details:");
        for (scanner_name, metrics) in &validation_result.scanner_performance {
        println!("  {}:", scanner_name);
        println!("    Opportunities/min: {:.1}", metrics.opportunities_per_minute);
        println!("    Avg Processing Time: {:.1}ms", metrics.average_processing_time_ms);
        println!("    Error Rate: {:.1}%", metrics.error_rate * 100.0);
        println!("    Memory Usage: {:.1}MB", metrics.memory_usage_mb);
        println!("    CPU Usage: {:.1}%", metrics.cpu_usage_percent);
        }
    }
    
    // Determine overall success
    let overall_success = validation_result.quality_metrics.precision >= 0.8 
        && validation_result.quality_metrics.recall >= 0.8
        && validation_result.profit_accuracy_percentage >= 90.0;
    
    println!("\n=== Overall Result: {} ===", 
             if overall_success { "PASSED" } else { "FAILED" });
    
    if !overall_success {
    println!("Validation failed. Check precision (≥80%), recall (≥80%), and profit accuracy (≥90%).");
    }
    
    Ok(())
}

/// Run scanner performance test
async fn run_scanner_performance_test(scanner_name: &str, duration_secs: u64) -> Result<(), BasiliskError> {
    info!("Starting scanner performance test for {}", scanner_name);
    
    // Create test data provider
    let test_data_provider = Arc::new(TestDataProvider::new()?);
    
    // Create opportunity validator
    let opportunity_validator = OpportunityValidator::new(test_data_provider)?;
    
    // Run performance test
    let test_duration = std::time::Duration::from_secs(duration_secs);
    let performance_result = opportunity_validator.validate_scanner_performance(scanner_name, test_duration).await?;
    
    // Display results
    println!("\n=== Scanner Performance Test Results ===");
    println!("Scanner: {}", performance_result.scanner_name);
    println!("Test Duration: {}s", performance_result.test_duration.as_secs());
    println!();
    
    println!("Performance Metrics:");
    println!("  Opportunities Processed: {}", performance_result.opportunities_processed);
    println!("  Processing Rate: {:.1} ops/sec", performance_result.processing_rate);
    println!("  Average Latency: {:.1}ms", performance_result.average_latency_ms);
    println!("  95th Percentile Latency: {:.1}ms", performance_result.p95_latency_ms);
    println!("  99th Percentile Latency: {:.1}ms", performance_result.p99_latency_ms);
    println!("  Error Count: {}", performance_result.error_count);
    
    println!("\nResource Usage:");
    println!("  Peak Memory: {:.1}MB", performance_result.resource_usage.peak_memory_mb);
    println!("  Average CPU: {:.1}%", performance_result.resource_usage.average_cpu_percent);
    println!("  Network Bandwidth: {:.1}MB", performance_result.resource_usage.network_bandwidth_mb);
    println!("  Database Queries: {}", performance_result.resource_usage.database_queries);
    
    // Performance thresholds
    let latency_ok = performance_result.average_latency_ms <= 100.0; // 100ms requirement
    let error_rate = performance_result.error_count as f64 / performance_result.opportunities_processed as f64;
    let error_rate_ok = error_rate <= 0.05; // 5% max error rate
    let throughput_ok = performance_result.processing_rate >= 10.0; // 10 ops/sec minimum
    
    println!("\n=== Performance Assessment ===");
    println!("Latency: {} (avg: {:.1}ms, target: ≤100ms)", 
             if latency_ok { "PASS" } else { "FAIL" }, performance_result.average_latency_ms);
    println!("Error Rate: {} ({:.1}%, target: ≤5%)", 
             if error_rate_ok { "PASS" } else { "FAIL" }, error_rate * 100.0);
    println!("Throughput: {} ({:.1} ops/sec, target: ≥10 ops/sec)", 
             if throughput_ok { "PASS" } else { "FAIL" }, performance_result.processing_rate);
    
    let overall_pass = latency_ok && error_rate_ok && throughput_ok;
    println!("\n=== Overall Result: {} ===", if overall_pass { "PASSED" } else { "FAILED" });
    
    Ok(())
}