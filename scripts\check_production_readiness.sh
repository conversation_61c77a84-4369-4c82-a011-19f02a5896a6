#!/bin/bash
# Production readiness check for Aetheric Resonance Engine fixes
# This script validates that the system is ready for production deployment

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/production_readiness_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        WARN)  echo -e "${YELLOW}[WARN]${NC} $message" | tee -a "$LOG_FILE" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        DEBUG) echo -e "${BLUE}[DEBUG]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
}

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

# Production readiness checklist
declare -A CHECKS=(
    ["security_hardening"]="Security hardening validation"
    ["performance_optimization"]="Performance optimization validation"
    ["monitoring_comprehensive"]="Comprehensive monitoring validation"
    ["documentation_complete"]="Documentation completeness validation"
    ["configuration_validation"]="Configuration validation"
    ["error_handling_robust"]="Robust error handling validation"
    ["logging_comprehensive"]="Comprehensive logging validation"
    ["backup_recovery"]="Backup and recovery validation"
    ["load_testing"]="Load testing validation"
    ["failover_testing"]="Failover testing validation"
    ["resource_limits"]="Resource limits validation"
    ["dependency_management"]="Dependency management validation"
)

# Track check results
declare -A CHECK_RESULTS=()
TOTAL_CHECKS=0
PASSED_CHECKS=0

# Function to run a production readiness check
run_check() {
    local check_name=$1
    local check_description=${CHECKS[$check_name]}
    
    log INFO "Running check: $check_description"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    case $check_name in
        "security_hardening")
            check_security_hardening
            ;;
        "performance_optimization")
            check_performance_optimization
            ;;
        "monitoring_comprehensive")
            check_monitoring_comprehensive
            ;;
        "documentation_complete")
            check_documentation_complete
            ;;
        "configuration_validation")
            check_configuration_validation
            ;;
        "error_handling_robust")
            check_error_handling_robust
            ;;
        "logging_comprehensive")
            check_logging_comprehensive
            ;;
        "backup_recovery")
            check_backup_recovery
            ;;
        "load_testing")
            check_load_testing
            ;;
        "failover_testing")
            check_failover_testing
            ;;
        "resource_limits")
            check_resource_limits
            ;;
        "dependency_management")
            check_dependency_management
            ;;
        *)
            log ERROR "Unknown check: $check_name"
            CHECK_RESULTS[$check_name]="FAILED"
            return 1
            ;;
    esac
    
    local result=$?
    if [[ $result -eq 0 ]]; then
        CHECK_RESULTS[$check_name]="PASSED"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        log INFO "Check passed: $check_description"
    else
        CHECK_RESULTS[$check_name]="FAILED"
        log ERROR "Check failed: $check_description"
    fi
    
    return $result
}

# Security hardening validation
check_security_hardening() {
    local issues=0
    
    # Check for hardcoded secrets
    log DEBUG "Checking for hardcoded secrets..."
    if grep -r "password\|secret\|key" --include="*.rs" --include="*.toml" "$PROJECT_ROOT/src" | grep -v "// TODO\|// FIXME" | grep -E "(=|:)" > /dev/null; then
        log WARN "Potential hardcoded secrets found in source code"
        issues=$((issues + 1))
    fi
    
    # Check for proper input validation
    log DEBUG "Checking for input validation..."
    if ! grep -r "validate\|sanitize" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Limited input validation found"
        issues=$((issues + 1))
    fi
    
    # Check for secure communication
    log DEBUG "Checking for secure communication..."
    if ! grep -r "tls\|ssl\|https" --include="*.rs" --include="*.toml" "$PROJECT_ROOT" > /dev/null; then
        log WARN "Limited secure communication configuration found"
        issues=$((issues + 1))
    fi
    
    # Check for proper error handling that doesn't leak information
    log DEBUG "Checking for secure error handling..."
    if grep -r "unwrap()\|expect(" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Potential information leakage through error messages"
        issues=$((issues + 1))
    fi
    
    # Check for rate limiting
    log DEBUG "Checking for rate limiting..."
    if ! grep -r "rate_limit\|throttle" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Rate limiting not implemented"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Security hardening validation passed"
        return 0
    else
        log ERROR "Security hardening validation failed with $issues issues"
        return 1
    fi
}

# Performance optimization validation
check_performance_optimization() {
    local issues=0
    
    # Check for release build optimizations
    log DEBUG "Checking for release build optimizations..."
    if [[ -f "$PROJECT_ROOT/Cargo.toml" ]]; then
        if ! grep -A 10 "\[profile.release\]" "$PROJECT_ROOT/Cargo.toml" | grep -q "lto = true"; then
            log WARN "Link-time optimization not enabled for release builds"
            issues=$((issues + 1))
        fi
        
        if ! grep -A 10 "\[profile.release\]" "$PROJECT_ROOT/Cargo.toml" | grep -q "codegen-units = 1"; then
            log WARN "Codegen units not optimized for release builds"
            issues=$((issues + 1))
        fi
    fi
    
    # Check for connection pooling
    log DEBUG "Checking for connection pooling..."
    if ! grep -r "pool\|connection_pool" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Connection pooling not implemented"
        issues=$((issues + 1))
    fi
    
    # Check for caching mechanisms
    log DEBUG "Checking for caching mechanisms..."
    if ! grep -r "cache\|memoize" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Caching mechanisms not implemented"
        issues=$((issues + 1))
    fi
    
    # Run performance benchmarks
    log DEBUG "Running performance benchmarks..."
    if [[ -d "$PROJECT_ROOT/benches" ]]; then
        if ! cargo bench --quiet > /dev/null 2>&1; then
            log WARN "Performance benchmarks failed"
            issues=$((issues + 1))
        fi
    else
        log WARN "No performance benchmarks found"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Performance optimization validation passed"
        return 0
    else
        log ERROR "Performance optimization validation failed with $issues issues"
        return 1
    fi
}

# Comprehensive monitoring validation
check_monitoring_comprehensive() {
    local issues=0
    
    # Check for metrics collection
    log DEBUG "Checking for metrics collection..."
    if ! grep -r "metrics\|prometheus\|gauge\|counter\|histogram" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Metrics collection not implemented"
        issues=$((issues + 1))
    fi
    
    # Check for health check endpoints
    log DEBUG "Checking for health check endpoints..."
    if ! grep -r "health\|/health\|healthcheck" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Health check endpoints not implemented"
        issues=$((issues + 1))
    fi
    
    # Check for alerting configuration
    log DEBUG "Checking for alerting configuration..."
    if [[ ! -f "$PROJECT_ROOT/config/prometheus.yml" ]] && [[ ! -d "$PROJECT_ROOT/config/grafana" ]]; then
        log WARN "Monitoring configuration not found"
        issues=$((issues + 1))
    fi
    
    # Check for distributed tracing
    log DEBUG "Checking for distributed tracing..."
    if ! grep -r "tracing\|span\|trace" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Distributed tracing not implemented"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Comprehensive monitoring validation passed"
        return 0
    else
        log ERROR "Comprehensive monitoring validation failed with $issues issues"
        return 1
    fi
}

# Documentation completeness validation
check_documentation_complete() {
    local issues=0
    
    # Check for README
    log DEBUG "Checking for README documentation..."
    if [[ ! -f "$PROJECT_ROOT/README.md" ]]; then
        log WARN "README.md not found"
        issues=$((issues + 1))
    fi
    
    # Check for API documentation
    log DEBUG "Checking for API documentation..."
    if ! find "$PROJECT_ROOT/docs" -name "*.md" -type f | grep -i api > /dev/null 2>&1; then
        log WARN "API documentation not found"
        issues=$((issues + 1))
    fi
    
    # Check for deployment documentation
    log DEBUG "Checking for deployment documentation..."
    if ! find "$PROJECT_ROOT/docs" -name "*.md" -type f | grep -i deploy > /dev/null 2>&1; then
        log WARN "Deployment documentation not found"
        issues=$((issues + 1))
    fi
    
    # Check for configuration documentation
    log DEBUG "Checking for configuration documentation..."
    if ! find "$PROJECT_ROOT/docs" -name "*.md" -type f | grep -i config > /dev/null 2>&1; then
        log WARN "Configuration documentation not found"
        issues=$((issues + 1))
    fi
    
    # Check for troubleshooting documentation
    log DEBUG "Checking for troubleshooting documentation..."
    if ! find "$PROJECT_ROOT/docs" -name "*.md" -type f | grep -i troubleshoot > /dev/null 2>&1; then
        log WARN "Troubleshooting documentation not found"
        issues=$((issues + 1))
    fi
    
    # Check for code documentation
    log DEBUG "Checking for code documentation..."
    if ! cargo doc --no-deps --quiet > /dev/null 2>&1; then
        log WARN "Code documentation generation failed"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Documentation completeness validation passed"
        return 0
    else
        log ERROR "Documentation completeness validation failed with $issues issues"
        return 1
    fi
}

# Configuration validation
check_configuration_validation() {
    local issues=0
    
    # Check for configuration file validation
    log DEBUG "Checking configuration file validation..."
    if [[ -f "$PROJECT_ROOT/config/production.toml" ]]; then
        if ! cargo run --bin validate_config -- --config "$PROJECT_ROOT/config/production.toml" > /dev/null 2>&1; then
            log WARN "Production configuration validation failed"
            issues=$((issues + 1))
        fi
    else
        log WARN "Production configuration file not found"
        issues=$((issues + 1))
    fi
    
    # Check for environment variable documentation
    log DEBUG "Checking environment variable documentation..."
    if [[ ! -f "$PROJECT_ROOT/.env.example" ]]; then
        log WARN "Environment variable example file not found"
        issues=$((issues + 1))
    fi
    
    # Check for configuration schema validation
    log DEBUG "Checking configuration schema validation..."
    if ! grep -r "serde\|Deserialize\|Serialize" --include="*.rs" "$PROJECT_ROOT/src/config" > /dev/null 2>&1; then
        log WARN "Configuration schema validation not implemented"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Configuration validation passed"
        return 0
    else
        log ERROR "Configuration validation failed with $issues issues"
        return 1
    fi
}

# Robust error handling validation
check_error_handling_robust() {
    local issues=0
    
    # Check for proper error types
    log DEBUG "Checking for proper error types..."
    if ! grep -r "anyhow\|thiserror\|Error" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Proper error handling not implemented"
        issues=$((issues + 1))
    fi
    
    # Check for graceful degradation
    log DEBUG "Checking for graceful degradation..."
    if ! grep -r "fallback\|graceful\|degradation" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Graceful degradation not implemented"
        issues=$((issues + 1))
    fi
    
    # Check for circuit breaker pattern
    log DEBUG "Checking for circuit breaker pattern..."
    if ! grep -r "circuit_breaker\|CircuitBreaker" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Circuit breaker pattern not implemented"
        issues=$((issues + 1))
    fi
    
    # Check for retry mechanisms
    log DEBUG "Checking for retry mechanisms..."
    if ! grep -r "retry\|backoff" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Retry mechanisms not implemented"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Robust error handling validation passed"
        return 0
    else
        log ERROR "Robust error handling validation failed with $issues issues"
        return 1
    fi
}

# Comprehensive logging validation
check_logging_comprehensive() {
    local issues=0
    
    # Check for structured logging
    log DEBUG "Checking for structured logging..."
    if ! grep -r "tracing\|log\|slog" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Structured logging not implemented"
        issues=$((issues + 1))
    fi
    
    # Check for log levels
    log DEBUG "Checking for appropriate log levels..."
    if ! grep -r "debug!\|info!\|warn!\|error!" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Appropriate log levels not used"
        issues=$((issues + 1))
    fi
    
    # Check for log configuration
    log DEBUG "Checking for log configuration..."
    if [[ ! -f "$PROJECT_ROOT/config/log_config.toml" ]]; then
        log WARN "Log configuration file not found"
        issues=$((issues + 1))
    fi
    
    # Check for log rotation
    log DEBUG "Checking for log rotation..."
    if ! grep -r "rotation\|rolling" --include="*.toml" "$PROJECT_ROOT/config" > /dev/null 2>&1; then
        log WARN "Log rotation not configured"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Comprehensive logging validation passed"
        return 0
    else
        log ERROR "Comprehensive logging validation failed with $issues issues"
        return 1
    fi
}

# Backup and recovery validation
check_backup_recovery() {
    local issues=0
    
    # Check for backup scripts
    log DEBUG "Checking for backup scripts..."
    if [[ ! -f "$PROJECT_ROOT/scripts/backup.sh" ]]; then
        log WARN "Backup script not found"
        issues=$((issues + 1))
    fi
    
    # Check for backup directory
    log DEBUG "Checking for backup directory..."
    if [[ ! -d "$PROJECT_ROOT/backups" ]]; then
        log WARN "Backup directory not found"
        issues=$((issues + 1))
    fi
    
    # Check for disaster recovery documentation
    log DEBUG "Checking for disaster recovery documentation..."
    if ! find "$PROJECT_ROOT/docs" -name "*.md" -type f | xargs grep -l "disaster\|recovery\|backup" > /dev/null 2>&1; then
        log WARN "Disaster recovery documentation not found"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Backup and recovery validation passed"
        return 0
    else
        log ERROR "Backup and recovery validation failed with $issues issues"
        return 1
    fi
}

# Load testing validation
check_load_testing() {
    local issues=0
    
    # Check for load testing scripts
    log DEBUG "Checking for load testing scripts..."
    if ! find "$PROJECT_ROOT" -name "*load*test*" -o -name "*stress*test*" | head -1 > /dev/null; then
        log WARN "Load testing scripts not found"
        issues=$((issues + 1))
    fi
    
    # Check for performance benchmarks
    log DEBUG "Checking for performance benchmarks..."
    if [[ ! -d "$PROJECT_ROOT/benches" ]]; then
        log WARN "Performance benchmarks not found"
        issues=$((issues + 1))
    fi
    
    # Run a simple load test
    log DEBUG "Running simple load test..."
    if command -v ab > /dev/null; then
        if ! timeout 30 ab -n 100 -c 10 http://localhost:8080/health > /dev/null 2>&1; then
            log WARN "Simple load test failed or service not running"
            issues=$((issues + 1))
        fi
    else
        log WARN "Apache Bench (ab) not available for load testing"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Load testing validation passed"
        return 0
    else
        log ERROR "Load testing validation failed with $issues issues"
        return 1
    fi
}

# Failover testing validation
check_failover_testing() {
    local issues=0
    
    # Check for failover configuration
    log DEBUG "Checking for failover configuration..."
    if ! grep -r "failover\|redundancy\|backup" --include="*.toml" "$PROJECT_ROOT/config" > /dev/null 2>&1; then
        log WARN "Failover configuration not found"
        issues=$((issues + 1))
    fi
    
    # Check for health check endpoints
    log DEBUG "Checking for health check endpoints..."
    if ! grep -r "/health\|healthcheck" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Health check endpoints not implemented"
        issues=$((issues + 1))
    fi
    
    # Check for graceful shutdown
    log DEBUG "Checking for graceful shutdown..."
    if ! grep -r "shutdown\|SIGTERM\|SIGINT" --include="*.rs" "$PROJECT_ROOT/src" > /dev/null; then
        log WARN "Graceful shutdown not implemented"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Failover testing validation passed"
        return 0
    else
        log ERROR "Failover testing validation failed with $issues issues"
        return 1
    fi
}

# Resource limits validation
check_resource_limits() {
    local issues=0
    
    # Check for memory limits
    log DEBUG "Checking for memory limits..."
    if ! grep -r "memory\|heap\|stack" --include="*.toml" "$PROJECT_ROOT/config" > /dev/null 2>&1; then
        log WARN "Memory limits not configured"
        issues=$((issues + 1))
    fi
    
    # Check for connection limits
    log DEBUG "Checking for connection limits..."
    if ! grep -r "max_connections\|connection_limit" --include="*.toml" "$PROJECT_ROOT/config" > /dev/null 2>&1; then
        log WARN "Connection limits not configured"
        issues=$((issues + 1))
    fi
    
    # Check for timeout configurations
    log DEBUG "Checking for timeout configurations..."
    if ! grep -r "timeout\|deadline" --include="*.toml" "$PROJECT_ROOT/config" > /dev/null 2>&1; then
        log WARN "Timeout configurations not found"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Resource limits validation passed"
        return 0
    else
        log ERROR "Resource limits validation failed with $issues issues"
        return 1
    fi
}

# Dependency management validation
check_dependency_management() {
    local issues=0
    
    # Check for security audit
    log DEBUG "Checking for security audit..."
    if ! cargo audit > /dev/null 2>&1; then
        log WARN "Security audit failed or cargo-audit not installed"
        issues=$((issues + 1))
    fi
    
    # Check for outdated dependencies
    log DEBUG "Checking for outdated dependencies..."
    if command -v cargo-outdated > /dev/null; then
        if ! cargo outdated --exit-code 1 > /dev/null 2>&1; then
            log WARN "Outdated dependencies found"
            issues=$((issues + 1))
        fi
    else
        log WARN "cargo-outdated not installed, cannot check for outdated dependencies"
        issues=$((issues + 1))
    fi
    
    # Check for dependency licenses
    log DEBUG "Checking for dependency licenses..."
    if command -v cargo-license > /dev/null; then
        if ! cargo license > /dev/null 2>&1; then
            log WARN "License check failed"
            issues=$((issues + 1))
        fi
    else
        log WARN "cargo-license not installed, cannot check dependency licenses"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log INFO "Dependency management validation passed"
        return 0
    else
        log ERROR "Dependency management validation failed with $issues issues"
        return 1
    fi
}

# Function to generate production readiness report
generate_report() {
    local report_file="$PROJECT_ROOT/logs/production_readiness_report_$(date +%Y%m%d_%H%M%S).md"
    
    log INFO "Generating production readiness report: $report_file"
    
    cat > "$report_file" << EOF
# Production Readiness Report

**Generated:** $(date)
**System:** Aetheric Resonance Engine
**Version:** $(git describe --tags --always 2>/dev/null || echo "unknown")

## Summary

- **Total Checks:** $TOTAL_CHECKS
- **Passed Checks:** $PASSED_CHECKS
- **Failed Checks:** $((TOTAL_CHECKS - PASSED_CHECKS))
- **Success Rate:** $(echo "scale=1; $PASSED_CHECKS * 100 / $TOTAL_CHECKS" | bc)%

## Detailed Results

EOF
    
    for check_name in "${!CHECKS[@]}"; do
        local check_description=${CHECKS[$check_name]}
        local result=${CHECK_RESULTS[$check_name]:-"NOT_RUN"}
        
        case $result in
            "PASSED")
                echo "- ✅ **$check_description**: PASSED" >> "$report_file"
                ;;
            "FAILED")
                echo "- ❌ **$check_description**: FAILED" >> "$report_file"
                ;;
            *)
                echo "- ⚠️ **$check_description**: NOT RUN" >> "$report_file"
                ;;
        esac
    done
    
    cat >> "$report_file" << EOF

## Recommendations

EOF
    
    if [[ $PASSED_CHECKS -lt $TOTAL_CHECKS ]]; then
        cat >> "$report_file" << EOF
### Critical Issues to Address

The following checks failed and should be addressed before production deployment:

EOF
        
        for check_name in "${!CHECK_RESULTS[@]}"; do
            if [[ ${CHECK_RESULTS[$check_name]} == "FAILED" ]]; then
                echo "- ${CHECKS[$check_name]}" >> "$report_file"
            fi
        done
        
        cat >> "$report_file" << EOF

### Next Steps

1. Review the detailed log file: $LOG_FILE
2. Address all failed checks
3. Re-run the production readiness check
4. Ensure all checks pass before proceeding with production deployment

EOF
    else
        cat >> "$report_file" << EOF
### System Ready for Production

All production readiness checks have passed. The system is ready for production deployment.

### Recommended Monitoring

- Set up continuous monitoring for all validated components
- Implement alerting for critical metrics
- Schedule regular production readiness checks
- Maintain backup and recovery procedures

EOF
    fi
    
    log INFO "Production readiness report generated: $report_file"
}

# Main function
main() {
    local command=${1:-"all"}
    
    case $command in
        "all")
            log INFO "Running all production readiness checks..."
            
            for check_name in "${!CHECKS[@]}"; do
                run_check "$check_name" || true # Continue even if checks fail
            done
            
            generate_report
            
            if [[ $PASSED_CHECKS -eq $TOTAL_CHECKS ]]; then
                log INFO "All production readiness checks passed ($PASSED_CHECKS/$TOTAL_CHECKS)"
                exit 0
            else
                log ERROR "Production readiness checks failed ($PASSED_CHECKS/$TOTAL_CHECKS passed)"
                exit 1
            fi
            ;;
        "report")
            # Generate report from existing results
            generate_report
            ;;
        *)
            # Run specific check
            if [[ -n "${CHECKS[$command]:-}" ]]; then
                run_check "$command"
                exit $?
            else
                echo "Usage: $0 {all|report|check_name}"
                echo
                echo "Available checks:"
                for check_name in "${!CHECKS[@]}"; do
                    echo "  $check_name - ${CHECKS[$check_name]}"
                done
                exit 1
            fi
            ;;
    esac
}

# Run main function with all arguments
main "$@"