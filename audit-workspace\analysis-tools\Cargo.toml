[package]
name = "basilisk_analysis_tools"
version = "0.1.0"
edition = "2021"

[workspace]

[dependencies]
rust_decimal = { version = "1.35", features = ["serde"] }
rust_decimal_macros = "1.35"
geo = "0.28"
chrono = { version = "0.4", features = ["serde"] }
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
anyhow = "1.0"
petgraph = "0.6"
page_rank = "0.2"
rustfft = "6.2"
rand = "0.8"
ethers = { version = "2.0", features = ["ws", "rustls"] }

[[bin]]
name = "aetheric_resonance_score_tests"
path = "aetheric_resonance_score_tests.rs"

[[bin]]
name = "geometric_score_validation_tests"
path = "geometric_score_validation_tests.rs"

[[bin]]
name = "hurst_exponent_test_suite"
path = "hurst_exponent_test_suite.rs"

[[bin]]
name = "network_state_validation_tests"
path = "network_state_validation_tests.rs"

[[bin]]
name = "p_wave_s_wave_validation_tests"
path = "p_wave_s_wave_validation_tests.rs"

[[bin]]
name = "asset_centrality_validation_tests"
path = "asset_centrality_validation_tests.rs"

[[bin]]
name = "decision_making_validation_tests"
path = "decision_making_validation_tests.rs"

[[bin]]
name = "network_resonance_integration_tests"
path = "network_resonance_integration_tests.rs"

[[bin]]
name = "temporal_harmonics_test"
path = "temporal_harmonics_test.rs"

[[bin]]
name = "vesica_piscis_validation_tests"
path = "vesica_piscis_validation_tests.rs"

[[bin]]
name = "simple_centrality_validation"
path = "simple_centrality_validation.rs"

[[bin]]
name = "sequencer_monitoring_validation"
path = "sequencer_monitoring_validation.rs"

[[bin]]
name = "reorg_detection_validation"
path = "reorg_detection_validation.rs"

[[bin]]
name = "performance_analysis_standalone"
path = "performance_analysis_standalone.rs"

[[bin]]
name = "performance_latency_analyzer"
path = "performance_latency_analyzer.rs"

[[bin]]
name = "run_performance_analysis"
path = "run_performance_analysis.rs"

[[bin]]
name = "run_performance_tests"
path = "run_performance_tests.rs"

[[bin]]
name = "scoring_engine_integration_tests"
path = "scoring_engine_integration_tests.rs"