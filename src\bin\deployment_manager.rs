// src/bin/deployment_manager.rs
// Deployment manager CLI for Aetheric Resonance Engine fixes

use anyhow::Result;
use basilisk_bot::config::Config;
use basilisk_bot::deployment::{DeploymentConfig, DeploymentPhase};
use basilisk_bot::deployment::feature_flags::FeatureFlagManager;
use basilisk_bot::deployment::phased_deployment::PhasedDeploymentManager;
use basilisk_bot::deployment::rollback_manager::RollbackManager;
use basilisk_bot::deployment::health_checks::HealthCheckManager;
use basilisk_bot::deployment::monitoring::DeploymentMonitor;
use clap::{Arg, Command};
use std::sync::{Arc, RwLock};
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    let matches = Command::new("deployment_manager")
        .version("1.0.0")
        .about("Deployment manager for Aetheric Resonance Engine fixes")
        .subcommand(
            Command::new("deploy")
                .about("Deploy to a specific phase")
                .arg(
                    Arg::new("phase")
                        .help("Target deployment phase")
                        .required(true)
                        .value_parser([
                            "development",
                            "core-scoring", 
                            "mathematical-components",
                            "component-integration",
                            "data-quality",
                            "configuration-monitoring",
                            "full-production"
                        ])
                )
        )
        .subcommand(
            Command::new("rollback")
                .about("Rollback to a specific phase")
                .arg(
                    Arg::new("phase")
                        .help("Target rollback phase")
                        .required(true)
                        .value_parser([
                            "development",
                            "core-scoring",
                            "mathematical-components", 
                            "component-integration",
                            "data-quality",
                            "configuration-monitoring"
                        ])
                )
        )
        .subcommand(
            Command::new("status")
                .about("Show current deployment status")
        )
        .subcommand(
            Command::new("health")
                .about("Run health checks")
        )
        .subcommand(
            Command::new("features")
                .about("Manage feature flags")
                .subcommand(
                    Command::new("list")
                        .about("List all feature flags")
                )
                .subcommand(
                    Command::new("enable")
                        .about("Enable a feature flag")
                        .arg(
                            Arg::new("flag")
                                .help("Feature flag name")
                                .required(true)
                        )
                )
                .subcommand(
                    Command::new("disable")
                        .about("Disable a feature flag")
                        .arg(
                            Arg::new("flag")
                                .help("Feature flag name")
                                .required(true)
                        )
                )
        )
        .subcommand(
            Command::new("monitor")
                .about("Monitor deployment health")
                .arg(
                    Arg::new("duration")
                        .help("Monitoring duration in seconds")
                        .short('d')
                        .long("duration")
                        .default_value("300")
                )
        )
        .get_matches();

    // Load configuration
    let config = Config::load().map_err(|e| {
        error!("Failed to load configuration: {}", e);
        e
    })?;

    // Initialize deployment components
    let deployment_config = DeploymentConfig::default();
    let current_phase = deployment_config.current_phase;
    
    let feature_flags = Arc::new(FeatureFlagManager::new(current_phase));
    let health_checks = Arc::new(HealthCheckManager::new());
    let monitor = Arc::new(DeploymentMonitor::new());
    
    let deployment_manager = Arc::new(PhasedDeploymentManager::new(
        deployment_config.clone(),
        Arc::clone(&feature_flags),
        Arc::clone(&health_checks),
        Arc::clone(&monitor),
    ));
    
    let config_arc = Arc::new(tokio::sync::RwLock::new(deployment_config));
    let rollback_manager = Arc::new(RollbackManager::new(
        config_arc,
        Arc::clone(&feature_flags),
        Arc::clone(&health_checks),
        Arc::clone(&monitor),
    ));

    // Handle subcommands
    match matches.subcommand() {
        Some(("deploy", sub_matches)) => {
            let phase_str = sub_matches.get_one::<String>("phase").unwrap();
            let target_phase: DeploymentPhase = phase_str.parse()?;
            
            info!("Deploying to phase: {}", target_phase);
            
            match deployment_manager.deploy_to_phase(target_phase).await {
                Ok(()) => {
                    info!("Deployment completed successfully");
                    println!("✅ Successfully deployed to phase: {}", target_phase);
                }
                Err(e) => {
                    error!("Deployment failed: {}", e);
                    println!("❌ Deployment failed: {}", e);
                    std::process::exit(1);
                }
            }
        }
        
        Some(("rollback", sub_matches)) => {
            let phase_str = sub_matches.get_one::<String>("phase").unwrap();
            let target_phase: DeploymentPhase = phase_str.parse()?;
            
            info!("Rolling back to phase: {}", target_phase);
            
            match rollback_manager.rollback_to_phase(target_phase).await {
                Ok(()) => {
                    info!("Rollback completed successfully");
                    println!("✅ Successfully rolled back to phase: {}", target_phase);
                }
                Err(e) => {
                    error!("Rollback failed: {}", e);
                    println!("❌ Rollback failed: {}", e);
                    std::process::exit(1);
                }
            }
        }
        
        Some(("status", _)) => {
            let status = deployment_manager.get_deployment_status();
            
            println!("🎯 Deployment Status");
            println!("==================");
            println!("Current Phase: {}", status.current_phase);
            if let Some(target) = status.target_phase {
                println!("Target Phase: {}", target);
            }
            println!("Traffic Routing:");
            println!("  New Implementation: {:.1}%", status.traffic_routing.new_implementation_percentage);
            println!("  Legacy Implementation: {:.1}%", status.traffic_routing.legacy_implementation_percentage);
            println!("Feature Flags Enabled: {}", status.feature_flags_enabled);
            println!("Health Status: {}", status.health_status);
            
            if !status.recent_checkpoints.is_empty() {
                println!("\nRecent Checkpoints:");
                for checkpoint in status.recent_checkpoints.iter().take(3) {
                    println!("  {} - {} ({})", 
                        checkpoint.phase, 
                        checkpoint.status, 
                        checkpoint.timestamp.format("%Y-%m-%d %H:%M:%S")
                    );
                }
            }
        }
        
        Some(("health", _)) => {
            println!("🏥 Running Health Checks");
            println!("=======================");
            
            match health_checks.run_all_checks().await {
                Ok(true) => {
                    println!("✅ All health checks passed");
                    
                    let results = health_checks.get_latest_results();
                    for (name, result) in results {
                        println!("  {} - {} ({}ms)", name, result.status, result.duration_ms);
                    }
                }
                Ok(false) => {
                    println!("❌ Some health checks failed");
                    
                    let results = health_checks.get_latest_results();
                    for (name, result) in results {
                        let status_icon = match result.status {
                            basilisk_bot::deployment::health_checks::HealthCheckStatus::Passed => "✅",
                            basilisk_bot::deployment::health_checks::HealthCheckStatus::Warning => "⚠️",
                            basilisk_bot::deployment::health_checks::HealthCheckStatus::Failed => "❌",
                            basilisk_bot::deployment::health_checks::HealthCheckStatus::Unknown => "❓",
                        };
                        println!("  {} {} - {} ({}ms)", status_icon, name, result.message, result.duration_ms);
                    }
                    std::process::exit(1);
                }
                Err(e) => {
                    error!("Health check execution failed: {}", e);
                    println!("❌ Health check execution failed: {}", e);
                    std::process::exit(1);
                }
            }
        }
        
        Some(("features", sub_matches)) => {
            match sub_matches.subcommand() {
                Some(("list", _)) => {
                    println!("🚩 Feature Flags");
                    println!("================");
                    
                    let all_flags = feature_flags.get_all_flags();
                    for (name, flag) in all_flags {
                        let status_icon = if flag.enabled { "✅" } else { "❌" };
                        println!("  {} {} - {} (Phase: {})", 
                            status_icon, 
                            name, 
                            flag.description,
                            flag.phase
                        );
                    }
                }
                
                Some(("enable", flag_matches)) => {
                    let flag_name = flag_matches.get_one::<String>("flag").unwrap();
                    
                    match feature_flags.enable_flag(flag_name) {
                        Ok(()) => {
                            println!("✅ Enabled feature flag: {}", flag_name);
                        }
                        Err(e) => {
                            println!("❌ Failed to enable feature flag '{}': {}", flag_name, e);
                            std::process::exit(1);
                        }
                    }
                }
                
                Some(("disable", flag_matches)) => {
                    let flag_name = flag_matches.get_one::<String>("flag").unwrap();
                    
                    match feature_flags.disable_flag(flag_name) {
                        Ok(()) => {
                            println!("✅ Disabled feature flag: {}", flag_name);
                        }
                        Err(e) => {
                            println!("❌ Failed to disable feature flag '{}': {}", flag_name, e);
                            std::process::exit(1);
                        }
                    }
                }
                
                _ => {
                    println!("❌ Unknown features subcommand");
                    std::process::exit(1);
                }
            }
        }
        
        Some(("monitor", sub_matches)) => {
            let duration: u64 = sub_matches.get_one::<String>("duration")
                .unwrap()
                .parse()
                .unwrap_or(300);
            
            println!("📊 Monitoring Deployment Health for {} seconds", duration);
            println!("===============================================");
            
            // Start monitoring
            monitor.start_monitoring().await?;
            
            let start_time = std::time::Instant::now();
            let mut last_summary_time = start_time;
            
            while start_time.elapsed().as_secs() < duration {
                // Show summary every 30 seconds
                if last_summary_time.elapsed().as_secs() >= 30 {
                    match monitor.get_deployment_health_summary().await {
                        Ok(summary) => {
                            println!("\n📈 Health Summary ({})", 
                                chrono::Utc::now().format("%H:%M:%S")
                            );
                            println!("  Overall Health: {:.2}", summary.overall_health);
                            println!("  Active Alerts: {}", summary.active_alerts_count);
                            println!("  Critical Alerts: {}", summary.critical_alerts_count);
                            
                            // Show key metrics
                            if let Some(&error_rate) = summary.metrics_snapshot.get("error_rate_percentage") {
                                println!("  Error Rate: {:.2}%", error_rate);
                            }
                            if let Some(&response_time) = summary.metrics_snapshot.get("avg_response_time_ms") {
                                println!("  Avg Response Time: {:.0}ms", response_time);
                            }
                        }
                        Err(e) => {
                            println!("❌ Failed to get health summary: {}", e);
                        }
                    }
                    last_summary_time = std::time::Instant::now();
                }
                
                tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
            }
            
            monitor.stop_monitoring();
            println!("\n✅ Monitoring completed");
        }
        
        _ => {
            println!("❌ No subcommand provided. Use --help for usage information.");
            std::process::exit(1);
        }
    }

    Ok(())
}