// MISSION: Integration tests for Enhanced Error Propagation and Alerting
// WHY: Verify that all error propagation components work together correctly
// HOW: Test error handling, retry logic, circuit breakers, and alerting mechanisms

use basilisk_bot::error::{
    BasiliskError, NetworkError, DataProviderError, ExecutionError, StrategyError, CriticalError,
    ErrorPropagationManager, default_alert_channels,
    enhanced::{ErrorContext, AlertChannelConfig, AlertChannelType, AlertMessage},
    circuit_breaker::{CircuitBreakerConfig, CircuitState},
};
use basilisk_bot::logging::{ErrorCode, AlertSeverity};
use std::collections::HashMap;
use tokio::time::{sleep, Duration};

#[tokio::test]
async fn test_error_propagation_manager_initialization() {
    let manager = ErrorPropagationManager::new(default_alert_channels());
    manager.initialize_circuit_breakers().await;

    let stats = manager.get_circuit_breaker_stats().await;
    
    // Verify all expected circuit breakers are initialized
    assert!(stats.contains_key("rpc_ethereum"));
    assert!(stats.contains_key("rpc_base"));
    assert!(stats.contains_key("rpc_arbitrum"));
    assert!(stats.contains_key("price_oracle"));
    assert!(stats.contains_key("dex_data_provider"));
    assert!(stats.contains_key("network_seismology"));
    assert!(stats.contains_key("transaction_execution"));
    assert!(stats.contains_key("gas_estimation"));

    // Verify initial state is closed
    for (name, stat) in stats {
        assert_eq!(stat.state, CircuitState::Closed, "Circuit breaker {} should start closed", name);
        assert_eq!(stat.failure_count, 0);
        assert_eq!(stat.success_count, 0);
        assert_eq!(stat.total_calls, 0);
    }
}

#[tokio::test]
async fn test_rpc_error_handling_with_context() {
    let manager = ErrorPropagationManager::new(default_alert_channels());
    manager.initialize_circuit_breakers().await;

    let rpc_error = NetworkError::RpcTimeout {
        endpoint: "https://mainnet.infura.io/v3/test".to_string(),
        timeout_ms: 5000,
    };

    let enhanced_error = manager.handle_rpc_error(
        rpc_error,
        1, // Ethereum mainnet
        "https://mainnet.infura.io/v3/test",
        "get_latest_block"
    ).await;

    assert_eq!(enhanced_error.error_code, ErrorCode::ERpcTimeout);
    assert_eq!(enhanced_error.severity, AlertSeverity::Warning);
    assert!(enhanced_error.is_retryable);
    assert!(!enhanced_error.recovery_suggestions.is_empty());
    assert_eq!(enhanced_error.context.component, "RpcProvider");
    assert_eq!(enhanced_error.context.function, "get_latest_block");
    assert_eq!(enhanced_error.context.chain_id, Some(1));
    assert!(enhanced_error.context.additional_data.contains_key("endpoint"));
}

#[tokio::test]
async fn test_data_provider_error_handling() {
    let manager = ErrorPropagationManager::new(default_alert_channels());

    let data_error = DataProviderError::StaleData {
        data_source: "CoinGecko".to_string(),
        age_ms: 300000, // 5 minutes
        threshold_ms: 60000, // 1 minute threshold
    };

    let enhanced_error = manager.handle_data_provider_error(
        data_error,
        "CoinGecko",
        "fetch_token_prices"
    ).await;

    assert_eq!(enhanced_error.error_code, ErrorCode::EDataStale);
    assert_eq!(enhanced_error.severity, AlertSeverity::Warning);
    assert_eq!(enhanced_error.context.component, "DataProvider");
    assert_eq!(enhanced_error.context.function, "fetch_token_prices");
    assert!(enhanced_error.context.additional_data.contains_key("data_source"));
    assert_eq!(enhanced_error.context.propagation_chain.len(), 1);
}

#[tokio::test]
async fn test_execution_error_handling_with_opportunity_context() {
    let manager = ErrorPropagationManager::new(default_alert_channels());

    let execution_error = ExecutionError::InsufficientLiquidity {
        token_path: vec!["WETH".to_string(), "USDC".to_string()],
        available_usd: rust_decimal_macros::dec!(1000.0),
        required_usd: rust_decimal_macros::dec!(5000.0),
    };

    let enhanced_error = manager.handle_execution_error(
        execution_error,
        "opp_12345678",
        8453, // Base chain
        "execute_arbitrage"
    ).await;

    assert_eq!(enhanced_error.error_code, ErrorCode::EInsufficientLiquidity);
    assert_eq!(enhanced_error.severity, AlertSeverity::Error);
    assert!(!enhanced_error.is_retryable);
    assert_eq!(enhanced_error.context.component, "ExecutionManager");
    assert_eq!(enhanced_error.context.function, "execute_arbitrage");
    assert_eq!(enhanced_error.context.opportunity_id, Some("opp_12345678".to_string()));
    assert_eq!(enhanced_error.context.chain_id, Some(8453));
    assert_eq!(enhanced_error.context.propagation_chain.len(), 1);
}

#[tokio::test]
async fn test_strategy_error_handling() {
    let manager = ErrorPropagationManager::new(default_alert_channels());

    let strategy_error = StrategyError::OpportunityExpired {
        opportunity_id: "opp_87654321".to_string(),
        age_ms: 15000, // 15 seconds
    };

    let enhanced_error = manager.handle_strategy_error(
        strategy_error,
        "opp_87654321",
        "zen_geometer",
        "evaluate_opportunity"
    ).await;

    assert_eq!(enhanced_error.error_code, ErrorCode::EOpportunityExpired);
    assert_eq!(enhanced_error.severity, AlertSeverity::Error);
    assert!(!enhanced_error.is_retryable);
    assert_eq!(enhanced_error.context.component, "StrategyManager");
    assert_eq!(enhanced_error.context.function, "evaluate_opportunity");
    assert_eq!(enhanced_error.context.opportunity_id, Some("opp_87654321".to_string()));
    assert!(enhanced_error.context.additional_data.contains_key("strategy_type"));
}

#[tokio::test]
async fn test_critical_error_handling() {
    let manager = ErrorPropagationManager::new(default_alert_channels());

    let critical_error = CriticalError::SecurityViolation {
        violation_type: "Unauthorized wallet access attempt".to_string(),
        details: "Multiple failed authentication attempts detected".to_string(),
    };

    let enhanced_error = manager.handle_critical_error(
        critical_error,
        "WalletManager",
        "authenticate_transaction"
    ).await;

    assert_eq!(enhanced_error.error_code, ErrorCode::ESecurityViolation);
    assert_eq!(enhanced_error.severity, AlertSeverity::Critical);
    assert!(!enhanced_error.is_retryable);
    assert_eq!(enhanced_error.context.component, "WalletManager");
    assert_eq!(enhanced_error.context.function, "authenticate_transaction");
    assert!(enhanced_error.context.additional_data.contains_key("severity"));
    assert!(enhanced_error.context.additional_data.contains_key("requires_immediate_attention"));
}

#[tokio::test]
async fn test_circuit_breaker_protection() {
    let manager = ErrorPropagationManager::new(default_alert_channels());
    manager.initialize_circuit_breakers().await;

    // Test successful operation
    let result = manager.execute_with_protection(
        "rpc_ethereum",
        "RpcProvider",
        "get_block_number",
        || -> Result<u64, NetworkError> {
            Ok(12345)
        }
    ).await;

    assert!(result.is_ok());
    assert_eq!(result.unwrap(), 12345);

    // Test failed operation
    let result = manager.execute_with_protection(
        "rpc_ethereum",
        "RpcProvider",
        "get_block_number",
        || -> Result<u64, NetworkError> {
            Err(NetworkError::RpcTimeout {
                endpoint: "test".to_string(),
                timeout_ms: 5000,
            })
        }
    ).await;

    assert!(result.is_err());
    let enhanced_error = result.unwrap_err();
    assert_eq!(enhanced_error.error_code, ErrorCode::ERpcTimeout);
}

#[tokio::test]
async fn test_circuit_breaker_state_transitions() {
    let manager = ErrorPropagationManager::new(default_alert_channels());
    manager.initialize_circuit_breakers().await;

    // Cause multiple failures to open circuit breaker
    for _ in 0..5 {
        let _ = manager.execute_with_protection(
            "gas_estimation", // Use a circuit breaker with low thresholds
            "ExecutionManager",
            "estimate_gas",
            || -> Result<u64, ExecutionError> {
                Err(ExecutionError::GasEstimationFailed {
                    operation: "test".to_string(),
                    reason: "Mock failure".to_string(),
                })
            }
        ).await;
        
        // Small delay between attempts
        sleep(Duration::from_millis(10)).await;
    }

    let stats = manager.get_circuit_breaker_stats().await;
    let gas_estimation_stats = stats.get("gas_estimation").unwrap();
    
    // Circuit breaker should have recorded failures
    assert!(gas_estimation_stats.failure_count > 0);
    assert!(gas_estimation_stats.total_calls > 0);
}

#[tokio::test]
async fn test_error_context_propagation() {
    let manager = ErrorPropagationManager::new(default_alert_channels());

    let network_error = NetworkError::RpcConnectionFailed {
        endpoint: "test".to_string(),
        reason: "Connection refused".to_string(),
    };

    let enhanced_error = manager.handle_error_with_propagation(
        BasiliskError::Network(network_error),
        "TestComponent",
        "test_function",
        Some("opp_test123"),
        Some(1),
    ).await;

    assert_eq!(enhanced_error.context.component, "TestComponent");
    assert_eq!(enhanced_error.context.function, "test_function");
    assert_eq!(enhanced_error.context.opportunity_id, Some("opp_test123".to_string()));
    assert_eq!(enhanced_error.context.chain_id, Some(1));
    assert!(!enhanced_error.context.error_id.is_empty());
    assert!(enhanced_error.context.timestamp <= chrono::Utc::now());
}

#[tokio::test]
async fn test_component_context_tracking() {
    let manager = ErrorPropagationManager::new(default_alert_channels());

    // Handle errors from different components
    let _ = manager.handle_error_with_propagation(
        BasiliskError::Network(NetworkError::RpcTimeout {
            endpoint: "test1".to_string(),
            timeout_ms: 1000,
        }),
        "Component1",
        "function1",
        None,
        None,
    ).await;

    let _ = manager.handle_error_with_propagation(
        BasiliskError::DataProvider(DataProviderError::SourceUnavailable {
            data_source: "test".to_string(),
            reason: "offline".to_string(),
        }),
        "Component2",
        "function2",
        None,
        None,
    ).await;

    let contexts = manager.get_component_contexts().await;
    assert!(contexts.contains_key("Component1"));
    assert!(contexts.contains_key("Component2"));
    assert_eq!(contexts.get("Component1").unwrap().function, "function1");
    assert_eq!(contexts.get("Component2").unwrap().function, "function2");
}

#[tokio::test]
async fn test_alert_message_creation() {
    let manager = ErrorPropagationManager::new(default_alert_channels());

    let execution_error = ExecutionError::HighSlippage {
        token_path: vec!["WETH".to_string(), "USDC".to_string()],
        actual_percent: rust_decimal_macros::dec!(5.0),
        threshold_percent: rust_decimal_macros::dec!(2.0),
    };

    let enhanced_error = manager.handle_execution_error(
        execution_error,
        "opp_test456",
        42161, // Arbitrum
        "execute_swap"
    ).await;

    let alert = AlertMessage::from_enhanced_error(&enhanced_error);

    assert!(!alert.alert_id.is_empty());
    assert_eq!(alert.severity, AlertSeverity::Warning);
    assert_eq!(alert.error_code, ErrorCode::EHighSlippage);
    assert!(alert.title.contains("E_HIGH_SLIPPAGE"));
    assert!(alert.title.contains("ExecutionManager"));
    assert!(!alert.recovery_suggestions.is_empty());
    assert_eq!(alert.context.opportunity_id, Some("opp_test456".to_string()));
    assert_eq!(alert.context.chain_id, Some(42161));
}

#[tokio::test]
async fn test_default_alert_channels_configuration() {
    let channels = default_alert_channels();
    
    // Should have at least log channel enabled
    assert!(!channels.is_empty());
    
    let log_channel = channels.iter().find(|c| matches!(c.channel_type, AlertChannelType::Log));
    assert!(log_channel.is_some());
    assert!(log_channel.unwrap().enabled);
    assert_eq!(log_channel.unwrap().min_severity, AlertSeverity::Info);
    assert_eq!(log_channel.unwrap().rate_limit_seconds, 0);
}

#[tokio::test]
async fn test_error_recovery_suggestions() {
    let manager = ErrorPropagationManager::new(default_alert_channels());

    // Test different error types have appropriate recovery suggestions
    let rpc_error = manager.handle_rpc_error(
        NetworkError::RpcRateLimited {
            endpoint: "test".to_string(),
            retry_after_ms: 1000,
        },
        1,
        "test",
        "test_op"
    ).await;

    assert!(rpc_error.recovery_suggestions.iter().any(|s| s.contains("exponential backoff")));
    assert!(rpc_error.recovery_suggestions.iter().any(|s| s.contains("different RPC endpoint")));

    let liquidity_error = manager.handle_execution_error(
        ExecutionError::InsufficientLiquidity {
            token_path: vec!["WETH".to_string(), "USDC".to_string()],
            available_usd: rust_decimal_macros::dec!(100.0),
            required_usd: rust_decimal_macros::dec!(1000.0),
        },
        "opp_test",
        1,
        "execute"
    ).await;

    assert!(liquidity_error.recovery_suggestions.iter().any(|s| s.contains("smaller chunks")));
    assert!(liquidity_error.recovery_suggestions.iter().any(|s| s.contains("alternative trading path")));
}