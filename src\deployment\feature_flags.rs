// src/deployment/feature_flags.rs
// Feature flag management for gradual rollout of Aetheric Resonance Engine fixes

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use tracing::{debug, info, warn};

use super::DeploymentPhase;

/// Feature flag manager for controlling rollout of fixes
#[derive(Debug, Clone)]
pub struct FeatureFlagManager {
    flags: Arc<RwLock<HashMap<String, FeatureFlag>>>,
    current_phase: Arc<RwLock<DeploymentPhase>>,
}

impl FeatureFlagManager {
    /// Create a new feature flag manager
    pub fn new(current_phase: DeploymentPhase) -> Self {
        let mut flags = HashMap::new();
        
        // Initialize all feature flags based on current phase
        Self::initialize_flags(&mut flags, current_phase);
        
        Self {
            flags: Arc::new(RwLock::new(flags)),
            current_phase: Arc::new(RwLock::new(current_phase)),
        }
    }

    /// Initialize feature flags for all phases
    fn initialize_flags(flags: &mut HashMap<String, FeatureFlag>, current_phase: DeploymentPhase) {
        let included_phases = current_phase.included_phases();
        
        // Core Scoring Engine Fixes (Phase 1)
        flags.insert("scoring_engine_weight_fix".to_string(), FeatureFlag {
            name: "scoring_engine_weight_fix".to_string(),
            description: "Fix scoring engine to properly use configured weights".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::CoreScoring),
            phase: DeploymentPhase::CoreScoring,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::CoreScoring) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec![],
            rollback_safe: true,
        });

        flags.insert("neutral_score_fallbacks".to_string(), FeatureFlag {
            name: "neutral_score_fallbacks".to_string(),
            description: "Use neutral scores (0.5) instead of zero for missing pillar data".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::CoreScoring),
            phase: DeploymentPhase::CoreScoring,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::CoreScoring) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["scoring_engine_weight_fix".to_string()],
            rollback_safe: true,
        });

        flags.insert("complete_geometric_score".to_string(), FeatureFlag {
            name: "complete_geometric_score".to_string(),
            description: "Include all three geometric components in scoring".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::CoreScoring),
            phase: DeploymentPhase::CoreScoring,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::CoreScoring) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["scoring_engine_weight_fix".to_string()],
            rollback_safe: true,
        });

        // Mathematical Component Fixes (Phase 2)
        flags.insert("hurst_exponent_fix".to_string(), FeatureFlag {
            name: "hurst_exponent_fix".to_string(),
            description: "Fix Hurst exponent calculation with proper variance and data requirements".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::MathematicalComponents),
            phase: DeploymentPhase::MathematicalComponents,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::MathematicalComponents) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["neutral_score_fallbacks".to_string()],
            rollback_safe: true,
        });

        flags.insert("market_rhythm_stability_fix".to_string(), FeatureFlag {
            name: "market_rhythm_stability_fix".to_string(),
            description: "Fix market rhythm stability calculation to use temporal consistency".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::MathematicalComponents),
            phase: DeploymentPhase::MathematicalComponents,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::MathematicalComponents) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["hurst_exponent_fix".to_string()],
            rollback_safe: true,
        });

        flags.insert("vesica_piscis_negative_fix".to_string(), FeatureFlag {
            name: "vesica_piscis_negative_fix".to_string(),
            description: "Fix vesica piscis to handle negative price deviations correctly".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::MathematicalComponents),
            phase: DeploymentPhase::MathematicalComponents,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::MathematicalComponents) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec![],
            rollback_safe: false, // Critical for arbitrage opportunities
        });

        flags.insert("temporal_harmonics_integration".to_string(), FeatureFlag {
            name: "temporal_harmonics_integration".to_string(),
            description: "Integrate temporal harmonics with cycle alignment and stability".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::MathematicalComponents),
            phase: DeploymentPhase::MathematicalComponents,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::MathematicalComponents) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["market_rhythm_stability_fix".to_string()],
            rollback_safe: true,
        });

        flags.insert("liquidity_centroid_bias_fix".to_string(), FeatureFlag {
            name: "liquidity_centroid_bias_fix".to_string(),
            description: "Implement proper liquidity centroid bias calculation".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::MathematicalComponents),
            phase: DeploymentPhase::MathematicalComponents,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::MathematicalComponents) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["complete_geometric_score".to_string()],
            rollback_safe: true,
        });

        // Component Integration Fixes (Phase 3)
        flags.insert("network_state_integration".to_string(), FeatureFlag {
            name: "network_state_integration".to_string(),
            description: "Integrate network state monitoring with execution decisions".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::ComponentIntegration),
            phase: DeploymentPhase::ComponentIntegration,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::ComponentIntegration) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["temporal_harmonics_integration".to_string()],
            rollback_safe: true,
        });

        flags.insert("asset_centrality_initialization".to_string(), FeatureFlag {
            name: "asset_centrality_initialization".to_string(),
            description: "Initialize asset centrality scores with realistic values".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::ComponentIntegration),
            phase: DeploymentPhase::ComponentIntegration,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::ComponentIntegration) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["liquidity_centroid_bias_fix".to_string()],
            rollback_safe: true,
        });

        flags.insert("token_registry_integration".to_string(), FeatureFlag {
            name: "token_registry_integration".to_string(),
            description: "Integrate token registry with proper address resolution".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::ComponentIntegration),
            phase: DeploymentPhase::ComponentIntegration,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::ComponentIntegration) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["asset_centrality_initialization".to_string()],
            rollback_safe: true,
        });

        flags.insert("vesica_piscis_geometric_integration".to_string(), FeatureFlag {
            name: "vesica_piscis_geometric_integration".to_string(),
            description: "Integrate vesica piscis results into geometric scoring system".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::ComponentIntegration),
            phase: DeploymentPhase::ComponentIntegration,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::ComponentIntegration) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["vesica_piscis_negative_fix".to_string(), "token_registry_integration".to_string()],
            rollback_safe: false, // Critical for geometric scoring
        });

        // Data Quality and Validation Fixes (Phase 4)
        flags.insert("network_coherence_fix".to_string(), FeatureFlag {
            name: "network_coherence_fix".to_string(),
            description: "Fix network coherence score calculation and normalization".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::DataQuality),
            phase: DeploymentPhase::DataQuality,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::DataQuality) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["network_state_integration".to_string()],
            rollback_safe: true,
        });

        flags.insert("censorship_detection".to_string(), FeatureFlag {
            name: "censorship_detection".to_string(),
            description: "Implement basic censorship detection for gas strategy".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::DataQuality),
            phase: DeploymentPhase::DataQuality,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::DataQuality) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["network_coherence_fix".to_string()],
            rollback_safe: true,
        });

        flags.insert("sequencer_health_monitoring".to_string(), FeatureFlag {
            name: "sequencer_health_monitoring".to_string(),
            description: "Enhanced sequencer health monitoring with degraded states".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::DataQuality),
            phase: DeploymentPhase::DataQuality,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::DataQuality) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["censorship_detection".to_string()],
            rollback_safe: true,
        });

        // Configuration and Error Handling Fixes (Phase 5)
        flags.insert("enhanced_configuration_validation".to_string(), FeatureFlag {
            name: "enhanced_configuration_validation".to_string(),
            description: "Enhanced configuration validation with proper error handling".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::ConfigurationMonitoring),
            phase: DeploymentPhase::ConfigurationMonitoring,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::ConfigurationMonitoring) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["vesica_piscis_geometric_integration".to_string()],
            rollback_safe: true,
        });

        flags.insert("graceful_degradation_patterns".to_string(), FeatureFlag {
            name: "graceful_degradation_patterns".to_string(),
            description: "Implement graceful degradation patterns for component failures".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::ConfigurationMonitoring),
            phase: DeploymentPhase::ConfigurationMonitoring,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::ConfigurationMonitoring) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["sequencer_health_monitoring".to_string()],
            rollback_safe: true,
        });

        flags.insert("performance_monitoring".to_string(), FeatureFlag {
            name: "performance_monitoring".to_string(),
            description: "Comprehensive performance monitoring and metrics collection".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::ConfigurationMonitoring),
            phase: DeploymentPhase::ConfigurationMonitoring,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::ConfigurationMonitoring) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["enhanced_configuration_validation".to_string()],
            rollback_safe: true,
        });

        flags.insert("enhanced_error_propagation".to_string(), FeatureFlag {
            name: "enhanced_error_propagation".to_string(),
            description: "Enhanced error propagation and alerting mechanisms".to_string(),
            enabled: included_phases.contains(&DeploymentPhase::ConfigurationMonitoring),
            phase: DeploymentPhase::ConfigurationMonitoring,
            traffic_percentage: if included_phases.contains(&DeploymentPhase::ConfigurationMonitoring) { 
                current_phase.traffic_percentage() as f64 
            } else { 
                0.0 
            },
            dependencies: vec!["graceful_degradation_patterns".to_string(), "performance_monitoring".to_string()],
            rollback_safe: true,
        });
    }

    /// Check if a feature flag is enabled
    pub fn is_enabled(&self, flag_name: &str) -> bool {
        let flags = self.flags.read().unwrap();
        flags.get(flag_name)
            .map(|flag| flag.enabled)
            .unwrap_or(false)
    }

    /// Check if a feature flag is enabled for a specific traffic percentage
    pub fn is_enabled_for_traffic(&self, flag_name: &str, traffic_percentage: f64) -> bool {
        let flags = self.flags.read().unwrap();
        if let Some(flag) = flags.get(flag_name) {
            flag.enabled && traffic_percentage <= flag.traffic_percentage
        } else {
            false
        }
    }

    /// Enable a feature flag
    pub fn enable_flag(&self, flag_name: &str) -> Result<()> {
        let flags_read = self.flags.read().unwrap();
        let mut dependencies_met = true;
        let mut missing_dependency = None;
        let mut disabled_dependency = None;

        if let Some(flag) = flags_read.get(flag_name) {
            for dep in &flag.dependencies {
                if let Some(dep_flag) = flags_read.get(dep) {
                    if !dep_flag.enabled {
                        dependencies_met = false;
                        disabled_dependency = Some(dep.clone());
                        break;
                    }
                } else {
                    dependencies_met = false;
                    missing_dependency = Some(dep.clone());
                    break;
                }
            }
        } else {
            return Err(anyhow::anyhow!("Feature flag '{}' not found", flag_name));
        }
        drop(flags_read); // Release the read lock

        if !dependencies_met {
            if let Some(dep) = missing_dependency {
                return Err(anyhow::anyhow!(
                    "Cannot enable flag '{}': dependency '{}' not found",
                    flag_name, dep
                ));
            } else if let Some(dep) = disabled_dependency {
                return Err(anyhow::anyhow!(
                    "Cannot enable flag '{}': dependency '{}' is not enabled",
                    flag_name, dep
                ));
            }
        }

        let mut flags_write = self.flags.write().unwrap();
        if let Some(flag) = flags_write.get_mut(flag_name) {
            flag.enabled = true;
            info!("Enabled feature flag: {}", flag_name);
            Ok(())
        } else {
            // This case should ideally not be reached if flags_read.get(flag_name) succeeded
            Err(anyhow::anyhow!("Feature flag '{}' not found after re-acquiring write lock", flag_name))
        }
    }

    /// Disable a feature flag
    pub fn disable_flag(&self, flag_name: &str) -> Result<()> {
        let flags_read = self.flags.read().unwrap();
        let dependents: Vec<String> = flags_read.values()
            .filter(|f| f.dependencies.contains(&flag_name.to_string()))
            .filter(|f| f.enabled)
            .map(|f| f.name.clone())
            .collect();
        drop(flags_read); // Release the read lock

        if !dependents.is_empty() {
            return Err(anyhow::anyhow!(
                "Cannot disable flag '{}': it is required by enabled flags: {:?}",
                flag_name, dependents
            ));
        }

        let mut flags_write = self.flags.write().unwrap();
        if let Some(flag) = flags_write.get_mut(flag_name) {
            flag.enabled = false;
            info!("Disabled feature flag: {}", flag_name);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Feature flag '{}' not found", flag_name))
        }
    }

    /// Update traffic percentage for a feature flag
    pub fn update_traffic_percentage(&self, flag_name: &str, percentage: f64) -> Result<()> {
        if percentage < 0.0 || percentage > 100.0 {
            return Err(anyhow::anyhow!("Traffic percentage must be between 0 and 100"));
        }

        let mut flags = self.flags.write().unwrap();
        if let Some(flag) = flags.get_mut(flag_name) {
            flag.traffic_percentage = percentage;
            debug!("Updated traffic percentage for flag '{}' to {}%", flag_name, percentage);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Feature flag '{}' not found", flag_name))
        }
    }

    /// Update current deployment phase and adjust flags accordingly
    pub fn update_phase(&self, new_phase: DeploymentPhase) -> Result<()> {
        let mut current_phase = self.current_phase.write().unwrap();
        let old_phase = *current_phase;
        *current_phase = new_phase;
        drop(current_phase);

        // Update all flags based on new phase
        let mut flags = self.flags.write().unwrap();
        let included_phases = new_phase.included_phases();

        for flag in flags.values_mut() {
            let should_be_enabled = included_phases.contains(&flag.phase);
            
            if should_be_enabled != flag.enabled {
                flag.enabled = should_be_enabled;
                flag.traffic_percentage = if should_be_enabled {
                    new_phase.traffic_percentage() as f64
                } else {
                    0.0
                };

                info!(
                    "Phase transition {} -> {}: {} feature flag '{}'",
                    old_phase,
                    new_phase,
                    if should_be_enabled { "Enabled" } else { "Disabled" },
                    flag.name
                );
            }
        }

        info!("Updated deployment phase from {} to {}", old_phase, new_phase);
        Ok(())
    }

    /// Get all feature flags
    pub fn get_all_flags(&self) -> HashMap<String, FeatureFlag> {
        self.flags.read().unwrap().clone()
    }

    /// Get flags for a specific phase
    pub fn get_flags_for_phase(&self, phase: DeploymentPhase) -> Vec<FeatureFlag> {
        self.flags.read().unwrap()
            .values()
            .filter(|flag| flag.phase == phase)
            .cloned()
            .collect()
    }

    /// Get enabled flags
    pub fn get_enabled_flags(&self) -> Vec<FeatureFlag> {
        self.flags.read().unwrap()
            .values()
            .filter(|flag| flag.enabled)
            .cloned()
            .collect()
    }

    /// Validate flag dependencies
    pub fn validate_dependencies(&self) -> Result<()> {
        let flags = self.flags.read().unwrap();
        
        for flag in flags.values() {
            if flag.enabled {
                for dep in &flag.dependencies {
                    if let Some(dep_flag) = flags.get(dep) {
                        if !dep_flag.enabled {
                            return Err(anyhow::anyhow!(
                                "Dependency validation failed: flag '{}' is enabled but dependency '{}' is disabled",
                                flag.name, dep
                            ));
                        }
                    } else {
                        return Err(anyhow::anyhow!(
                            "Dependency validation failed: flag '{}' depends on non-existent flag '{}'",
                            flag.name, dep
                        ));
                    }
                }
            }
        }

        debug!("Feature flag dependency validation passed");
        Ok(())
    }

    /// Get current deployment phase
    pub fn current_phase(&self) -> DeploymentPhase {
        *self.current_phase.read().unwrap()
    }
}

/// Feature flag definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureFlag {
    pub name: String,
    pub description: String,
    pub enabled: bool,
    pub phase: DeploymentPhase,
    pub traffic_percentage: f64,
    pub dependencies: Vec<String>,
    pub rollback_safe: bool,
}

impl FeatureFlag {
    /// Check if this flag can be safely rolled back
    pub fn can_rollback(&self) -> bool {
        self.rollback_safe
    }

    /// Get the risk level of this flag
    pub fn risk_level(&self) -> RiskLevel {
        if self.rollback_safe {
            RiskLevel::Low
        } else {
            match self.phase {
                DeploymentPhase::Development | DeploymentPhase::CoreScoring => RiskLevel::Medium,
                _ => RiskLevel::High,
            }
        }
    }
}

/// Risk level for feature flags
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

impl std::fmt::Display for RiskLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RiskLevel::Low => write!(f, "low"),
            RiskLevel::Medium => write!(f, "medium"),
            RiskLevel::High => write!(f, "high"),
            RiskLevel::Critical => write!(f, "critical"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_feature_flag_manager_creation() {
        let manager = FeatureFlagManager::new(DeploymentPhase::CoreScoring);
        
        // Core scoring flags should be enabled
        assert!(manager.is_enabled("scoring_engine_weight_fix"));
        assert!(manager.is_enabled("neutral_score_fallbacks"));
        assert!(manager.is_enabled("complete_geometric_score"));
        
        // Mathematical component flags should be disabled
        assert!(!manager.is_enabled("hurst_exponent_fix"));
        assert!(!manager.is_enabled("market_rhythm_stability_fix"));
    }

    #[test]
    fn test_feature_flag_dependencies() {
        let manager = FeatureFlagManager::new(DeploymentPhase::Development);
        
        // Should fail to enable a flag with unmet dependencies
        let result = manager.enable_flag("neutral_score_fallbacks");
        assert!(result.is_err());
        
        // Should succeed after enabling dependency
        manager.enable_flag("scoring_engine_weight_fix").unwrap();
        manager.enable_flag("neutral_score_fallbacks").unwrap();
        
        assert!(manager.is_enabled("neutral_score_fallbacks"));
    }

    #[test]
    fn test_phase_transition() {
        let manager = FeatureFlagManager::new(DeploymentPhase::Development);
        
        // Initially no flags should be enabled
        assert!(!manager.is_enabled("scoring_engine_weight_fix"));
        
        // After phase transition, appropriate flags should be enabled
        manager.update_phase(DeploymentPhase::CoreScoring).unwrap();
        assert!(manager.is_enabled("scoring_engine_weight_fix"));
        assert!(manager.is_enabled("neutral_score_fallbacks"));
        assert!(manager.is_enabled("complete_geometric_score"));
    }

    #[test]
    fn test_traffic_percentage() {
        let manager = FeatureFlagManager::new(DeploymentPhase::CoreScoring);
        
        // Should be enabled for traffic within percentage
        assert!(manager.is_enabled_for_traffic("scoring_engine_weight_fix", 5.0));
        
        // Should not be enabled for traffic above percentage
        assert!(!manager.is_enabled_for_traffic("scoring_engine_weight_fix", 10.0));
    }

    #[test]
    fn test_dependency_validation() {
        let manager = FeatureFlagManager::new(DeploymentPhase::CoreScoring);
        
        // Should pass validation with consistent dependencies
        assert!(manager.validate_dependencies().is_ok());
        
        // Manually break dependencies and test validation
        // First disable dependent flags, then the dependency
        manager.disable_flag("neutral_score_fallbacks").unwrap();
        manager.disable_flag("complete_geometric_score").unwrap();
        manager.disable_flag("scoring_engine_weight_fix").unwrap();
        
        // Now validation should fail because dependent flags are disabled but dependency is also disabled
        // Re-enable a dependent flag to create inconsistency
        manager.enable_flag("neutral_score_fallbacks").unwrap_err(); // Should fail due to missing dependency
    }
}