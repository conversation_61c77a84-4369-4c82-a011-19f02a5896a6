// src/validation/cross_chain_validator_demo.rs

//! Cross-Chain Validation Framework Demo and Examples
//! 
//! This module provides demonstration functions and usage examples for the
//! cross-chain execution validation framework.

use crate::validation::{
    CrossChainValidator, CrossChainValidationConfig, ValidationFramework, ValidationConfig
};
use crate::error::BasiliskError;
use tracing::{error, info, warn};

/// Run a comprehensive cross-chain validation demo
pub async fn run_cross_chain_validation_demo() -> Result<(), BasiliskError> {
    info!("🚀 Starting Cross-Chain Validation Framework Demo");
    
    // Create default configuration
    let config = CrossChainValidationConfig::default();
    let mut validator = CrossChainValidator::new(config);
    
    let config = validator.get_config();
    info!("📋 Configuration loaded:");
    info!("  - Base Chain: {} (port {})", 
          config.base_config.chain_name, 
          config.base_config.anvil_port);
    info!("  - Degen Chain: {} (port {})", 
          config.degen_config.chain_name, 
          config.degen_config.anvil_port);
    
    // Start Anvil environments
    info!("🔧 Starting Anvil simulation environments...");
    match validator.start_anvil_environments().await {
        Ok(_) => info!("✅ Anvil environments started successfully"),
        Err(e) => {
            error!("❌ Failed to start Anvil environments: {}", e);
            return Err(e);
        }
    }
    
    // Run validation demonstrations
    demo_hub_spoke_validation(&validator).await?;
    demo_base_hub_validation(&validator).await?;
    demo_degen_execution_validation(&validator).await?;
    demo_stargate_integration_validation(&validator).await?;
    demo_arbitrage_profitability_validation(&validator).await?;
    demo_bridge_prediction_validation(&validator).await?;
    
    // Clean up
    info!("🧹 Cleaning up Anvil environments...");
    validator.stop_anvil_environments().await?;
    
    info!("🎉 Cross-Chain Validation Framework Demo completed successfully!");
    Ok(())
}

/// Demonstrate Hub and Spoke architecture validation
async fn demo_hub_spoke_validation(validator: &CrossChainValidator) -> Result<(), BasiliskError> {
    info!("\n📊 Demonstrating Hub and Spoke Architecture Validation");
    info!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    match validator.validate_hub_spoke_architecture().await {
        Ok(result) => {
            info!("✅ Hub and Spoke validation completed: {}", result.status);
            info!("   Architecture consistency: {:.1}%", 
                  result.metrics.hub_spoke_metrics.architecture_consistency_score * 100.0);
            info!("   Capital flow success rate: {:.1}%", 
                  result.metrics.hub_spoke_metrics.capital_flow_success_rate * 100.0);
            info!("   Cross-chain latency: {:.1}ms", 
                  result.metrics.hub_spoke_metrics.cross_chain_latency_ms);
            info!("   Coordination effectiveness: {:.1}%", 
                  result.metrics.hub_spoke_metrics.coordination_effectiveness * 100.0);
            
            if !result.warnings.is_empty() {
                warn!("   Warnings: {}", result.warnings.len());
                for warning in &result.warnings {
                    warn!("     • {}", warning.message);
                }
            }
        }
        Err(e) => {
            error!("❌ Hub and Spoke validation failed: {}", e);
            return Err(e);
        }
    }
    
    Ok(())
}

/// Demonstrate Base L2 settlement hub validation
async fn demo_base_hub_validation(validator: &CrossChainValidator) -> Result<(), BasiliskError> {
    info!("\n🏦 Demonstrating Base L2 Settlement Hub Validation");
    info!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    match validator.validate_base_settlement_hub().await {
        Ok(result) => {
            info!("✅ Base hub validation completed: {}", result.status);
            info!("   Capital management effectiveness: {:.1}%", 
                  result.metrics.base_hub_metrics.capital_management_effectiveness * 100.0);
            info!("   Flash loan success rate: {:.1}%", 
                  result.metrics.base_hub_metrics.flash_loan_success_rate * 100.0);
            info!("   Settlement accuracy: {:.1}%", 
                  result.metrics.base_hub_metrics.settlement_accuracy * 100.0);
            info!("   Average settlement time: {:.1}s", 
                  result.metrics.base_hub_metrics.average_settlement_time_seconds);
            info!("   Aave integration health: {:.1}%", 
                  result.metrics.base_hub_metrics.aave_integration_health * 100.0);
        }
        Err(e) => {
            error!("❌ Base hub validation failed: {}", e);
            return Err(e);
        }
    }
    
    Ok(())
}

/// Demonstrate Degen Chain L3 execution venue validation
async fn demo_degen_execution_validation(validator: &CrossChainValidator) -> Result<(), BasiliskError> {
    info!("\n⚡ Demonstrating Degen Chain L3 Execution Venue Validation");
    info!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    match validator.validate_degen_execution_venue().await {
        Ok(result) => {
            info!("✅ Degen execution validation completed: {}", result.status);
            info!("   Execution performance score: {:.1}%", 
                  result.metrics.degen_execution_metrics.execution_performance_score * 100.0);
            info!("   DEX integration success rate: {:.1}%", 
                  result.metrics.degen_execution_metrics.dex_integration_success_rate * 100.0);
            info!("   Trade execution latency: {:.1}ms", 
                  result.metrics.degen_execution_metrics.trade_execution_latency_ms);
            info!("   Slippage accuracy: {:.1}%", 
                  result.metrics.degen_execution_metrics.slippage_accuracy * 100.0);
            info!("   Gas optimization effectiveness: {:.1}%", 
                  result.metrics.degen_execution_metrics.gas_optimization_effectiveness * 100.0);
        }
        Err(e) => {
            error!("❌ Degen execution validation failed: {}", e);
            return Err(e);
        }
    }
    
    Ok(())
}

/// Demonstrate Stargate bridge integration validation
async fn demo_stargate_integration_validation(validator: &CrossChainValidator) -> Result<(), BasiliskError> {
    info!("\n🌉 Demonstrating Stargate Bridge Integration Validation");
    info!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    match validator.validate_stargate_bridge_integration().await {
        Ok(result) => {
            info!("✅ Stargate integration validation completed: {}", result.status);
            info!("   Bridge success rate: {:.1}%", 
                  result.metrics.stargate_integration_metrics.bridge_success_rate * 100.0);
            info!("   Atomic transaction score: {:.1}%", 
                  result.metrics.stargate_integration_metrics.atomic_transaction_score * 100.0);
            info!("   LayerZero fee accuracy: {:.1}%", 
                  result.metrics.stargate_integration_metrics.layerzero_fee_accuracy * 100.0);
            info!("   Bridge completion time: {:.1}s", 
                  result.metrics.stargate_integration_metrics.bridge_completion_time_seconds);
            info!("   Cross-chain state consistency: {:.1}%", 
                  result.metrics.stargate_integration_metrics.cross_chain_state_consistency * 100.0);
        }
        Err(e) => {
            error!("❌ Stargate integration validation failed: {}", e);
            return Err(e);
        }
    }
    
    Ok(())
}

/// Demonstrate cross-chain arbitrage profitability validation
async fn demo_arbitrage_profitability_validation(validator: &CrossChainValidator) -> Result<(), BasiliskError> {
    info!("\n💰 Demonstrating Cross-Chain Arbitrage Profitability Validation");
    info!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    match validator.validate_cross_chain_arbitrage_profitability().await {
        Ok(result) => {
            info!("✅ Arbitrage profitability validation completed: {}", result.status);
            info!("   Profitable opportunities: {}", 
                  result.metrics.arbitrage_profitability_metrics.profitable_opportunities);
            info!("   Average profit margin: {:.2}%", 
                  result.metrics.arbitrage_profitability_metrics.average_profit_margin);
            info!("   Profit realization rate: {:.1}%", 
                  result.metrics.arbitrage_profitability_metrics.profit_realization_rate * 100.0);
            info!("   Cost prediction accuracy: {:.1}%", 
                  result.metrics.arbitrage_profitability_metrics.cost_prediction_accuracy * 100.0);
            info!("   Net profit: ${}", 
                  result.metrics.arbitrage_profitability_metrics.net_profit_usd);
        }
        Err(e) => {
            error!("❌ Arbitrage profitability validation failed: {}", e);
            return Err(e);
        }
    }
    
    Ok(())
}

/// Demonstrate bridge fee and slippage prediction validation
async fn demo_bridge_prediction_validation(validator: &CrossChainValidator) -> Result<(), BasiliskError> {
    info!("\n📈 Demonstrating Bridge Fee and Slippage Prediction Validation");
    info!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    match validator.validate_bridge_fee_slippage_prediction().await {
        Ok(result) => {
            info!("✅ Bridge prediction validation completed: {}", result.status);
            info!("   Fee prediction accuracy: {:.1}%", 
                  result.metrics.bridge_prediction_metrics.fee_prediction_accuracy);
            info!("   Slippage prediction accuracy: {:.1}%", 
                  result.metrics.bridge_prediction_metrics.slippage_prediction_accuracy);
            info!("   Average prediction error: ${}", 
                  result.metrics.bridge_prediction_metrics.average_prediction_error_usd);
            info!("   Prediction consistency score: {:.1}%", 
                  result.metrics.bridge_prediction_metrics.prediction_consistency_score * 100.0);
        }
        Err(e) => {
            error!("❌ Bridge prediction validation failed: {}", e);
            return Err(e);
        }
    }
    
    Ok(())
}

/// Run a quick cross-chain validation test
pub async fn run_quick_cross_chain_validation() -> Result<(), BasiliskError> {
    info!("⚡ Running Quick Cross-Chain Validation Test");
    
    let config = CrossChainValidationConfig::default();
    let mut validator = CrossChainValidator::new(config);
    
    // Start environments
    validator.start_anvil_environments().await?;
    
    // Run a single validation test
    let result = validator.validate_hub_spoke_architecture().await?;
    
    match result.status {
        crate::validation::ValidationStatus::Passed => {
            info!("✅ Quick validation PASSED in {}ms", result.execution_time.as_millis());
        }
        crate::validation::ValidationStatus::Warning => {
            warn!("⚠️  Quick validation completed with WARNINGS in {}ms", result.execution_time.as_millis());
        }
        _ => {
            error!("❌ Quick validation FAILED in {}ms", result.execution_time.as_millis());
        }
    }
    
    // Clean up
    validator.stop_anvil_environments().await?;
    
    Ok(())
}

/// Show usage examples for the cross-chain validator
pub fn show_cross_chain_usage_examples() {
    info!("📚 Cross-Chain Validation Framework Usage Examples");
    info!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    println!("\n1. Run complete cross-chain validation suite:");
    println!("   cargo run --bin zen_geometer -- validate cross-chain complete");
    
    println!("\n2. Run specific validation tests:");
    println!("   cargo run --bin zen_geometer -- validate cross-chain hub-spoke");
    println!("   cargo run --bin zen_geometer -- validate cross-chain base-hub");
    println!("   cargo run --bin zen_geometer -- validate cross-chain degen-execution");
    println!("   cargo run --bin zen_geometer -- validate cross-chain stargate-integration");
    println!("   cargo run --bin zen_geometer -- validate cross-chain arbitrage-profitability");
    println!("   cargo run --bin zen_geometer -- validate cross-chain bridge-prediction");
    
    println!("\n3. Use custom configuration:");
    println!("   cargo run --bin zen_geometer -- validate cross-chain complete \\");
    println!("     --config cross_chain_config.toml");
    
    println!("\n4. Override RPC endpoints:");
    println!("   cargo run --bin zen_geometer -- validate cross-chain complete \\");
    println!("     --base-rpc https://mainnet.base.org \\");
    println!("     --degen-rpc https://rpc.degen.tips");
    
    println!("\n5. Use custom Anvil ports:");
    println!("   cargo run --bin zen_geometer -- validate cross-chain complete \\");
    println!("     --base-port 8545 --degen-port 8546");
    
    println!("\n6. Save results to file:");
    println!("   cargo run --bin zen_geometer -- validate cross-chain complete \\");
    println!("     --save-results cross_chain_results.json");
    
    println!("\n7. Use different output formats:");
    println!("   cargo run --bin zen_geometer -- validate cross-chain complete --output json");
    println!("   cargo run --bin zen_geometer -- validate cross-chain complete --output table");
    println!("   cargo run --bin zen_geometer -- validate cross-chain complete --output summary");
    
    println!("\n8. Skip Anvil startup (use existing instances):");
    println!("   cargo run --bin zen_geometer -- validate cross-chain complete --skip-anvil");
    
    info!("\n💡 Tips:");
    info!("  - Ensure Anvil is installed and available in PATH");
    info!("  - Make sure the specified RPC endpoints are accessible");
    info!("  - Use --skip-anvil if you have Anvil instances already running");
    info!("  - Check the logs for detailed validation progress");
}

/// Run framework integration demo
pub async fn run_framework_integration_demo() -> Result<(), BasiliskError> {
    info!("🔧 Running Cross-Chain Validation Framework Integration Demo");
    
    // Create validation framework
    let mut validation_config = ValidationConfig::default();
    validation_config.storage_config.persist_to_disk = false; // For demo
    let framework = ValidationFramework::new(validation_config)?;
    
    // Create cross-chain validator
    let cross_chain_config = CrossChainValidationConfig::default();
    let mut validator = CrossChainValidator::new(cross_chain_config);
    
    // Start Anvil
    validator.start_anvil_environments().await?;
    
    // Execute validation through framework
    let result = framework.execute_validation(
        "cross_chain_integration_demo",
        || async {
            validator.validate_hub_spoke_architecture().await
                .map(|result| result.metrics)
        }
    ).await?;
    
    info!("Framework integration result: {}", result.status);
    info!("Execution time: {}ms", result.execution_time.as_millis());
    
    // Clean up
    validator.stop_anvil_environments().await?;
    
    Ok(())
}