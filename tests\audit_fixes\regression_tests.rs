//! AUDIT-FIX: Regression tests to prevent issue recurrence - Task 6.5
//! This module provides regression tests for all previously identified audit findings

use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::collections::HashMap;
use anyhow::Result;

/// Regression test suite for mathematical fixes
#[cfg(test)]
mod math_regression_tests {
    use super::*;
    use basilisk_bot::math::vesica;
    use basilisk_bot::data::fractal_analyzer::FractalAnalyzer;

    /// REGRESSION TEST: Vesica Piscis negative deviation fix
    /// Previously: negative deviations caused incorrect calculations
    /// Fix: Always return positive result (absolute value)
    #[test]
    fn test_vesica_negative_deviation_regression() {
        // Test case that previously failed
        let result = vesica::calculate_amount_to_equalize(
            dec!(2000.0), // pool_a_reserves
            dec!(1000.0), // pool_b_reserves
            dec!(-0.15),  // -15% price deviation (negative)
        );

        // CRITICAL: Result must be positive (the fix)
        assert!(result > dec!(0.0), 
               "REGRESSION: Negative deviation should yield positive result, got: {}", result);
        
        // Additional validation: result should be reasonable
        assert!(result < dec!(1000.0), 
               "Result should be bounded by pool sizes");
        assert!(result.is_finite(), 
               "Result should be finite");
    }

    /// REGRESSION TEST: Vesica Piscis mathematical symmetry
    /// Previously: asymmetric behavior with positive/negative deviations
    /// Fix: Consistent absolute value handling
    #[test]
    fn test_vesica_symmetry_regression() {
        let pool_a = dec!(1500.0);
        let pool_b = dec!(1000.0);
        let deviation_magnitude = dec!(0.2);

        let positive_result = vesica::calculate_amount_to_equalize(
            pool_a, pool_b, deviation_magnitude
        );
        let negative_result = vesica::calculate_amount_to_equalize(
            pool_a, pool_b, -deviation_magnitude
        );

        // Both should be positive (the fix)
        assert!(positive_result > dec!(0.0), "Positive deviation result should be positive");
        assert!(negative_result > dec!(0.0), "Negative deviation result should be positive");
        
        // Results should be similar in magnitude (symmetry)
        let difference = (positive_result - negative_result).abs();
        let tolerance = positive_result * dec!(0.1); // 10% tolerance
        assert!(difference <= tolerance, 
               "Results should be symmetric within tolerance: {} vs {}, diff: {}", 
               positive_result, negative_result, difference);
    }

    /// REGRESSION TEST: FFT buffer size handling
    /// Previously: small datasets caused buffer overflow or incorrect sizing
    /// Fix: Proper buffer size validation and handling
    #[tokio::test]
    async fn test_fft_buffer_size_regression() {
        let analyzer = FractalAnalyzer::new();
        
        // Test cases that previously caused issues
        let problematic_sizes = vec![1, 2, 3, 5, 7, 9, 10, 15, 17];
        
        for size in problematic_sizes {
            let test_data: Vec<f64> = (0..size).map(|i| (i as f64 * 0.1).sin()).collect();
            
            let result = analyzer.calculate_temporal_harmonics(&test_data).await;
            
            match result {
                Ok(harmonics) => {
                    // If successful, validate the output
                    assert!(harmonics.market_rhythm_stability >= 0.0 && harmonics.market_rhythm_stability <= 1.0,
                           "Market rhythm stability should be in [0,1] range for size {}", size);
                    assert!(!harmonics.dominant_cycles_minutes.is_empty() || size < 4,
                           "Should have dominant cycles for reasonable sizes");
                },
                Err(_) => {
                    // Failure is acceptable for very small datasets, but should be graceful
                    assert!(size < 4, "Datasets of size {} should be handled gracefully", size);
                }
            }
        }
    }

    /// REGRESSION TEST: Zero and extreme value handling
    /// Previously: division by zero and overflow issues
    /// Fix: Proper bounds checking and edge case handling
    #[test]
    fn test_extreme_values_regression() {
        // Test zero pool reserves (previously caused division by zero)
        let zero_pool_a = vesica::calculate_amount_to_equalize(
            dec!(0.0), dec!(1000.0), dec!(0.1)
        );
        assert!(zero_pool_a.is_finite(), "Should handle zero pool A reserves");

        let zero_pool_b = vesica::calculate_amount_to_equalize(
            dec!(1000.0), dec!(0.0), dec!(0.1)
        );
        assert!(zero_pool_b.is_finite(), "Should handle zero pool B reserves");

        // Test very large values (previously caused overflow)
        let large_values = vesica::calculate_amount_to_equalize(
            dec!(1e15), dec!(1e14), dec!(0.01)
        );
        assert!(large_values.is_finite(), "Should handle large values without overflow");
        assert!(large_values >= dec!(0.0), "Large value result should be non-negative");

        // Test very small values (previously caused underflow)
        let small_values = vesica::calculate_amount_to_equalize(
            dec!(0.000001), dec!(0.000002), dec!(0.1)
        );
        assert!(small_values.is_finite(), "Should handle small values without underflow");
    }
}

/// Regression test suite for execution component fixes
#[cfg(test)]
mod execution_regression_tests {
    use super::*;
    use basilisk_bot::execution::{NonceManager, GasEstimator, CircuitBreaker};
    use basilisk_bot::shared_types::GasUrgency;
    use ethers::types::{Address, U256, H256};
    use std::time::Duration;

    /// REGRESSION TEST: Nonce race condition fix
    /// Previously: concurrent nonce requests could return duplicate nonces
    /// Fix: Proper synchronization and atomic operations
    #[tokio::test]
    async fn test_nonce_race_condition_regression() {
        let nonce_manager = NonceManager::new(Address::zero(), 1).await.unwrap();
        
        // Spawn many concurrent nonce requests (previously caused duplicates)
        let concurrent_requests = 50;
        let mut handles = Vec::new();
        
        for _ in 0..concurrent_requests {
            let nm = nonce_manager.clone();
            let handle = tokio::spawn(async move {
                nm.get_next_nonce().await
            });
            handles.push(handle);
        }

        // Collect all nonces
        let mut nonces = Vec::new();
        for handle in handles {
            match handle.await {
                Ok(Ok(nonce)) => nonces.push(nonce),
                _ => panic!("Nonce request should not fail"),
            }
        }

        // Verify no duplicates (the fix)
        nonces.sort();
        for i in 1..nonces.len() {
            assert_ne!(nonces[i], nonces[i-1], 
                      "REGRESSION: Duplicate nonce detected: {} at positions {} and {}", 
                      nonces[i], i-1, i);
        }

        // Verify sequential ordering
        let first_nonce = nonces[0];
        for (i, &nonce) in nonces.iter().enumerate() {
            assert_eq!(nonce, first_nonce + i as u64,
                      "Nonces should be sequential starting from {}", first_nonce);
        }
    }

    /// REGRESSION TEST: Gas estimation bounds checking
    /// Previously: extreme gas prices could cause overflow or unreasonable values
    /// Fix: Proper bounds checking and capping
    #[tokio::test]
    async fn test_gas_estimation_bounds_regression() {
        let gas_estimator = GasEstimator::new(
            ethers::providers::Provider::try_from("https://mainnet.base.org").unwrap(),
            1,
        );

        // Test all urgency levels for bounds compliance
        let urgencies = vec![
            GasUrgency::Low,
            GasUrgency::Standard,
            GasUrgency::High,
            GasUrgency::Emergency,
        ];

        for urgency in urgencies {
            let result = gas_estimator.estimate_gas_params(urgency).await;
            
            if let Ok(params) = result {
                // Verify reasonable bounds (the fix)
                assert!(params.gas_price > U256::zero(), 
                       "Gas price should be positive for {:?}", urgency);
                assert!(params.gas_price <= U256::from(1000_000_000_000u64), 
                       "Gas price should be capped at 1000 gwei for {:?}, got: {}", 
                       urgency, params.gas_price);
                
                assert!(params.gas_limit > U256::zero(), 
                       "Gas limit should be positive for {:?}", urgency);
                assert!(params.gas_limit <= U256::from(10_000_000u64), 
                       "Gas limit should be reasonable for {:?}, got: {}", 
                       urgency, params.gas_limit);
            }
        }
    }

    /// REGRESSION TEST: Circuit breaker state consistency
    /// Previously: race conditions could cause inconsistent circuit breaker state
    /// Fix: Proper state synchronization
    #[tokio::test]
    async fn test_circuit_breaker_state_consistency_regression() {
        let circuit_breaker = CircuitBreaker::new(
            5, // max_failures
            Duration::from_millis(100), // reset_timeout
        );

        // Concurrent failure recording (previously caused state inconsistency)
        let mut handles = Vec::new();
        for _ in 0..10 {
            let cb = circuit_breaker.clone();
            let handle = tokio::spawn(async move {
                cb.record_failure().await;
            });
            handles.push(handle);
        }

        // Wait for all failures to be recorded
        for handle in handles {
            handle.await.unwrap();
        }

        // Circuit should be open after exceeding max failures
        assert!(circuit_breaker.is_open().await, 
               "REGRESSION: Circuit breaker should be open after exceeding max failures");

        // Test reset functionality
        circuit_breaker.reset().await;
        assert!(!circuit_breaker.is_open().await, 
               "Circuit breaker should be closed after reset");

        // Test that state remains consistent after reset
        for _ in 0..3 {
            circuit_breaker.record_failure().await;
            assert!(!circuit_breaker.is_open().await, 
                   "Circuit should remain closed below threshold after reset");
        }
    }

    /// REGRESSION TEST: Transaction replacement validation
    /// Previously: could replace transactions with lower gas prices
    /// Fix: Proper gas price validation for replacements
    #[tokio::test]
    async fn test_transaction_replacement_validation_regression() {
        let nonce_manager = NonceManager::new(Address::zero(), 1).await.unwrap();
        
        let nonce = nonce_manager.get_next_nonce().await.unwrap();
        let original_hash = H256::random();
        let original_gas_price = U256::from(20_000_000_000u64); // 20 gwei

        // Register original transaction
        nonce_manager.register_pending_transaction(
            nonce,
            original_hash,
            original_gas_price,
            None,
            None,
            ethers::types::TransactionRequest::default(),
        );

        // Attempt to replace with lower gas price (should fail)
        let replacement_hash = H256::random();
        let lower_gas_price = U256::from(15_000_000_000u64); // 15 gwei

        let result = nonce_manager.replace_transaction(
            nonce,
            replacement_hash,
            lower_gas_price,
        ).await;

        assert!(result.is_err(), 
               "REGRESSION: Should not allow replacement with lower gas price");

        // Replacement with higher gas price should succeed
        let higher_gas_price = U256::from(25_000_000_000u64); // 25 gwei
        let valid_replacement_hash = H256::random();

        let result = nonce_manager.replace_transaction(
            nonce,
            valid_replacement_hash,
            higher_gas_price,
        ).await;

        assert!(result.is_ok(), 
               "Replacement with higher gas price should succeed");
    }
}

/// Regression test suite for risk management fixes
#[cfg(test)]
mod risk_regression_tests {
    use super::*;
    use basilisk_bot::risk::kelly::KellyCriterion;
    use basilisk_bot::risk::manager::RiskManager;
    use basilisk_bot::config::RiskConfig;
    use basilisk_bot::shared_types::RunMode;

    /// REGRESSION TEST: Kelly Criterion overflow protection
    /// Previously: extreme values could cause overflow in Kelly calculations
    /// Fix: Proper bounds checking and overflow protection
    #[test]
    fn test_kelly_criterion_overflow_regression() {
        let kelly = KellyCriterion::new(dec!(0.25));

        // Test with extreme values that previously caused overflow
        let extreme_win = dec!(1e10);
        let extreme_loss = dec!(1e9);
        
        let result = kelly.calculate_optimal_fraction(
            dec!(0.6), // win_rate
            extreme_win,
            extreme_loss,
        );

        // Should not overflow and should respect cap (the fix)
        assert!(result.is_finite(), "Kelly result should be finite with extreme values");
        assert!(result <= dec!(0.25), "Kelly result should respect cap: {}", result);
        assert!(result >= dec!(0.0), "Kelly result should be non-negative: {}", result);
    }

    /// REGRESSION TEST: Kelly Criterion with zero/negative scenarios
    /// Previously: division by zero or incorrect handling of edge cases
    /// Fix: Proper edge case handling
    #[test]
    fn test_kelly_criterion_edge_cases_regression() {
        let kelly = KellyCriterion::new(dec!(0.25));

        // Zero win rate (previously caused division by zero)
        let zero_win_rate = kelly.calculate_optimal_fraction(
            dec!(0.0), // 0% win rate
            dec!(100.0),
            dec!(50.0),
        );
        assert_eq!(zero_win_rate, dec!(0.0), "Zero win rate should give zero fraction");

        // Zero average loss (edge case)
        let zero_loss = kelly.calculate_optimal_fraction(
            dec!(0.8), // 80% win rate
            dec!(100.0),
            dec!(0.0), // zero loss
        );
        assert_eq!(zero_loss, dec!(0.25), "Zero loss with positive win rate should hit cap");

        // Negative expectation (unprofitable scenario)
        let negative_expectation = kelly.calculate_optimal_fraction(
            dec!(0.3), // 30% win rate
            dec!(50.0), // small win
            dec!(100.0), // large loss
        );
        assert!(negative_expectation <= dec!(0.0), "Negative expectation should give zero or negative fraction");
    }

    /// REGRESSION TEST: Risk manager circuit breaker race conditions
    /// Previously: concurrent PnL updates could cause inconsistent state
    /// Fix: Proper synchronization of risk state
    #[tokio::test]
    async fn test_risk_manager_concurrent_updates_regression() {
        let risk_config = RiskConfig {
            max_position_size_usd: dec!(1000.0),
            max_daily_loss: dec!(500.0),
            max_consecutive_failures: 5,
            kelly_fraction_cap: dec!(0.25),
            volatility_lookback_periods: 20,
            regime_risk_multipliers: HashMap::new(),
        };

        let nats_client = basilisk_bot::nats::NatsClient::new("nats://localhost:4222").await.unwrap();
        let risk_manager = RiskManager::new(
            nats_client,
            risk_config,
            RunMode::Live,
        ).await.unwrap();

        // Concurrent PnL updates (previously caused race conditions)
        let mut handles = Vec::new();
        for i in 0..100 {
            let rm = risk_manager.clone();
            let handle = tokio::spawn(async move {
                rm.update_pnl(dec!(-10.0), &format!("concurrent_trade_{}", i)).await;
            });
            handles.push(handle);
        }

        // Wait for all updates
        for handle in handles {
            handle.await.unwrap();
        }

        // Risk manager should be in consistent state (the fix)
        // After 100 * $10 = $1000 loss, should exceed $500 limit and halt
        assert!(risk_manager.is_trading_halted().await, 
               "REGRESSION: Risk manager should halt after exceeding daily loss limit");

        // State should remain consistent
        assert!(!risk_manager.is_trade_acceptable(dec!(100.0)).await, 
               "Should reject trades when halted");
    }

    /// REGRESSION TEST: Position sizing validation
    /// Previously: could approve positions exceeding limits due to race conditions
    /// Fix: Atomic position size checking
    #[tokio::test]
    async fn test_position_sizing_validation_regression() {
        let risk_config = RiskConfig {
            max_position_size_usd: dec!(1000.0),
            max_daily_loss: dec!(5000.0), // High limit to avoid daily loss halt
            max_consecutive_failures: 10,
            kelly_fraction_cap: dec!(0.25),
            volatility_lookback_periods: 20,
            regime_risk_multipliers: HashMap::new(),
        };

        let nats_client = basilisk_bot::nats::NatsClient::new("nats://localhost:4222").await.unwrap();
        let risk_manager = RiskManager::new(
            nats_client,
            risk_config,
            RunMode::Live,
        ).await.unwrap();

        // Test position size validation
        assert!(risk_manager.is_trade_acceptable(dec!(500.0)).await, 
               "Should accept trade within position limit");
        assert!(risk_manager.is_trade_acceptable(dec!(1000.0)).await, 
               "Should accept trade at position limit");
        assert!(!risk_manager.is_trade_acceptable(dec!(1500.0)).await, 
               "REGRESSION: Should reject trade exceeding position limit");

        // Concurrent position checks (previously could cause race conditions)
        let mut handles = Vec::new();
        for _ in 0..50 {
            let rm = risk_manager.clone();
            let handle = tokio::spawn(async move {
                rm.is_trade_acceptable(dec!(1200.0)).await // Above limit
            });
            handles.push(handle);
        }

        // All should consistently reject the oversized position
        for handle in handles {
            let result = handle.await.unwrap();
            assert!(!result, "All concurrent checks should reject oversized position");
        }
    }
}

/// Regression test suite for configuration validation fixes
#[cfg(test)]
mod config_regression_tests {
    use super::*;
    use basilisk_bot::config::ScoringConfig;

    /// REGRESSION TEST: Configuration validation completeness
    /// Previously: invalid configurations could pass validation
    /// Fix: Comprehensive validation rules
    #[test]
    fn test_config_validation_regression() {
        // Valid configuration should pass
        let valid_config = ScoringConfig {
            temporal_harmonics_weight: dec!(0.33),
            geometric_score_weight: dec!(0.33),
            network_resonance_weight: dec!(0.34),
            quality_ratio_floor: dec!(0.3),
            risk_aversion_k: dec!(0.5),
            regime_multiplier_retail_fomo: dec!(1.2),
            regime_multiplier_high_vol: dec!(0.8),
            regime_multiplier_calm: dec!(1.0),
            regime_multiplier_gas_war_penalty: dec!(0.5),
        };
        assert!(valid_config.validate().is_ok(), "Valid configuration should pass validation");

        // Previously passing invalid configurations should now fail

        // Invalid weight sum (previously not checked)
        let invalid_weights = ScoringConfig {
            temporal_harmonics_weight: dec!(0.6),
            geometric_score_weight: dec!(0.6),
            network_resonance_weight: dec!(0.6), // Sum = 1.8 (invalid)
            ..valid_config.clone()
        };
        assert!(invalid_weights.validate().is_err(), 
               "REGRESSION: Invalid weight sum should fail validation");

        // Invalid risk aversion (previously not bounded)
        let invalid_risk_aversion = ScoringConfig {
            risk_aversion_k: dec!(5.0), // > 2.0 limit
            ..valid_config.clone()
        };
        assert!(invalid_risk_aversion.validate().is_err(), 
               "REGRESSION: Invalid risk aversion should fail validation");

        // Invalid regime multipliers (previously not bounded)
        let invalid_regime_multiplier = ScoringConfig {
            regime_multiplier_retail_fomo: dec!(10.0), // > 5.0 limit
            ..valid_config.clone()
        };
        assert!(invalid_regime_multiplier.validate().is_err(), 
               "REGRESSION: Invalid regime multiplier should fail validation");

        // Negative quality ratio floor (previously allowed)
        let negative_quality_floor = ScoringConfig {
            quality_ratio_floor: dec!(-0.1), // Negative (invalid)
            ..valid_config.clone()
        };
        assert!(negative_quality_floor.validate().is_err(), 
               "REGRESSION: Negative quality ratio floor should fail validation");
    }
}

/// Integration regression tests for component interactions
#[cfg(test)]
mod integration_regression_tests {
    use super::*;

    /// REGRESSION TEST: End-to-end scoring pipeline consistency
    /// Previously: missing pillar data could cause inconsistent scoring
    /// Fix: Proper fallback handling and neutral defaults
    #[tokio::test]
    async fn test_scoring_pipeline_consistency_regression() {
        use basilisk_bot::strategies::scoring::ScoringEngine;
        use basilisk_bot::config::ScoringConfig;
        use basilisk_bot::shared_types::{TemporalHarmonics, NetworkResonanceState, MarketRegime};

        let config = ScoringConfig::default();
        let scoring_engine = create_test_scoring_engine(config);
        let opportunity = create_test_opportunity();
        let market_regime = MarketRegime::CalmOrderly;
        let centrality_scores = std::sync::Arc::new(create_test_centrality_scores());

        // Test with complete data
        let complete_temporal = Some(TemporalHarmonics {
            dominant_cycles_minutes: vec![60, 240],
            market_rhythm_stability: 0.8,
            wavelet_features: vec![0.1, 0.2],
        });
        let complete_network = Some(NetworkResonanceState {
            sp_time_ms: 120.0,
            network_coherence_score: 0.85,
            is_shock_event: false,
            sp_time_20th_percentile: 90.0,
            sequencer_status: "Healthy".to_string(),
            censorship_detected: false,
        });

        let complete_score = scoring_engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &complete_temporal,
            &complete_network,
            &centrality_scores,
        ).await;

        // Test with missing data (previously caused inconsistent results)
        let missing_score = scoring_engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &None, // Missing temporal data
            &None, // Missing network data
            &centrality_scores,
        ).await;

        // Both scores should be valid and positive (the fix)
        assert!(complete_score > dec!(0.0), "Complete data score should be positive");
        assert!(missing_score > dec!(0.0), "Missing data score should be positive with fallbacks");
        
        // Scores should be reasonably close (consistent fallback behavior)
        let score_ratio = if complete_score > missing_score {
            complete_score / missing_score
        } else {
            missing_score / complete_score
        };
        assert!(score_ratio <= dec!(3.0), 
               "REGRESSION: Scores with/without data should be reasonably consistent, ratio: {}", 
               score_ratio);
    }

    // Helper functions
    fn create_test_scoring_engine(config: ScoringConfig) -> ScoringEngine {
        use basilisk_bot::data::oracle::MockPriceOracle;
        use basilisk_bot::math::geometry::MockGeometricScorer;
        
        let mock_oracle = MockPriceOracle::new();
        let mock_scorer = MockGeometricScorer::new();
        
        ScoringEngine::new(config, mock_scorer)
    }
    
    fn create_test_centrality_scores() -> HashMap<String, Decimal> {
        let mut scores = HashMap::new();
        scores.insert("WETH".to_string(), dec!(0.95));
        scores.insert("USDC".to_string(), dec!(0.90));
        scores
    }
    
    fn create_test_opportunity() -> basilisk_bot::shared_types::Opportunity {
        use basilisk_bot::shared_types::{Opportunity, OpportunityBase, ArbitragePath, ArbitragePool};
        use ethers::types::Address;
        
        Opportunity::Swap {
            base: OpportunityBase {
                id: "regression_test_opportunity".to_string(),
                estimated_gross_profit_usd: dec!(100.0),
                intersection_value_usd: dec!(50.0),
                associated_volatility: dec!(0.1),
                source_scanner: "regression_test_scanner".to_string(),
                created_at: chrono::Utc::now(),
            },
            path: ArbitragePath {
                pools: vec![
                    ArbitragePool {
                        address: Address::zero(),
                        token_a: Address::zero(),
                        token_b: Address::zero(),
                        reserve_a: dec!(1000.0),
                        reserve_b: dec!(2000.0),
                        fee_bps: 30,
                    },
                ],
            },
        }
    }
}
