// MISSION: Control Message Types - TUI Command & Control Infrastructure
// WHY: Standardized message types for TUI control commands and responses
// HOW: Serde-serializable structs for NATS message passing

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;

/// Emergency stop command - highest priority control message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmergencyStopCommand {
    pub timestamp: DateTime<Utc>,
    pub reason: String,
    pub initiated_by: String, // "TUI", "API", "SIGINT", etc.
    pub command_id: uuid::Uuid,
}

/// Trading control commands for pause/resume/stop operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingControlCommand {
    pub action: TradingAction,
    pub timestamp: DateTime<Utc>,
    pub initiated_by: String,
    pub command_id: uuid::Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TradingAction {
    Pause,
    Resume,
    Stop,
    Restart,
}

/// Configuration parameter update command
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigUpdateCommand {
    pub section: String,
    pub parameter: String,
    pub old_value: serde_json::Value,
    pub new_value: serde_json::Value,
    pub timestamp: DateTime<Utc>,
    pub initiated_by: String,
    pub command_id: uuid::Uuid,
}

/// Strategy-specific control commands
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyControlCommand {
    pub strategy_name: String,
    pub action: StrategyAction,
    pub timestamp: DateTime<Utc>,
    pub command_id: uuid::Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StrategyAction {
    Pause,
    Resume,
    Restart,
    UpdateParameters(serde_json::Value),
}

/// Service management commands
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceControlCommand {
    pub service_name: String,
    pub action: ServiceAction,
    pub timestamp: DateTime<Utc>,
    pub command_id: uuid::Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServiceAction {
    Start,
    Stop,
    Restart,
    HealthCheck,
}

/// Command acknowledgment response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandAcknowledgment {
    pub command_id: uuid::Uuid,
    pub success: bool,
    pub message: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub service_name: String,
    pub execution_time_ms: Option<u64>,
}

/// Treasury/balance update message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TreasuryUpdate {
    pub total_balance_usd: Decimal,
    pub available_balance_usd: Decimal,
    pub locked_balance_usd: Decimal,
    pub pnl_24h: Decimal,
    pub timestamp: DateTime<Utc>,
    pub chain_balances: std::collections::HashMap<String, ChainBalance>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChainBalance {
    pub chain_id: u64,
    pub native_balance: Decimal,
    pub token_balances: std::collections::HashMap<String, Decimal>,
    pub last_updated: DateTime<Utc>,
}

/// System health status update
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthUpdate {
    pub overall_status: String, // "healthy", "degraded", "critical"
    pub services: std::collections::HashMap<String, ServiceHealthStatus>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHealthStatus {
    pub status: String, // "running", "stopped", "error"
    pub last_heartbeat: DateTime<Utc>,
    pub error_message: Option<String>,
    pub uptime_seconds: u64,
}

/// Opportunity detection message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpportunityDetected {
    pub id: uuid::Uuid,
    pub scanner: String,
    pub opportunity_type: String,
    pub pair: String,
    pub estimated_profit_usd: Decimal,
    pub confidence_score: Decimal,
    pub timestamp: DateTime<Utc>,
    pub chain_id: u64,
}

/// Trade execution completion message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeCompleted {
    pub trade_id: String,
    pub opportunity_id: Option<String>,
    pub strategy: String,
    pub assets: String,
    pub profit_loss_token: Decimal,
    pub profit_loss_usd: Decimal,
    pub gas_used: Option<u64>,
    pub gas_price_gwei: Option<Decimal>,
    pub transaction_hash: Option<String>,
    pub status: String, // "success", "failed_slippage", "failed_network", "failed_gas"
    pub timestamp: DateTime<Utc>,
    pub execution_time_ms: u64,
    pub market_regime_at_execution: Option<String>,
    pub aetheric_resonance_at_execution: Option<super::AethericResonanceScoreDetail>,
    pub execution_details: Option<String>,
    pub trade_explanation: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SigintReport {
    pub report_id: String,
    pub expires_at: String, // ISO 8601 timestamp
    pub directives: Vec<super::SigintDirective>,
    pub created_at: u64, // Unix timestamp
}
