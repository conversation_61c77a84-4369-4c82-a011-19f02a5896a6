// Test for Backend Integration Tester
// This test verifies that the Backend Integration Tester component works correctly

use std::time::Duration;
use anyhow::Result;

// Import the integration testing modules
mod integration {
    pub mod stargate_compass {
        pub mod core;
        pub mod backend_tester;
        pub mod anvil_client;
        pub mod utils;
    }
}

use integration::stargate_compass::{
    core::*,
    backend_tester::BackendIntegrationTester,
    anvil_client::{AnvilClient, is_anvil_available},
};

#[tokio::test]
async fn test_backend_integration_tester_creation() -> Result<()> {
    println!("🧪 Testing Backend Integration Tester creation...");

    // Test that BackendIntegrationTester can be created
    let backend_tester = BackendIntegrationTester::new();
    assert_eq!(backend_tester.component_name(), "BackendIntegration");

    // Test that it reports as ready
    let is_ready = backend_tester.is_ready().await?;
    println!("✅ Backend tester ready status: {}", is_ready);

    // Test setup
    backend_tester.setup().await?;
    println!("✅ Backend tester setup completed");

    // Test cleanup
    backend_tester.cleanup().await?;
    println!("✅ Backend tester cleanup completed");

    println!("✅ Backend Integration Tester creation test passed!");
    Ok(())
}
