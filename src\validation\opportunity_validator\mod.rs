// src/validation/opportunity_validator/mod.rs

//! Opportunity detection and scanning validation

pub mod opportunity_validator;
pub mod swap_scanner_validator;
pub mod mempool_scanner_validator;
pub mod gaze_scanner_validator;

#[cfg(test)]
mod test_integration;

pub use opportunity_validator::{
    OpportunityValidator, OpportunityDetectionMetrics, ScannerMetrics, ScannerValidationMetrics,
    ProfitAccuracyMetrics, ScannerPerformanceMetrics, OpportunityQualityMetrics,
    ValidationTestResult, ResourceUsageMetrics
};
pub use swap_scanner_validator::SwapScannerValidator;
pub use mempool_scanner_validator::MempoolScannerValidator;
pub use gaze_scanner_validator::GazeScannerValidator;

use crate::validation::{ValidationFrameworkResult, TestScenario};
use std::time::Duration;

/// Trait for scanner-specific validation
#[async_trait::async_trait]
pub trait ScannerValidator {
    /// Validate scanner-specific functionality
    async fn validate_scanner(&self, test_scenario: &TestScenario) -> ValidationFrameworkResult<ScannerValidationMetrics>;
    
    /// Validate profit calculation accuracy
    async fn validate_profit_calculations(&self, opportunities: &[crate::shared_types::Opportunity]) -> ValidationFrameworkResult<ProfitAccuracyMetrics>;
    
    /// Validate scanner performance under load
    async fn validate_performance(&self, duration: Duration) -> ValidationFrameworkResult<ScannerPerformanceMetrics>;
    
    /// Get scanner name
    fn scanner_name(&self) -> &str;
}