// src/deployment/cli.rs
// CLI commands for deployment and rollback management

use anyhow::Result;
use clap::{Args, Subcommand};
use std::sync::Arc;
use tracing::{info, warn};

use super::{
    DeploymentConfig, DeploymentPhase, RollbackStrategy,
    feature_flags::FeatureFlagManager,
    health_checks::HealthCheckManager,
    monitoring::DeploymentMonitor,
    phased_deployment::PhasedDeploymentManager,
    rollback_manager::RollbackManager,
};

/// Deployment management CLI commands
#[derive(Debug, Args)]
pub struct DeploymentArgs {
    #[command(subcommand)]
    pub command: DeploymentCommand,
}

/// Deployment subcommands
#[derive(Debug, Subcommand)]
pub enum DeploymentCommand {
    /// Deploy to a specific phase
    Deploy {
        /// Target deployment phase
        #[arg(value_enum)]
        phase: DeploymentPhase,
        
        /// Skip validation checkpoints
        #[arg(long)]
        skip_validation: bool,
        
        /// Force deployment even if health checks fail
        #[arg(long)]
        force: bool,
    },
    
    /// Rollback to a previous phase
    Rollback {
        /// Target rollback phase (optional, defaults to previous phase)
        #[arg(value_enum)]
        phase: Option<DeploymentPhase>,
        
        /// Rollback strategy to use
        #[arg(long, value_enum)]
        strategy: Option<RollbackStrategy>,
        
        /// Force rollback even if not recommended
        #[arg(long)]
        force: bool,
    },
    
    /// Show current deployment status
    Status,
    
    /// Manage feature flags
    FeatureFlags {
        #[command(subcommand)]
        command: FeatureFlagCommand,
    },
    
    /// Run health checks
    Health {
        /// Run specific health check
        #[arg(long)]
        check: Option<String>,
        
        /// Continuous health monitoring
        #[arg(long)]
        continuous: bool,
    },
    
    /// Monitor deployment metrics
    Monitor {
        /// Monitoring interval in seconds
        #[arg(long, default_value = "30")]
        interval: u64,
        
        /// Output format (json, table, dashboard)
        #[arg(long, default_value = "dashboard")]
        format: String,
    },
    
    /// Validate deployment configuration
    Validate,
}

/// Feature flag subcommands
#[derive(Debug, Subcommand)]
pub enum FeatureFlagCommand {
    /// List all feature flags
    List {
        /// Show only enabled flags
        #[arg(long)]
        enabled_only: bool,
        
        /// Filter by deployment phase
        #[arg(long, value_enum)]
        phase: Option<DeploymentPhase>,
    },
    
    /// Enable a feature flag
    Enable {
        /// Feature flag name
        flag_name: String,
    },
    
    /// Disable a feature flag
    Disable {
        /// Feature flag name
        flag_name: String,
    },
    
    /// Update traffic percentage for a flag
    Traffic {
        /// Feature flag name
        flag_name: String,
        
        /// Traffic percentage (0-100)
        percentage: f64,
    },
    
    /// Validate feature flag dependencies
    Validate,
}

/// Handle deployment CLI commands
pub async fn handle_deployment_command(args: DeploymentArgs) -> Result<()> {
    match args.command {
        DeploymentCommand::Deploy { phase, skip_validation, force } => {
            handle_deploy_command(phase, skip_validation, force).await
        }
        DeploymentCommand::Rollback { phase, strategy, force } => {
            handle_rollback_command(phase, strategy, force).await
        }
        DeploymentCommand::Status => {
            handle_status_command().await
        }
        DeploymentCommand::FeatureFlags { command } => {
            handle_feature_flags_command(command).await
        }
        DeploymentCommand::Health { check, continuous } => {
            handle_health_command(check, continuous).await
        }
        DeploymentCommand::Monitor { interval, format } => {
            handle_monitor_command(interval, format).await
        }
        DeploymentCommand::Validate => {
            handle_validate_command().await
        }
    }
}

/// Handle deploy command
async fn handle_deploy_command(
    target_phase: DeploymentPhase,
    skip_validation: bool,
    force: bool,
) -> Result<()> {
    info!("Starting deployment to phase: {}", target_phase);

    // Load deployment configuration
    let config = load_deployment_config().await?;
    
    // Create deployment components
    let feature_flags = Arc::new(FeatureFlagManager::new(config.current_phase));
    let health_checks = Arc::new(HealthCheckManager::with_config(config.health_check_config.clone()));
    let monitor = Arc::new(DeploymentMonitor::with_config(config.monitoring_config.clone()));
    
    // Create deployment manager
    let deployment_manager = PhasedDeploymentManager::new(
        config,
        feature_flags,
        health_checks,
        monitor,
    );

    // Validate deployment if not skipped
    if !skip_validation {
        info!("Validating deployment prerequisites");
        // Add validation logic here
    }

    // Check if force is needed
    if !force {
        // Add safety checks here
        warn!("Use --force to bypass safety checks");
    }

    // Execute deployment
    deployment_manager.deploy_to_phase(target_phase).await?;
    
    info!("Deployment completed successfully to phase: {}", target_phase);
    Ok(())
}

/// Handle rollback command
async fn handle_rollback_command(
    target_phase: Option<DeploymentPhase>,
    strategy: Option<RollbackStrategy>,
    force: bool,
) -> Result<()> {
    info!("Starting rollback");

    // Load deployment configuration
    let config = Arc::new(tokio::sync::RwLock::new(load_deployment_config().await?));
    
    // Create deployment components
    let current_phase = {
        let cfg = config.read().await;
        cfg.current_phase
    };
    
    let feature_flags = Arc::new(FeatureFlagManager::new(current_phase));
    let health_checks = Arc::new(HealthCheckManager::new());
    let monitor = Arc::new(DeploymentMonitor::new());
    
    // Create rollback manager
    let rollback_manager = RollbackManager::new(
        Arc::new(tokio::sync::RwLock::new(config.read().await.clone())),
        Arc::clone(&feature_flags),
        Arc::clone(&health_checks),
        Arc::clone(&monitor),
    );

    // Determine target phase
    let target = target_phase.unwrap_or_else(|| {
        current_phase.previous_phase().unwrap_or(current_phase)
    });

    // Check rollback safety if not forced
    if !force && !rollback_manager.is_rollback_safe(target) {
        return Err(anyhow::anyhow!(
            "Rollback to {} is not safe. Use --force to override.",
            target
        ));
    }

    // Execute rollback
    rollback_manager.rollback_to_phase(target).await?;
    
    info!("Rollback completed successfully to phase: {}", target);
    Ok(())
}

/// Handle status command
async fn handle_status_command() -> Result<()> {
    let config = load_deployment_config().await?;
    
    println!("=== Deployment Status ===");
    println!("Current Phase: {}", config.current_phase);
    println!("Traffic Routing: {:.1}% new, {:.1}% legacy", 
        config.traffic_routing.new_implementation_percentage,
        config.traffic_routing.legacy_implementation_percentage
    );
    
    // Count enabled feature flags
    let enabled_flags = config.feature_flags.values().filter(|&&enabled| enabled).count();
    println!("Enabled Feature Flags: {}", enabled_flags);
    
    // Show health status
    let health_manager = HealthCheckManager::with_config(config.health_check_config);
    let health_results = health_manager.run_all_checks().await?;
    println!("Health Status: {}", if health_results { "Healthy" } else { "Unhealthy" });
    
    Ok(())
}

/// Handle feature flags command
async fn handle_feature_flags_command(command: FeatureFlagCommand) -> Result<()> {
    let config = load_deployment_config().await?;
    let feature_flags = FeatureFlagManager::new(config.current_phase);
    
    match command {
        FeatureFlagCommand::List { enabled_only, phase } => {
            let flags = if let Some(phase) = phase {
                feature_flags.get_flags_for_phase(phase)
            } else if enabled_only {
                feature_flags.get_enabled_flags()
            } else {
                feature_flags.get_all_flags().into_values().collect()
            };
            
            println!("=== Feature Flags ===");
            for flag in flags {
                let status = if flag.enabled { "ENABLED" } else { "DISABLED" };
                println!("{}: {} ({}% traffic) - {}", 
                    flag.name, status, flag.traffic_percentage, flag.description);
            }
        }
        FeatureFlagCommand::Enable { flag_name } => {
            feature_flags.enable_flag(&flag_name)?;
            println!("Enabled feature flag: {}", flag_name);
        }
        FeatureFlagCommand::Disable { flag_name } => {
            feature_flags.disable_flag(&flag_name)?;
            println!("Disabled feature flag: {}", flag_name);
        }
        FeatureFlagCommand::Traffic { flag_name, percentage } => {
            feature_flags.update_traffic_percentage(&flag_name, percentage)?;
            println!("Updated traffic percentage for {}: {}%", flag_name, percentage);
        }
        FeatureFlagCommand::Validate => {
            feature_flags.validate_dependencies()?;
            println!("Feature flag dependencies are valid");
        }
    }
    
    Ok(())
}

/// Handle health command
async fn handle_health_command(check: Option<String>, continuous: bool) -> Result<()> {
    let config = load_deployment_config().await?;
    let health_manager = HealthCheckManager::with_config(config.health_check_config);
    
    if continuous {
        info!("Starting continuous health monitoring");
        loop {
            let results = health_manager.run_all_checks().await?;
            println!("[{}] Health Status: {}", 
                chrono::Utc::now().format("%Y-%m-%d %H:%M:%S"),
                if results { "Healthy" } else { "Unhealthy" }
            );
            tokio::time::sleep(tokio::time::Duration::from_secs(30)).await;
        }
    } else if let Some(check_name) = check {
        let result = health_manager.run_check(&check_name).await?;
        println!("Health Check '{}': {} - {}", 
            result.name, result.status, result.message);
    } else {
        let results = health_manager.run_all_checks().await?;
        println!("Overall Health Status: {}", 
            if results { "Healthy" } else { "Unhealthy" });
        
        let latest_results = health_manager.get_latest_results();
        for (name, result) in latest_results {
            println!("  {}: {} - {}", name, result.status, result.message);
        }
    }
    
    Ok(())
}

/// Handle monitor command
async fn handle_monitor_command(interval: u64, format: String) -> Result<()> {
    let config = load_deployment_config().await?;
    let monitor = DeploymentMonitor::with_config(config.monitoring_config);
    
    match format.as_str() {
        "json" => {
            loop {
                let metrics = monitor.collect_deployment_metrics().await?;
                println!("{}", serde_json::to_string_pretty(&metrics)?);
                tokio::time::sleep(tokio::time::Duration::from_secs(interval)).await;
            }
        }
        "table" => {
            loop {
                let metrics = monitor.collect_deployment_metrics().await?;
                println!("=== Deployment Metrics ===");
                for (key, value) in metrics {
                    println!("{}: {}", key, value);
                }
                println!();
                tokio::time::sleep(tokio::time::Duration::from_secs(interval)).await;
            }
        }
        "dashboard" => {
            info!("Starting deployment monitoring dashboard");
            monitor.start_monitoring().await?;
            
            // Keep running until interrupted
            tokio::signal::ctrl_c().await?;
            monitor.stop_monitoring();
        }
        _ => {
            return Err(anyhow::anyhow!("Unknown format: {}", format));
        }
    }
    
    Ok(())
}

/// Handle validate command
async fn handle_validate_command() -> Result<()> {
    let config = load_deployment_config().await?;
    
    println!("=== Deployment Configuration Validation ===");
    
    // Validate configuration
    println!("✓ Configuration loaded successfully");
    
    // Validate feature flags
    let feature_flags = FeatureFlagManager::new(config.current_phase);
    feature_flags.validate_dependencies()?;
    println!("✓ Feature flag dependencies are valid");
    
    // Validate health checks
    let health_manager = HealthCheckManager::with_config(config.health_check_config);
    let health_results = health_manager.run_all_checks().await?;
    if health_results {
        println!("✓ Health checks passed");
    } else {
        println!("✗ Health checks failed");
    }
    
    // Validate monitoring
    let monitor = DeploymentMonitor::with_config(config.monitoring_config);
    let _metrics = monitor.collect_deployment_metrics().await?;
    println!("✓ Metrics collection working");
    
    println!("Deployment configuration validation completed");
    Ok(())
}

/// Load deployment configuration
async fn load_deployment_config() -> Result<DeploymentConfig> {
    // This would typically load from the deployment.toml file
    // For now, return a default configuration
    Ok(DeploymentConfig::default())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_load_deployment_config() {
        let config = load_deployment_config().await;
        assert!(config.is_ok());
    }

    #[tokio::test]
    async fn test_handle_status_command() {
        let result = handle_status_command().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_handle_validate_command() {
        let result = handle_validate_command().await;
        assert!(result.is_ok());
    }
}